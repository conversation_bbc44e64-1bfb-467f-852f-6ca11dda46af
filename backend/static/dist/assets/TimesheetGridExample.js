import{T as y}from"./TimesheetGrid.js";import{r as b,c as n,b as c,j as l,e as f,l as D,t as k,F as G,p as j,o as m}from"./vendor.js";import"./app.js";const L={class:"space-y-8"},I={class:"space-y-4"},O={class:"space-y-4"},W={class:"flex items-center justify-between mb-4"},B={class:"flex items-center space-x-2"},H={class:"text-sm text-gray-500"},R={class:"space-y-4"},V={class:"space-y-4"},q={class:"bg-gray-50 dark:bg-gray-800 rounded-lg p-4 max-h-64 overflow-y-auto"},J={key:0,class:"text-gray-500 dark:text-gray-400 text-sm"},U={key:1,class:"space-y-2"},K={class:"text-gray-500 dark:text-gray-400"},Q={class:"ml-2"},te={__name:"TimesheetGridExample",setup(X){const g=b(!1),o=b(new Map),v=b([]),u=[{key:"2024-01-01",label:"1",sublabel:"Lun",isWeekend:!1,isToday:!1},{key:"2024-01-02",label:"2",sublabel:"Mar",isWeekend:!1,isToday:!1},{key:"2024-01-03",label:"3",sublabel:"Mer",isWeekend:!1,isToday:!1},{key:"2024-01-04",label:"4",sublabel:"Gio",isWeekend:!1,isToday:!1},{key:"2024-01-05",label:"5",sublabel:"Ven",isWeekend:!1,isToday:!1},{key:"2024-01-06",label:"6",sublabel:"Sab",isWeekend:!0,isToday:!1},{key:"2024-01-07",label:"7",sublabel:"Dom",isWeekend:!0,isToday:!1},{key:"2024-01-08",label:"8",sublabel:"Lun",isWeekend:!1,isToday:!0},{key:"2024-01-09",label:"9",sublabel:"Mar",isWeekend:!1,isToday:!1},{key:"2024-01-10",label:"10",sublabel:"Mer",isWeekend:!1,isToday:!1}],h=[{id:1,name:"CRM Development - Frontend Dashboard",projectName:"CRM Development",taskName:"Frontend Dashboard",assignees:"Mario R., Giulia B.",hours:{"2024-01-01":"8.0","2024-01-02":"7.5","2024-01-03":"8.0"},billing:{"2024-01-01":!0,"2024-01-02":!0,"2024-01-03":!0},total:"23.5"},{id:2,name:"E-commerce Platform - API Integration",projectName:"E-commerce Platform",taskName:"API Integration",assignees:"Luca M.",hours:{"2024-01-01":"4.0","2024-01-02":"6.0","2024-01-04":"8.0"},billing:{"2024-01-01":!0,"2024-01-02":!0,"2024-01-04":!1},total:"18.0"},{id:3,name:"Internal Tools - Documentation",projectName:"Internal Tools",taskName:"Documentation",assignees:"Sara T.",hours:{"2024-01-03":"4.0","2024-01-05":"3.5"},billing:{"2024-01-03":!1,"2024-01-05":!1},total:"7.5"}],d=b(JSON.parse(JSON.stringify(h))),T=[{id:4,name:"Sistema di Gestione Avanzata per il Monitoraggio delle Performance Aziendali con Dashboard Interattive e Report Automatizzati",projectName:"Sistema di Gestione Avanzata per il Monitoraggio delle Performance Aziendali",taskName:"Dashboard Interattive e Report Automatizzati",assignees:"Team Frontend: Alessandro M., Francesca R., Roberto T.",hours:{"2024-01-01":"8.0","2024-01-02":"6.5","2024-01-03":"7.0"},billing:{"2024-01-01":!0,"2024-01-02":!0,"2024-01-03":!0},total:"21.5"},{id:5,name:"Piattaforma E-commerce Multi-Tenant con Integrazione Avanzata di Sistemi di Pagamento e Gestione Inventario Automatizzata",projectName:"Piattaforma E-commerce Multi-Tenant con Integrazione Avanzata",taskName:"Sistemi di Pagamento e Gestione Inventario Automatizzata",assignees:"Team Backend: Marco V., Elena S., Davide P., Chiara L.",hours:{"2024-01-02":"8.0","2024-01-03":"8.0","2024-01-04":"7.5"},billing:{"2024-01-02":!0,"2024-01-03":!0,"2024-01-04":!0},total:"23.5"}],x=n(()=>{const t={};return u.forEach(e=>{t[e.key]=h.reduce((a,s)=>a+parseFloat(s.hours[e.key]||0),0)}),t}),w=n(()=>Object.values(x.value).reduce((t,e)=>t+e,0)),F=n(()=>({status:"pending",totalHours:w.value,billableHours:h.reduce((t,e)=>t+Object.entries(e.billing||{}).filter(([a,s])=>s&&e.hours[a]).reduce((a,[s])=>a+parseFloat(e.hours[s]||0),0),0),pendingChanges:0})),z=n(()=>{const t={};return u.forEach(e=>{t[e.key]=d.value.reduce((a,s)=>a+parseFloat(s.hours[e.key]||0),0)}),t}),C=n(()=>Object.values(z.value).reduce((t,e)=>t+e,0)),M=n(()=>({status:"pending",totalHours:C.value,billableHours:d.value.reduce((t,e)=>t+Object.entries(e.billing||{}).filter(([a,s])=>s&&e.hours[a]).reduce((a,[s])=>a+parseFloat(e.hours[s]||0),0),0),pendingChanges:o.value.size})),N=n(()=>{const t={};return u.slice(0,7).forEach(e=>{t[e.key]=T.reduce((a,s)=>a+parseFloat(s.hours[e.key]||0),0)}),t}),_=n(()=>Object.values(N.value).reduce((t,e)=>t+e,0)),i=t=>{v.value.push({timestamp:new Date().toLocaleTimeString(),message:t})},p=(t,e)=>{i(`Clic su cella: ${t.name} - ${e.label}/${e.sublabel} (${t.hours[e.key]||0}h)`)},$=async(t,e,a)=>{i(`Aggiornamento diretto: Task ${t}, ${e}, ${a}h`),g.value=!0,await new Promise(r=>setTimeout(r,500));const s=d.value.find(r=>r.id===t);s&&(parseFloat(a)>0?s.hours[e]=parseFloat(a).toFixed(1):delete s.hours[e],s.total=Object.values(s.hours).reduce((r,A)=>r+parseFloat(A),0).toFixed(1)),g.value=!1,i(`✅ Salvato: Task ${t}, ${e}, ${a}h`)},E=(t,e,a)=>{const s=`${t}-${e}`;a&&parseFloat(a)>0?(o.value.set(s,{taskId:t,date:e,hours:parseFloat(a)}),i(`📝 Modifica in attesa: Task ${t}, ${e}, ${a}h`)):(o.value.delete(s),i(`🗑️ Rimossa modifica: Task ${t}, ${e}`))},S=async t=>{i(`💾 Inizio salvataggio batch: ${t.size} modifiche`),g.value=!0,await new Promise(e=>setTimeout(e,1e3));for(const[e,a]of t.entries()){const s=d.value.find(r=>r.id===a.taskId);s&&(a.hours>0?s.hours[a.date]=a.hours.toFixed(1):delete s.hours[a.date])}d.value.forEach(e=>{e.total=Object.values(e.hours).reduce((a,s)=>a+parseFloat(s),0).toFixed(1)}),o.value.clear(),g.value=!1,i(`✅ Salvataggio batch completato: ${t.size} modifiche salvate`)},P=()=>{const t=o.value.size;o.value.clear(),i(`🧹 Annullate ${t} modifiche in attesa`)};return(t,e)=>(m(),c("div",L,[e[6]||(e[6]=l("div",null,[l("h2",{class:"text-xl font-semibold text-gray-900 dark:text-white mb-4"}," TimesheetGrid - Griglia Timesheet Ottimizzata "),l("p",{class:"text-gray-600 dark:text-gray-400 mb-6"}," Griglia per la gestione delle ore con funzionalità avanzate: tooltip per nomi lunghi, celle editabili per inserimento massivo, salvataggio batch delle modifiche. ")],-1)),l("div",I,[e[0]||(e[0]=l("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Esempio Base - Modalità Solo Lettura ",-1)),f(y,{title:"Timesheet Gennaio 2024",tasks:h,days:u,"daily-totals":x.value,"grand-total":w.value,loading:!1,editable:!1,"show-stats":!0,"show-day-totals":!0,"show-indicators":!0,"show-legend":!0,status:F.value,"row-header-label":"Progetto/Task","empty-message":"Nessun timesheet per questo periodo",onCellClick:p},null,8,["daily-totals","grand-total","status"])]),l("div",O,[e[2]||(e[2]=l("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Esempio Editabile - Con Inserimento Massivo ",-1)),l("div",W,[e[1]||(e[1]=l("p",{class:"text-sm text-gray-600 dark:text-gray-400"},` Clicca su "Modifica Massiva" per abilitare l'editing delle celle. Le modifiche vengono salvate tutte insieme. `,-1)),l("div",B,[l("span",H," Modifiche in attesa: "+k(o.value.size),1),o.value.size>0?(m(),c("button",{key:0,onClick:P,class:"text-sm text-red-600 hover:text-red-700"}," Annulla Tutto ")):D("",!0)])]),f(y,{title:"Timesheet Gennaio 2024 - Editabile",tasks:d.value,days:u,"daily-totals":z.value,"grand-total":C.value,loading:g.value,editable:!0,"show-stats":!0,"show-day-totals":!0,"show-indicators":!0,"show-legend":!0,status:M.value,"row-header-label":"Progetto/Task","empty-message":"Nessun timesheet per questo periodo",onCellClick:p,onCellUpdate:$,onBulkSave:S,onHoursChanged:E},null,8,["tasks","daily-totals","grand-total","loading","status"])]),l("div",R,[e[3]||(e[3]=l("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Esempio con Nomi Lunghi - Tooltip e Troncamento ",-1)),e[4]||(e[4]=l("p",{class:"text-sm text-gray-600 dark:text-gray-400 mb-4"}," I nomi dei task vengono troncati automaticamente e mostrati per intero nel tooltip. ",-1)),f(y,{title:"Timesheet con Task Lunghi",tasks:T,days:u.slice(0,7),"daily-totals":N.value,"grand-total":_.value,loading:!1,editable:!1,"show-stats":!0,"show-day-totals":!0,"show-indicators":!0,"show-legend":!0,status:{status:"pending",totalHours:45.5,billableHours:32},"row-header-label":"Progetto/Task",onCellClick:p},null,8,["days","daily-totals","grand-total"])]),l("div",V,[e[5]||(e[5]=l("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Log Eventi ",-1)),l("div",q,[v.value.length===0?(m(),c("div",J," Nessun evento registrato ")):(m(),c("div",U,[(m(!0),c(G,null,j(v.value.slice().reverse(),(a,s)=>(m(),c("div",{key:s,class:"text-sm font-mono bg-white dark:bg-gray-700 p-2 rounded border"},[l("span",K,k(a.timestamp),1),l("span",Q,k(a.message),1)]))),128))]))])])]))}};export{te as default};
