import{c as W,H as R,m as rt}from"./app.js";import{d as at,r as L,c as C,b as l,o as n,j as t,e as v,k as N,s as D,l as h,t as i,F as P,p as U,B as J,Z as X,n as Q,H as et,I as it,h as st,x as ot,y as nt,v as S}from"./vendor.js";import{S as I}from"./StandardButton.js";const lt=at("security",()=>{const s=L([]),T=L([]),e=L({total_issues:0,by_severity:{critical:0,high:0,medium:0,low:0},risk_score:0,status:"unknown",reports_count:{static:0,dynamic:0}}),E=L({static_analysis:{status:"idle",last_scan:null},dynamic_testing:{status:"idle",last_scan:null}}),x=L(!1),w=L(null),z=L(null),A=C(()=>e.value.total_issues),k=C(()=>e.value.by_severity.critical),c=C(()=>e.value.by_severity.high),a=C(()=>e.value.by_severity.medium),b=C(()=>e.value.by_severity.low),u=C(()=>e.value.status),r=C(()=>e.value.risk_score),o=C(()=>s.value.length>0||T.value.length>0),V=C(()=>s.value.length>0?s.value[0]:null),F=C(()=>T.value.length>0?T.value[0]:null),B=C(()=>y=>({critical:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",high:"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"})[y]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"),$=C(()=>y=>{const p={critical:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",warning:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",good:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",unknown:"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"};return p[y]||p.unknown}),d=C(()=>y=>{const p={critical:"shield-exclamation",warning:"exclamation-triangle",good:"shield-check",unknown:"question-mark-circle"};return p[y]||p.unknown});async function _(){var y,p,G;x.value=!0,w.value=null;try{const j=await W.get("/api/security/reports");if(j.data.success){const H=j.data.data;s.value=H.static_analysis||[],T.value=H.dynamic_testing||[],e.value=H.summary||e.value,z.value=H.last_updated}else throw new Error(j.data.message||"Failed to fetch security reports")}catch(j){w.value=j.message||"An error occurred while fetching security reports",console.error("Security reports fetch error:",j),console.error("Error details:",{status:(y=j.response)==null?void 0:y.status,statusText:(p=j.response)==null?void 0:p.statusText,data:(G=j.response)==null?void 0:G.data,message:j.message})}finally{x.value=!1}}async function M(){x.value=!0,w.value=null;try{const y=await W.get("/api/security/reports/static");if(y.data.success)s.value=y.data.data.reports||[];else throw new Error(y.data.message||"Failed to fetch static reports")}catch(y){w.value=y.message||"An error occurred while fetching static reports",console.error("Static reports fetch error:",y)}finally{x.value=!1}}async function O(){x.value=!0,w.value=null;try{const y=await W.get("/api/security/reports/dynamic");if(y.data.success)T.value=y.data.data.reports||[];else throw new Error(y.data.message||"Failed to fetch dynamic reports")}catch(y){w.value=y.message||"An error occurred while fetching dynamic reports",console.error("Dynamic reports fetch error:",y)}finally{x.value=!1}}async function q(){try{const y=await W.get("/api/security/scan-status");if(y.data.success)E.value=y.data.data;else throw new Error(y.data.message||"Failed to fetch scan status")}catch(y){w.value=y.message||"An error occurred while fetching scan status",console.error("Scan status fetch error:",y)}}async function g(y="http://localhost:5000"){x.value=!0,w.value=null;try{const p=await W.post("/api/security/scan/start",{scan_type:"dynamic",target_url:y});if(p.data.success){const G=p.data.data;return E.value.dynamic_testing.status="running",console.log("Dynamic scan started:",G),setTimeout(()=>{_()},2e3),G}else throw new Error(p.data.message||"Failed to start dynamic scan")}catch(p){throw w.value=p.message||"An error occurred while starting dynamic scan",console.error("Dynamic scan start error:",p),p}finally{x.value=!1}}async function m(y){x.value=!0,w.value=null;try{const p=await W.post("/api/security/export",{report_type:"single_report",report_data:y});if(p.data.success)return p.data.data;throw new Error(p.data.message||"Failed to export report")}catch(p){throw w.value=p.message||"An error occurred while exporting report",console.error("Report export error:",p),p}finally{x.value=!1}}async function f(y){x.value=!0,w.value=null;try{const p=await W.post("/api/security/fix-vulnerability",{vulnerability:y});return p.data.success?p.data.data:p.data.data||{success:!1,reason:p.data.message,recommendations:["Manual intervention required"]}}catch(p){throw w.value=p.message||"An error occurred while fixing vulnerability",console.error("Vulnerability fix error:",p),p}finally{x.value=!1}}function Z(){w.value=null}return{staticReports:s,dynamicReports:T,summary:e,scanStatus:E,loading:x,error:w,lastUpdated:z,totalIssues:A,criticalIssues:k,highIssues:c,mediumIssues:a,lowIssues:b,securityStatus:u,riskScore:r,hasReports:o,latestStaticReport:V,latestDynamicReport:F,getSeverityClass:B,getStatusClass:$,getStatusIcon:d,fetchSecurityReports:_,fetchStaticReports:M,fetchDynamicReports:O,fetchScanStatus:q,startDynamicScan:g,exportReport:m,fixVulnerability:f,clearError:Z,formatters:{timeAgo:y=>{if(!y)return"N/A";try{const p=new Date(y),j=new Date-p,H=Math.floor(j/6e4),Y=Math.floor(j/36e5),tt=Math.floor(j/864e5);return H<1?"Appena ora":H<60?`${H} min fa`:Y<24?`${Y}h fa`:tt<7?`${tt}g fa`:p.toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit",year:"numeric"})}catch{return"N/A"}},formatDate:y=>{if(!y)return"N/A";try{return new Date(y).toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"})}catch{return"N/A"}},severity:y=>({critical:"Critico",high:"Alto",medium:"Medio",low:"Basso"})[y]||y,status:y=>({critical:"Critico",warning:"Attenzione",good:"Sicuro",unknown:"Sconosciuto"})[y]||y}}}),dt={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"},ct={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},ut={class:"flex items-center justify-between"},gt={class:"flex items-center"},mt={class:"flex items-center space-x-2"},yt={class:"p-6"},xt={key:0,class:"flex items-center justify-center py-8"},vt={key:1,class:"text-center py-8"},bt={key:2,class:"space-y-4"},ft={key:0,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},pt={class:"flex items-center justify-between mb-3"},kt={class:"text-xs text-gray-500 dark:text-gray-400"},ht={class:"grid grid-cols-2 md:grid-cols-4 gap-3"},wt={class:"text-xs text-gray-600 dark:text-gray-400 capitalize"},$t={class:"mt-3 pt-3 border-t border-gray-200 dark:border-gray-600"},_t={class:"flex items-center justify-between"},St={class:"text-sm font-bold text-gray-900 dark:text-white"},zt={class:"text-sm font-medium text-gray-900 dark:text-white mb-3"},Ct={class:"space-y-2 max-h-80 md:max-h-96 overflow-y-auto"},Dt={class:"flex items-center space-x-3"},At={class:"text-sm font-medium text-gray-900 dark:text-white"},Rt={class:"text-xs text-gray-500 dark:text-gray-400"},Nt={class:"flex items-center space-x-2"},It=["title"],Vt=["title"],Tt=["title"],Et={__name:"StaticAnalysisPanel",props:{reports:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1}},emits:["refresh","view-report"],setup(s,{emit:T}){const e=s,E=T,x=C(()=>e.reports&&e.reports.length>0?e.reports[0]:null);function w(k){return{critical:"text-red-600 dark:text-red-400",high:"text-orange-600 dark:text-orange-400",medium:"text-yellow-600 dark:text-yellow-400",low:"text-green-600 dark:text-green-400"}[k]||"text-gray-600 dark:text-gray-400"}function z(k){E("view-report",k)}const A={timeAgo:k=>{if(!k)return"N/A";try{const c=new Date(k),b=new Date-c,u=Math.floor(b/6e4),r=Math.floor(b/36e5),o=Math.floor(b/864e5);return u<1?"Appena ora":u<60?`${u} min fa`:r<24?`${r}h fa`:o<7?`${o}g fa`:c.toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit",year:"numeric"})}catch{return"N/A"}},formatDate:k=>{if(!k)return"N/A";try{return new Date(k).toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"})}catch{return"N/A"}},severity:k=>({critical:"Critico",high:"Alto",medium:"Medio",low:"Basso"})[k]||k};return(k,c)=>(n(),l("div",dt,[t("div",ct,[t("div",ut,[t("div",gt,[v(R,{name:"code-bracket",size:"sm",className:"text-blue-600 mr-2"}),c[1]||(c[1]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Analisi Statica ",-1))]),t("div",mt,[c[3]||(c[3]=t("span",{class:"text-xs text-gray-500 dark:text-gray-400"}," SonarQube ",-1)),v(I,{variant:"outline-primary",size:"xs",icon:"arrow-path",loading:s.loading,onClick:c[0]||(c[0]=a=>k.$emit("refresh"))},{default:N(()=>c[2]||(c[2]=[D(" Aggiorna ")])),_:1,__:[2]},8,["loading"])])])]),t("div",yt,[s.loading?(n(),l("div",xt,c[4]||(c[4]=[t("div",{class:"flex items-center space-x-3"},[t("div",{class:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"}),t("span",{class:"text-sm text-gray-600 dark:text-gray-400"}," Caricamento report... ")],-1)]))):!s.reports||s.reports.length===0?(n(),l("div",vt,[v(R,{name:"document-text",size:"lg",className:"text-gray-400 mx-auto mb-3"}),c[5]||(c[5]=t("p",{class:"text-sm text-gray-600 dark:text-gray-400"}," Nessun report di analisi statica disponibile ",-1)),c[6]||(c[6]=t("p",{class:"text-xs text-gray-500 dark:text-gray-500 mt-1"}," Esegui una scansione SonarQube per vedere i risultati ",-1))])):(n(),l("div",bt,[x.value?(n(),l("div",ft,[t("div",pt,[c[7]||(c[7]=t("h4",{class:"text-sm font-medium text-gray-900 dark:text-white"}," Report Più Recente ",-1)),t("span",kt,i(A.timeAgo(x.value.timestamp)),1)]),t("div",ht,[(n(!0),l(P,null,U(x.value.summary,(a,b)=>J((n(),l("div",{key:b,class:"text-center"},[t("div",{class:Q(["text-lg font-bold",w(b)])},i(a),3),t("div",wt,i(A.severity(b)),1)])),[[X,b!=="total_issues"]])),128))]),t("div",$t,[t("div",_t,[c[8]||(c[8]=t("span",{class:"text-sm font-medium text-gray-900 dark:text-white"}," Totale Problemi: ",-1)),t("span",St,i(x.value.summary.total_issues),1)])])])):h("",!0),t("div",null,[t("h4",zt," Storico Report ("+i(s.reports.length)+") ",1),t("div",Ct,[(n(!0),l(P,null,U(s.reports,a=>(n(),l("div",{key:a.timestamp,class:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"},[t("div",Dt,[v(R,{name:"document-text",size:"sm",className:"text-gray-500"}),t("div",null,[t("div",At,i(A.formatDate(a.timestamp)),1),t("div",Rt,i(a.summary.total_issues)+" problemi trovati ",1)])]),t("div",Nt,[a.summary.critical>0?(n(),l("div",{key:0,class:"w-2 h-2 bg-red-500 rounded-full",title:`${a.summary.critical} critici`},null,8,It)):h("",!0),a.summary.high>0?(n(),l("div",{key:1,class:"w-2 h-2 bg-orange-500 rounded-full",title:`${a.summary.high} alta priorità`},null,8,Vt)):h("",!0),a.summary.medium>0?(n(),l("div",{key:2,class:"w-2 h-2 bg-yellow-500 rounded-full",title:`${a.summary.medium} media priorità`},null,8,Tt)):h("",!0),v(I,{variant:"outline-primary",size:"xs",icon:"eye",onClick:b=>z(a)},{default:N(()=>c[9]||(c[9]=[D(" Dettagli ")])),_:2,__:[9]},1032,["onClick"])])]))),128))])])]))])]))}},Mt={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"},jt={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Lt={class:"flex items-center justify-between"},Ft={class:"flex items-center"},Bt={class:"flex items-center space-x-2"},Pt={class:"p-6"},Ut={key:0,class:"flex items-center justify-center py-8"},Ot={key:1,class:"text-center py-8"},Qt={key:2,class:"space-y-4"},Ht={key:0,class:"bg-red-50 dark:bg-red-900/20 rounded-lg p-4 border border-red-200 dark:border-red-700"},qt={class:"flex items-center justify-between mb-3"},Wt={class:"text-xs text-red-700 dark:text-red-300"},Zt={class:"grid grid-cols-2 md:grid-cols-4 gap-3"},Gt={class:"text-xs text-gray-600 dark:text-gray-400 capitalize"},Jt={key:0,class:"mt-4"},Kt={class:"space-y-1"},Xt={class:"text-red-800 dark:text-red-300 truncate"},Yt={key:0,class:"text-xs text-red-700 dark:text-red-300"},te={key:1,class:"mt-3 pt-3 border-t border-red-200 dark:border-red-700"},ee={class:"grid grid-cols-2 gap-4 text-xs"},se={class:"font-medium text-red-900 dark:text-red-200 ml-1"},re={class:"font-medium text-red-900 dark:text-red-200 ml-1"},ae={class:"text-sm font-medium text-gray-900 dark:text-white mb-3"},ie={class:"space-y-2 max-h-80 md:max-h-96 overflow-y-auto"},oe={class:"flex items-center space-x-3"},ne={class:"text-sm font-medium text-gray-900 dark:text-white"},le={class:"text-xs text-gray-500 dark:text-gray-400"},de={class:"flex items-center space-x-2"},ce={key:0,class:"flex items-center space-x-1"},ue={class:"text-xs text-red-600 dark:text-red-400 font-medium"},ge={class:"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700"},me={class:"flex justify-between items-center"},ye={class:"flex space-x-2"},xe={__name:"DynamicTestingPanel",props:{reports:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1}},emits:["refresh","view-report","start-scan","export-report"],setup(s,{emit:T}){const e=s,E=T,x=C(()=>e.reports&&e.reports.length>0?e.reports[0]:null);function w(c){return{critical:"text-red-600 dark:text-red-400",high:"text-orange-600 dark:text-orange-400",medium:"text-yellow-600 dark:text-yellow-400",low:"text-green-600 dark:text-green-400"}[c]||"text-gray-600 dark:text-gray-400"}function z(c){return{critical:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",high:"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"}[c]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}function A(c){E("view-report",c)}const k={timeAgo:c=>{if(!c)return"N/A";try{const a=new Date(c),u=new Date-a,r=Math.floor(u/6e4),o=Math.floor(u/36e5),V=Math.floor(u/864e5);return r<1?"Appena ora":r<60?`${r} min fa`:o<24?`${o}h fa`:V<7?`${V}g fa`:a.toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit",year:"numeric"})}catch{return"N/A"}},formatDate:c=>{if(!c)return"N/A";try{return new Date(c).toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"})}catch{return"N/A"}},severity:c=>({critical:"Critico",high:"Alto",medium:"Medio",low:"Basso"})[c]||c};return(c,a)=>(n(),l("div",Mt,[t("div",jt,[t("div",Lt,[t("div",Ft,[v(R,{name:"bug-ant",size:"sm",className:"text-red-600 mr-2"}),a[4]||(a[4]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Test Dinamici ",-1))]),t("div",Bt,[a[6]||(a[6]=t("span",{class:"text-xs text-gray-500 dark:text-gray-400"}," Penetration Testing ",-1)),v(I,{variant:"outline-primary",size:"xs",icon:"arrow-path",loading:s.loading,onClick:a[0]||(a[0]=b=>c.$emit("refresh"))},{default:N(()=>a[5]||(a[5]=[D(" Aggiorna ")])),_:1,__:[5]},8,["loading"])])])]),t("div",Pt,[s.loading?(n(),l("div",Ut,a[7]||(a[7]=[t("div",{class:"flex items-center space-x-3"},[t("div",{class:"animate-spin rounded-full h-6 w-6 border-b-2 border-red-600"}),t("span",{class:"text-sm text-gray-600 dark:text-gray-400"}," Caricamento test... ")],-1)]))):!s.reports||s.reports.length===0?(n(),l("div",Ot,[v(R,{name:"shield-exclamation",size:"lg",className:"text-gray-400 mx-auto mb-3"}),a[9]||(a[9]=t("p",{class:"text-sm text-gray-600 dark:text-gray-400"}," Nessun test dinamico disponibile ",-1)),a[10]||(a[10]=t("p",{class:"text-xs text-gray-500 dark:text-gray-500 mt-1"}," Esegui una scansione di penetration testing per vedere i risultati ",-1)),v(I,{variant:"primary",size:"sm",icon:"play",class:"mt-3",onClick:a[1]||(a[1]=b=>c.$emit("start-scan"))},{default:N(()=>a[8]||(a[8]=[D(" Avvia Scansione ")])),_:1,__:[8]})])):(n(),l("div",Qt,[x.value?(n(),l("div",Ht,[t("div",qt,[a[11]||(a[11]=t("h4",{class:"text-sm font-medium text-red-900 dark:text-red-200"}," Ultima Scansione ",-1)),t("span",Wt,i(k.timeAgo(x.value.timestamp)),1)]),t("div",Zt,[(n(!0),l(P,null,U(x.value.summary,(b,u)=>J((n(),l("div",{key:u,class:"text-center"},[t("div",{class:Q(["text-lg font-bold",w(u)])},i(b),3),t("div",Gt,i(k.severity(u)),1)])),[[X,u!=="total_issues"]])),128))]),x.value.vulnerabilities&&x.value.vulnerabilities.length>0?(n(),l("div",Jt,[a[12]||(a[12]=t("h5",{class:"text-xs font-medium text-red-900 dark:text-red-200 mb-2"}," Vulnerabilità Principali: ",-1)),t("div",Kt,[(n(!0),l(P,null,U(x.value.vulnerabilities.slice(0,3),b=>(n(),l("div",{key:b.id,class:"flex items-center justify-between text-xs"},[t("span",Xt,i(b.type)+" - "+i(b.endpoint),1),t("span",{class:Q(["px-2 py-1 rounded text-xs font-medium ml-2 flex-shrink-0",z(b.severity)])},i(k.severity(b.severity)),3)]))),128)),x.value.vulnerabilities.length>3?(n(),l("div",Yt," +"+i(x.value.vulnerabilities.length-3)+" altre vulnerabilità ",1)):h("",!0)])])):h("",!0),x.value.scan_metadata?(n(),l("div",te,[t("div",ee,[t("div",null,[a[13]||(a[13]=t("span",{class:"text-red-700 dark:text-red-300"},"Endpoint testati:",-1)),t("span",se,i(x.value.scan_metadata.endpoints_tested),1)]),t("div",null,[a[14]||(a[14]=t("span",{class:"text-red-700 dark:text-red-300"},"Durata:",-1)),t("span",re,i(x.value.scan_metadata.scan_duration),1)])])])):h("",!0)])):h("",!0),t("div",null,[t("h4",ae," Storico Scansioni ("+i(s.reports.length)+") ",1),t("div",ie,[(n(!0),l(P,null,U(s.reports,b=>(n(),l("div",{key:b.timestamp,class:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"},[t("div",oe,[v(R,{name:"shield-exclamation",size:"sm",className:"text-red-500"}),t("div",null,[t("div",ne,i(k.formatDate(b.timestamp)),1),t("div",le,i(b.summary.total_issues)+" vulnerabilità trovate ",1)])]),t("div",de,[b.summary.critical>0?(n(),l("div",ce,[a[15]||(a[15]=t("div",{class:"w-2 h-2 bg-red-500 rounded-full animate-pulse"},null,-1)),t("span",ue,i(b.summary.critical),1)])):h("",!0),v(I,{variant:"outline-primary",size:"xs",icon:"eye",onClick:u=>A(b)},{default:N(()=>a[16]||(a[16]=[D(" Dettagli ")])),_:2,__:[16]},1032,["onClick"])])]))),128))])]),t("div",ge,[t("div",me,[a[19]||(a[19]=t("span",{class:"text-sm text-gray-600 dark:text-gray-400"}," Azioni Rapide ",-1)),t("div",ye,[v(I,{variant:"outline-primary",size:"xs",icon:"play",onClick:a[2]||(a[2]=b=>c.$emit("start-scan"))},{default:N(()=>a[17]||(a[17]=[D(" Nuova Scansione ")])),_:1,__:[17]}),v(I,{variant:"outline-secondary",size:"xs",icon:"document-arrow-down",onClick:a[3]||(a[3]=b=>c.$emit("export-report"))},{default:N(()=>a[18]||(a[18]=[D(" Export PDF ")])),_:1,__:[18]})])])])]))])]))}},ve={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"},be={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},fe={class:"flex items-center justify-between"},pe={class:"flex items-center space-x-3"},ke={class:"p-6"},he={key:0,class:"flex items-center justify-center py-8"},we={key:1,class:"text-center py-8"},$e={class:"text-sm text-gray-600 dark:text-gray-400"},_e={key:0,class:"text-xs text-gray-500 dark:text-gray-500 mt-1"},Se={key:2,class:"space-y-4"},ze={class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6"},Ce={class:"grid grid-cols-2 md:grid-cols-5 gap-4 text-center"},De={class:"text-lg font-bold text-gray-900 dark:text-white"},Ae=["onClick"],Re={class:"text-xs text-gray-600 dark:text-gray-400 capitalize"},Ne={class:"overflow-hidden shadow ring-1 ring-black ring-opacity-5 rounded-lg"},Ie={class:"min-w-full divide-y divide-gray-300 dark:divide-gray-600"},Ve={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Te={class:"px-6 py-4"},Ee={class:"flex items-start space-x-3"},Me={class:"min-w-0 flex-1"},je={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},Le={class:"text-xs text-gray-500 dark:text-gray-400 mt-1"},Fe={class:"px-6 py-4"},Be={class:"text-sm text-gray-900 dark:text-white"},Pe={class:"px-6 py-4"},Ue={class:"px-6 py-4"},Oe={class:"flex items-center space-x-2"},Qe={class:"text-sm text-gray-900 dark:text-white"},He={class:"px-6 py-4"},qe={class:"flex space-x-2"},We={key:0,class:"flex items-center justify-between mt-6"},Ze={class:"text-sm text-gray-700 dark:text-gray-300"},Ge={class:"flex space-x-2"},K=10,Je={__name:"VulnerabilityList",props:{staticReports:{type:Array,default:()=>[]},dynamicReports:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1}},emits:["view-details","fix-vulnerability"],setup(s,{emit:T}){const e=s,E=T,x=L(""),w=L(""),z=L(1),A=C(()=>{const g=[];return e.staticReports.forEach(m=>{m.vulnerabilities&&m.vulnerabilities.forEach(f=>{g.push({...f,source:"static",reportTimestamp:m.timestamp})})}),e.dynamicReports.forEach(m=>{m.vulnerabilities&&m.vulnerabilities.forEach(f=>{g.push({...f,source:"dynamic",reportTimestamp:m.timestamp})})}),g}),k=C(()=>{let g=A.value;x.value&&(g=g.filter(f=>f.severity===x.value)),w.value&&(g=g.filter(f=>f.source===w.value));const m={critical:0,high:1,medium:2,low:3};return g.sort((f,Z)=>m[f.severity]-m[Z.severity]),g}),c=C(()=>Math.ceil(k.value.length/K)),a=C(()=>(z.value-1)*K),b=C(()=>Math.min(a.value+K,k.value.length)),u=C(()=>k.value.slice(a.value,b.value));function r(g){return k.value.filter(m=>m.severity===g).length}function o(g){x.value=x.value===g?"":g,z.value=1}function V(g){return{critical:"text-red-600 dark:text-red-400",high:"text-orange-600 dark:text-orange-400",medium:"text-yellow-600 dark:text-yellow-400",low:"text-green-600 dark:text-green-400"}[g]||"text-gray-600 dark:text-gray-400"}function F(g){return{critical:"text-red-500",high:"text-orange-500",medium:"text-yellow-500",low:"text-green-500"}[g]||"text-gray-500"}function B(g){return{critical:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",high:"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"}[g]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}function $(g){return g.source==="static"?"code-bracket":{"SQL Injection":"database","Cross-Site Scripting":"cursor-arrow-rays","Authentication Bypass":"key",CSRF:"shield-exclamation","Information Disclosure":"eye"}[g.type]||"bug-ant"}function d(g){return g.source==="static"?g.message||g.rule||"Problema di codice":g.type||"Vulnerabilità"}function _(g){return g.source==="static"?g.component||g.file||"Analisi statica":g.description||g.impact||"Test dinamico"}function M(g){return g.source==="static"?g.component||g.file||"N/A":g.endpoint||"N/A"}function O(g){E("view-details",g)}const q={severity:g=>({critical:"Critico",high:"Alto",medium:"Medio",low:"Basso"})[g]||g};return(g,m)=>(n(),l("div",ve,[t("div",be,[t("div",fe,[m[6]||(m[6]=t("div",null,[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Lista Vulnerabilità "),t("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Tutte le vulnerabilità trovate nelle scansioni ")],-1)),t("div",pe,[J(t("select",{"onUpdate:modelValue":m[0]||(m[0]=f=>x.value=f),class:"text-sm border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},m[4]||(m[4]=[it('<option value="">Tutti i livelli</option><option value="critical">Critico</option><option value="high">Alto</option><option value="medium">Medio</option><option value="low">Basso</option>',5)]),512),[[et,x.value]]),J(t("select",{"onUpdate:modelValue":m[1]||(m[1]=f=>w.value=f),class:"text-sm border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},m[5]||(m[5]=[t("option",{value:""},"Tutti i tipi",-1),t("option",{value:"static"},"Analisi Statica",-1),t("option",{value:"dynamic"},"Test Dinamici",-1)]),512),[[et,w.value]])])])]),t("div",ke,[s.loading?(n(),l("div",he,m[7]||(m[7]=[t("div",{class:"flex items-center space-x-3"},[t("div",{class:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"}),t("span",{class:"text-sm text-gray-600 dark:text-gray-400"}," Caricamento vulnerabilità... ")],-1)]))):k.value.length===0?(n(),l("div",we,[v(R,{name:"shield-check",size:"lg",className:"text-green-500 mx-auto mb-3"}),t("p",$e,i(A.value.length===0?"Nessuna vulnerabilità trovata":"Nessuna vulnerabilità corrisponde ai filtri"),1),A.value.length===0?(n(),l("p",_e," Sistema al sicuro! 🛡️ ")):h("",!0)])):(n(),l("div",Se,[t("div",ze,[t("div",Ce,[t("div",null,[t("div",De,i(k.value.length),1),m[8]||(m[8]=t("div",{class:"text-xs text-gray-600 dark:text-gray-400"}," Totale ",-1))]),(n(),l(P,null,U(["critical","high","medium","low"],f=>t("div",{key:f,class:"cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 rounded p-2 transition-colors",onClick:Z=>o(f)},[t("div",{class:Q(["text-lg font-bold",V(f)])},i(r(f)),3),t("div",Re,i(q.severity(f)),1)],8,Ae)),64))])]),t("div",Ne,[t("table",Ie,[m[11]||(m[11]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Vulnerabilità "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Endpoint "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Severità "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Fonte "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ")])],-1)),t("tbody",Ve,[(n(!0),l(P,null,U(u.value,f=>(n(),l("tr",{key:`${f.source}-${f.id||f.rule}`,class:"hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"},[t("td",Te,[t("div",Ee,[v(R,{name:$(f),size:"sm",className:F(f.severity)},null,8,["name","className"]),t("div",Me,[t("div",je,i(d(f)),1),t("div",Le,i(_(f)),1)])])]),t("td",Fe,[t("div",Be,i(M(f)),1)]),t("td",Pe,[t("span",{class:Q(["px-2 py-1 text-xs font-medium rounded",B(f.severity)])},i(q.severity(f.severity)),3)]),t("td",Ue,[t("div",Oe,[v(R,{name:f.source==="static"?"code-bracket":"bug-ant",size:"sm",className:f.source==="static"?"text-blue-500":"text-red-500"},null,8,["name","className"]),t("span",Qe,i(f.source==="static"?"SonarQube":"Pentest"),1)])]),t("td",He,[t("div",qe,[v(I,{variant:"outline-primary",size:"xs",icon:"eye",onClick:Z=>O(f)},{default:N(()=>m[9]||(m[9]=[D(" Dettagli ")])),_:2,__:[9]},1032,["onClick"]),f.severity==="critical"||f.severity==="high"?(n(),st(I,{key:0,variant:"primary",size:"xs",icon:"wrench-screwdriver",onClick:Z=>g.$emit("fix-vulnerability",f)},{default:N(()=>m[10]||(m[10]=[D(" Risolvi ")])),_:2,__:[10]},1032,["onClick"])):h("",!0)])])]))),128))])])]),c.value>1?(n(),l("div",We,[t("div",Ze," Mostrando "+i(a.value+1)+"-"+i(b.value)+" di "+i(k.value.length)+" vulnerabilità ",1),t("div",Ge,[v(I,{variant:"outline-secondary",size:"sm",icon:"chevron-left",disabled:z.value===1,onClick:m[2]||(m[2]=f=>z.value--)},{default:N(()=>m[12]||(m[12]=[D(" Precedente ")])),_:1,__:[12]},8,["disabled"]),v(I,{variant:"outline-secondary",size:"sm",icon:"chevron-right",disabled:z.value===c.value,onClick:m[3]||(m[3]=f=>z.value++)},{default:N(()=>m[13]||(m[13]=[D(" Successivo ")])),_:1,__:[13]},8,["disabled"])])])):h("",!0)]))])]))}},Ke={key:0,class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},Xe={class:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},Ye={class:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full sm:p-6"},ts={class:"flex items-center justify-between mb-6"},es={class:"flex items-center"},ss={class:"text-xl font-bold text-gray-900 dark:text-white",id:"modal-title"},rs={class:"text-sm text-gray-600 dark:text-gray-400"},as={key:0,class:"space-y-6"},is={class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},os={class:"grid grid-cols-2 md:grid-cols-4 gap-4"},ns={class:"text-xs text-gray-600 dark:text-gray-400 capitalize"},ls={class:"text-center"},ds={class:"text-2xl font-bold text-gray-900 dark:text-white"},cs={key:0,class:"grid grid-cols-1 md:grid-cols-2 gap-6"},us={key:0,class:"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4"},gs={class:"space-y-2 text-sm"},ms={class:"flex justify-between"},ys={class:"font-medium text-blue-900 dark:text-blue-200"},xs={class:"flex justify-between"},vs={class:"font-medium text-blue-900 dark:text-blue-200"},bs={key:1,class:"bg-red-50 dark:bg-red-900/20 rounded-lg p-4"},fs={class:"space-y-2 text-sm"},ps={class:"flex justify-between"},ks={class:"font-medium text-red-900 dark:text-red-200"},hs={class:"flex justify-between"},ws={class:"font-medium text-red-900 dark:text-red-200"},$s={class:"flex justify-between"},_s={class:"font-medium text-red-900 dark:text-red-200 break-all"},Ss={key:1},zs={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},Cs={class:"space-y-3 max-h-96 overflow-y-auto"},Ds={class:"flex items-start justify-between"},As={class:"flex-1"},Rs={class:"flex items-center space-x-2 mb-2"},Ns={class:"text-sm font-medium text-gray-900 dark:text-white"},Is={class:"text-xs text-gray-600 dark:text-gray-400 mt-1"},Vs={key:0,class:"mt-2"},Ts={class:"text-xs bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded"},Es={key:2,class:"text-center py-8"},Ms={class:"flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700"},js={__name:"SecurityReportModal",props:{isOpen:{type:Boolean,default:!1},report:{type:Object,default:null}},emits:["close","export-report","view-vulnerability"],setup(s,{emit:T}){const e=s;function E(){return e.report?e.report.source==="static"?"Report Analisi Statica":"Report Test Dinamici":"Report di Sicurezza"}function x(u){return{critical:"text-red-600 dark:text-red-400",high:"text-orange-600 dark:text-orange-400",medium:"text-yellow-600 dark:text-yellow-400",low:"text-green-600 dark:text-green-400"}[u]||"text-gray-600 dark:text-gray-400"}function w(u){return{critical:"text-red-500",high:"text-orange-500",medium:"text-yellow-500",low:"text-green-500"}[u]||"text-gray-500"}function z(u){return{critical:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",high:"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"}[u]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}function A(u){var o;return((o=e.report)==null?void 0:o.source)==="static"?"code-bracket":{"SQL Injection":"database","Cross-Site Scripting":"cursor-arrow-rays","Authentication Bypass":"key",CSRF:"shield-exclamation","Information Disclosure":"eye"}[u.type]||"bug-ant"}function k(u){var r;return((r=e.report)==null?void 0:r.source)==="static"?u.message||u.rule||"Problema di codice":u.type||"Vulnerabilità"}function c(u){var r;return((r=e.report)==null?void 0:r.source)==="static"?u.component||u.file||"Analisi statica":u.description||u.impact||"Test dinamico"}function a(u){var r;return((r=e.report)==null?void 0:r.source)==="static"?u.component||u.file:u.endpoint}const b={formatDate:u=>{if(!u)return"N/A";try{return new Date(u).toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"})}catch{return"N/A"}},severity:u=>({critical:"Critico",high:"Alto",medium:"Medio",low:"Basso"})[u]||u};return(u,r)=>{var o,V,F,B,$;return s.isOpen?(n(),l("div",Ke,[t("div",Xe,[t("div",{class:"fixed inset-0 bg-gray-500 dark:bg-gray-900 bg-opacity-75 dark:bg-opacity-75 transition-opacity","aria-hidden":"true",onClick:r[0]||(r[0]=d=>u.$emit("close"))}),t("div",Ye,[t("div",ts,[t("div",es,[v(R,{name:((o=s.report)==null?void 0:o.source)==="static"?"code-bracket":"bug-ant",size:"lg",className:((V=s.report)==null?void 0:V.source)==="static"?"text-blue-600 mr-3":"text-red-600 mr-3"},null,8,["name","className"]),t("div",null,[t("h3",ss,i(E()),1),t("p",rs,i(b.formatDate((F=s.report)==null?void 0:F.timestamp)),1)])]),v(I,{variant:"outline-secondary",size:"sm",icon:"x-mark",onClick:r[1]||(r[1]=d=>u.$emit("close"))},{default:N(()=>r[4]||(r[4]=[D(" Chiudi ")])),_:1,__:[4]})]),s.report?(n(),l("div",as,[t("div",is,[r[6]||(r[6]=t("h4",{class:"text-sm font-medium text-gray-900 dark:text-white mb-3"}," Riepilogo Scansione ",-1)),t("div",os,[(n(!0),l(P,null,U(s.report.summary,(d,_)=>J((n(),l("div",{key:_,class:"text-center"},[t("div",{class:Q(["text-2xl font-bold",x(_)])},i(d),3),t("div",ns,i(b.severity(_)),1)])),[[X,_!=="total_issues"]])),128)),t("div",ls,[t("div",ds,i(s.report.summary.total_issues),1),r[5]||(r[5]=t("div",{class:"text-xs text-gray-600 dark:text-gray-400"}," Totale ",-1))])])]),s.report.scan_metadata||s.report.source==="static"?(n(),l("div",cs,[s.report.source==="static"?(n(),l("div",us,[r[10]||(r[10]=t("h4",{class:"text-sm font-medium text-blue-900 dark:text-blue-200 mb-3"}," Dettagli Analisi Statica ",-1)),t("div",gs,[r[9]||(r[9]=t("div",{class:"flex justify-between"},[t("span",{class:"text-blue-700 dark:text-blue-300"},"Strumento:"),t("span",{class:"font-medium text-blue-900 dark:text-blue-200"},"SonarQube")],-1)),t("div",ms,[r[7]||(r[7]=t("span",{class:"text-blue-700 dark:text-blue-300"},"Linee analizzate:",-1)),t("span",ys,i(((B=s.report.scan_metadata)==null?void 0:B.lines_analyzed)||"N/A"),1)]),t("div",xs,[r[8]||(r[8]=t("span",{class:"text-blue-700 dark:text-blue-300"},"File analizzati:",-1)),t("span",vs,i((($=s.report.scan_metadata)==null?void 0:$.files_analyzed)||"N/A"),1)])])])):h("",!0),s.report.scan_metadata&&s.report.source==="dynamic"?(n(),l("div",bs,[r[14]||(r[14]=t("h4",{class:"text-sm font-medium text-red-900 dark:text-red-200 mb-3"}," Dettagli Test Dinamici ",-1)),t("div",fs,[t("div",ps,[r[11]||(r[11]=t("span",{class:"text-red-700 dark:text-red-300"},"Endpoint testati:",-1)),t("span",ks,i(s.report.scan_metadata.endpoints_tested),1)]),t("div",hs,[r[12]||(r[12]=t("span",{class:"text-red-700 dark:text-red-300"},"Durata scansione:",-1)),t("span",ws,i(s.report.scan_metadata.scan_duration),1)]),t("div",$s,[r[13]||(r[13]=t("span",{class:"text-red-700 dark:text-red-300"},"Target URL:",-1)),t("span",_s,i(s.report.scan_metadata.target_url||"N/A"),1)])])])):h("",!0)])):h("",!0),s.report.vulnerabilities&&s.report.vulnerabilities.length>0?(n(),l("div",Ss,[t("h4",zs," Vulnerabilità Trovate ("+i(s.report.vulnerabilities.length)+") ",1),t("div",Cs,[(n(!0),l(P,null,U(s.report.vulnerabilities,(d,_)=>(n(),l("div",{key:_,class:"border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"},[t("div",Ds,[t("div",As,[t("div",Rs,[t("span",{class:Q(["px-2 py-1 text-xs font-medium rounded",z(d.severity)])},i(b.severity(d.severity)),3),v(R,{name:A(d),size:"sm",className:w(d.severity)},null,8,["name","className"])]),t("h5",Ns,i(k(d)),1),t("p",Is,i(c(d)),1),a(d)?(n(),l("div",Vs,[r[15]||(r[15]=t("span",{class:"text-xs text-gray-500 dark:text-gray-400"}," Endpoint: ",-1)),t("code",Ts,i(a(d)),1)])):h("",!0)]),v(I,{variant:"outline-primary",size:"xs",icon:"eye",onClick:M=>u.$emit("view-vulnerability",d)},{default:N(()=>r[16]||(r[16]=[D(" Dettagli ")])),_:2,__:[16]},1032,["onClick"])])]))),128))])])):s.report.summary.total_issues===0?(n(),l("div",Es,[v(R,{name:"shield-check",size:"lg",className:"text-green-500 mx-auto mb-3"}),r[17]||(r[17]=t("p",{class:"text-sm text-gray-600 dark:text-gray-400"}," Nessuna vulnerabilità trovata in questa scansione ",-1)),r[18]||(r[18]=t("p",{class:"text-xs text-gray-500 dark:text-gray-500 mt-1"}," Sistema sicuro! 🛡️ ",-1))])):h("",!0)])):h("",!0),t("div",Ms,[v(I,{variant:"outline-secondary",size:"sm",icon:"document-arrow-down",onClick:r[2]||(r[2]=d=>u.$emit("export-report",s.report))},{default:N(()=>r[19]||(r[19]=[D(" Export PDF ")])),_:1,__:[19]}),v(I,{variant:"outline-secondary",size:"sm",onClick:r[3]||(r[3]=d=>u.$emit("close"))},{default:N(()=>r[20]||(r[20]=[D(" Chiudi ")])),_:1,__:[20]})])])])])):h("",!0)}}},Ls={key:0,class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},Fs={class:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},Bs={class:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full sm:p-6"},Ps={class:"flex items-center justify-between mb-6"},Us={class:"flex items-center"},Os={class:"text-xl font-bold text-gray-900 dark:text-white",id:"modal-title"},Qs={class:"flex items-center space-x-2 mt-1"},Hs={class:"text-sm text-gray-600 dark:text-gray-400"},qs={key:0,class:"space-y-6"},Ws={class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},Zs={class:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm"},Gs={class:"font-medium text-gray-900 dark:text-white ml-2"},Js={class:"font-medium text-gray-900 dark:text-white ml-2 break-all"},Ks={key:0},Xs={class:"font-medium text-gray-900 dark:text-white ml-2"},Ys={key:1},tr={class:"font-medium text-gray-900 dark:text-white ml-2"},er={class:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4"},sr={class:"text-sm text-gray-700 dark:text-gray-300"},rr={key:0,class:"mt-3 pt-3 border-t border-gray-200 dark:border-gray-600"},ar={class:"text-sm text-gray-700 dark:text-gray-300"},ir={key:0},or={class:"text-sm font-medium text-gray-900 dark:text-white mb-3"},nr={class:"bg-gray-900 dark:bg-gray-950 rounded-lg p-4 overflow-x-auto"},lr={class:"text-sm text-green-400 font-mono whitespace-pre-wrap"},dr={key:1,class:"grid grid-cols-1 md:grid-cols-2 gap-4"},cr={class:"bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4"},ur={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},gr={class:"text-xs text-orange-700 dark:text-orange-300"},mr={key:0,class:"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4"},yr={class:"text-2xl font-bold text-blue-600 dark:text-blue-400"},xr={class:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-4"},vr={class:"flex"},br={class:"text-sm text-green-800 dark:text-green-200"},fr={key:0,class:"mt-3 pt-3 border-t border-green-200 dark:border-green-700"},pr={class:"text-sm text-green-800 dark:text-green-200"},kr={key:2},hr={class:"space-y-2"},wr={key:0,class:"flex items-center space-x-2"},$r={class:"text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded"},_r=["href"],Sr={key:1,class:"flex items-center space-x-2"},zr={class:"text-xs text-gray-600 dark:text-gray-400"},Cr={key:2,class:"space-y-1"},Dr=["href"],Ar={class:"flex justify-between items-center mt-6 pt-6 border-t border-gray-200 dark:border-gray-700"},Rr={class:"flex space-x-2"},Nr={__name:"VulnerabilityDetailsModal",props:{isOpen:{type:Boolean,default:!1},vulnerability:{type:Object,default:null}},emits:["close","fix-vulnerability"],setup(s,{emit:T}){const e=s;function E(){var o,V;return((o=e.vulnerability)==null?void 0:o.source)==="static"?"code-bracket":{"SQL Injection":"database","Cross-Site Scripting":"cursor-arrow-rays","Authentication Bypass":"key",CSRF:"shield-exclamation","Information Disclosure":"eye"}[(V=e.vulnerability)==null?void 0:V.type]||"bug-ant"}function x(r){return{critical:"text-red-500",high:"text-orange-500",medium:"text-yellow-500",low:"text-green-500"}[r]||"text-gray-500"}function w(r){return{critical:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",high:"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"}[r]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}function z(){return e.vulnerability?e.vulnerability.source==="static"?e.vulnerability.message||e.vulnerability.rule||"Problema di codice":e.vulnerability.type||"Vulnerabilità":"Dettagli Vulnerabilità"}function A(){return e.vulnerability?e.vulnerability.source==="static"?e.vulnerability.rule||e.vulnerability.type||"Code Analysis":e.vulnerability.type||"Security Issue":"N/A"}function k(){return e.vulnerability?e.vulnerability.source==="static"?e.vulnerability.message||e.vulnerability.description||"Problema rilevato dall'analisi statica del codice":e.vulnerability.description||e.vulnerability.impact||"Vulnerabilità rilevata durante i test dinamici":"Nessuna descrizione disponibile"}function c(){return e.vulnerability?e.vulnerability.source==="static"?e.vulnerability.component||e.vulnerability.file||"N/A":e.vulnerability.endpoint||"N/A":"N/A"}function a(){if(!e.vulnerability)return"Nessuna raccomandazione disponibile";const r={"SQL Injection":"Utilizza prepared statements e parametrized queries. Valida e sanitizza sempre gli input utente.","Cross-Site Scripting":"Sanitizza gli output e usa Content Security Policy. Valida gli input lato server.","Authentication Bypass":"Implementa autenticazione robusta e gestione sicura delle sessioni.",CSRF:"Implementa token CSRF e verifica il referrer per le operazioni sensibili.","Information Disclosure":"Limita le informazioni esposte e implementa controlli di accesso appropriati."};return e.vulnerability.source==="dynamic"&&r[e.vulnerability.type]?r[e.vulnerability.type]:e.vulnerability.source==="static"?"Rivedi il codice evidenziato e applica le best practices di sicurezza per il linguaggio utilizzato.":"Analizza il problema specifico e consulta la documentazione di sicurezza appropriata per la risoluzione."}async function b(){if(!e.vulnerability)return;const r=`
Vulnerabilità: ${z()}
Tipo: ${A()}
Severità: ${u.severity(e.vulnerability.severity)}
Endpoint/File: ${c()}
${e.vulnerability.line?`Linea: ${e.vulnerability.line}`:""}
${e.vulnerability.method?`Metodo: ${e.vulnerability.method}`:""}

Descrizione:
${k()}

${e.vulnerability.impact?`Impatto:
${e.vulnerability.impact}
`:""}

Raccomandazioni:
${a()}
${e.vulnerability.remediation?`
Dettagli tecnici:
${e.vulnerability.remediation}`:""}
  `.trim();try{await navigator.clipboard.writeText(r),console.log("Dettagli vulnerabilità copiati negli appunti")}catch(o){console.error("Errore durante la copia:",o)}}const u={severity:r=>({critical:"Critico",high:"Alto",medium:"Medio",low:"Basso"})[r]||r};return(r,o)=>{var V,F,B,$,d,_;return s.isOpen?(n(),l("div",Ls,[t("div",Fs,[t("div",{class:"fixed inset-0 bg-gray-500 dark:bg-gray-900 bg-opacity-75 dark:bg-opacity-75 transition-opacity","aria-hidden":"true",onClick:o[0]||(o[0]=M=>r.$emit("close"))}),t("div",Bs,[t("div",Ps,[t("div",Us,[v(R,{name:E(),size:"lg",className:x((V=s.vulnerability)==null?void 0:V.severity)+" mr-3"},null,8,["name","className"]),t("div",null,[t("h3",Os,i(z()),1),t("div",Qs,[t("span",{class:Q(["px-2 py-1 text-xs font-medium rounded",w((F=s.vulnerability)==null?void 0:F.severity)])},i(u.severity((B=s.vulnerability)==null?void 0:B.severity)),3),t("span",Hs,i((($=s.vulnerability)==null?void 0:$.source)==="static"?"Analisi Statica":"Test Dinamici"),1)])])]),v(I,{variant:"outline-secondary",size:"sm",icon:"x-mark",onClick:o[1]||(o[1]=M=>r.$emit("close"))},{default:N(()=>o[4]||(o[4]=[D(" Chiudi ")])),_:1,__:[4]})]),s.vulnerability?(n(),l("div",qs,[t("div",Ws,[o[9]||(o[9]=t("h4",{class:"text-sm font-medium text-gray-900 dark:text-white mb-3"}," Dettagli Vulnerabilità ",-1)),t("div",Zs,[t("div",null,[o[5]||(o[5]=t("span",{class:"text-gray-600 dark:text-gray-400"},"Tipo:",-1)),t("span",Gs,i(A()),1)]),t("div",null,[o[6]||(o[6]=t("span",{class:"text-gray-600 dark:text-gray-400"},"Endpoint/File:",-1)),t("span",Js,i(c()),1)]),s.vulnerability.line?(n(),l("div",Ks,[o[7]||(o[7]=t("span",{class:"text-gray-600 dark:text-gray-400"},"Linea:",-1)),t("span",Xs,i(s.vulnerability.line),1)])):h("",!0),s.vulnerability.method?(n(),l("div",Ys,[o[8]||(o[8]=t("span",{class:"text-gray-600 dark:text-gray-400"},"Metodo HTTP:",-1)),t("span",tr,i(s.vulnerability.method),1)])):h("",!0)])]),t("div",null,[o[11]||(o[11]=t("h4",{class:"text-sm font-medium text-gray-900 dark:text-white mb-3"}," Descrizione ",-1)),t("div",er,[t("p",sr,i(k()),1),s.vulnerability.impact?(n(),l("div",rr,[o[10]||(o[10]=t("h5",{class:"text-xs font-medium text-gray-600 dark:text-gray-400 mb-2"},"Impatto:",-1)),t("p",ar,i(s.vulnerability.impact),1)])):h("",!0)])]),s.vulnerability.code||s.vulnerability.payload?(n(),l("div",ir,[t("h4",or,i(s.vulnerability.source==="static"?"Codice Problematico":"Payload di Test"),1),t("div",nr,[t("pre",lr,i(s.vulnerability.code||s.vulnerability.payload),1)])])):h("",!0),s.vulnerability.cvss_score||s.vulnerability.risk_score?(n(),l("div",dr,[t("div",cr,[o[12]||(o[12]=t("h4",{class:"text-sm font-medium text-orange-900 dark:text-orange-200 mb-2"}," Score di Rischio ",-1)),t("div",ur,i(s.vulnerability.cvss_score||s.vulnerability.risk_score||"N/A"),1),t("div",gr,i(s.vulnerability.cvss_score?"CVSS Score":"Risk Score"),1)]),s.vulnerability.confidence?(n(),l("div",mr,[o[13]||(o[13]=t("h4",{class:"text-sm font-medium text-blue-900 dark:text-blue-200 mb-2"}," Confidenza ",-1)),t("div",yr,i(s.vulnerability.confidence)+"% ",1),o[14]||(o[14]=t("div",{class:"text-xs text-blue-700 dark:text-blue-300"}," Accuratezza rilevamento ",-1))])):h("",!0)])):h("",!0),t("div",null,[o[16]||(o[16]=t("h4",{class:"text-sm font-medium text-gray-900 dark:text-white mb-3"}," Raccomandazioni per la Risoluzione ",-1)),t("div",xr,[t("div",vr,[v(R,{name:"light-bulb",size:"sm",className:"text-green-600 dark:text-green-400 mr-3 mt-0.5 flex-shrink-0"}),t("div",br,i(a()),1)]),s.vulnerability.remediation?(n(),l("div",fr,[o[15]||(o[15]=t("h5",{class:"text-xs font-medium text-green-700 dark:text-green-300 mb-2"},"Dettagli tecnici:",-1)),t("p",pr,i(s.vulnerability.remediation),1)])):h("",!0)])]),s.vulnerability.references||s.vulnerability.cwe||s.vulnerability.owasp?(n(),l("div",kr,[o[18]||(o[18]=t("h4",{class:"text-sm font-medium text-gray-900 dark:text-white mb-3"}," Riferimenti ",-1)),t("div",hr,[s.vulnerability.cwe?(n(),l("div",wr,[t("span",$r," CWE-"+i(s.vulnerability.cwe),1),t("a",{href:`https://cwe.mitre.org/data/definitions/${s.vulnerability.cwe}.html`,target:"_blank",rel:"noopener noreferrer",class:"text-xs text-blue-600 dark:text-blue-400 hover:underline"}," MITRE CWE Database ",8,_r)])):h("",!0),s.vulnerability.owasp?(n(),l("div",Sr,[o[17]||(o[17]=t("span",{class:"text-xs bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-2 py-1 rounded"}," OWASP ",-1)),t("span",zr,i(s.vulnerability.owasp),1)])):h("",!0),s.vulnerability.references?(n(),l("div",Cr,[(n(!0),l(P,null,U(s.vulnerability.references,(M,O)=>(n(),l("div",{key:O},[t("a",{href:M,target:"_blank",rel:"noopener noreferrer",class:"text-xs text-blue-600 dark:text-blue-400 hover:underline block"},i(M),9,Dr)]))),128))])):h("",!0)])])):h("",!0)])):h("",!0),t("div",Ar,[t("div",Rr,[((d=s.vulnerability)==null?void 0:d.severity)==="critical"||((_=s.vulnerability)==null?void 0:_.severity)==="high"?(n(),st(I,{key:0,variant:"primary",size:"sm",icon:"wrench-screwdriver",onClick:o[2]||(o[2]=M=>r.$emit("fix-vulnerability",s.vulnerability))},{default:N(()=>o[19]||(o[19]=[D(" Applica Fix Automatico ")])),_:1,__:[19]})):h("",!0),v(I,{variant:"outline-primary",size:"sm",icon:"clipboard-document",onClick:b},{default:N(()=>o[20]||(o[20]=[D(" Copia Dettagli ")])),_:1,__:[20]})]),v(I,{variant:"outline-secondary",size:"sm",onClick:o[3]||(o[3]=M=>r.$emit("close"))},{default:N(()=>o[21]||(o[21]=[D(" Chiudi ")])),_:1,__:[21]})])])])])):h("",!0)}}},Ir={class:"min-h-screen bg-gray-50 dark:bg-gray-900"},Vr={class:"bg-white dark:bg-gray-800 shadow"},Tr={class:"px-4 sm:px-6 lg:px-8"},Er={class:"flex flex-col md:flex-row md:items-center md:justify-between py-6"},Mr={class:"text-2xl font-bold text-gray-900 dark:text-white flex items-center"},jr={class:"mt-4 md:mt-0 flex space-x-3"},Lr={key:0,class:"p-6"},Fr={class:"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4"},Br={class:"flex"},Pr={key:1,class:"p-6"},Ur={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},Or={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Qr={class:"p-5"},Hr={class:"flex items-center"},qr={class:"flex-shrink-0"},Wr={class:"ml-5 w-0 flex-1"},Zr={class:"text-lg font-medium text-gray-900 dark:text-white"},Gr={class:"text-xs text-gray-500 dark:text-gray-400"},Jr={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Kr={class:"p-5"},Xr={class:"flex items-center"},Yr={class:"flex-shrink-0"},ta={class:"ml-5 w-0 flex-1"},ea={class:"text-lg font-medium text-gray-900 dark:text-white"},sa={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},ra={class:"p-5"},aa={class:"flex items-center"},ia={class:"flex-shrink-0"},oa={class:"ml-5 w-0 flex-1"},na={class:"text-lg font-medium text-gray-900 dark:text-white"},la={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},da={class:"p-5"},ca={class:"flex items-center"},ua={class:"flex-shrink-0"},ga={class:"ml-5 w-0 flex-1"},ma={class:"text-lg font-medium text-gray-900 dark:text-white"},ya={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8"},xa={key:0,class:"mt-6 text-center"},va={class:"text-xs text-gray-500 dark:text-gray-400"},ha={__name:"SecurityDashboard",setup(s){const{isFeatureEnabled:T}=rt(),e=lt(),E=C(()=>T("security_dashboard")),x=L(!1),w=L(!1),z=L(null),A=L(null);let k=null;async function c(){await e.fetchSecurityReports(),await e.fetchScanStatus()}async function a(){try{await e.startDynamicScan()}catch($){console.error("Failed to start security scan:",$)}}function b($){const d={critical:"text-red-500",warning:"text-yellow-500",good:"text-green-500",unknown:"text-gray-500"};return d[$]||d.unknown}function u($){z.value=$,x.value=!0}function r(){x.value=!1,z.value=null}function o($){A.value=$,w.value=!0}function V(){w.value=!1,A.value=null}async function F($){try{console.log("Exporting report:",$);const d=await e.exportReport($);if(d&&d.download_url){const _=document.createElement("a");_.href=d.download_url,_.download=`security_report_${$.id}_${new Date().toISOString().split("T")[0]}.pdf`,document.body.appendChild(_),_.click(),document.body.removeChild(_),alert(`Report esportato con successo! ID: ${d.export_id}`)}else if(d&&d.export_id){const _=`/api/security/reports/${d.export_id}/download`,M=await fetch(_,{credentials:"include"});if(M.ok){const O=await M.blob(),q=window.URL.createObjectURL(O),g=document.createElement("a");g.href=q,g.download=`security_report_${$.id}_${new Date().toISOString().split("T")[0]}.pdf`,document.body.appendChild(g),g.click(),window.URL.revokeObjectURL(q),document.body.removeChild(g),alert("Report scaricato con successo!")}else alert(`Report generato con successo! ID: ${d.export_id}`)}else alert("Export completato")}catch(d){console.error("Failed to export report:",d),alert("Errore durante l'export del report")}}async function B($){try{console.log("Fixing vulnerability:",$);const d=await e.fixVulnerability($);if(d.success)alert("Vulnerabilità risolta automaticamente!"),await c();else{const _=d.recommendations||[],M=`Fix automatico non disponibile.

Motivo: ${d.reason}

Raccomandazioni:
${_.map(O=>`• ${O}`).join(`
`)}`;alert(M)}}catch(d){console.error("Failed to fix vulnerability:",d),alert("Errore durante il fix della vulnerabilità")}}return ot(async()=>{E.value&&(await c(),k=setInterval(()=>{e.loading||c()},5*60*1e3))}),nt(()=>{k&&clearInterval(k)}),($,d)=>(n(),l("div",Ir,[t("div",Vr,[t("div",Tr,[t("div",Er,[t("div",null,[t("h1",Mr,[v(R,{name:"shield-check",size:"md",className:"mr-3 text-blue-600"}),d[0]||(d[0]=D(" Security Dashboard "))]),d[1]||(d[1]=t("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Monitoraggio sicurezza e gestione vulnerabilità ",-1))]),t("div",jr,[v(I,{variant:"outline-primary",size:"sm",icon:"arrow-path",loading:S(e).loading,onClick:c},{default:N(()=>d[2]||(d[2]=[D(" Aggiorna ")])),_:1,__:[2]},8,["loading"])])])])]),E.value?(n(),l("div",Pr,[t("div",Ur,[t("div",Or,[t("div",Qr,[t("div",Hr,[t("div",qr,[v(R,{name:S(e).getStatusIcon(S(e).securityStatus),size:"md",className:b(S(e).securityStatus)},null,8,["name","className"])]),t("div",Wr,[t("dl",null,[d[4]||(d[4]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Problemi Totali ",-1)),t("dd",Zr,i(S(e).totalIssues),1),t("dd",Gr,[t("span",{class:Q(["px-2 py-1 rounded text-xs font-medium",S(e).getStatusClass(S(e).securityStatus)])},i(S(e).formatters.status(S(e).securityStatus)),3)])])])])])]),t("div",Jr,[t("div",Kr,[t("div",Xr,[t("div",Yr,[v(R,{name:"exclamation-circle",size:"md",className:"text-red-500"})]),t("div",ta,[t("dl",null,[d[5]||(d[5]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Critici ",-1)),t("dd",ea,i(S(e).criticalIssues),1),d[6]||(d[6]=t("dd",{class:"text-xs text-red-600 dark:text-red-400"}," Attenzione immediata ",-1))])])])])]),t("div",sa,[t("div",ra,[t("div",aa,[t("div",ia,[v(R,{name:"exclamation-triangle",size:"md",className:"text-orange-500"})]),t("div",oa,[t("dl",null,[d[7]||(d[7]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Alta Priorità ",-1)),t("dd",na,i(S(e).highIssues),1),d[8]||(d[8]=t("dd",{class:"text-xs text-orange-600 dark:text-orange-400"}," Risolvi presto ",-1))])])])])]),t("div",la,[t("div",da,[t("div",ca,[t("div",ua,[v(R,{name:"calculator",size:"md",className:"text-blue-500"})]),t("div",ga,[t("dl",null,[d[9]||(d[9]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Risk Score ",-1)),t("dd",ma,i(S(e).riskScore),1),d[10]||(d[10]=t("dd",{class:"text-xs text-gray-500 dark:text-gray-400"}," Score ponderato ",-1))])])])])])]),t("div",ya,[v(Et,{reports:S(e).staticReports,loading:S(e).loading,onRefresh:S(e).fetchStaticReports,onViewReport:u},null,8,["reports","loading","onRefresh"]),v(xe,{reports:S(e).dynamicReports,loading:S(e).loading,onRefresh:S(e).fetchDynamicReports,onStartScan:a,onViewReport:u,onExportReport:F},null,8,["reports","loading","onRefresh"])]),v(Je,{"static-reports":S(e).staticReports,"dynamic-reports":S(e).dynamicReports,loading:S(e).loading,onViewDetails:o,onFixVulnerability:B},null,8,["static-reports","dynamic-reports","loading"]),S(e).lastUpdated?(n(),l("div",xa,[t("p",va," Ultimo aggiornamento: "+i(S(e).formatters.formatDate(S(e).lastUpdated)),1)])):h("",!0)])):(n(),l("div",Lr,[t("div",Fr,[t("div",Br,[v(R,{name:"exclamation-triangle",size:"sm",className:"text-yellow-400 mr-3 mt-0.5"}),d[3]||(d[3]=t("div",null,[t("h3",{class:"text-sm font-medium text-yellow-800 dark:text-yellow-200"}," Security Dashboard Disabilitato "),t("p",{class:"mt-1 text-sm text-yellow-700 dark:text-yellow-300"}," Il Security Dashboard è attualmente disabilitato. Contatta l'amministratore per abilitarlo. ")],-1))])])])),v(js,{"is-open":x.value,report:z.value,onClose:r,onExportReport:F,onViewVulnerability:o},null,8,["is-open","report"]),v(Nr,{"is-open":w.value,vulnerability:A.value,onClose:V,onFixVulnerability:B},null,8,["is-open","vulnerability"])]))}};export{ha as default};
