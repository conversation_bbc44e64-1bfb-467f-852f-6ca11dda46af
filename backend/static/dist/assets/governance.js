import{d as x,r as i,c as v}from"./vendor.js";import{d as C,c as t}from"./app.js";const W=x("governance",()=>{const{showToast:s}=C(),c=i([]),l=i([]),d=i([]),g=i([]),o=i(!1),h=i(null),f=i(null),y=i(null),E=i(null),p=i({metrics:{},breakdowns:{},trends:{},recent_events:[],high_risk_activities:[]}),w=i({total_risks:0,critical_risks:0,overdue_reviews:0,by_category:[],by_level:[],by_status:[]}),m=i({critical:0,open:0,resolved:0,avg_resolution_time:0}),G=v(()=>c.value.length),k=v(()=>l.value.length),R=v(()=>d.value.length),_=v(()=>c.value.filter(r=>r.risk_level==="critical")),P=v(()=>l.value.filter(r=>r.status==="open")),b=v(()=>l.value.filter(r=>r.severity==="critical")),z=v(()=>d.value.filter(r=>r.is_active));return{risks:c,events:l,policies:d,auditLogs:g,loading:o,error:h,currentRisk:f,currentEvent:y,currentPolicy:E,dashboardData:p,riskStats:w,eventStats:m,risksCount:G,eventsCount:k,policiesCount:R,criticalRisks:_,openEvents:P,criticalEvents:b,activePolicies:z,fetchDashboard:async(r=30)=>{o.value=!0;try{const e=await t.get(`/api/governance/dashboard?days=${r}`);if(e.data.success)return console.log("✅ [Governance] Dashboard loaded successfully:",e.data.data),p.value=e.data.data,e.data.data;throw console.error("❌ [Governance] Dashboard API returned success=false:",e.data.message),new Error(e.data.message||"Errore nel caricamento dashboard")}catch(e){throw console.error("💥 [Governance] Dashboard error:",e),console.error("💥 [Governance] Error response:",e.response),s("Errore nel caricamento della dashboard compliance","error"),e}finally{o.value=!1}},fetchRisks:async(r={})=>{o.value=!0,h.value=null;try{console.log("🔍 [Governance] Fetching risks with params:",r);const e=await t.get("/api/governance/risks",{params:r});if(console.log("📥 [Governance] Risks response:",e),e.data.success)return console.log("✅ [Governance] Risks loaded successfully:",e.data.data),c.value=e.data.data.risks||[],e.data.data;throw new Error(e.data.message||"Errore nel caricamento rischi")}catch(e){throw console.error("💥 [Governance] Risks error:",e),h.value=e.message||"Errore nel caricamento dei rischi",s("Errore nel caricamento dei rischi","error"),e}finally{o.value=!1}},fetchRiskStats:async()=>{try{const r=await t.get("/api/governance/risks/stats");if(r.data.success)return w.value=r.data.data,r.data.data;throw new Error(r.data.message||"Errore nel caricamento statistiche rischi")}catch(r){throw console.error("Errore caricamento statistiche rischi:",r),s("Errore nel caricamento delle statistiche","error"),r}},createRisk:async r=>{o.value=!0;try{const e=await t.post("/api/governance/risks",r);if(e.data.success)return c.value.unshift(e.data.data.risk||e.data.data),s("Rischio creato con successo","success"),e.data.data;throw new Error(e.data.message||"Errore nella creazione del rischio")}catch(e){throw console.error("Errore creazione rischio:",e),s("Errore nella creazione del rischio","error"),e}finally{o.value=!1}},updateRisk:async(r,e)=>{o.value=!0;try{console.log("🔍 [Governance] Updating risk:",r,e);const a=await t.put(`/api/governance/risks/${r}`,e);if(console.log("📥 [Governance] Update risk response:",a),a.data.success){console.log("✅ [Governance] Risk updated successfully:",a.data.data);const n=c.value.findIndex(u=>u.id===r);return n!==-1&&(c.value[n]={...c.value[n],...e}),s("Rischio aggiornato con successo","success"),a.data.data}else throw new Error(a.data.message||"Errore nell'aggiornamento del rischio")}catch(a){throw console.error("💥 [Governance] Update risk error:",a),s("Errore nell'aggiornamento del rischio","error"),a}finally{o.value=!1}},deleteRisk:async r=>{o.value=!0;try{console.log("🔍 [Governance] Deleting risk:",r);const e=await t.delete(`/api/governance/risks/${r}`);if(console.log("📥 [Governance] Delete risk response:",e),e.data.success)return console.log("✅ [Governance] Risk deleted successfully"),c.value=c.value.filter(a=>a.id!==r),s("Rischio eliminato con successo","success"),!0;throw new Error(e.data.message||"Errore nell'eliminazione del rischio")}catch(e){throw console.error("💥 [Governance] Delete risk error:",e),s("Errore nell'eliminazione del rischio","error"),e}finally{o.value=!1}},mitigateRisk:async(r,e="")=>{o.value=!0;try{console.log("🔍 [Governance] Mitigating risk:",r,e);const a=await t.put(`/api/governance/risks/${r}/mitigate`,{mitigation_notes:e});if(console.log("📥 [Governance] Mitigate risk response:",a),a.data.success){console.log("✅ [Governance] Risk mitigated successfully");const n=c.value.findIndex(u=>u.id===r);return n!==-1&&(c.value[n].status="mitigated",c.value[n].mitigation_notes=e),s("Rischio mitigato con successo","success"),a.data.data}else throw new Error(a.data.message||"Errore nella mitigazione del rischio")}catch(a){throw console.error("💥 [Governance] Mitigate risk error:",a),s("Errore nella mitigazione del rischio","error"),a}finally{o.value=!1}},fetchEvents:async(r={})=>{o.value=!0;try{console.log("🔍 [Governance] Fetching events with params:",r);const e=await t.get("/api/governance/events",{params:r});if(console.log("📥 [Governance] Events response:",e),e.data.success)return console.log("✅ [Governance] Events loaded successfully:",e.data.data),l.value=e.data.data.events||[],e.data.data;throw new Error(e.data.message||"Errore nel caricamento eventi")}catch(e){throw console.error("💥 [Governance] Events error:",e),s("Errore nel caricamento degli eventi","error"),e}finally{o.value=!1}},createEvent:async r=>{o.value=!0;try{console.log("🔍 [Governance] Creating event:",r);const e=await t.post("/api/governance/events",r);if(console.log("📥 [Governance] Create event response:",e),e.data.success)return console.log("✅ [Governance] Event created successfully:",e.data.data),l.value.unshift(e.data.data.event||e.data.data),s("Evento creato con successo","success"),e.data.data;throw new Error(e.data.message||"Errore nella creazione dell'evento")}catch(e){throw console.error("💥 [Governance] Create event error:",e),s("Errore nella creazione dell'evento","error"),e}finally{o.value=!1}},updateEvent:async(r,e)=>{o.value=!0;try{console.log("🔍 [Governance] Updating event:",r,e);const a=await t.put(`/api/governance/events/${r}`,e);if(console.log("📥 [Governance] Update event response:",a),a.data.success){console.log("✅ [Governance] Event updated successfully:",a.data.data);const n=l.value.findIndex(u=>u.id===r);return n!==-1&&(l.value[n]={...l.value[n],...e}),s("Evento aggiornato con successo","success"),a.data.data}else throw new Error(a.data.message||"Errore nell'aggiornamento dell'evento")}catch(a){throw console.error("💥 [Governance] Update event error:",a),s("Errore nell'aggiornamento dell'evento","error"),a}finally{o.value=!1}},resolveEvent:async(r,e="")=>{try{console.log("🔍 [Governance] Resolving event:",r,e);const a=await t.put(`/api/governance/events/${r}/resolve`,{resolution_notes:e});if(console.log("📥 [Governance] Resolve event response:",a),a.data.success){console.log("✅ [Governance] Event resolved successfully");const n=l.value.findIndex(u=>u.id===r);return n!==-1&&(l.value[n].status="resolved",l.value[n].resolved_at=new Date().toISOString(),l.value[n].resolution_notes=e),s("Evento risolto con successo","success"),a.data.data}else throw new Error(a.data.message||"Errore nella risoluzione dell'evento")}catch(a){throw console.error("💥 [Governance] Resolve event error:",a),s("Errore nella risoluzione dell'evento","error"),a}},fetchPolicies:async(r={})=>{o.value=!0;try{console.log("🔍 [Governance] Fetching policies");const e=await t.get("/api/governance/policies",{params:r});if(console.log("📥 [Governance] Policies response:",e),e.data.success)return console.log("✅ [Governance] Policies loaded successfully:",e.data.data),d.value=e.data.data.policies||[],e.data.data;throw new Error(e.data.message||"Errore nel caricamento delle policy")}catch(e){throw console.error("💥 [Governance] Policies error:",e),s("Errore nel caricamento delle policy","error"),e}finally{o.value=!1}},createPolicy:async r=>{o.value=!0;try{console.log("🔍 [Governance] Creating policy:",r);const e=await t.post("/api/governance/policies",r);if(console.log("📥 [Governance] Create policy response:",e),e.data.success)return console.log("✅ [Governance] Policy created successfully:",e.data.data),d.value.unshift(e.data.data.policy||e.data.data),s("Policy creata con successo","success"),e.data.data;throw new Error(e.data.message||"Errore nella creazione della policy")}catch(e){throw console.error("💥 [Governance] Create policy error:",e),s("Errore nella creazione della policy","error"),e}finally{o.value=!1}},updatePolicy:async(r,e)=>{o.value=!0;try{console.log("🔍 [Governance] Updating policy:",r,e);const a=await t.put(`/api/governance/policies/${r}`,e);if(console.log("📥 [Governance] Update policy response:",a),a.data.success){console.log("✅ [Governance] Policy updated successfully:",a.data.data);const n=d.value.findIndex(u=>u.id===r);return n!==-1&&(d.value[n]={...d.value[n],...e}),s("Policy aggiornata con successo","success"),a.data.data}else throw new Error(a.data.message||"Errore nell'aggiornamento della policy")}catch(a){throw console.error("💥 [Governance] Update policy error:",a),s("Errore nell'aggiornamento della policy","error"),a}finally{o.value=!1}},fetchAuditTrail:async(r={})=>{o.value=!0;try{console.log("🔍 [Governance] Fetching audit trail");const e=await t.get("/api/governance/audit-trail",{params:r});if(console.log("📥 [Governance] Audit trail response:",e),e.data.success)return console.log("✅ [Governance] Audit trail loaded successfully:",e.data.data),g.value=e.data.data.audit_logs||[],e.data.data;throw new Error(e.data.message||"Errore nel caricamento audit trail")}catch(e){throw console.error("💥 [Governance] Audit trail error:",e),s("Errore nel caricamento audit trail","error"),e}finally{o.value=!1}},reset:()=>{c.value=[],l.value=[],d.value=[],g.value=[],f.value=null,y.value=null,E.value=null,p.value={metrics:{},breakdowns:{},trends:{},recent_events:[],high_risk_activities:[]}}}});export{W as u};
