import{S as s}from"./StandardButton.js";import{_ as S,H as z}from"./app.js";import{r as y,x as L,y as B,b as r,h as d,l as n,k as g,e as C,j as A,F as p,p as I,o as i,s as c,t as u,n as V}from"./vendor.js";const D={class:"flex items-center space-x-2"},E={class:"py-1"},T=["onClick"],M={__name:"ActionButtonGroup",props:{showView:{type:Boolean,default:!0},showEdit:{type:Boolean,default:!0},showDelete:{type:Boolean,default:!0},viewIcon:{type:String,default:"eye"},editIcon:{type:String,default:"pencil"},deleteIcon:{type:String,default:"trash"},showLabels:{type:Boolean,default:!1},viewLabel:{type:String,default:"Visualizza"},editLabel:{type:String,default:"Modifica"},deleteLabel:{type:String,default:"Elimina"},viewTitle:{type:String,default:"Visualizza dettagli"},editTitle:{type:String,default:"Modifica"},deleteTitle:{type:String,default:"Elimina"},moreActions:{type:Array,default:()=>[]},confirmDelete:{type:Boolean,default:!0},deleteMessage:{type:String,default:"Sei sicuro di voler eliminare questo elemento?"}},emits:["view","edit","delete","action"],setup(e,{emit:k}){const v=e,f=k,o=y(!1),h=y(null),m=y(null),b=()=>{v.confirmDelete?confirm(v.deleteMessage)&&f("delete"):f("delete")},x=l=>{o.value=!1,f("action",l.key,l)},w=l=>{m.value&&!m.value.contains(l.target)&&(o.value=!1)};return L(()=>{document.addEventListener("click",w)}),B(()=>{document.removeEventListener("click",w)}),(l,a)=>(i(),r("div",D,[e.showView?(i(),d(s,{key:0,variant:"ghost",size:"sm",icon:e.viewIcon,onClick:a[0]||(a[0]=t=>l.$emit("view")),title:e.viewTitle},{default:g(()=>[c(u(e.showLabels?e.viewLabel:""),1)]),_:1},8,["icon","title"])):n("",!0),e.showEdit?(i(),d(s,{key:1,variant:"secondary",size:"sm",icon:e.editIcon,onClick:a[1]||(a[1]=t=>l.$emit("edit")),title:e.editTitle},{default:g(()=>[c(u(e.showLabels?e.editLabel:""),1)]),_:1},8,["icon","title"])):n("",!0),e.showDelete?(i(),d(s,{key:2,variant:"danger",size:"sm",icon:e.deleteIcon,onClick:b,title:e.deleteTitle},{default:g(()=>[c(u(e.showLabels?e.deleteLabel:""),1)]),_:1},8,["icon","title"])):n("",!0),e.moreActions&&e.moreActions.length>0?(i(),r("div",{key:3,class:"relative",ref_key:"dropdownContainerRef",ref:m},[C(s,{variant:"ghost",size:"sm",icon:"ellipsis-horizontal",onClick:a[2]||(a[2]=t=>o.value=!o.value),title:"Altre azioni"}),o.value?(i(),r("div",{key:0,ref_key:"dropdownRef",ref:h,class:"absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50"},[A("div",E,[(i(!0),r(p,null,I(e.moreActions,t=>(i(),r("button",{key:t.key,onClick:$=>x(t),class:V(["flex items-center w-full px-4 py-2 text-sm text-left",t.danger?"text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20":"text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"])},[t.icon?(i(),d(z,{key:0,name:t.icon,size:"sm",class:"mr-3"},null,8,["name"])):n("",!0),c(" "+u(t.label),1)],10,T))),128))])],512)):n("",!0)],512)):n("",!0)]))}},G=S(M,[["__scopeId","data-v-84a00997"]]);export{G as A};
