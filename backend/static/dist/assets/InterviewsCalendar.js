import{r as m,c as $,x as ne,b as n,j as r,l as F,e as u,t as i,B as W,H as E,F as y,p,v as j,A as L,q as le,o as l,n as T,E as ie}from"./vendor.js";import{u as de}from"./recruiting.js";import{S as g}from"./StandardButton.js";import{_ as ue,H as ce}from"./app.js";const ge={class:"interviews-calendar"},ve={class:"mb-6"},me={class:"flex items-center justify-between"},ye={class:"flex items-center space-x-3"},pe={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-4 mb-6"},be={class:"flex flex-wrap items-center justify-between gap-4"},he={class:"flex items-center space-x-4"},xe={class:"flex items-center space-x-2"},ke={class:"text-lg font-medium text-gray-900 dark:text-white"},we={class:"flex items-center space-x-3"},fe={class:"flex rounded-md shadow-sm"},_e=["value"],De={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},Ce={class:"flex items-center"},Se={class:"flex-shrink-0"},Te={class:"ml-3"},Ie={class:"text-sm font-medium text-gray-500 dark:text-gray-400"},Me={class:"text-lg font-semibold text-gray-900 dark:text-white"},Oe={key:0,class:"flex justify-center py-12"},$e={key:1,class:"bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden"},ze={key:0,class:"week-view"},Fe={class:"grid grid-cols-8 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600"},Le={class:"text-sm font-medium text-gray-900 dark:text-white"},Ne={class:"grid grid-cols-8"},Pe={class:"border-r border-gray-200 dark:border-gray-600"},Ve=["onClick"],qe=["onClick"],Ae={class:"text-xs font-semibold text-blue-800 dark:text-blue-200 truncate"},We={class:"text-xs text-blue-600 dark:text-blue-300 truncate"},Ee={key:1,class:"month-view"},je={class:"grid grid-cols-7 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600"},Be={class:"grid grid-cols-7"},He=["onClick"],Re={class:"flex justify-between items-start mb-1"},Ye={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},Ge={class:"space-y-1"},Ue=["onClick"],Qe={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},Je={class:"mt-3"},Ke={class:"text-sm text-gray-600 dark:text-gray-400 mb-4"},Xe={class:"space-y-3"},Ze={class:"flex justify-end mt-4"},et={__name:"InterviewsCalendar",setup(tt){const N=le(),I=de(),M=m(!1),c=m("week"),s=m(new Date),b=m([]),z=m([]),w=m(""),f=m(""),_=m(!1),h=m(null),D=m(null),P=Array.from({length:10},(a,t)=>t+9),B=["Dom","Lun","Mar","Mer","Gio","Ven","Sab"],H=$(()=>{if(c.value==="week"){const a=C(s.value),t=new Date(a);return t.setDate(t.getDate()+6),`${A(a)} - ${A(t)}`}else return s.value.toLocaleDateString("it-IT",{year:"numeric",month:"long"})}),V=$(()=>{const a=C(s.value),t=[];for(let e=0;e<7;e++){const o=new Date(a);o.setDate(o.getDate()+e),t.push({date:o.toISOString().split("T")[0],dayName:o.toLocaleDateString("it-IT",{weekday:"short"}),dayNumber:o.getDate()})}return t}),R=$(()=>{const a=s.value.getFullYear(),t=s.value.getMonth(),e=new Date(a,t,1),o=C(e),d=[];for(let v=0;v<42;v++){const S=new Date(o);S.setDate(S.getDate()+v),d.push({date:S.toISOString().split("T")[0],dayNumber:S.getDate(),isCurrentMonth:S.getMonth()===t})}return d}),Y=$(()=>[{label:"Colloqui Oggi",value:k(new Date().toISOString().split("T")[0]).length,icon:"calendar",iconClass:"text-blue-500"},{label:"Questa Settimana",value:K().length,icon:"clock",iconClass:"text-green-500"},{label:"Programmati",value:b.value.filter(a=>a.status==="scheduled").length,icon:"check-circle",iconClass:"text-purple-500"},{label:"Completati",value:b.value.filter(a=>a.status==="completed").length,icon:"academic-cap",iconClass:"text-orange-500"}]),x=async()=>{M.value=!0;try{const a=c.value==="week"?C(s.value):new Date(s.value.getFullYear(),s.value.getMonth(),1),t=c.value==="week"?new Date(a.getTime()+6*24*60*60*1e3):new Date(s.value.getFullYear(),s.value.getMonth()+1,0),e={date_from:a.toISOString().split("T")[0],date_to:t.toISOString().split("T")[0]};w.value&&(e.status=w.value),f.value&&(e.interviewer_id=f.value),console.log("📅 Loading calendar data with params:",e);const o=await I.fetchInterviews(e);if(b.value=(o==null?void 0:o.interviews)||[],console.log("📊 Loaded interviews:",b.value.length,b.value),z.value.length===0){const d=await I.fetchInterviewers();z.value=d||[]}}catch(a){console.error("Error loading calendar data:",a)}finally{M.value=!1}},C=a=>{const t=new Date(a),e=t.getDay(),o=t.getDate()-e+(e===0?-6:1);return new Date(t.setDate(o))},G=()=>{c.value==="week"?s.value.setDate(s.value.getDate()-7):s.value.setMonth(s.value.getMonth()-1),s.value=new Date(s.value),x()},U=()=>{c.value==="week"?s.value.setDate(s.value.getDate()+7):s.value.setMonth(s.value.getMonth()+1),s.value=new Date(s.value),x()},Q=()=>{s.value=new Date,console.log("📅 Going to today:",s.value.toISOString().split("T")[0]),x()},k=a=>b.value.filter(t=>new Date(t.scheduled_date).toISOString().split("T")[0]===a),J=(a,t)=>k(a).filter(e=>new Date(e.scheduled_date).getHours()===t),K=a=>{const e=C(new Date),o=new Date(e.getTime()+6*24*60*60*1e3);return b.value.filter(d=>{const v=new Date(d.scheduled_date);return v>=e&&v<=o})},O=a=>{const t=new Date().toISOString().split("T")[0];return a===t},X=(a,t)=>{h.value=a,D.value=t,_.value=!0},Z=a=>{h.value=a,D.value=null,_.value=!0},ee=()=>{const a=new URLSearchParams;if(h.value){const t=D.value?`${h.value}T${D.value.toString().padStart(2,"0")}:00`:`${h.value}T09:00`;a.set("scheduled_date",t)}_.value=!1,N.push(`/app/recruiting/interviews/new?${a.toString()}`)},q=a=>{N.push(`/app/recruiting/interviews/${a.id}`)},te=a=>({phone:"Tel",phone_screening:"Tel Screen",video:"Video",video_technical:"Video Tech",in_person:"Persona",onsite_cultural:"In Sede",technical:"Tecnico",behavioral:"Comp.",final:"Finale",final_executive:"Finale Exec"})[a]||a,ae=a=>({scheduled:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",completed:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",cancelled:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"})[a]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",re=a=>a?new Date(a).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"}):"",A=a=>a.toLocaleDateString("it-IT",{month:"short",day:"numeric"}),se=a=>a?new Date(a).toLocaleTimeString("it-IT",{hour:"2-digit",minute:"2-digit"}):"",oe=async()=>{try{const a={};if(w.value&&(a.status=w.value),f.value&&(a.interviewer_id=f.value),c.value==="week"){const t=C(s.value),e=new Date(t.getTime()+6*24*60*60*1e3);a.date_from=t.toISOString().split("T")[0],a.date_to=e.toISOString().split("T")[0],await I.downloadInterviewsBatchCalendar(a)}else{const t=s.value.getFullYear(),e=s.value.getMonth()+1;await I.downloadMonthlyCalendar(t,e)}}catch(a){console.error("Error exporting calendar:",a),alert("Errore nell'esportazione calendario: "+a.message)}};return ne(()=>{x()}),(a,t)=>(l(),n("div",ge,[r("div",ve,[r("div",me,[t[9]||(t[9]=r("div",null,[r("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"}," Calendario Colloqui "),r("p",{class:"text-sm text-gray-600 dark:text-gray-400"}," Gestione e pianificazione dei colloqui di selezione ")],-1)),r("div",ye,[u(g,{variant:"outline-primary",icon:"arrow-path",text:"Aggiorna",onClick:x,loading:M.value,size:"sm"},null,8,["loading"]),u(g,{variant:"outline-primary",icon:"calendar",text:"Esporta Periodo",onClick:oe,size:"sm"}),u(g,{variant:"primary",icon:"plus",text:"Nuovo Colloquio",onClick:t[0]||(t[0]=e=>a.$router.push("/app/recruiting/interviews/new"))})])])]),r("div",pe,[r("div",be,[r("div",he,[r("div",xe,[u(g,{variant:"outline-secondary",icon:"chevron-left",onClick:G,size:"sm"}),u(g,{variant:"outline-secondary",icon:"chevron-right",onClick:U,size:"sm"})]),r("h2",ke,i(H.value),1),u(g,{variant:"outline-secondary",text:"Oggi",onClick:Q,size:"sm"})]),r("div",we,[r("div",fe,[u(g,{variant:c.value==="week"?"primary":"secondary",text:"Settimana",onClick:t[1]||(t[1]=e=>c.value="week"),size:"sm"},null,8,["variant"]),u(g,{variant:c.value==="month"?"primary":"secondary",text:"Mese",onClick:t[2]||(t[2]=e=>c.value="month"),size:"sm"},null,8,["variant"])]),W(r("select",{"onUpdate:modelValue":t[3]||(t[3]=e=>w.value=e),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm",onChange:x},t[10]||(t[10]=[r("option",{value:""},"Tutti gli stati",-1),r("option",{value:"scheduled"},"Programmati",-1),r("option",{value:"completed"},"Completati",-1),r("option",{value:"cancelled"},"Annullati",-1)]),544),[[E,w.value]]),W(r("select",{"onUpdate:modelValue":t[4]||(t[4]=e=>f.value=e),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm",onChange:x},[t[11]||(t[11]=r("option",{value:""},"Tutti gli intervistatori",-1)),(l(!0),n(y,null,p(z.value,e=>(l(),n("option",{key:e.id,value:e.id},i(e.full_name),9,_e))),128))],544),[[E,f.value]])])])]),r("div",De,[(l(!0),n(y,null,p(Y.value,e=>(l(),n("div",{key:e.label,class:"bg-white dark:bg-gray-800 rounded-lg shadow p-4"},[r("div",Ce,[r("div",Se,[u(ce,{name:e.icon,size:"md",class:T(e.iconClass)},null,8,["name","class"])]),r("div",Te,[r("p",Ie,i(e.label),1),r("p",Me,i(e.value),1)])])]))),128))]),M.value?(l(),n("div",Oe,t[12]||(t[12]=[r("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary-600"},null,-1)]))):(l(),n("div",$e,[c.value==="week"?(l(),n("div",ze,[r("div",Fe,[t[13]||(t[13]=r("div",{class:"p-3 text-sm font-medium text-gray-500 dark:text-gray-400"},"Orario",-1)),(l(!0),n(y,null,p(V.value,e=>(l(),n("div",{key:e.date,class:"p-3 text-center"},[r("div",Le,i(e.dayName),1),r("div",{class:T(["text-lg font-bold",O(e.date)?"text-primary-600":"text-gray-900 dark:text-white"])},i(e.dayNumber),3)]))),128))]),r("div",Ne,[r("div",Pe,[(l(!0),n(y,null,p(j(P),e=>(l(),n("div",{key:e,class:"h-16 p-2 border-b border-gray-100 dark:border-gray-700 text-xs text-gray-500 dark:text-gray-400"},i(e)+":00 ",1))),128))]),(l(!0),n(y,null,p(V.value,e=>(l(),n("div",{key:e.date,class:"border-r border-gray-200 dark:border-gray-600 last:border-r-0 relative"},[(l(!0),n(y,null,p(j(P),o=>(l(),n("div",{key:o,class:"h-16 border-b border-gray-100 dark:border-gray-700 relative p-1",onClick:d=>X(e.date,o)},[(l(!0),n(y,null,p(J(e.date,o),d=>{var v;return l(),n("div",{key:d.id,class:"absolute inset-x-1 top-1 bg-blue-100 dark:bg-blue-900 border-l-4 border-blue-500 rounded-md p-2 cursor-pointer hover:bg-blue-200 dark:hover:bg-blue-800 transition-all duration-200 shadow-sm",style:ie({height:`${Math.max(Math.min(d.duration_minutes/15*4,60),48)}px`}),onClick:L(S=>q(d),["stop"])},[r("div",Ae,i(((v=d.candidate)==null?void 0:v.full_name)||"Candidato N/A"),1),r("div",We,i(te(d.interview_type)),1)],12,qe)}),128))],8,Ve))),128))]))),128))])])):(l(),n("div",Ee,[r("div",je,[(l(),n(y,null,p(B,e=>r("div",{key:e,class:"p-3 text-center text-sm font-medium text-gray-500 dark:text-gray-400"},i(e),1)),64))]),r("div",Be,[(l(!0),n(y,null,p(R.value,e=>(l(),n("div",{key:e.date,class:T(["min-h-24 border-r border-b border-gray-200 dark:border-gray-600 last:border-r-0 p-2",{"bg-gray-50 dark:bg-gray-700":!e.isCurrentMonth,"bg-primary-50 dark:bg-primary-900/20":O(e.date)}]),onClick:o=>Z(e.date)},[r("div",Re,[r("span",{class:T(["text-sm font-medium",{"text-gray-400 dark:text-gray-500":!e.isCurrentMonth,"text-primary-600 dark:text-primary-400":O(e.date),"text-gray-900 dark:text-white":e.isCurrentMonth&&!O(e.date)}])},i(e.dayNumber),3),k(e.date).length>0?(l(),n("div",Ye,i(k(e.date).length),1)):F("",!0)]),r("div",Ge,[(l(!0),n(y,null,p(k(e.date).slice(0,3),o=>{var d;return l(),n("div",{key:o.id,class:T(["text-xs p-1 rounded cursor-pointer truncate",ae(o.status)]),onClick:L(v=>q(o),["stop"])},i(se(o.scheduled_date))+" "+i(((d=o.candidate)==null?void 0:d.full_name)||"Candidato N/A"),11,Ue)}),128)),k(e.date).length>3?(l(),n("div",Qe," +"+i(k(e.date).length-3)+" altri ",1)):F("",!0)])],10,He))),128))])]))])),_.value?(l(),n("div",{key:2,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:t[8]||(t[8]=e=>_.value=!1)},[r("div",{class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:t[7]||(t[7]=L(()=>{},["stop"]))},[r("div",Je,[t[14]||(t[14]=r("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Programma Colloquio Rapido ",-1)),r("div",Ke,i(h.value?re(h.value):"")+" "+i(D.value?`alle ${D.value}:00`:""),1),r("div",Xe,[u(g,{variant:"outline-primary",icon:"plus",text:"Colloquio Completo",block:"",onClick:ee}),u(g,{variant:"outline-secondary",icon:"calendar",text:"Visualizza Tutti i Colloqui",block:"",onClick:t[5]||(t[5]=e=>a.$router.push("/app/recruiting/interviews"))})]),r("div",Ze,[u(g,{variant:"secondary",text:"Chiudi",onClick:t[6]||(t[6]=e=>_.value=!1)})])])])])):F("",!0)]))}},nt=ue(et,[["__scopeId","data-v-d6fee846"]]);export{nt as default};
