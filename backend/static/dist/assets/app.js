const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.js","assets/vendor.js","assets/index2.js","assets/index3.js","assets/PlusIcon.js","assets/Home.js","assets/About.js","assets/Contact.js","assets/Services.js","assets/Privacy.js","assets/CookiePolicy.js","assets/Careers.js","assets/Careers.css","assets/Login.js","assets/StandardButton.js","assets/Register.js","assets/Dashboard.js","assets/DashboardTemplate.js","assets/DashboardTemplate.css","assets/Dashboard.css","assets/Projects.js","assets/projects2.js","assets/StatusBadge.js","assets/StatusBadge.css","assets/StandardInput.js","assets/PageHeader.js","assets/FilterBar.js","assets/ErrorPage.js","assets/ProjectCreate.js","assets/ProjectView.js","assets/ProjectView.css","assets/ProjectEdit.js","assets/TimesheetEntry.js","assets/AlertsSection.js","assets/TimesheetGrid.js","assets/TimesheetGrid.css","assets/timesheet.js","assets/TimesheetRequests.js","assets/ActionButtonGroup.js","assets/TimesheetDashboard.js","assets/StatsGrid.js","assets/TabContainer.js","assets/TabContainer.css","assets/personnel.js","assets/TabNavigation.css","assets/TimesheetAnalytics.js","assets/DataTable.js","assets/formatters.js","assets/auto.js","assets/CommunicationDashboard.js","assets/useFormatters.js","assets/CommunicationDashboard.css","assets/ForumIndex.js","assets/ListPageTemplate.js","assets/Pagination.js","assets/Pagination.css","assets/EditTopicModal.js","assets/ConfirmationModal.js","assets/ForumIndex.css","assets/TopicView.js","assets/TopicView.css","assets/PollsIndex.js","assets/EditPollModal.js","assets/EditPollModal.css","assets/PollsIndex.css","assets/PollView.js","assets/PollView.css","assets/MessagesIndex.js","assets/useDebounce.js","assets/MessagesIndex.css","assets/EventsIndex.js","assets/EventEditModal.js","assets/EventEditModal.css","assets/EventsIndex.css","assets/EventView.js","assets/NewsIndex.js","assets/FormBuilder.js","assets/FormBuilder.css","assets/NewsIndex.css","assets/NewsView.js","assets/NewsView.css","assets/HRAssistantChat.js","assets/ConfidenceBadge.js","assets/HRKnowledgeBase.js","assets/DashboardExample.js","assets/DashboardExample.css","assets/TimesheetGridExample.js","assets/ComponentsExample.js","assets/TabNavigation.js","assets/ViewModeToggle.js","assets/ViewModeToggle.css","assets/KanbanView.js","assets/ProposalCard.js","assets/ProposalCard.css","assets/FormBuilderExample.js","assets/FormBuilderExample.css","assets/ViewModeToggleExample.js","assets/ViewModeToggleExample.css","assets/KanbanExample.js","assets/ProposalCardExample.js","assets/IconSystemExample.js","assets/WizardContainerExample.js","assets/WizardContainer.js","assets/WizardContainer.css","assets/WizardContainerExample.css","assets/PersonnelOrgChart.js","assets/PersonnelOrgChart.css","assets/SkillsMatrix.js","assets/SkillsMatrix.css","assets/JobLevels.js","assets/JobLevels.css","assets/DepartmentCreate.js","assets/DepartmentView.js","assets/DepartmentEdit.js","assets/PersonnelAllocation.js","assets/PersonnelAdmin.js","assets/PersonnelAdmin.css","assets/PersonnelPerformance.js","assets/ReviewEditModal.js","assets/ReviewEditModal.css","assets/ReviewDetailModal.js","assets/MarkdownContent.js","assets/MarkdownContent.css","assets/ReviewDetailModal.css","assets/PersonnelPerformance.css","assets/PersonnelPerformanceReviews.js","assets/PersonnelPerformanceReviewDetail.js","assets/PersonnelProfile.js","assets/PersonnelProfile.css","assets/PersonnelPerformancePersonal.js","assets/BaseModal.js","assets/PersonnelPerformancePersonal.css","assets/RecruitingDashboard.js","assets/recruiting.js","assets/RecruitingDashboard.css","assets/JobPostingsList.js","assets/JobPostingsList.css","assets/CandidatesList.js","assets/CandidatesList.css","assets/CandidateForm.js","assets/CandidateForm.css","assets/CandidateView.js","assets/CandidateView.css","assets/CandidateEdit.js","assets/CandidateEdit.css","assets/ApplicationsList.js","assets/ApplicationsList.css","assets/ApplicationView.js","assets/ApplicationView.css","assets/JobPostingForm.js","assets/JobPostingForm.css","assets/JobPostingView.js","assets/JobPostingView.css","assets/RecruitingPipeline.js","assets/RecruitingPipeline.css","assets/InterviewsCalendar.js","assets/InterviewsCalendar.css","assets/InterviewForm.js","assets/InterviewForm.css","assets/InterviewView.js","assets/InterviewView.css","assets/Admin.js","assets/AdminSettings.js","assets/KPITemplates.js","assets/FeatureFlags.js","assets/SelfHealingDashboard.js","assets/ClaudePromptModal.js","assets/ClaudePromptModal.css","assets/SelfHealingDashboard.css","assets/SelfHealingPatterns.js","assets/SecurityDashboard.js","assets/Profile.js","assets/Settings.js","assets/CRMDashboard.js","assets/crm.js","assets/CRMDashboard.css","assets/ClientsList.js","assets/industries.js","assets/ClientForm.js","assets/ClientView.js","assets/ContactsList.js","assets/ProposalsPipeline.js","assets/ProposalsPipeline.css","assets/ProposalForm.js","assets/ProposalView.js","assets/ContractsList.js","assets/contractTypes.js","assets/ContractForm.js","assets/ContractView.js","assets/PreInvoicesList.js","assets/PreInvoiceForm.js","assets/PreInvoiceView.js","assets/BIDashboard.js","assets/CaseStudies.js","assets/CoreSkills.js","assets/TechnicalOffer.js","assets/MarketIntelligence.js","assets/AdvancedReports.js","assets/CertificationsDashboard.js","assets/certifications.js","assets/CertificationsList.js","assets/CertificationsCatalog.js","assets/Breadcrumb.js","assets/CertificationCreate.js","assets/CertificationView.js","assets/CertificationEdit.js","assets/CertificationReadiness.js","assets/CertificationReadiness.css","assets/CEODashboard.js","assets/marked.esm.js","assets/CEODashboard.css","assets/AIAssistant.js","assets/InsightsReports.js","assets/jspdf.es.min.js","assets/InsightsReports.css","assets/ResearchConfig.js","assets/FundingDashboard.js","assets/funding.js","assets/FundingSearch.js","assets/FundingSearch.css","assets/FundingOpportunityView.js","assets/FundingOpportunityView.css","assets/FundingApplicationView.js","assets/FundingApplicationView.css","assets/FundingReporting.js","assets/FundingReporting.css","assets/FundingApplicationForm.js","assets/FundingExpenseForm.js","assets/FundingExpenseView.js","assets/ComplianceDashboard.js","assets/governance.js","assets/AuditTrail.js","assets/PolicyCenter.js","assets/ComplianceEvents.js","assets/RiskAssessment.js","assets/EngagementDashboard.js","assets/engagement.js","assets/EngagementDashboard.css","assets/Leaderboard.js","assets/Leaderboard.css","assets/EngagementCampaigns.js","assets/EngagementCampaigns.css","assets/EngagementCampaignDetail.js","assets/EngagementRewards.js","assets/EngagementRewards.css","assets/EngagementAdmin.js","assets/EngagementPoints.js","assets/HelpDashboard.js","assets/help.js","assets/HelpContent.js","assets/HelpChatWidget.js","assets/HelpChatWidget.css","assets/HelpContent.css","assets/HelpContentList.js","assets/HelpContentList.css","assets/HelpFAQ.js","assets/HelpFAQ.css","assets/HelpContact.js","assets/HelpContact.css","assets/ErrorReport.js","assets/DraftsDashboard.js","assets/DraftsModuleDetails.js","assets/DesignSystemTest.js"])))=>i.map(i=>d[i]);
import{r as q,w as te,a as it,d as ge,c as w,u as qe,b as h,e as E,f as re,o as i,g as ht,h as U,m as gt,i as lt,n as R,j as a,t as S,k as z,l as T,F as ae,p as ie,q as fe,s as N,v as H,x as ce,y as $e,z as ft,A as ct,B as de,C as pe,D as ke,E as Re,T as vt,G as Le,H as _t,I as wt,J as yt,K as bt,L as xt,M as kt,N as Et,O as At,P as Pt}from"./vendor.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))o(n);new MutationObserver(n=>{for(const s of n)if(s.type==="childList")for(const u of s.addedNodes)u.tagName==="LINK"&&u.rel==="modulepreload"&&o(u)}).observe(document,{childList:!0,subtree:!0});function r(n){const s={};return n.integrity&&(s.integrity=n.integrity),n.referrerPolicy&&(s.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?s.credentials="include":n.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function o(n){if(n.ep)return;n.ep=!0;const s=r(n);fetch(n.href,s)}})();const oe=q(!1);let Ne=!1;const ut=t=>{t?(document.documentElement.classList.add("dark"),localStorage.setItem("darkMode","true")):(document.documentElement.classList.remove("dark"),localStorage.setItem("darkMode","false"))},Ct=()=>{Ne||(te(oe,t=>{ut(t)}),Ne=!0)};function Oe(){return Ct(),{isDarkMode:oe,toggleDarkMode:()=>{oe.value=!oe.value},setDarkMode:o=>{oe.value=o},initializeDarkMode:()=>{const o=localStorage.getItem("darkMode"),n=document.documentElement.classList.contains("dark");if(o==="true")oe.value=!0;else if(o==="false")oe.value=!1;else{const f=window.matchMedia("(prefers-color-scheme: dark)").matches;oe.value=n||f}ut(oe.value);const s=window.matchMedia("(prefers-color-scheme: dark)"),u=f=>{const p=localStorage.getItem("darkMode");(!p||p==="null")&&(oe.value=f.matches)};s.addEventListener("change",u)}}}const P=it.create({baseURL:"",timeout:6e4,withCredentials:!0,headers:{"Content-Type":"application/json"}}),ne={default:6e4,export:3e5,upload:12e4,ai_analysis:18e4},se=t=>{const e=it.create({baseURL:P.defaults.baseURL,timeout:t,withCredentials:!0,headers:P.defaults.headers});return e.interceptors.request.use(r=>{var o;if(["post","put","patch","delete"].includes((o=r.method)==null?void 0:o.toLowerCase())){const n=dt();n?r.headers["X-CSRFToken"]=n:console.warn("⚠️ CSRF token non trovato per richiesta:",r.method,r.url)}return r},r=>Promise.reject(r)),e},dt=()=>{var t,e;return((t=document.querySelector('meta[name="csrf-token"]'))==null?void 0:t.getAttribute("content"))||((e=window.APP_CONFIG)==null?void 0:e.csrfToken)};P.interceptors.request.use(t=>{var e;if(["post","put","patch","delete"].includes((e=t.method)==null?void 0:e.toLowerCase())){const r=dt();r?t.headers["X-CSRFToken"]=r:console.warn("⚠️ CSRF token non trovato per richiesta:",t.method,t.url)}return t},t=>Promise.reject(t));let ve=!1;P.interceptors.response.use(t=>t,t=>{var e;if(((e=t.response)==null?void 0:e.status)===401&&!ve){ve=!0;try{localStorage.removeItem("user"),console.warn("[API] Session expired, authentication required");const r=window.location.pathname;!r.startsWith("/auth/")&&!r.startsWith("/error/")?setTimeout(()=>{window.location.href="/auth/login",ve=!1},100):ve=!1}catch(r){console.error("[API] Error during 401 cleanup:",r),ve=!1}}return Promise.reject(t)});const Js=Object.freeze(Object.defineProperty({__proto__:null,API_TIMEOUTS:ne,createApiWithTimeout:se,default:P},Symbol.toStringTag,{value:"Module"})),be=ge("tenant",()=>{const t=q(null),e=q(!1),r=q(null),o=w(()=>{var y;return((y=t.value)==null?void 0:y.company)||{}}),n=w(()=>{var y;return((y=t.value)==null?void 0:y.contact)||{}}),s=w(()=>{var y;return((y=t.value)==null?void 0:y.branding)||{}}),u=w(()=>{var y;return((y=t.value)==null?void 0:y.pages)||{}}),f=w(()=>{var y;return((y=t.value)==null?void 0:y.navigation)||{}}),p=w(()=>{var y;return((y=t.value)==null?void 0:y.footer)||{}});async function g(){try{if(e.value=!0,window.TENANT_CONFIG){t.value=window.TENANT_CONFIG;return}const y=await fetch("/api/config/tenant");if(!y.ok)throw new Error(`HTTP ${y.status}`);const x=await y.json();t.value=x}catch(y){r.value="Errore nel caricamento della configurazione",console.error("TenantStore - Error loading config:",y)}finally{e.value=!1}}function b(y="primary"){const x=s.value;return(y==="transparent"?x.logo_transparent:x.logo_primary)||null}function l(y,x={}){if(!y||typeof y!="string")return y;let _=y;const k={"company.name":o.value.name||"DatVinci","company.tagline":o.value.tagline||"","company.description":o.value.description||"","company.mission":o.value.mission||"","company.vision":o.value.vision||"","company.founded":o.value.founded||"","company.team_size":o.value.team_size||"","contact.email":n.value.email||"","contact.phone":n.value.phone||"","contact.address":n.value.address||"",current_year:new Date().getFullYear().toString(),...x};for(const[m,c]of Object.entries(k)){const C=new RegExp(`\\{${m}\\}`,"g");_=_.replace(C,c||"")}return _}return{config:t,loading:e,error:r,company:o,contact:n,branding:s,pages:u,navigation:f,footer:p,loadConfig:g,getLogoUrl:b,interpolateText:l}}),Be=q("");function qt(){const t=qe(),e=be(),r={home:"home",about:"about",contact:"contact",services:"services",privacy:"privacy",login:"Accesso - {company.name}",register:"Registrazione - {company.name}",dashboard:"Dashboard - {company.name}",projects:"Progetti - {company.name}","project-view":"Progetto - {company.name}","project-create":"Nuovo Progetto - {company.name}","project-edit":"Modifica Progetto - {company.name}",tasks:"Task - {company.name}","task-view":"Task - {company.name}","task-create":"Nuovo Task - {company.name}","task-edit":"Modifica Task - {company.name}",personnel:"Personale - {company.name}","personnel-profile":"Profilo Personale - {company.name}",timesheet:"Timesheet - {company.name}","timesheet-entry":"Inserimento Timesheet - {company.name}","timesheet-analytics":"Analytics Timesheet - {company.name}","timesheet-requests":"Richieste Timesheet - {company.name}","timesheet-dashboard":"Dashboard Timesheet - {company.name}",sales:"Vendite - {company.name}","sales-dashboard":"Dashboard Vendite - {company.name}","sales-opportunities":"Opportunità - {company.name}","sales-clients":"Clienti - {company.name}","sales-reports":"Report Vendite - {company.name}",invoicing:"Fatturazione - {company.name}","invoicing-invoices":"Fatture - {company.name}","invoicing-pre-invoices":"Pre-fatture - {company.name}","invoicing-invoice-create":"Nuova Fattura - {company.name}","invoicing-pre-invoice-create":"Nuova Pre-fattura - {company.name}","business-intelligence":"Business Intelligence - {company.name}","business-intelligence-dashboard":"BI Dashboard - {company.name}","business-intelligence-case-studies":"Case Studies - {company.name}","business-intelligence-core-skills":"Competenze Core - {company.name}","business-intelligence-technical-offer":"Offerta Tecnica - {company.name}","business-intelligence-market-intel":"Market Intelligence - {company.name}","business-intelligence-advanced-reports":"Reportistica Avanzata - {company.name}",admin:"Amministrazione - {company.name}","admin-users":"Gestione Utenti - {company.name}","admin-kpi-templates":"Template KPI - {company.name}",profile:"Il tuo Profilo - {company.name}",settings:"Impostazioni - {company.name}"};function o(p,g){var b,l;return!p||!g?"Portale":p.replace(/{company\.name}/g,((b=g.company)==null?void 0:b.name)||"Portale").replace(/{company\.tagline}/g,((l=g.company)==null?void 0:l.tagline)||"").replace(/{current_year}/g,new Date().getFullYear())}function n(p,g){var l,y;let b=r[p];return b||["home","about","contact","services","privacy"].includes(p)&&(b=(y=(l=g==null?void 0:g.routes)==null?void 0:l[p])==null?void 0:y.title),b||(b="{company.name}"),b.includes("{")?o(b,g):b}function s(p){p&&(Be.value=p,document.title=p)}function u(){const p=e.config;if(!p)return;const g=t.name||t.path.split("/").pop()||"home",b=n(g,p);s(b)}function f(p){const g=e.config,b=o(p,g);s(b)}return te(()=>t.name,()=>u(),{immediate:!0}),te(()=>e.config,()=>u(),{immediate:!0}),{currentTitle:Be,updateTitle:s,setTitleFromRoute:u,setCustomTitle:f,getTitleForRoute:n}}const $t={id:"app"},It={__name:"App",setup(t){const{initializeDarkMode:e}=Oe();return e(),qt(),(r,o)=>{const n=re("router-view");return i(),h("div",$t,[E(n)])}}},Mt="modulepreload",Tt=function(t){return"/"+t},He={},d=function(e,r,o){let n=Promise.resolve();if(r&&r.length>0){document.getElementsByTagName("link");const u=document.querySelector("meta[property=csp-nonce]"),f=(u==null?void 0:u.nonce)||(u==null?void 0:u.getAttribute("nonce"));n=Promise.allSettled(r.map(p=>{if(p=Tt(p),p in He)return;He[p]=!0;const g=p.endsWith(".css"),b=g?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${p}"]${b}`))return;const l=document.createElement("link");if(l.rel=g?"stylesheet":Mt,g||(l.as="script"),l.crossOrigin="",l.href=p,f&&l.setAttribute("nonce",f),document.head.appendChild(l),g)return new Promise((y,x)=>{l.addEventListener("load",y),l.addEventListener("error",()=>x(new Error(`Unable to preload CSS for ${p}`)))})}))}function s(u){const f=new Event("vite:preloadError",{cancelable:!0});if(f.payload=u,window.dispatchEvent(f),!f.defaultPrevented)throw u}return n.then(u=>{for(const f of u||[])f.status==="rejected"&&s(f.reason);return e().catch(s)})},me=ge("auth",()=>{var L;const t=localStorage.getItem("user"),e=q(t?JSON.parse(t):null),r=q(!1),o=q(null),n=q(!1),s=q(((L=document.querySelector('meta[name="csrf-token"]'))==null?void 0:L.getAttribute("content"))||""),u=q({}),f=q(!1),p=q(null),g=async()=>{var v,A,M;try{const F=await P.get("/api/auth/csrf-token");(v=F.data)!=null&&v.success&&((M=(A=F.data)==null?void 0:A.data)!=null&&M.csrf_token)&&(s.value=F.data.data.csrf_token,console.log("CSRF token refreshed successfully"))}catch(F){console.error("Error refreshing CSRF token:",F)}return s.value},b=w(()=>!!e.value&&n.value),l={admin:["admin","manage_users","assign_roles","view_all_projects","create_project","edit_project","delete_project","assign_to_project","manage_project_tasks","manage_project_resources","approve_timesheets","view_personnel_data","edit_personnel_data","view_contracts","manage_contracts","view_crm","manage_clients","manage_proposals","view_reports","view_dashboard","submit_timesheet","view_own_timesheets","view_funding","manage_funding","view_products","manage_products","view_performance","manage_performance","view_communications","manage_communications","view_startup","manage_startup","view_ceo","view_compliance","view_agents","view_communication","manage_communication","create_polls","moderate_forum","send_company_messages","manage_events","view_engagement","participate_engagement","manage_engagement_campaigns","manage_engagement_rewards","view_engagement_analytics"],manager:["view_dashboard","view_all_projects","edit_project","assign_to_project","manage_project_tasks","manage_project_resources","approve_timesheets","view_personnel_data","view_crm","view_reports","submit_timesheet","view_own_timesheets","manage_clients","manage_proposals","view_funding","manage_funding","view_products","manage_products","view_performance","manage_performance","view_communications","manage_communications","view_startup","manage_startup","view_ceo","view_compliance","view_agents","view_communication","manage_communication","create_polls","moderate_forum","send_company_messages","manage_events","view_engagement","participate_engagement","manage_engagement_campaigns","manage_engagement_rewards","view_engagement_analytics"],employee:["view_dashboard","view_own_timesheets","submit_timesheet","view_funding","view_communication","view_engagement","participate_engagement"],sales:["view_dashboard","view_crm","manage_clients","manage_proposals","submit_timesheet","view_own_timesheets","view_reports","view_funding","view_products","manage_products","view_communication","create_polls","view_engagement","participate_engagement"],human_resources:["view_dashboard","manage_users","view_personnel_data","edit_personnel_data","view_contracts","manage_contracts","submit_timesheet","view_own_timesheets","view_reports","view_funding","manage_funding","view_performance","manage_performance","view_communications","manage_communications","view_startup","manage_startup","view_communication","manage_communication","create_polls","send_company_messages","manage_events","view_engagement","participate_engagement","view_engagement_analytics"]},y=v=>!v||typeof v!="string"||!e.value||!e.value.role?!1:e.value.role==="admin"?!0:(l[e.value.role]||[]).includes(v),x=()=>{var v,A;console.log("Current user:",e.value),console.log("User role:",(v=e.value)==null?void 0:v.role),console.log("Has admin permission:",y("admin")),console.log("Available permissions for role:",l[(A=e.value)==null?void 0:A.role])};async function _(v){var A,M;r.value=!0,o.value=null;try{await g();const F=await P.post("/api/auth/login",v);return F.data.success?(e.value=F.data.data.user,localStorage.setItem("user",JSON.stringify(e.value)),n.value=!0,{success:!0}):(o.value=F.data.message||"Errore durante il login",{success:!1,error:o.value})}catch(F){return o.value=((M=(A=F.response)==null?void 0:A.data)==null?void 0:M.message)||"Errore di connessione",{success:!1,error:o.value}}finally{r.value=!1}}async function k(v){var A,M;r.value=!0,o.value=null;try{await g();const F=await P.post("/api/auth/register",v);return F.data.success?{success:!0,message:F.data.message}:(o.value=F.data.message||"Errore durante la registrazione",{success:!1,error:o.value})}catch(F){return o.value=((M=(A=F.response)==null?void 0:A.data)==null?void 0:M.message)||"Errore di connessione",{success:!1,error:o.value}}finally{r.value=!1}}async function m(){try{await P.post("/api/auth/logout")}catch(v){console.warn("Errore durante il logout:",v)}finally{e.value=null,n.value=!1,localStorage.removeItem("user")}}async function c(){if(n.value)return b.value;try{const v=await P.get("/api/auth/me");return v.data.success?(e.value=v.data.data.user,localStorage.setItem("user",JSON.stringify(e.value)),n.value=!0,!0):(await m(),!1)}catch{return await m(),!1}}let C=!1,Z=null;async function $(){if(C&&Z)return console.debug("[Auth] Waiting for ongoing initialization"),await Z;if(n.value)return console.debug("[Auth] Already initialized"),b.value;C=!0,Z=I();try{return await Z}finally{C=!1,Z=null}}async function I(){console.debug("[Auth] Starting initialization");let v=!1;try{return e.value?(await g(),v=await c()):n.value=!0,await K(),console.debug("[Auth] Initialization completed:",v),v}catch(A){return console.error("[Auth] Initialization error:",A),n.value=!0,!1}}async function B(){var v,A,M,F;f.value=!0,p.value=null;try{const j=await P.get("/api/auth/oauth/providers");if((v=j.data)!=null&&v.success)return u.value=j.data.data.providers||{},u.value;throw new Error(((A=j.data)==null?void 0:A.message)||"Errore nel caricamento provider OAuth")}catch(j){return p.value=((F=(M=j.response)==null?void 0:M.data)==null?void 0:F.message)||j.message||"Errore nel caricamento provider OAuth",console.error("Error fetching OAuth providers:",j),u.value={},{}}finally{f.value=!1}}async function le(v){if(!v||!u.value[v])return p.value=`Provider OAuth ${v} non disponibile`,{success:!1,error:p.value};try{const A=u.value[v].login_url;return window.location.href=A,{success:!0,redirecting:!0}}catch(A){return p.value=A.message||"Errore durante il login OAuth",console.error("OAuth login error:",A),{success:!1,error:p.value}}}async function ee(){var v,A;try{const M=await P.get("/api/auth/oauth/accounts");if((v=M.data)!=null&&v.success)return M.data.data.oauth_accounts||[];throw new Error(((A=M.data)==null?void 0:A.message)||"Errore nel caricamento account OAuth")}catch(M){return console.error("Error fetching OAuth accounts:",M),[]}}async function ue(v){var A,M,F,j;try{const Q=await P.delete(`/api/auth/oauth/accounts/${v}`);if((A=Q.data)!=null&&A.success)return{success:!0,message:Q.data.message};throw new Error(((M=Q.data)==null?void 0:M.message)||"Errore nello scollegamento account OAuth")}catch(Q){const Me=((j=(F=Q.response)==null?void 0:F.data)==null?void 0:j.message)||Q.message||"Errore nello scollegamento account OAuth";return console.error("Error unlinking OAuth account:",Q),{success:!1,error:Me}}}const D=w(()=>Object.keys(u.value).length>0),G=w(()=>{var v;return((v=u.value.google)==null?void 0:v.enabled)===!0}),J=w(()=>{var v;return((v=u.value.microsoft)==null?void 0:v.enabled)===!0}),W=w(()=>Object.entries(u.value).filter(([v,A])=>A.enabled).map(([v,A])=>({provider:v,name:A.name,displayName:A.display_name,loginUrl:A.login_url}))),K=async()=>{try{await B()}catch(v){console.warn("Failed to initialize OAuth providers:",v)}};return{user:e,loading:r,error:o,sessionChecked:n,isAuthenticated:b,hasPermission:y,debugPermissions:x,login:_,register:k,logout:m,checkAuth:c,initializeAuth:$,csrfToken:s,refreshCsrfToken:g,oauthProviders:u,oauthLoading:f,oauthError:p,hasOAuthProviders:D,isGoogleOAuthEnabled:G,isMicrosoftOAuthEnabled:J,getOAuthProvidersList:W,fetchOAuthProviders:B,loginWithOAuth:le,getUserOAuthAccounts:ee,unlinkOAuthAccount:ue,initializeOAuth:K}}),Ie=ge("featureFlags",()=>{const t=q({}),e=q([]),r=q(!1),o=q(null),n=q(null),s=5*60*1e3,u=w(()=>r.value),f=w(()=>!!o.value),p=w(()=>n.value?Date.now()-n.value<s:!1),g=D=>!n.value&&Object.keys(t.value).length===0?!1:t.value[D]!==void 0?t.value[D]:!0,b=D=>D in t.value,l=D=>D.every(G=>g(G)),y=D=>D.some(G=>g(G)),x=async(D={})=>{var G,J,W,K;if(p.value&&!D.forceRefresh)return t.value;if(r.value&&!D.forceRefresh)return console.debug("[FeatureFlags] Request already in progress, skipping"),t.value;r.value=!0,o.value=null;try{const L={enabled_only:D.enabledOnly===!0};console.debug("[FeatureFlags] Fetching feature flags...");const v=await P.get("/api/feature-flags",{params:L});if((G=v.data)!=null&&G.success)t.value=v.data.data.flags||{},e.value=v.data.data.details||[],n.value=Date.now();else throw new Error(((J=v.data)==null?void 0:J.message)||"Errore nel caricamento feature flags");return t.value}catch(L){throw o.value=((K=(W=L.response)==null?void 0:W.data)==null?void 0:K.message)||L.message||"Errore nel caricamento feature flags",console.error("[FeatureFlags] Fetch error:",L),Object.keys(t.value).length===0&&(console.warn("[FeatureFlags] No cached flags, using defaults"),t.value={oauth_enabled:!0,recruiting_module:!0,ceo_assistant:!0,certifications_module:!0,engagement_module:!0,personnel_module:!0,timesheet_module:!0,communications_module:!0}),L}finally{r.value=!1}},_=async(D={})=>{var G,J,W,K;r.value=!0,o.value=null;try{const L={page:D.page||1,per_page:D.perPage||50};D.search&&(L.search=D.search);const v=await P.get("/api/feature-flags/admin",{params:L});if((G=v.data)!=null&&G.success)return e.value=v.data.data.feature_flags||[],v.data.data;throw new Error(((J=v.data)==null?void 0:J.message)||"Errore nel caricamento feature flags admin")}catch(L){throw o.value=((K=(W=L.response)==null?void 0:W.data)==null?void 0:K.message)||L.message||"Errore nel caricamento feature flags admin",console.error("Error fetching admin feature flags:",L),L}finally{r.value=!1}},k=async(D,G)=>{var J,W,K,L;r.value=!0,o.value=null;try{const v=await P.put(`/api/feature-flags/${D}`,G);if((J=v.data)!=null&&J.success){const A=v.data.data.feature_flag;t.value[A.feature_key]=A.is_enabled;const M=e.value.findIndex(F=>F.id===D);return M!==-1&&(e.value[M]=A),console.log(`Feature flag ${A.feature_key} updated:`,A.is_enabled?"enabled":"disabled"),v.data.data}else throw new Error(((W=v.data)==null?void 0:W.message)||"Errore nell'aggiornamento feature flag")}catch(v){throw o.value=((L=(K=v.response)==null?void 0:K.data)==null?void 0:L.message)||v.message||"Errore nell'aggiornamento feature flag",console.error("Error updating feature flag:",v),v}finally{r.value=!1}},m=async D=>{var G,J,W,K;r.value=!0,o.value=null;try{const L=await P.post("/api/feature-flags",D);if((G=L.data)!=null&&G.success){const v=L.data.data.feature_flag;return t.value[v.feature_key]=v.is_enabled,e.value.push(v),console.log(`Feature flag ${v.feature_key} created`),L.data.data}else throw new Error(((J=L.data)==null?void 0:J.message)||"Errore nella creazione feature flag")}catch(L){throw o.value=((K=(W=L.response)==null?void 0:W.data)==null?void 0:K.message)||L.message||"Errore nella creazione feature flag",console.error("Error creating feature flag:",L),L}finally{r.value=!1}},c=async D=>{var G,J,W,K;r.value=!0,o.value=null;try{const L=await P.delete(`/api/feature-flags/${D}`);if((G=L.data)!=null&&G.success){const v=e.value.find(A=>A.id===D);return v&&(delete t.value[v.feature_key],e.value=e.value.filter(A=>A.id!==D)),console.log("Feature flag deleted"),L.data}else throw new Error(((J=L.data)==null?void 0:J.message)||"Errore nell'eliminazione feature flag")}catch(L){throw o.value=((K=(W=L.response)==null?void 0:W.data)==null?void 0:K.message)||L.message||"Errore nell'eliminazione feature flag",console.error("Error deleting feature flag:",L),L}finally{r.value=!1}},C=async D=>{var G,J;try{const W=await P.get(`/api/feature-flags/check/${D}`);if((G=W.data)!=null&&G.success){const K=W.data.data;return t.value[D]=K.enabled,K}else throw new Error(((J=W.data)==null?void 0:J.message)||"Errore nel controllo feature flag")}catch(W){return console.error(`Error checking feature flag ${D}:`,W),{feature_key:D,enabled:!0,exists:!1}}},Z=()=>{t.value={},e.value=[],n.value=null,o.value=null},$=D=>e.value,I=()=>[],B=async()=>{try{await x()}catch{console.warn("Failed to initialize feature flags, using defaults")}};let le=!1,ee=null;return{flags:t,flagsDetails:e,loading:r,error:o,lastFetch:n,isLoading:u,hasError:f,isCacheFresh:p,isFeatureEnabled:g,hasFeature:b,areAllFeaturesEnabled:l,areAnyFeaturesEnabled:y,fetchFeatureFlags:x,fetchAdminFeatureFlags:_,updateFeatureFlag:k,createFeatureFlag:m,deleteFeatureFlag:c,checkFeatureFlag:C,clearCache:Z,getFeaturesByCategory:$,getCategories:I,initializeFeatureFlags:B,ensureInitialized:async()=>{if(!le&&!ee){ee=B();try{await ee,le=!0}catch(D){console.error("[FeatureFlags] Initialization failed:",D),ee=null}}else ee&&await ee}}});function St(){const t=Ie(),e=w(()=>t.flags),r=w(()=>t.isLoading),o=w(()=>t.hasError),n=w(()=>t.error),s=$=>t.isFeatureEnabled($),u=$=>t.hasFeature($),f=$=>t.areAllFeaturesEnabled($),p=$=>t.areAnyFeaturesEnabled($),g=w(()=>({oauthEnabled:s("oauth_enabled"),googleAuth:s("google_auth"),microsoftAuth:s("microsoft_auth"),recruitingModule:s("recruiting_module"),ceoAssistant:s("ceo_assistant"),certificationsModule:s("certifications_module"),engagementModule:s("engagement_module"),communicationModule:s("communication_module"),aiResources:s("ai_resources"),projectAnalytics:s("project_analytics"),timesheetApproval:s("timesheet_approval"),expenseTracking:s("expense_tracking"),userManagement:s("user_management"),systemSettings:s("system_settings"),auditLogs:s("audit_logs"),darkMode:s("dark_mode"),notifications:s("notifications"),realTimeUpdates:s("real_time_updates"),betaFeatures:s("beta_features"),experimentalUI:s("experimental_ui"),advancedAnalytics:s("advanced_analytics")})),b=async($={})=>{await t.fetchFeatureFlags({forceRefresh:!0,...$})},l=async $=>await t.checkFeatureFlag($),y=w(()=>g.value.oauthEnabled&&(g.value.googleAuth||g.value.microsoftAuth)),x=$=>{const I={recruiting:g.value.recruitingModule,ceo:g.value.ceoAssistant,certifications:g.value.certificationsModule,engagement:g.value.engagementModule,communication:g.value.communicationModule};return I[$]!==void 0?I[$]:!0},_=w(()=>g.value.aiResources||g.value.ceoAssistant),k=w(()=>p(["project_analytics","advanced_analytics","ai_resources","experimental_ui"])),m=w(()=>Object.entries(g.value).filter(([$])=>["recruitingModule","ceoAssistant","certificationsModule","engagementModule","communicationModule"].includes($)).reduce(($,[I,B])=>({...$,[I]:B}),{})),c=w(()=>Object.entries(g.value).filter(([$])=>["oauthEnabled","googleAuth","microsoftAuth"].includes($)).reduce(($,[I,B])=>({...$,[I]:B}),{})),C=w(()=>Object.entries(g.value).filter(([$])=>["betaFeatures","experimentalUI","advancedAnalytics"].includes($)).reduce(($,[I,B])=>({...$,[I]:B}),{}));return{flags:e,isLoading:r,hasError:o,error:n,features:g,isFeatureEnabled:s,hasFeature:u,areAllFeaturesEnabled:f,areAnyFeaturesEnabled:p,canUseOAuth:y,canAccessModule:x,canUseAI:_,hasAdvancedFeatures:k,getCoreFeatures:m,getAuthFeatures:c,getExperimentalFeatures:C,refreshFlags:b,checkFeature:l,...{}}}function Ys(){const t=Ie(),e=w(()=>t.flagsDetails),r=w(()=>t.isLoading),o=w(()=>t.hasError),n=w(()=>t.error),s=async(m={})=>await t.fetchAdminFeatureFlags(m),u=async(m,c)=>await t.updateFeatureFlag(m,c),f=async m=>await t.createFeatureFlag(m),p=async m=>await t.deleteFeatureFlag(m),g=async m=>await u(m.id,{is_enabled:!m.is_enabled}),b=m=>t.getFeaturesByCategory(m),l=()=>t.getCategories(),y=m=>{if(console.log("searchFlags called with:",m,typeof m),!m)return e.value;try{const c=String(m||"").toLowerCase();return e.value.filter(C=>{if(!C)return!1;console.log("Processing flag:",C);const Z=String(C.feature_key||"").toLowerCase(),$=String(C.display_name||"").toLowerCase(),I=String(C.description||"").toLowerCase();return Z.includes(c)||$.includes(c)||I.includes(c)})}catch(c){return console.error("Error in searchFlags:",c,"with query:",m),e.value}},x=w(()=>e.value.filter(m=>m.is_enabled).length),_=w(()=>e.value.filter(m=>!m.is_enabled).length),k=w(()=>e.value.length);return{flagsDetails:e,isLoading:r,hasError:o,error:n,fetchAdminFlags:s,updateFlag:u,createFlag:f,deleteFlag:p,toggleFlag:g,getByCategory:b,getCategories:l,searchFlags:y,getEnabledCount:x,getDisabledCount:_,getTotalCount:k}}const Ft={add:"plus",create:"plus",new:"plus",edit:"pencil",modify:"pencil",delete:"trash",remove:"trash",save:"check",cancel:"x-mark",close:"x-mark",search:"magnifying-glass",filter:"funnel",sort:"bars-3",refresh:"arrow-path",reload:"arrow-path",download:"arrow-down-tray",upload:"arrow-up-tray",export:"arrow-up-tray",import:"arrow-down-tray",menu:"bars-3",hamburger:"bars-3",back:"arrow-left",forward:"arrow-right",next:"chevron-right",previous:"chevron-left",prev:"chevron-left",up:"chevron-up",down:"chevron-down",expand:"chevron-down",collapse:"chevron-up",external:"arrow-top-right-on-square","external-link":"arrow-top-right-on-square",success:"check-circle",error:"x-circle",warning:"exclamation-triangle",info:"information-circle",loading:"arrow-path",spinner:"arrow-path",pending:"clock",approved:"check-circle",rejected:"x-circle",draft:"document",user:"user",users:"users","user-circle":"user-circle",team:"user-group",profile:"user-circle",account:"user-circle",ban:"no-symbol",block:"no-symbol",disable:"no-symbol",client:"home-modern",clients:"home-modern",company:"home-modern",project:"folder",projects:"folder",task:"clipboard",tasks:"clipboard","clipboard-list":"clipboard",flag:"flag",target:"flag",goal:"flag",goals:"flag",objective:"flag",document:"document",file:"document",folder:"folder",calendar:"calendar",date:"calendar",time:"clock",timer:"clock",timesheet:"clipboard",report:"presentation-chart-bar",reports:"presentation-chart-bar",analytics:"chart-bar",dashboard:"squares-plus",settings:"wrench-screwdriver",config:"wrench-screwdriver","cog-6-tooth":"cog-6-tooth",admin:"shield-check","shield-check":"shield-check","book-open":"book-open","clipboard-document-check":"clipboard-document-check","calendar-days":"calendar-days",contact:"user",contract:"document-text",contracts:"document-text",sales:"currency-euro",email:"envelope",mail:"envelope",message:"chat-bubble-left",chat:"chat-bubble-left","chat-bubble-left-right":"chat-bubble-left","hr-assistant":"user-circle",assistant:"user-circle",notification:"bell",alert:"bell",phone:"phone",call:"phone",communications:"chat-bubble-left",communication:"chat-bubble-left",megaphone:"megaphone",announcement:"megaphone",campaign:"megaphone",campaigns:"megaphone",money:"banknotes",payment:"credit-card",invoice:"document-text",bill:"document-text",expense:"credit-card",budget:"calculator",price:"currency-euro",cost:"currency-euro",revenue:"arrow-trending-up",profit:"arrow-trending-up","trending-up":"arrow-trending-up","user-management":"users",newspaper:"newspaper","calendar-plus":"calendar","business-intelligence":"chart-bar",market:"globe-americas","market-intel":"globe-americas","case-study":"document-text",skills:"academic-cap","core-skills":"academic-cap",technical:"wrench-screwdriver","technical-offer":"wrench-screwdriver","advanced-reports":"presentation-chart-bar",funding:"banknotes",grants:"gift",opportunity:"light-bulb",application:"document-plus","lightning-bolt":"bolt","light-bulb":"light-bulb",reporting:"presentation-chart-bar",database:"circle-stack",server:"server",cloud:"cloud",api:"code-bracket",code:"code-bracket",bug:"bug-ant",security:"shield-check",lock:"lock-closed",unlock:"lock-open",key:"key",ai:"cpu-chip",agents:"cpu-chip","artificial-intelligence":"cpu-chip",automation:"cpu-chip",bot:"cpu-chip","machine-learning":"cpu-chip",brain:"cpu-chip",education:"academic-cap",certification:"academic-cap",certifications:"academic-cap",compliance:"academic-cap",training:"academic-cap",learning:"academic-cap",course:"academic-cap",degree:"academic-cap",qualification:"academic-cap",readiness:"clipboard-document-check",audit:"calendar-days",audits:"calendar-days",catalog:"book-open",standards:"book-open",renewal:"arrow-path",health:"heart",score:"chart-bar",organization:"building-office-2",office:"building-office-2",building:"building-office-2",department:"building-office-2",departments:"building-office-2",ceo:"building-office-2",executive:"building-office-2",management:"building-office-2",corporate:"building-office-2",headquarters:"building-office-2",design:"sparkles",creative:"sparkles",magic:"sparkles",inspiration:"sparkles",innovation:"sparkles",style:"sparkles",theme:"sparkles",image:"photo",photo:"photo",video:"video-camera",camera:"camera",microphone:"microphone",speaker:"speaker-wave",volume:"speaker-wave",play:"play",pause:"pause",stop:"stop",home:"home",star:"star",heart:"heart",bookmark:"bookmark",tag:"tag",label:"tag",link:"link",share:"share",copy:"document-duplicate",paste:"clipboard",cut:"scissors",print:"printer",view:"eye",hide:"eye-slash",visible:"eye",invisible:"eye-slash",grid:"squares-plus",list:"list-bullet",table:"table-cells",card:"rectangle-stack",sidebar:"bars-3",fullscreen:"rectangle-group",minimize:"minus",kanban:"view-columns",columns:"view-columns",board:"view-columns",proposal:"identification",badge:"identification","id-card":"identification",move:"hand-raised",drag:"hand-raised",resize:"hand-raised",rotate:"arrow-path",flip:"arrow-path","arrows-right-left":"arrows-right-left","arrows-h":"arrows-right-left","horizontal-arrows":"arrows-right-left",chart:"chart-bar","chart-line":"presentation-chart-line","chart-pie":"chart-pie","pie-chart":"chart-pie","analytics-pie":"chart-pie",scale:"chart-bar",balance:"chart-bar",weight:"chart-bar","squares-2x2":"squares-2x2","grid-2x2":"squares-2x2",components:"squares-2x2","components-base":"squares-2x2",squares2x2:"squares-2x2",briefcase:"briefcase",location:"map-pin",address:"map-pin",map:"map",pin:"map-pin",marker:"map-pin",governance:"shield-check",compliance:"shield-check",audit:"document-text","audit-trail":"document-text",policy:"book-open",policies:"book-open",risk:"exclamation-triangle","risk-assessment":"exclamation-triangle",event:"bell",events:"bell",framework:"squares-2x2",gdpr:"shield-check",iso27001:"shield-check",soc2:"shield-check",engagement:"trophy",gamification:"trophy",points:"star",reward:"gift",rewards:"gift",achievement:"trophy",achievements:"trophy",badge:"shield-check",badges:"shield-check",leaderboard:"chart-bar",ranking:"chart-bar",streak:"fire",challenge:"flag",challenges:"flag","team-challenge":"users","activity-feed":"clock",level:"arrow-trending-up",xp:"star",experience:"star",competition:"trophy",marketplace:"shopping-bag",redeem:"gift"};function Vt(t){const e=t.toLowerCase().replace(/_/g,"-");return Ft[e]||e}function Xs(t){const e={"Sviluppo Software":"code-bracket","Intelligenza Artificiale":"cpu-chip","Consulenza IT":"computer-desktop","Gestione Progetti Innovativi":"briefcase","Gestione Progetti":"briefcase","Supporto su Bandi e Finanziamenti":"banknotes",Finanziamenti:"banknotes",default:"wrench-screwdriver"};return e[t]||e.default}const xe=(t,e)=>{const r=t.__vccOpts||t;for(const[o,n]of e)r[o]=n;return r},Dt={__name:"HeroIcon",props:{name:{type:String,required:!0},variant:{type:String,default:"outline",validator:t=>["outline","solid","mini"].includes(t)},size:{type:[String,Number],default:"md",validator:t=>typeof t=="number"?!0:["xs","sm","md","lg","xl","2xl"].includes(t)},color:{type:String,default:"currentColor"},ariaLabel:{type:String,default:null},className:{type:String,default:""}},setup(t){const e=t,r={xs:"w-3 h-3",sm:"w-4 h-4",md:"w-5 h-5",lg:"w-6 h-6",xl:"w-8 h-8","2xl":"w-10 h-10"},o=g=>g==="squares-2x2"?"Squares2X2Icon":g.split("-").map(b=>b.charAt(0).toUpperCase()+b.slice(1)).join("")+"Icon",n=new Map,s=q(null),u=async(g,b)=>{const l=`${g}-${b}`;if(n.has(l))return n.get(l);try{let y;switch(b){case"solid":y=await d(()=>import("./index.js"),__vite__mapDeps([0,1]));break;case"mini":y=await d(()=>import("./index2.js"),__vite__mapDeps([2,1]));break;case"outline":default:y=await d(()=>import("./index3.js"),__vite__mapDeps([3,1,4]));break}const x=y[g];if(!x){console.warn(`HeroIcon: Icon "${g}" not found in ${b} variant`);const k=(await d(()=>import("./index3.js"),__vite__mapDeps([3,1,4]))).QuestionMarkCircleIcon;return n.set(l,k),k}return n.set(l,x),x}catch(y){return console.error(`Failed to load icon ${g}:`,y),(await d(()=>import("./index3.js"),__vite__mapDeps([3,1,4]))).QuestionMarkCircleIcon}};ht(async()=>{const g=Vt(e.name),b=o(g),l=await u(b,e.variant);s.value=l});const f=w(()=>{const g=[];return typeof e.size=="string"&&r[e.size]?g.push(r[e.size]):e.size,e.color&&e.color!=="currentColor"&&g.push(e.color),e.className&&g.push(e.className),g.join(" ")}),p=w(()=>typeof e.size=="number"?{width:`${e.size}px`,height:`${e.size}px`}:{});return(g,b)=>(i(),U(lt(s.value),gt({class:f.value,style:p.value,"aria-hidden":!t.ariaLabel,"aria-label":t.ariaLabel},g.$attrs),null,16,["class","style","aria-hidden","aria-label"]))}},V=xe(Dt,[["__scopeId","data-v-fa3f35e1"]]),Rt=["src","alt"],Lt={__name:"TenantLogo",props:{variant:{type:String,default:"primary",validator:t=>["primary","transparent"].includes(t)},size:{type:String,default:"md",validator:t=>["sm","md","lg","xl"].includes(t)},fallbackInitials:{type:String,default:""}},setup(t){const e=t,r=be(),o=q(!1),n=q(!1),s=w(()=>{var k;return((k=r.company)==null?void 0:k.name)||"DatPortal"}),u=w(()=>r.getLogoUrl(e.variant)),f=w(()=>s.value.split(" ").map(m=>m[0]).join("").toUpperCase().slice(0,2));w(()=>e.fallbackInitials||f.value);const p=w(()=>["flex items-center justify-center",{"w-6 h-6":e.size==="sm","w-8 h-8":e.size==="md","w-10 h-10":e.size==="lg","w-12 h-12":e.size==="xl"}]),g=w(()=>["object-contain max-w-full max-h-full",{"opacity-0 transition-opacity duration-200":!n.value,"opacity-100 transition-opacity duration-200":n.value}]),b=w(()=>["w-full h-full rounded flex items-center justify-center","bg-primary-600 text-white font-bold",{"text-xs":e.size==="sm","text-sm":e.size==="md","text-base":e.size==="lg","text-lg":e.size==="xl"}]),l=w(()=>["select-none"]),y=()=>{o.value=!0,n.value=!1},x=()=>{o.value=!1,n.value=!0};return te(u,()=>{o.value=!1,n.value=!1}),(_,k)=>(i(),h("div",{class:R(["tenant-logo",p.value])},[u.value&&!o.value?(i(),h("img",{key:0,src:u.value,alt:`${s.value} Logo`,class:R(g.value),onError:y,onLoad:x},null,42,Rt)):(i(),h("div",{key:1,class:R(b.value)},[a("span",{class:R(l.value)},S(t.fallbackInitials),3)],2))],2))}},je=xe(Lt,[["__scopeId","data-v-67dfd586"]]);function jt(){const t=me(),e=w(()=>x=>t.hasPermission(x)),r=w(()=>{var x;return((x=t.user)==null?void 0:x.role)||null}),o=w(()=>r.value==="admin"),n=w(()=>r.value==="manager"),s=w(()=>r.value==="employee"),u=w(()=>r.value==="sales"),f=w(()=>r.value==="human_resources"),p=w(()=>e.value("create_project")||e.value("edit_project")||e.value("delete_project")),g=w(()=>e.value("manage_users")||e.value("assign_roles")),b=w(()=>e.value("view_all_projects")),l=w(()=>e.value("view_personnel_data")||e.value("edit_personnel_data")),y=w(()=>e.value("approve_timesheets"));return{hasPermission:e,userRole:r,isAdmin:o,isManager:n,isEmployee:s,isSales:u,isHR:f,canManageProjects:p,canManageUsers:g,canViewAllProjects:b,canManagePersonnel:l,canApproveTimesheets:y}}function Te(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5"})])}function Ot(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5M10 11.25h4M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z"})])}function zt(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 18 9 11.25l4.306 4.306a11.95 11.95 0 0 1 5.814-5.518l2.74-1.22m0 0-5.94-2.281m5.94 2.28-2.28 5.941"})])}function Nt(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M7.5 21 3 16.5m0 0L7.5 12M3 16.5h13.5m0-13.5L21 7.5m0 0L16.5 12M21 7.5H7.5"})])}function Bt(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z"})])}function Ht(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9.75 3.104v5.714a2.25 2.25 0 0 1-.659 1.591L5 14.5M9.75 3.104c-.251.023-.501.05-.75.082m.75-.082a24.301 24.301 0 0 1 4.5 0m0 0v5.714c0 .597.237 1.17.659 1.591L19.8 15.3M14.25 3.104c.251.023.501.05.75.082M19.8 15.3l-1.57.393A9.065 9.065 0 0 1 12 15a9.065 9.065 0 0 0-6.23-.693L5 14.5m14.8.8 1.402 1.402c1.232 1.232.65 3.318-1.067 3.611A48.309 48.309 0 0 1 12 21c-2.773 0-5.491-.235-8.135-.687-1.718-.293-2.3-2.379-1.067-3.61L5 14.5"})])}function Ut(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"})])}function Ue(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"})])}function Zt(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z"})])}function Gt(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M20.25 14.15v4.25c0 1.094-.787 2.036-1.872 2.18-2.087.277-4.216.42-6.378.42s-4.291-.143-6.378-.42c-1.085-.144-1.872-1.086-1.872-2.18v-4.25m16.5 0a2.18 2.18 0 0 0 .75-1.661V8.706c0-1.081-.768-2.015-1.837-2.175a48.114 48.114 0 0 0-3.413-.387m4.5 8.006c-.194.165-.42.295-.673.38A23.978 23.978 0 0 1 12 15.75c-2.648 0-5.195-.429-7.577-1.22a2.016 2.016 0 0 1-.673-.38m0 0A2.18 2.18 0 0 1 3 12.489V8.706c0-1.081.768-2.015 1.837-2.175a48.111 48.111 0 0 1 3.413-.387m7.5 0V5.25A2.25 2.25 0 0 0 13.5 3h-3a2.25 2.25 0 0 0-2.25 2.25v.894m7.5 0a48.667 48.667 0 0 0-7.5 0M12 12.75h.008v.008H12v-.008Z"})])}function Ze(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Z"})])}function Wt(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 21h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 6.75h1.5m-1.5 3h1.5m-1.5 3h1.5m3-6H15m-1.5 3H15m-1.5 3H15M9 21v-3.375c0-.621.504-1.125 1.125-1.125h3.75c.621 0 1.125.504 1.125 1.125V21"})])}function Ge(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"})])}function We(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"})])}function Qe(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M10.5 6a7.5 7.5 0 1 0 7.5 7.5h-7.5V6Z"}),a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M13.5 10.5H21A7.5 7.5 0 0 0 13.5 3v7.5Z"})])}function Qt(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"})])}function Kt(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 12.76c0 1.6 1.123 2.994 2.707 3.227 1.087.16 2.185.283 3.293.369V21l4.076-4.076a1.526 1.526 0 0 1 1.037-.443 48.282 48.282 0 0 0 5.68-.494c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z"})])}function Jt(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])}function Ke(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M11.35 3.836c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m8.9-4.414c.376.023.75.05 1.124.08 1.131.094 1.976 1.057 1.976 2.192V16.5A2.25 2.25 0 0 1 18 18.75h-2.25m-7.5-10.5H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V18.75m-7.5-10.5h6.375c.621 0 1.125.504 1.125 1.125v9.375m-8.25-3 1.5 1.5 3-3.75"})])}function Yt(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"})])}function Xt(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.666 3.888A2.25 2.25 0 0 0 13.5 2.25h-3c-1.03 0-1.9.693-2.166 1.638m7.332 0c.055.194.084.4.084.612v0a.75.75 0 0 1-.75.75H9a.75.75 0 0 1-.75-.75v0c0-.212.03-.418.084-.612m7.332 0c.646.049 1.288.11 1.927.184 1.1.128 1.907 1.077 1.907 2.185V19.5a2.25 2.25 0 0 1-2.25 2.25H6.75A2.25 2.25 0 0 1 4.5 19.5V6.257c0-1.108.806-2.057 1.907-2.185a48.208 48.208 0 0 1 1.927-.184"})])}function Se(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])}function er(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"}),a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"})])}function tr(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"})])}function Je(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z"})])}function rr(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.25 7.756a4.5 4.5 0 1 0 0 8.488M7.5 10.5h5.25m-5.25 3h5.25M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])}function ar(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25M9 16.5v.75m3-3v3M15 12v5.25m-4.5-15H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"})])}function Ye(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"})])}function or(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"})])}function nr(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"})])}function sr(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 3v1.5M3 21v-6m0 0 2.77-.693a9 9 0 0 1 6.208.682l.108.054a9 9 0 0 0 6.086.71l3.114-.732a48.524 48.524 0 0 1-.005-10.499l-3.11.732a9 9 0 0 1-6.085-.711l-.108-.054a9 9 0 0 0-6.208-.682L3 4.5M3 15V4.5"})])}function Xe(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 12.75V12A2.25 2.25 0 0 1 4.5 9.75h15A2.25 2.25 0 0 1 21.75 12v.75m-8.69-6.44-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25h-5.379a1.5 1.5 0 0 1-1.06-.44Z"})])}function ir(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M21 11.25v8.25a1.5 1.5 0 0 1-1.5 1.5H5.25a1.5 1.5 0 0 1-1.5-1.5v-8.25M12 4.875A2.625 2.625 0 1 0 9.375 7.5H12m0-2.625V7.5m0-2.625A2.625 2.625 0 1 1 14.625 7.5H12m0 0V21m-8.625-9.75h18c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125h-18c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z"})])}function lr(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418"})])}function cr(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 9h3.75M15 12h3.75M15 15h3.75M4.5 19.5h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Zm6-10.125a1.875 1.875 0 1 1-3.75 0 1.875 1.875 0 0 1 3.75 0Zm1.294 6.336a6.721 6.721 0 0 1-3.17.789 6.721 6.721 0 0 1-3.168-.789 3.376 3.376 0 0 1 6.338 0Z"})])}function ur(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"})])}function dr(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M10.34 15.84c-.688-.06-1.386-.09-2.09-.09H7.5a4.5 4.5 0 1 1 0-9h.75c.704 0 1.402-.03 2.09-.09m0 9.18c.253.962.584 1.892.985 2.783.247.55.06 1.21-.463 1.511l-.657.38c-.551.318-1.26.117-1.527-.461a20.845 20.845 0 0 1-1.44-4.282m3.102.069a18.03 18.03 0 0 1-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18a23.848 23.848 0 0 1 8.835 2.535M10.34 6.66a23.847 23.847 0 0 0 8.835-2.535m0 0A23.74 23.74 0 0 0 18.795 3m.38 1.125a23.91 23.91 0 0 1 1.014 5.395m-1.014 8.855c-.118.38-.245.754-.38 1.125m.38-1.125a23.91 23.91 0 0 0 1.014-5.395m0-3.46c.495.413.811 1.035.811 1.73 0 .695-.316 1.317-.811 1.73m0-3.46a24.347 24.347 0 0 1 0 3.46"})])}function mr(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 7.5h1.5m-1.5 3h1.5m-7.5 3h7.5m-7.5 3h7.5m3-9h3.375c.621 0 1.125.504 1.125 1.125V18a2.25 2.25 0 0 1-2.25 2.25M16.5 7.5V18a2.25 2.25 0 0 0 2.25 2.25M16.5 7.5V4.875c0-.621-.504-1.125-1.125-1.125H4.125C3.504 3.75 3 4.254 3 4.875V18a2.25 2.25 0 0 0 2.25 2.25h13.5M6 7.5h3v3H6v-3Z"})])}function pr(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 3v11.25A2.25 2.25 0 0 0 6 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0 1 18 16.5h-2.25m-7.5 0h7.5m-7.5 0-1 3m8.5-3 1 3m0 0 .5 1.5m-.5-1.5h-9.5m0 0-.5 1.5m.75-9 3-3 2.148 2.148A12.061 12.061 0 0 1 16.5 7.605"})])}function hr(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 5.25h.008v.008H12v-.008Z"})])}function gr(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 7.125C2.25 6.504 2.754 6 3.375 6h6c.621 0 1.125.504 1.125 1.125v3.75c0 .621-.504 1.125-1.125 1.125h-6a1.125 1.125 0 0 1-1.125-1.125v-3.75ZM14.25 8.625c0-.621.504-1.125 1.125-1.125h5.25c.621 0 1.125.504 1.125 1.125v8.25c0 .621-.504 1.125-1.125 1.125h-5.25a1.125 1.125 0 0 1-1.125-1.125v-8.25ZM3.75 16.125c0-.621.504-1.125 1.125-1.125h5.25c.621 0 1.125.504 1.125 1.125v2.25c0 .621-.504 1.125-1.125 1.125h-5.25a1.125 1.125 0 0 1-1.125-1.125v-2.25Z"})])}function et(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"})])}function fr(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"})])}function vr(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 6A2.25 2.25 0 0 1 6 3.75h2.25A2.25 2.25 0 0 1 10.5 6v2.25a2.25 2.25 0 0 1-2.25 2.25H6a2.25 2.25 0 0 1-2.25-2.25V6ZM3.75 15.75A2.25 2.25 0 0 1 6 13.5h2.25a2.25 2.25 0 0 1 2.25 2.25V18a2.25 2.25 0 0 1-2.25 2.25H6A2.25 2.25 0 0 1 3.75 18v-2.25ZM13.5 6a2.25 2.25 0 0 1 2.25-2.25H18A2.25 2.25 0 0 1 20.25 6v2.25A2.25 2.25 0 0 1 18 10.5h-2.25a2.25 2.25 0 0 1-2.25-2.25V6ZM13.5 15.75a2.25 2.25 0 0 1 2.25-2.25H18a2.25 2.25 0 0 1 2.25 2.25V18A2.25 2.25 0 0 1 18 20.25h-2.25A2.25 2.25 0 0 1 13.5 18v-2.25Z"})])}function tt(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M13.5 16.875h3.375m0 0h3.375m-3.375 0V13.5m0 3.375v3.375M6 10.5h2.25a2.25 2.25 0 0 0 2.25-2.25V6a2.25 2.25 0 0 0-2.25-2.25H6A2.25 2.25 0 0 0 3.75 6v2.25A2.25 2.25 0 0 0 6 10.5Zm0 9.75h2.25A2.25 2.25 0 0 0 10.5 18v-2.25a2.25 2.25 0 0 0-2.25-2.25H6a2.25 2.25 0 0 0-2.25 2.25V18A2.25 2.25 0 0 0 6 20.25Zm9.75-9.75H18a2.25 2.25 0 0 0 2.25-2.25V6A2.25 2.25 0 0 0 18 3.75h-2.25A2.25 2.25 0 0 0 13.5 6v2.25a2.25 2.25 0 0 0 2.25 2.25Z"})])}function _r(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"})])}function wr(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.375 19.5h17.25m-17.25 0a1.125 1.125 0 0 1-1.125-1.125M3.375 19.5h7.5c.621 0 1.125-.504 1.125-1.125m-9.75 0V5.625m0 12.75v-1.5c0-.621.504-1.125 1.125-1.125m18.375 2.625V5.625m0 12.75c0 .621-.504 1.125-1.125 1.125m1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125m0 3.75h-7.5A1.125 1.125 0 0 1 12 18.375m9.75-12.75c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125m19.5 0v1.5c0 .621-.504 1.125-1.125 1.125M2.25 5.625v1.5c0 .621.504 1.125 1.125 1.125m0 0h17.25m-17.25 0h7.5c.621 0 1.125.504 1.125 1.125M3.375 8.25c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125m17.25-3.75h-7.5c-.621 0-1.125.504-1.125 1.125m8.625-1.125c.621 0 1.125.504 1.125 1.125v1.5c0 .621-.504 1.125-1.125 1.125m-17.25 0h7.5m-7.5 0c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125M12 10.875v-1.5m0 1.5c0 .621-.504 1.125-1.125 1.125M12 10.875c0 .621.504 1.125 1.125 1.125m-2.25 0c.621 0 1.125.504 1.125 1.125M13.125 12h7.5m-7.5 0c-.621 0-1.125.504-1.125 1.125M20.625 12c.621 0 1.125.504 1.125 1.125v1.5c0 .621-.504 1.125-1.125 1.125m-17.25 0h7.5M12 14.625v-1.5m0 1.5c0 .621-.504 1.125-1.125 1.125M12 14.625c0 .621.504 1.125 1.125 1.125m-2.25 0c.621 0 1.125.504 1.125 1.125m0 1.5v-1.5m0 0c0-.621.504-1.125 1.125-1.125m0 0h7.5"})])}function yr(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.5 18.75h-9m9 0a3 3 0 0 1 3 3h-15a3 3 0 0 1 3-3m9 0v-3.375c0-.621-.503-1.125-1.125-1.125h-.871M7.5 18.75v-3.375c0-.621.504-1.125 1.125-1.125h.872m5.007 0H9.497m5.007 0a7.454 7.454 0 0 1-.982-3.172M9.497 14.25a7.454 7.454 0 0 0 .981-3.172M5.25 4.236c-.982.143-1.954.317-2.916.52A6.003 6.003 0 0 0 7.73 9.728M5.25 4.236V4.5c0 2.108.966 3.99 2.48 5.228M5.25 4.236V2.721C7.456 2.41 9.71 2.25 12 2.25c2.291 0 4.545.16 6.75.47v1.516M7.73 9.728a6.726 6.726 0 0 0 2.748 1.35m8.272-6.842V4.5c0 2.108-.966 3.99-2.48 5.228m2.48-5.492a46.32 46.32 0 0 1 2.916.52 6.003 6.003 0 0 1-5.395 4.972m0 0a6.726 6.726 0 0 1-2.749 1.35m0 0a6.772 6.772 0 0 1-3.044 0"})])}function rt(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"})])}function Fe(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"})])}function br(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M18 7.5v3m0 0v3m0-3h3m-3 0h-3m-2.25-4.125a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0ZM3 19.235v-.11a6.375 6.375 0 0 1 12.75 0v.109A12.318 12.318 0 0 1 9.374 21c-2.331 0-4.512-.645-6.374-1.766Z"})])}function xr(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"})])}function kr(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"})])}function Er(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 4.5v15m6-15v15m-10.875 0h15.75c.621 0 1.125-.504 1.125-1.125V5.625c0-.621-.504-1.125-1.125-1.125H4.125C3.504 4.5 3 5.004 3 5.625v12.75c0 .621.504 1.125 1.125 1.125Z"})])}function Ar(t,e){return i(),h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z"})])}const Ce={__name:"SidebarIcon",props:{icon:{type:String,required:!0},className:{type:String,default:"w-6 h-6"}},setup(t){const e=t,r={dashboard:tt,projects:Xe,users:kr,timesheet:Xt,sales:zt,"business-intelligence":Je,funding:rr,admin:et,settings:tr,communications:Qt,certifications:Te,"chat-bubble-left":Kt,envelope:or,"calendar-days":Ge,newspaper:mr,clients:Wt,contact:rt,proposal:Ye,contract:Ke,invoice:Bt,reports:ar,analytics:We,clock:Se,"calendar-plus":Ge,"chart-bar":We,archive:Ot,"user-group":Fe,"chart-line":pr,directory:Ue,orgchart:Qe,skills:Te,allocation:gr,"user-profile":xr,team:Fe,departments:Ze,"case-study":Zt,technical:Ar,market:lr,robot:Ht,search:ur,reporting:Yt,history:Se,"check-circle":Jt,"users-check":Fe,"clock-play":Se,"user-management":br,products:Xe,"academic-cap":Te,"building-office-2":Ze,"cpu-chip":Je,sparkles:fr,"squares-plus":tt,"table-cells":wr,"document-text":Ye,"view-columns":Er,identification:cr,"chart-pie":Qe,"squares-2x2":vr,"arrows-right-left":Nt,"shield-check":et,"book-open":Ue,"clipboard-document-check":Ke,"user-circle":rt,"cog-6-tooth":er,bell:Ut,"exclamation-triangle":nr,star:_r,megaphone:dr,gift:ir,trophy:yr,briefcase:Gt,flag:sr},o=w(()=>r[e.icon]||hr);return(n,s)=>(i(),U(lt(o.value),{class:R(t.className)},null,8,["class"]))}},Pr={key:0,class:"truncate"},Cr={key:0,class:"truncate"},qr={__name:"SidebarNavItem",props:{item:{type:Object,required:!0},isCollapsed:{type:Boolean,default:!1}},emits:["click"],setup(t){const e=w(()=>["text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400"]);return(r,o)=>{const n=re("router-link");return i(),h("div",null,[t.item.path!=="#"?(i(),U(n,{key:0,to:t.item.path,class:R(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",[e.value,{"justify-center":t.isCollapsed}]]),"active-class":"text-primary-600 bg-primary-50 border-r-2 border-primary-600",onClick:o[0]||(o[0]=s=>r.$emit("click"))},{default:z(()=>[E(Ce,{icon:t.item.icon,class:R(["flex-shrink-0 h-6 w-6",{"mr-0":t.isCollapsed,"mr-3":!t.isCollapsed}])},null,8,["icon","class"]),t.isCollapsed?T("",!0):(i(),h("span",Pr,S(t.item.name),1))]),_:1},8,["to","class"])):(i(),h("div",{key:1,class:R(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150 cursor-not-allowed opacity-75",["text-gray-400 hover:text-gray-500",{"justify-center":t.isCollapsed}]])},[E(Ce,{icon:t.item.icon,class:R(["flex-shrink-0 h-6 w-6",{"mr-0":t.isCollapsed,"mr-3":!t.isCollapsed}])},null,8,["icon","class"]),t.isCollapsed?T("",!0):(i(),h("span",Cr,S(t.item.name),1))],2))])}}},$r={key:0,class:"flex-1 text-left truncate"},Ir={key:0,class:"ml-6 space-y-1 mt-1"},Mr={class:"truncate"},X={__name:"SidebarNavItemCollapsible",props:{item:{type:Object,required:!0},isCollapsed:{type:Boolean,default:!1}},emits:["click"],setup(t){const e=t,r=qe(),o=me(),n=q(!1),s=w(()=>["text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400",{"text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900":u.value}]),u=w(()=>e.item.children?e.item.children.some(b=>b.path!=="#"&&r.path.startsWith(b.path)):!1),f=w(()=>e.item.children?e.item.children.filter(b=>{var l;return b.admin?((l=o.user)==null?void 0:l.role)==="admin":!0}):[]);u.value&&(n.value=!0);function p(){e.isCollapsed||(n.value=!n.value)}function g(b){if(b.path==="#")return!1}return(b,l)=>{const y=re("router-link");return i(),h("div",null,[a("button",{onClick:p,class:R(["group w-full flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",[s.value,{"justify-center":t.isCollapsed}]])},[E(Ce,{icon:t.item.icon,class:R(["flex-shrink-0 h-6 w-6",{"mr-0":t.isCollapsed,"mr-3":!t.isCollapsed}])},null,8,["icon","class"]),t.isCollapsed?T("",!0):(i(),h("span",$r,S(t.item.name),1)),t.isCollapsed?T("",!0):(i(),U(V,{key:1,name:"chevron-right",size:"sm",class:R({"rotate-90":n.value,"ml-2 transition-transform duration-150":!0})},null,8,["class"]))],2),n.value&&!t.isCollapsed?(i(),h("div",Ir,[(i(!0),h(ae,null,ie(f.value,x=>(i(),U(y,{key:x.name,to:x.path,class:R(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",x.path==="#"?"text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-400 cursor-not-allowed opacity-75":"text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400"]),"active-class":"text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900",onClick:_=>g(x)},{default:z(()=>[x.icon?(i(),U(Ce,{key:0,icon:x.icon,class:"flex-shrink-0 h-4 w-4 mr-2"},null,8,["icon"])):T("",!0),a("span",Mr,S(x.name),1)]),_:2},1032,["to","class","onClick"]))),128))])):T("",!0)])}}},Tr={class:"mt-5 flex-grow flex flex-col overflow-hidden"},Sr={class:"flex-1 px-2 pb-4 space-y-1 overflow-y-auto sidebar-scroll"},Fr={__name:"SidebarNavigation",props:{isCollapsed:{type:Boolean,default:!1}},emits:["item-click"],setup(t){const{hasPermission:e}=jt(),{isFeatureEnabled:r}=St(),o=w(()=>r("dashboard_module")&&e.value("view_dashboard")),n=w(()=>r("personnel_module")&&e.value("view_personnel")),s=w(()=>r("timesheet_module")&&(e.value("manage_timesheets")||e.value("view_all_projects"))),u=w(()=>r("sales_module")&&e.value("view_all_projects")),f=w(()=>r("business_intelligence_module")&&(e.value("view_products")||e.value("view_all_projects"))),p=w(()=>r("communications_module")&&e.value("view_communications")),g=w(()=>r("certifications_module")&&e.value("view_compliance")),b=w(()=>r("ceo_module")&&e.value("view_ceo")),l=w(()=>r("governance_module")&&e.value("admin_access")),y=w(()=>r("engagement_module")&&e.value("view_engagement")),x=w(()=>r("recruiting_module")&&e.value("view_personnel_data")),_=w(()=>r("funding_module")&&(e.value("view_funding")||e.value("view_reports"))),k=w(()=>r("help_module")&&e.value("view_help")),m=w(()=>e.value("admin_access")),c=w(()=>r("security_dashboard")&&e.value("admin_access")),C=w(()=>{const $=[{name:"Configurazioni",path:"/app/admin/settings",icon:"settings"},{name:"Gestione Utenti",path:"/app/admin/users",icon:"user-management"},{name:"Feature Flags",path:"/app/admin/feature-flags",icon:"flag"},{name:"Template KPI",path:"/app/admin/kpi-templates",icon:"reports"},{name:"Self-Healing System",path:"/app/admin/self-healing",icon:"cpu-chip"}];return c.value&&$.push({name:"Sicurezza",path:"/app/admin/security",icon:"shield-check"}),{name:"Amministrazione",icon:"settings",children:$}}),Z=w(()=>e.value("view_dashboard"));return($,I)=>(i(),h("div",Tr,[a("nav",Sr,[o.value?(i(),U(qr,{key:0,item:{name:"Dashboard",path:"/app/dashboard",icon:"dashboard"},"is-collapsed":t.isCollapsed,onClick:I[0]||(I[0]=B=>$.$emit("item-click"))},null,8,["is-collapsed"])):T("",!0),n.value?(i(),U(X,{key:1,item:{name:"Personale",icon:"users",children:[{name:"Gestione",path:"/app/personnel/admin",icon:"admin",admin:!0},{name:"Inquadramenti",path:"/app/personnel/job-levels",icon:"user-group"},{name:"Performance",path:"/app/personnel/performance",icon:"chart-line"}]},"is-collapsed":t.isCollapsed,onClick:I[1]||(I[1]=B=>$.$emit("item-click"))},null,8,["is-collapsed"])):T("",!0),s.value?(i(),U(X,{key:2,item:{name:"Attività",icon:"timesheet",children:[{name:"Dashboard",path:"/app/timesheet/dashboard",icon:"dashboard"},{name:"Le Mie Ore",path:"/app/timesheet/entry",icon:"clock"},{name:"Progetti",path:"/app/projects",icon:"projects"},{name:"Reportistica",path:"/app/timesheet/analytics",icon:"chart-line",admin:!0},{name:"Richieste",path:"/app/timesheet/requests",icon:"calendar-plus"}]},"is-collapsed":t.isCollapsed,onClick:I[2]||(I[2]=B=>$.$emit("item-click"))},null,8,["is-collapsed"])):T("",!0),_.value?(i(),U(X,{key:3,item:{name:"Bandi",icon:"funding",children:[{name:"Dashboard",path:"/app/funding/dashboard",icon:"dashboard"},{name:"Ricerca",path:"/app/funding/search",icon:"search"},{name:"Rendicontazione",path:"/app/funding/reporting",icon:"reporting"}]},"is-collapsed":t.isCollapsed,onClick:I[3]||(I[3]=B=>$.$emit("item-click"))},null,8,["is-collapsed"])):T("",!0),f.value?(i(),U(X,{key:4,item:{name:"Business Intelligence",icon:"business-intelligence",children:[{name:"Dashboard",path:"/app/business-intelligence/dashboard",icon:"dashboard"},{name:"Case Studies",path:"/app/business-intelligence/case-studies",icon:"case-study"},{name:"Competenze Core",path:"/app/business-intelligence/core-skills",icon:"skills"},{name:"Market Intelligence",path:"/app/business-intelligence/market-intel",icon:"market"},{name:"Offerta Tecnica",path:"/app/business-intelligence/technical-offer",icon:"technical"},{name:"Reportistica",path:"/app/business-intelligence/advanced-reports",icon:"chart-bar"}]},"is-collapsed":t.isCollapsed,onClick:I[4]||(I[4]=B=>$.$emit("item-click"))},null,8,["is-collapsed"])):T("",!0),g.value?(i(),U(X,{key:5,item:{name:"Certificazioni",icon:"certifications",children:[{name:"Dashboard",path:"/app/certifications/dashboard",icon:"dashboard"},{name:"Gestione",path:"/app/certifications/list",icon:"shield-check"},{name:"Catalogo Standard",path:"/app/certifications/catalog",icon:"book-open"},{name:"Valuta Readiness",path:"/app/certifications/readiness",icon:"clipboard-document-check"}]},"is-collapsed":t.isCollapsed,onClick:I[5]||(I[5]=B=>$.$emit("item-click"))},null,8,["is-collapsed"])):T("",!0),p.value?(i(),U(X,{key:6,item:{name:"Comunicazioni",icon:"communications",children:[{name:"Dashboard",path:"/app/communications/dashboard",icon:"dashboard"},{name:"Forum",path:"/app/communications/forum",icon:"chat-bubble-left"},{name:"Sondaggi",path:"/app/communications/polls",icon:"chart-bar"},{name:"Messaggi",path:"/app/communications/messages",icon:"envelope"},{name:"Eventi",path:"/app/communications/events",icon:"calendar-days"},{name:"News",path:"/app/communications/news",icon:"newspaper"},{name:"Assistente HR",path:"/app/communications/hr-assistant",icon:"cpu-chip"},{name:"Knowledge Base HR",path:"/app/communications/hr-knowledge-base",icon:"academic-cap",admin:!0}]},"is-collapsed":t.isCollapsed,onClick:I[6]||(I[6]=B=>$.$emit("item-click"))},null,8,["is-collapsed"])):T("",!0),y.value?(i(),U(X,{key:7,item:{name:"Engagement",icon:"star",children:[{name:"Dashboard",path:"/app/engagement/dashboard",icon:"chart-bar"},{name:"Campagne",path:"/app/engagement/campaigns",icon:"megaphone"},{name:"Catalogo Premi",path:"/app/engagement/rewards",icon:"gift"},{name:"Classifica",path:"/app/engagement/leaderboard",icon:"trophy"},{name:"I Miei Punti",path:"/app/engagement/points",icon:"star"},{name:"Amministrazione",path:"/app/engagement/admin",icon:"cog-6-tooth",admin:!0}]},"is-collapsed":t.isCollapsed,onClick:I[7]||(I[7]=B=>$.$emit("item-click"))},null,8,["is-collapsed"])):T("",!0),l.value?(i(),U(X,{key:8,item:{name:"Governance",icon:"shield-check",children:[{name:"Compliance Dashboard",path:"/app/governance/compliance",icon:"chart-bar"},{name:"Audit Trail",path:"/app/governance/audit",icon:"document-text"},{name:"Policy Center",path:"/app/governance/policies",icon:"book-open"},{name:"Risk Assessment",path:"/app/governance/risk",icon:"exclamation-triangle"},{name:"Events",path:"/app/governance/events",icon:"bell"}]},"is-collapsed":t.isCollapsed,onClick:I[8]||(I[8]=B=>$.$emit("item-click"))},null,8,["is-collapsed"])):T("",!0),b.value?(i(),U(X,{key:9,item:{name:"Human CEO",icon:"user-circle",children:[{name:"Strategic Dashboard",path:"/app/ceo/dashboard",icon:"chart-bar"},{name:"AI Assistant",path:"/app/ceo/assistant",icon:"cpu-chip"},{name:"Insights & Reports",path:"/app/ceo/insights",icon:"document-text"},{name:"Research Config",path:"/app/ceo/config",icon:"cog-6-tooth"}]},"is-collapsed":t.isCollapsed,onClick:I[9]||(I[9]=B=>$.$emit("item-click"))},null,8,["is-collapsed"])):T("",!0),x.value?(i(),U(X,{key:10,item:{name:"Recruiting",icon:"user-group",children:[{name:"Dashboard",path:"/app/recruiting/dashboard",icon:"chart-bar"},{name:"Posizioni",path:"/app/recruiting/job-postings",icon:"briefcase"},{name:"Candidati",path:"/app/recruiting/candidates",icon:"users"},{name:"Candidature",path:"/app/recruiting/applications",icon:"document-text"},{name:"Pipeline",path:"/app/recruiting/pipeline",icon:"view-columns"},{name:"Colloqui",path:"/app/recruiting/interviews",icon:"calendar-days"}]},"is-collapsed":t.isCollapsed,onClick:I[10]||(I[10]=B=>$.$emit("item-click"))},null,8,["is-collapsed"])):T("",!0),u.value?(i(),U(X,{key:11,item:{name:"Sales",icon:"sales",children:[{name:"Dashboard",path:"/app/crm/dashboard",icon:"dashboard"},{name:"Clienti",path:"/app/crm/clients",icon:"clients"},{name:"Contatti",path:"/app/crm/contacts",icon:"contact"},{name:"Contratti",path:"/app/crm/contracts",icon:"contract"},{name:"Pre-Fatture",path:"/app/invoicing/pre-invoices",icon:"invoice"},{name:"Proposte",path:"/app/crm/proposals",icon:"proposal"}]},"is-collapsed":t.isCollapsed,onClick:I[11]||(I[11]=B=>$.$emit("item-click"))},null,8,["is-collapsed"])):T("",!0),k.value?(i(),U(X,{key:12,item:{name:"Supporto",icon:"academic-cap",children:[{name:"Help Center",path:"/app/help/dashboard",icon:"question-mark-circle"},{name:"Guide e Tutorial",path:"/app/help/content",icon:"book-open"},{name:"FAQ",path:"/app/help/faq",icon:"chat-bubble-left-ellipsis"},{name:"Contatta Supporto",path:"/app/help/contact",icon:"envelope"}]},"is-collapsed":t.isCollapsed,onClick:I[12]||(I[12]=B=>$.$emit("item-click"))},null,8,["is-collapsed"])):T("",!0),m.value?(i(),U(X,{key:13,item:C.value,"is-collapsed":t.isCollapsed,onClick:I[13]||(I[13]=B=>$.$emit("item-click"))},null,8,["item","is-collapsed"])):T("",!0),Z.value?(i(),U(X,{key:14,item:{name:"Design System",icon:"sparkles",children:[{name:"Dashboard Template",path:"/app/examples/dashboard",icon:"chart-pie"},{name:"Timesheet Grid",path:"/app/examples/timesheet-grid",icon:"table-cells"},{name:"Componenti Base",path:"/app/examples/components",icon:"squares-2x2"},{name:"FormBuilder",path:"/app/examples/form-builder",icon:"document-text"},{name:"ViewModeToggle",path:"/app/examples/view-mode-toggle",icon:"squares-plus"},{name:"KanbanView",path:"/app/examples/kanban",icon:"view-columns"},{name:"ProposalCard",path:"/app/examples/proposal-card",icon:"identification"},{name:"WizardContainer",path:"/app/examples/wizard-container",icon:"arrows-right-left"}]},"is-collapsed":t.isCollapsed,onClick:I[14]||(I[14]=B=>$.$emit("item-click"))},null,8,["is-collapsed"])):T("",!0)])]))}},at=xe(Fr,[["__scopeId","data-v-1b414b5f"]]),Vr={class:"flex-shrink-0 border-t border-gray-200 dark:border-gray-700 p-4"},Dr={class:"flex-shrink-0"},Rr={class:"h-9 w-9 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center overflow-hidden"},Lr=["src"],jr={key:0,class:"ml-3 flex-1 min-w-0"},Or={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},zr={class:"text-xs text-gray-500 dark:text-gray-400 truncate"},Nr={class:"py-1"},Br={key:0,class:"mt-3 text-xs text-gray-400 text-center"},ot={__name:"SidebarFooter",props:{isCollapsed:{type:Boolean,default:!1}},setup(t){const e=fe(),r=me(),o=q(!1),n=q(!1),s=w(()=>{const l=r.user;return l?l.first_name&&l.last_name?`${l.first_name} ${l.last_name}`:l.username||l.email||"Utente":"Utente"}),u=w(()=>{const l=r.user;return n.value?null:l!=null&&l.avatar_url&&l.avatar_url!=="/img/default-avatar.png"?l.avatar_url:null}),f=w(()=>r.user?{admin:"Amministratore",manager:"Manager",employee:"Dipendente",client:"Cliente"}[r.user.role]||r.user.role:""),p=w(()=>"1.0.0");function g(){n.value=!0}async function b(){o.value=!1,await r.logout(),e.push("/auth/login")}return(l,y)=>{const x=re("router-link");return i(),h("div",Vr,[a("div",{class:R(["flex items-center",{"justify-center":t.isCollapsed}])},[a("div",Dr,[a("div",Rr,[u.value&&u.value!=="/img/default-avatar.png"?(i(),h("img",{key:0,src:u.value,class:"h-full w-full rounded-full object-cover",alt:"User avatar",onError:g},null,40,Lr)):(i(),U(V,{key:1,name:"user-circle",size:"lg",class:"text-gray-400 dark:text-gray-500"}))])]),t.isCollapsed?T("",!0):(i(),h("div",jr,[a("p",Or,S(s.value),1),a("p",zr,S(f.value),1)])),a("div",{class:R(["relative",{"ml-3":!t.isCollapsed}])},[a("button",{onClick:y[0]||(y[0]=_=>o.value=!o.value),class:"p-1 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-500 dark:hover:text-gray-300 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary-500"},[E(V,{name:"ellipsis-vertical",size:"sm"})]),o.value?(i(),h("div",{key:0,onClick:y[3]||(y[3]=_=>o.value=!1),class:"origin-bottom-left fixed bottom-16 left-4 w-48 rounded-md shadow-xl bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 border border-gray-200 dark:border-gray-600",style:{"z-index":"99999"}},[a("div",Nr,[E(x,{to:"/app/profile",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:y[1]||(y[1]=_=>o.value=!1)},{default:z(()=>y[4]||(y[4]=[N(" Il tuo profilo ")])),_:1,__:[4]}),E(x,{to:"/app/settings",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:y[2]||(y[2]=_=>o.value=!1)},{default:z(()=>y[5]||(y[5]=[N(" Impostazioni ")])),_:1,__:[5]}),y[6]||(y[6]=a("hr",{class:"my-1 border-gray-200 dark:border-gray-600"},null,-1)),a("button",{onClick:b,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"}," Esci ")])])):T("",!0)],2)],2),p.value&&!t.isCollapsed?(i(),h("div",Br," v"+S(p.value),1)):T("",!0)])}}},Hr={class:"flex"},Ur={class:"hidden lg:flex lg:flex-shrink-0 lg:fixed lg:inset-y-0 z-10"},Zr={class:"flex flex-col h-full bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-sm"},Gr={class:"flex items-center flex-shrink-0 px-4 pt-5"},Wr={class:"mr-3"},Qr={class:"text-xl font-semibold text-gray-900 dark:text-white"},Kr={class:"flex flex-col h-full bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-sm"},Jr={class:"flex items-center justify-between px-4 pt-5 mb-4 flex-shrink-0"},Yr={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center mr-3"},Xr={class:"text-white font-bold text-sm"},ea={class:"text-xl font-semibold text-gray-900 dark:text-white"},ta={__name:"AppSidebar",props:{isMobileOpen:{type:Boolean,default:!1}},emits:["close","toggle-collapsed"],setup(t,{emit:e}){const r=e,o=be(),n=q(!1),s=w(()=>o.config||{}),u=w(()=>{var g;return((g=s.value.company)==null?void 0:g.name)||"DatPortal"});function f(){n.value=!n.value,r("toggle-collapsed",n.value)}function p(){n.value&&(n.value=!1)}return(g,b)=>{const l=re("router-link");return i(),h("div",Hr,[a("div",Ur,[a("div",{class:R(["flex flex-col transition-all duration-300",[n.value?"w-20":"w-64"]])},[a("div",Zr,[a("div",Gr,[a("div",{class:R(["flex items-center",{"justify-center":n.value}])},[E(l,{to:"/app/dashboard",class:R(["flex items-center",{hidden:n.value}])},{default:z(()=>[a("div",Wr,[E(je,{variant:"primary",size:"lg"})]),a("h3",Qr,S(u.value),1)]),_:1},8,["class"]),E(l,{to:"/app/dashboard",class:R(["flex items-center justify-center",{hidden:!n.value}])},{default:z(()=>[E(je,{variant:"primary",size:"md"})]),_:1},8,["class"])],2),a("button",{onClick:f,class:"ml-auto text-gray-600 dark:text-gray-400 focus:outline-none hover:bg-gray-100 dark:hover:bg-gray-700 p-1 rounded"},[n.value?(i(),U(V,{key:1,name:"chevron-double-right",size:"sm"})):(i(),U(V,{key:0,name:"chevron-double-left",size:"sm"}))])]),E(at,{"is-collapsed":n.value,onItemClick:p},null,8,["is-collapsed"]),E(ot,{"is-collapsed":n.value},null,8,["is-collapsed"])])],2)]),a("div",{class:R(["fixed inset-y-0 left-0 z-30 w-64 bg-primary-700 transform transition-transform duration-300 ease-in-out lg:hidden",t.isMobileOpen?"translate-x-0":"-translate-x-full"])},[a("div",Kr,[a("div",Jr,[E(l,{to:"/app/dashboard",class:"flex items-center"},{default:z(()=>[a("div",Yr,[a("span",Xr,S(g.brandInitials),1)]),a("h3",ea,S(u.value),1)]),_:1}),a("button",{onClick:b[0]||(b[0]=y=>g.$emit("close")),class:"p-2 rounded-md text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"},[E(V,{name:"x-mark",size:"md"})])]),E(at,{"is-collapsed":!1,onItemClick:b[1]||(b[1]=y=>g.$emit("close"))}),E(ot,{"is-collapsed":!1})])],2)])}}},ra={class:"flex","aria-label":"Breadcrumb"},aa={class:"flex items-center space-x-2 text-sm text-gray-500"},oa={key:0,class:"mr-2"},na={class:"flex items-center"},sa={key:2,class:"font-medium text-gray-900"},ia={__name:"HeaderBreadcrumbs",props:{breadcrumbs:{type:Array,required:!0}},setup(t){return(e,r)=>{const o=re("router-link");return i(),h("nav",ra,[a("ol",aa,[(i(!0),h(ae,null,ie(t.breadcrumbs,(n,s)=>(i(),h("li",{key:s,class:"flex items-center"},[s>0?(i(),h("div",oa,[E(V,{name:"chevron-right",size:"xs",color:"text-gray-400"})])):T("",!0),n.to&&s<t.breadcrumbs.length-1?(i(),U(o,{key:1,to:n.to,class:"hover:text-gray-700 transition-colors duration-150"},{default:z(()=>[a("span",na,[n.icon?(i(),U(V,{key:0,name:n.icon,size:"xs",className:"mr-1"},null,8,["name"])):T("",!0),N(" "+S(n.label),1)])]),_:2},1032,["to"])):(i(),h("span",sa,S(n.label),1))]))),128))])])}}},la={class:"flex items-center space-x-2"},ca=["title"],ua={__name:"HeaderQuickActions",emits:["quick-create-project"],setup(t){const e=qe(),{isDarkMode:r,toggleDarkMode:o}=Oe(),n=w(()=>{var s;return((s=e.name)==null?void 0:s.includes("projects"))||e.path.includes("/projects")});return(s,u)=>(i(),h("div",la,[n.value?(i(),h("button",{key:0,onClick:u[0]||(u[0]=f=>s.$emit("quick-create-project")),class:"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[E(V,{name:"add",size:"xs",className:"mr-1"}),u[2]||(u[2]=N(" Nuovo Progetto "))])):T("",!0),a("button",{onClick:u[1]||(u[1]=(...f)=>H(o)&&H(o)(...f)),class:"inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700",title:H(r)?"Modalità chiara":"Modalità scura"},[E(V,{name:H(r)?"sun":"moon",size:"xs"},null,8,["name"])],8,ca)]))}},da=ge("notifications",{state:()=>({notifications:[],unreadCount:0,loading:!1,error:null}),getters:{unreadNotifications:t=>t.notifications.filter(e=>!e.is_read),hasUnreadNotifications:t=>t.unreadCount>0},actions:{async fetchNotifications(t=!1){this.loading=!0,this.error=null;try{const e=new URLSearchParams;t&&e.append("unread_only","true"),e.append("limit","50");const r=await P.get(`/api/notifications?${e.toString()}`);r.data&&(this.notifications=r.data.notifications||[],this.unreadCount=r.data.unread_count||0)}catch(e){this.error=e.message||"Errore nel caricamento delle notifiche",console.error("Error fetching notifications:",e)}finally{this.loading=!1}},async fetchUnreadCount(){try{const t=await P.get("/api/notifications/unread-count");t.data&&(this.unreadCount=t.data.unread_count||0)}catch(t){console.error("Error fetching unread count:",t)}},async markAsRead(t){try{await P.put(`/api/notifications/${t}/read`);const e=this.notifications.find(r=>r.id===t);e&&!e.is_read&&(e.is_read=!0,this.unreadCount=Math.max(0,this.unreadCount-1))}catch(e){throw console.error("Error marking notification as read:",e),e}},async markAllAsRead(){try{await P.put("/api/notifications/mark-all-read"),this.notifications.forEach(t=>{t.is_read=!0}),this.unreadCount=0}catch(t){throw console.error("Error marking all notifications as read:",t),t}},async createNotification(t,e,r,o="info",n=null){try{const s={user_id:t,title:e,message:r,type:o,link:n};return(await P.post("/api/notifications",s)).data}catch(s){throw console.error("Error creating notification:",s),s}},async pollNotifications(){this.loading||await this.fetchUnreadCount()},reset(){this.notifications=[],this.unreadCount=0,this.loading=!1,this.error=null},addNotification(t){this.notifications.unshift(t),t.is_read||this.unreadCount++}}}),ma=ge("communication",{state:()=>({topics:[],currentTopic:null,categories:[],polls:[],currentPoll:null,userVotes:{},messages:[],conversations:[],events:[],userRegistrations:{},news:[],reactions:{},comments:[],searchResults:[],loading:{topics:!1,polls:!1,messages:!1,conversations:!1,events:!1,news:!1,reactions:!1,comments:!1,search:!1},unreadCounts:{messages:0,news:0},errors:{topics:null,polls:null,messages:null,conversations:null,events:null,news:null,reactions:null,comments:null,search:null},pagination:{topics:{page:1,totalPages:1,totalItems:0,hasMore:!1},polls:{page:1,totalPages:1,totalItems:0,hasMore:!1},messages:{page:1,totalPages:1,totalItems:0,hasMore:!1},conversations:{page:1,totalPages:1,totalItems:0,hasMore:!1},events:{page:1,totalPages:1,totalItems:0,hasMore:!1},news:{page:1,totalPages:1,totalItems:0,hasMore:!1},comments:{page:1,totalPages:1,totalItems:0,hasMore:!1},search:{page:1,totalPages:1,totalItems:0,hasMore:!1}},stats:{}}),getters:{getTopicsByCategory:t=>e=>(t.topics||[]).filter(r=>r.category===e),getPinnedTopics:t=>(t.topics||[]).filter(e=>e.is_pinned),getActivePolls:t=>Array.isArray(t.polls)?t.polls.filter(e=>e.is_expired!==void 0?e.is_active&&!e.is_expired:e.is_active?e.expires_at?new Date(e.expires_at)>new Date:!0:!1):[],getUserVoteForPoll:t=>e=>t.userVotes&&t.userVotes[e]||null,getUnreadMessages:t=>(t.messages||[]).filter(e=>!e.is_read),getUpcomingEvents:t=>{const e=new Date;return(t.events||[]).filter(r=>new Date(r.start_time||r.event_time)>e).sort((r,o)=>new Date(r.start_time||r.event_time)-new Date(o.start_time||o.event_time))},getUserEventRegistration:t=>e=>t.userRegistrations[e]||null,getConversationMessages:t=>e=>(t.messages||[]).filter(r=>r.sender_id===e||r.recipient_id===e).sort((r,o)=>new Date(r.sent_at)-new Date(o.sent_at)),getUnreadMessagesCount:t=>e=>(t.messages||[]).filter(r=>!r.is_read&&r.sender_id!==e).length,getUnreadNewsCount:t=>(t.news||[]).filter(e=>!e.is_read).length},actions:{async fetchForumTopics(t={}){var e,r,o,n;this.loading.topics=!0,this.errors.topics=null;try{const s=await P.get("/api/communication/forum/topics",{params:t});this.topics=((e=s.data.data)==null?void 0:e.topics)||[],this.pagination.topics=((r=s.data.data)==null?void 0:r.pagination)||{page:1,totalPages:1,totalItems:0,hasMore:!1}}catch(s){throw console.error("Failed to fetch forum topics:",s),this.errors.topics=((n=(o=s.response)==null?void 0:o.data)==null?void 0:n.message)||"Errore nel caricamento dei topic",this.topics=[],s}finally{this.loading.topics=!1}},async createForumTopic(t){try{const e=await P.post("/api/communication/forum/topics",t),r=e.data.data||e.data;return this.topics.unshift(r),r}catch(e){throw e}},async createTopic(t){return this.createForumTopic(t)},async updateTopic(t){try{const e=await P.put(`/api/communication/forum/topics/${t.id}`,t),r=e.data.data||e.data,o=this.topics.findIndex(n=>n.id===t.id);return o!==-1&&(this.topics[o]=r),r}catch(e){throw e}},async deleteTopic(t){try{await P.delete(`/api/communication/forum/topics/${t}`),this.topics=this.topics.filter(e=>e.id!==t)}catch(e){throw e}},async fetchTopic(t){try{const e=await P.get(`/api/communication/forum/topics/${t}`),r=e.data.data||e.data;return this.currentTopic=r,r}catch(e){throw e}},async incrementTopicViews(t){try{await P.post(`/api/communication/forum/topics/${t}/views`)}catch(e){console.warn("Errore nell'incremento delle visualizzazioni:",e)}},async toggleTopicPin(t){try{const e=await P.post(`/api/communication/forum/topics/${t}/pin`),r=e.data.data||e.data,o=this.topics.findIndex(n=>n.id===t);return o!==-1&&(this.topics[o].is_pinned=r.is_pinned),r}catch(e){throw e}},async toggleTopicLock(t){try{const e=await P.post(`/api/communication/forum/topics/${t}/lock`),r=e.data.data||e.data,o=this.topics.findIndex(n=>n.id===t);return o!==-1&&(this.topics[o].is_locked=r.is_locked),r}catch(e){throw e}},async fetchTopicComments(t){var e;try{const r=await P.get(`/api/communication/forum/topics/${t}/comments`);this.comments=((e=r.data.data)==null?void 0:e.comments)||[]}catch(r){throw r}},async createTopicComment(t,e){try{const r=await P.post(`/api/communication/forum/topics/${t}/comments`,e),o=r.data.data||r.data;return this.comments.push(o),o}catch(r){throw r}},async fetchPolls(t={}){var e,r,o,n;this.loading.polls=!0,this.errors.polls=null;try{const s=await P.get("/api/communication/polls",{params:t});this.polls=((e=s.data.data)==null?void 0:e.polls)||[],this.pagination.polls=((r=s.data.data)==null?void 0:r.pagination)||{page:1,totalPages:1,totalItems:0,hasMore:!1}}catch(s){throw console.error("Failed to fetch polls:",s),this.errors.polls=((n=(o=s.response)==null?void 0:o.data)==null?void 0:n.message)||"Errore nel caricamento dei sondaggi",this.polls=[],s}finally{this.loading.polls=!1}},async createPoll(t){try{const e=await P.post("/api/communication/polls",t),r=e.data.data||e.data;return this.polls.unshift(r),r}catch(e){throw e}},async updatePoll(t){try{const e=await P.put(`/api/communication/polls/${t.id}`,t),r=e.data.data||e.data,o=this.polls.findIndex(n=>n.id===t.id);return o!==-1&&(this.polls[o]=r),r}catch(e){throw e}},async deletePoll(t){try{await P.delete(`/api/communication/polls/${t}`),this.polls=this.polls.filter(e=>e.id!==t)}catch(e){throw e}},async fetchPoll(t){try{const e=await P.get(`/api/communication/polls/${t}`);return this.currentPoll=e.data.data||e.data,this.currentPoll}catch(e){throw e}},async fetchUserVoteForPoll(t){try{const e=await P.get(`/api/communication/polls/${t}/vote`),r=e.data.data||e.data;return!r||typeof r=="object"&&Object.keys(r).length===0?(this.userVotes[t]=null,null):(this.userVotes[t]=r,r)}catch{return this.userVotes[t]=null,null}},async votePoll(t,e){try{const r=await P.post(`/api/communication/polls/${t}/vote`,{option_id:e});this.userVotes[t]=e;const o=this.polls.findIndex(n=>n.id===t);return o!==-1&&(this.polls[o]=r.data.data||r.data),r.data.data||r.data}catch(r){throw r}},async submitPollVote(t){try{const{poll_id:e,option_id:r,option_ids:o}=t,n=o||(r?[r]:[]);if(!n.length)throw new Error("Nessuna opzione selezionata");const s=await P.post(`/api/communication/polls/${e}/vote`,{option_ids:n});return this.userVotes[e]={option_ids:n},await this.fetchPoll(e),s.data.data||s.data}catch(e){throw e}},async fetchMessages(t={}){var e,r,o,n;this.loading.messages=!0,this.errors.messages=null;try{const s=await P.get("/api/communication/messages",{params:t});this.messages=((e=s.data.data)==null?void 0:e.messages)||[],this.pagination.messages=((r=s.data.data)==null?void 0:r.pagination)||{page:1,totalPages:1,totalItems:0,hasMore:!1},this.unreadCounts.messages=(this.messages||[]).filter(u=>!u.is_read&&u.recipient_id&&u.recipient_id!==u.sender_id).length}catch(s){throw console.error("Failed to fetch messages:",s),this.errors.messages=((n=(o=s.response)==null?void 0:o.data)==null?void 0:n.message)||"Errore nel caricamento dei messaggi",this.messages=[],s}finally{this.loading.messages=!1}},async sendMessage(t){try{console.log("📤 Sending message:",t);const e=await P.post("/api/communication/messages",t);console.log("📤 Send response:",e),console.log("📤 Send response data:",e.data);const r=e.data.data||e.data;return console.log("📤 New message to store:",r),this.messages.unshift(r),console.log("📤 Messages after adding new:",this.messages.length),r}catch(e){throw console.error("❌ Error sending message:",e),e}},async markMessageAsRead(t){try{await P.put(`/api/communication/messages/${t}/read`);const e=this.messages.findIndex(r=>r.id===t);e!==-1&&(this.messages[e].is_read=!0,this.unreadCounts.messages=Math.max(0,this.unreadCounts.messages-1))}catch(e){throw e}},async deleteMessage(t){try{await P.delete(`/api/communication/messages/${t}`),this.messages=this.messages.filter(e=>e.id!==t)}catch(e){throw e}},async fetchConversations(t={}){var e,r,o,n;this.loading.conversations=!0,this.errors.conversations=null;try{const s=await P.get("/api/communication/messages",{params:t});this.conversations=((e=s.data.data)==null?void 0:e.messages)||[],this.pagination.conversations=((r=s.data.data)==null?void 0:r.pagination)||{page:1,totalPages:1,totalItems:0}}catch(s){throw this.errors.conversations=((n=(o=s.response)==null?void 0:o.data)==null?void 0:n.message)||"Errore nel caricamento delle conversazioni",this.conversations=[],s}finally{this.loading.conversations=!1}},async markConversationAsRead(t){try{await P.put(`/api/communication/messages/${t}/read`);const e=this.conversations.findIndex(r=>r.id===t);e!==-1&&(this.conversations[e].unread_count=0)}catch(e){throw e}},async fetchEvents(t={}){var e,r,o,n;this.loading.events=!0,this.errors.events=null;try{const s=await P.get("/api/communication/events",{params:t});this.events=((e=s.data.data)==null?void 0:e.events)||[],this.pagination.events=((r=s.data.data)==null?void 0:r.pagination)||{page:1,totalPages:1,totalItems:0,hasMore:!1}}catch(s){throw console.error("Failed to fetch events:",s),this.errors.events=((n=(o=s.response)==null?void 0:o.data)==null?void 0:n.message)||"Errore nel caricamento degli eventi",this.events=[],s}finally{this.loading.events=!1}},async fetchEvent(t){try{const e=await P.get(`/api/communication/events/${t}`),r=e.data.data||e.data,o=this.events.findIndex(n=>n.id==t);return o!==-1?this.events[o]=r:this.events.push(r),r}catch(e){throw console.error("Failed to fetch event:",e),e}},async registerForEvent(t){try{const e=await P.post(`/api/communication/events/${t}/register`);if(e.data.success){this.userRegistrations[t]=e.data.data;const r=this.events.findIndex(o=>o.id===t);return r!==-1&&(this.events[r].user_registered=!0,e.status===201&&this.events[r].participants_count!==void 0&&(this.events[r].participants_count+=1)),e.data.data}else throw new Error(e.data.message||"Errore nella registrazione")}catch(e){throw console.error("Error in registerForEvent store:",e),e}},async unregisterFromEvent(t){try{await P.delete(`/api/communication/events/${t}/register`),delete this.userRegistrations[t];const e=this.events.findIndex(r=>r.id===t);e!==-1&&(this.events[e].user_registered=!1)}catch(e){throw e}},async createEvent(t){try{const e=await P.post("/api/communication/events",t);return this.events.unshift(e.data),e.data}catch(e){throw e}},async updateEvent(t){try{const e=await P.put(`/api/communication/events/${t.id}`,t),r=this.events.findIndex(o=>o.id===t.id);return r!==-1&&(this.events[r]=e.data),e.data}catch(e){throw e}},async deleteEvent(t){try{await P.delete(`/api/communication/events/${t}`),this.events=this.events.filter(e=>e.id!==t)}catch(e){throw e}},async duplicateEvent(t){try{const e=await P.post(`/api/communication/events/${t}/duplicate`);return this.events.unshift(e.data.data||e.data),e.data.data||e.data}catch(e){throw e}},async fetchEventParticipants(t){try{const e=await P.get(`/api/communication/events/${t}/participants`);return e.data.data||e.data||[]}catch(e){throw console.error("Failed to fetch event participants:",e),e}},async fetchNews(t={}){var e,r,o,n;this.loading.news=!0,this.errors.news=null;try{const s=await P.get("/api/communication/company",{params:t});this.news=((e=s.data.data)==null?void 0:e.communications)||[],this.pagination.news=((r=s.data.data)==null?void 0:r.pagination)||{page:1,totalPages:1,totalItems:0,hasMore:!1},this.unreadCounts.news=(this.news||[]).filter(u=>!u.is_read).length}catch(s){throw this.errors.news=((n=(o=s.response)==null?void 0:o.data)==null?void 0:n.message)||"Errore nel caricamento delle news",this.news=[],s}finally{this.loading.news=!1}},async createNews(t){try{const e=await P.post("/api/communication/company",t),r=e.data.data||e.data;return this.news.unshift(r),r}catch(e){throw e}},async updateNews(t){try{const e=await P.put(`/api/communication/company/${t.id}`,t),r=e.data.data||e.data,o=this.news.findIndex(n=>n.id===t.id);return o!==-1&&(this.news[o]=r),r}catch(e){throw e}},async deleteNews(t){try{await P.delete(`/api/communication/company/${t}`),this.news=this.news.filter(e=>e.id!==t)}catch(e){throw e}},async markNewsAsRead(t){try{await P.post(`/api/communication/company/${t}/read`);const e=this.news.findIndex(r=>r.id===t);e!==-1&&!this.news[e].is_read&&(this.news[e].is_read=!0,this.unreadCounts.news=Math.max(0,this.unreadCounts.news-1))}catch(e){throw e}},async pinNews(t){try{const e=await P.post(`/api/communication/company/${t}/pin`),r=this.news.findIndex(o=>o.id===t);return r!==-1&&(this.news[r].is_pinned=!0),e.data}catch(e){throw e}},async unpinNews(t){try{const e=await P.delete(`/api/communication/company/${t}/pin`),r=this.news.findIndex(o=>o.id===t);return r!==-1&&(this.news[r].is_pinned=!1),e.data}catch(e){throw e}},async duplicateNews(t){try{const e=await P.post(`/api/communication/company/${t}/duplicate`);return this.news.unshift(e.data),e.data}catch(e){throw e}},async fetchComments(t,e,r={}){var o,n,s,u;this.loading.comments=!0,this.errors.comments=null;try{const f=await P.get(`/api/communication/${t}/${e}/comments`,{params:r});this.comments=((o=f.data.data)==null?void 0:o.comments)||f.data.comments||[],this.pagination.comments=((n=f.data.data)==null?void 0:n.pagination)||f.data.pagination||{page:1,totalPages:1,totalItems:0,hasMore:!1}}catch(f){throw this.errors.comments=((u=(s=f.response)==null?void 0:s.data)==null?void 0:u.message)||"Errore nel caricamento dei commenti",this.comments=[],f}finally{this.loading.comments=!1}},async createComment(t,e,r){try{const o=await P.post(`/api/communication/${t}/${e}/comments`,r);return this.comments.push(o.data.data||o.data),o.data.data||o.data}catch(o){throw o}},async updateComment(t,e){try{const r=await P.put(`/api/communication/comments/${t}`,e),o=this.comments.findIndex(n=>n.id===t);return o!==-1&&(this.comments[o]=r.data),r.data}catch(r){throw r}},async deleteComment(t){try{await P.delete(`/api/communication/comments/${t}`),this.comments=this.comments.filter(e=>e.id!==t)}catch(e){throw e}},async addReaction(t,e,r){try{const o={topic:"forum_topic",comment:"forum_comment",news:"news",event:"company_event"},n=await P.post("/api/communication/reactions",{content_type:o[t]||t,content_id:e,reaction_type:r});return this.reactions[t]||(this.reactions[t]={}),this.reactions[t][e]||(this.reactions[t][e]=[]),this.reactions[t][e].push(n.data.data||n.data),n.data.data||n.data}catch(o){throw o}},async removeReaction(t,e,r){try{await P.delete(`/api/communication/reactions/${r}`),this.reactions[t]&&this.reactions[t][e]&&(this.reactions[t][e]=this.reactions[t][e].filter(o=>o.id!==r))}catch(o){throw o}},async fetchCategories(){var t;try{const e=await P.get("/api/communication/categories");return this.categories=((t=e.data.data)==null?void 0:t.categories)||e.data||[],this.categories}catch(e){throw this.categories=[],e}},async fetchForumCategories(){try{const t=[...new Set((this.topics||[]).map(e=>e.category).filter(Boolean))];return this.categories=t,t}catch(t){throw this.categories=[],t}},async createCategory(t){try{const e=await P.post("/api/communication/categories",t);return this.categories.push(e.data),e.data}catch(e){throw e}},async updateCategory(t){try{const e=await P.put(`/api/communication/categories/${t.id}`,t),r=this.categories.findIndex(o=>o.id===t.id);return r!==-1&&(this.categories[r]=e.data),e.data}catch(e){throw e}},async deleteCategory(t){try{await P.delete(`/api/communication/categories/${t}`),this.categories=this.categories.filter(e=>e.id!==t)}catch(e){throw e}},async fetchUsers(){var t;try{const e=await P.get("/api/personnel/users");return(((t=e.data.data)==null?void 0:t.users)||e.data||[]).map(o=>({id:o.id,name:o.full_name||o.name,role:o.position||o.role,department:o.department_name||o.department,email:o.email}))}catch(e){throw console.error("Failed to fetch users for messaging:",e),e}},async searchUsers(t={}){var e;try{const r=await P.get("/api/personnel/users",{params:{search:t.query,per_page:50,...t}});return(((e=r.data.data)==null?void 0:e.users)||r.data||[]).filter(n=>t.exclude_self?n.id!==t.current_user_id:!0).map(n=>({id:n.id,name:n.full_name||n.name,role:n.position||n.role,department:n.department_name||n.department,email:n.email}))}catch(r){throw console.error("Failed to search users:",r),r}},async fetchCommunicationStats(){try{const t=await P.get("/api/communication/stats");return this.stats=t.data.data||t.data,this.stats}catch(t){throw console.error("Failed to fetch stats:",t),this.stats={},t}},async searchContent(t,e={}){var r,o;this.loading.search=!0,this.errors.search=null;try{const n=await P.get("/api/communication/search",{params:{query:t,...e}});return this.searchResults=n.data.results||[],this.pagination.search=n.data.pagination||{page:1,totalPages:1,totalItems:0,hasMore:!1},n.data}catch(n){throw this.errors.search=((o=(r=n.response)==null?void 0:r.data)==null?void 0:o.message)||"Errore nella ricerca",this.searchResults=[],n}finally{this.loading.search=!1}},clearSearchResults(){this.searchResults=[],this.pagination.search={page:1,totalPages:1,totalItems:0,hasMore:!1}},async markAllAsRead(){try{await P.post("/api/communication/notifications/mark-all-read")}catch(t){throw t}},async getNotificationSettings(){try{return(await P.get("/api/communication/notification-settings")).data}catch(t){throw t}},async updateNotificationSettings(t){try{return(await P.put("/api/communication/notification-settings",t)).data}catch(e){throw e}},clearErrors(){this.errors={topics:null,polls:null,messages:null,conversations:null,events:null,news:null,reactions:null,comments:null,search:null}},resetPagination(){this.pagination={topics:{page:1,totalPages:1,totalItems:0,hasMore:!1},polls:{page:1,totalPages:1,totalItems:0,hasMore:!1},messages:{page:1,totalPages:1,totalItems:0,hasMore:!1},conversations:{page:1,totalPages:1,totalItems:0,hasMore:!1},events:{page:1,totalPages:1,totalItems:0,hasMore:!1},news:{page:1,totalPages:1,totalItems:0,hasMore:!1},comments:{page:1,totalPages:1,totalItems:0,hasMore:!1},search:{page:1,totalPages:1,totalItems:0,hasMore:!1}}}}}),pa={class:"relative"},ha={class:"relative"},ga={key:0,class:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center"},fa={class:"py-1"},va={key:0,class:"px-4 py-8 text-center text-gray-500 text-sm"},_a={key:1,class:"px-4 py-8 text-center text-gray-500 text-sm"},wa={key:2,class:"max-h-64 overflow-y-auto"},ya=["onClick"],ba={class:"flex items-start"},xa={class:"flex-shrink-0"},ka={class:"ml-3 flex-1"},Ea={class:"text-sm font-medium text-gray-900"},Aa={class:"text-xs text-gray-500 mt-1"},Pa={class:"text-xs text-gray-400 mt-1"},Ca={key:0,class:"flex-shrink-0"},qa={key:3,class:"px-4 py-2 border-t border-gray-100"},$a={__name:"HeaderNotifications",setup(t){const e=q(!1),r=fe(),o=da(),n=ma();me();const s=w(()=>o.notifications),u=w(()=>{const _=o.unreadCount,k=n.unreadCounts.messages,m=n.unreadCounts.news;return _+k+m}),f=w(()=>o.loading);let p=null;ce(async()=>{await o.fetchNotifications();try{await Promise.all([n.fetchMessages({per_page:1}).catch(_=>console.warn("Messages failed:",_)),n.fetchNews({per_page:1}).catch(_=>console.warn("News failed:",_))])}catch(_){console.warn("Errore nel caricamento dati comunicazione:",_)}p=setInterval(async()=>{await o.pollNotifications();try{await Promise.all([n.fetchMessages({per_page:1}).catch(_=>console.warn("Messages poll failed:",_)),n.fetchNews({per_page:1}).catch(_=>console.warn("News poll failed:",_))])}catch(_){console.warn("Errore nel polling comunicazioni:",_)}},3e5)}),$e(()=>{p&&clearInterval(p)});function g(_){const k={task:"h-6 w-6 rounded-full bg-primary-100 text-primary-600 flex items-center justify-center",project:"h-6 w-6 rounded-full bg-green-100 text-green-600 flex items-center justify-center",user:"h-6 w-6 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center",message:"h-6 w-6 rounded-full bg-indigo-100 text-indigo-600 flex items-center justify-center",news:"h-6 w-6 rounded-full bg-orange-100 text-orange-600 flex items-center justify-center",communication:"h-6 w-6 rounded-full bg-pink-100 text-pink-600 flex items-center justify-center",system:"h-6 w-6 rounded-full bg-gray-100 text-gray-600 flex items-center justify-center"};return k[_]||k.system}function b(_){return{task:"clipboard-document-list",project:"folder",user:"user",message:"envelope",news:"newspaper",communication:"chat-bubble-left",system:"information-circle"}[_]||"information-circle"}function l(_){const k=new Date(_),c=new Date-k;return c<6e4?"Adesso":c<36e5?`${Math.floor(c/6e4)}m fa`:c<864e5?`${Math.floor(c/36e5)}h fa`:k.toLocaleDateString("it-IT")}async function y(_){try{_.is_read||await o.markAsRead(_.id),e.value=!1,_.link&&r.push(_.link)}catch(k){console.error("Errore gestione click notifica:",k)}}async function x(){try{await o.markAllAsRead()}catch(_){console.error("Errore segnando tutte come lette:",_)}}return(_,k)=>(i(),h("div",pa,[a("button",{onClick:k[0]||(k[0]=m=>e.value=!e.value),class:"p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[k[2]||(k[2]=a("span",{class:"sr-only"},"Visualizza notifiche",-1)),a("div",ha,[E(V,{name:"bell",size:"md"}),u.value>0?(i(),h("span",ga,S(u.value>9?"9+":u.value),1)):T("",!0)])]),e.value?(i(),h("div",{key:0,onClick:k[1]||(k[1]=m=>e.value=!1),class:"origin-top-right absolute right-0 mt-2 w-80 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50"},[a("div",fa,[k[5]||(k[5]=a("div",{class:"px-4 py-2 border-b border-gray-100"},[a("h3",{class:"text-sm font-medium text-gray-900"},"Notifiche")],-1)),f.value?(i(),h("div",va,k[3]||(k[3]=[a("div",{class:"animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500 mx-auto"},null,-1),a("div",{class:"mt-2"},"Caricamento...",-1)]))):s.value.length===0?(i(),h("div",_a," Nessuna notifica ")):(i(),h("div",wa,[(i(!0),h(ae,null,ie(s.value,m=>(i(),h("div",{key:m.id,class:"px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-50 last:border-b-0",onClick:c=>y(m)},[a("div",ba,[a("div",xa,[a("div",{class:R(g(m.type))},[E(V,{name:b(m.type),size:"sm"},null,8,["name"])],2)]),a("div",ka,[a("p",Ea,S(m.title),1),a("p",Aa,S(m.message),1),a("p",Pa,S(l(m.created_at)),1)]),m.is_read?T("",!0):(i(),h("div",Ca,k[4]||(k[4]=[a("div",{class:"h-2 w-2 bg-primary-500 rounded-full"},null,-1)])))])],8,ya))),128))])),s.value.length>0?(i(),h("div",qa,[a("button",{onClick:x,class:"text-xs text-primary-600 hover:text-primary-800"}," Segna tutte come lette ")])):T("",!0)])])):T("",!0)]))}},Ia={class:"relative"},Ma={class:"flex items-start justify-center min-h-screen pt-16 px-4 pb-20 text-center sm:block sm:p-0"},Ta={class:"inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6"},Sa={class:"flex items-center"},Fa={class:"flex-1"},Va={key:0,class:"mt-4 max-h-64 overflow-y-auto"},Da={class:"space-y-1"},Ra=["onClick"],La={class:"flex-shrink-0"},ja={class:"ml-3 flex-1 min-w-0"},Oa={class:"text-sm font-medium text-gray-900 truncate"},za={class:"text-xs text-gray-500 truncate"},Na={class:"ml-2 text-xs text-gray-400"},Ba={key:1,class:"mt-4 text-center py-4"},Ha={key:2,class:"mt-4 text-center py-4"},Ua={__name:"HeaderSearch",setup(t){const e=fe(),r=q(!1),o=q(""),n=q([]),s=q(-1),u=q(!1),f=q(null),p=[{id:1,type:"project",title:"Website Redesign",description:"Progetto di redesign del sito web aziendale",path:"/app/projects/1"},{id:2,type:"person",title:"Mario Rossi",description:"Senior Developer",path:"/app/personnel/directory/1"},{id:3,type:"document",title:"Specifiche Tecniche",description:"Documento delle specifiche del progetto mobile",path:"/app/documents/1"},{id:4,type:"task",title:"Implementazione API",description:"Task per l'implementazione delle API REST",path:"/app/projects/1/tasks/5"}];te(r,async m=>{var c;m?(await ft(),(c=f.value)==null||c.focus()):(o.value="",n.value=[],s.value=-1)});function g(){if(!o.value.trim()){n.value=[];return}u.value=!0,setTimeout(()=>{n.value=p.filter(m=>m.title.toLowerCase().includes(o.value.toLowerCase())||m.description.toLowerCase().includes(o.value.toLowerCase())),s.value=-1,u.value=!1},200)}function b(m){if(n.value.length===0)return;const c=s.value+m;c>=0&&c<n.value.length&&(s.value=c)}function l(){s.value>=0&&n.value[s.value]&&y(n.value[s.value])}function y(m){r.value=!1,e.push(m.path)}function x(m){const c={project:"h-6 w-6 rounded bg-primary-100 text-primary-600 flex items-center justify-center",person:"h-6 w-6 rounded bg-green-100 text-green-600 flex items-center justify-center",document:"h-6 w-6 rounded bg-yellow-100 text-yellow-600 flex items-center justify-center",task:"h-6 w-6 rounded bg-purple-100 text-purple-600 flex items-center justify-center"};return c[m]||c.document}function _(m){return{project:"folder",person:"user",document:"document",task:"clipboard-document-list"}[m]||"document"}function k(m){return{project:"Progetto",person:"Persona",document:"Documento",task:"Task"}[m]||"Elemento"}return(m,c)=>(i(),h("div",Ia,[a("button",{onClick:c[0]||(c[0]=C=>r.value=!r.value),class:"p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[c[7]||(c[7]=a("span",{class:"sr-only"},"Cerca",-1)),E(V,{name:"search",size:"md"})]),r.value?(i(),h("div",{key:0,class:"fixed inset-0 z-50 overflow-y-auto",onClick:c[6]||(c[6]=ct(C=>r.value=!1,["self"]))},[a("div",Ma,[c[10]||(c[10]=a("div",{class:"fixed inset-0 bg-black bg-opacity-25 transition-opacity"},null,-1)),a("div",Ta,[a("div",null,[a("div",Sa,[a("div",Fa,[de(a("input",{ref_key:"searchInput",ref:f,"onUpdate:modelValue":c[1]||(c[1]=C=>o.value=C),onInput:g,onKeydown:[c[2]||(c[2]=ke(C=>r.value=!1,["escape"])),ke(l,["enter"]),c[3]||(c[3]=ke(C=>b(-1),["up"])),c[4]||(c[4]=ke(C=>b(1),["down"]))],type:"text",placeholder:"Cerca progetti, persone, documenti...",class:"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"},null,544),[[pe,o.value]])]),a("button",{onClick:c[5]||(c[5]=C=>r.value=!1),class:"ml-3 p-2 text-gray-400 hover:text-gray-600"},[E(V,{name:"close",size:"md"})])]),n.value.length>0?(i(),h("div",Va,[a("div",Da,[(i(!0),h(ae,null,ie(n.value,(C,Z)=>(i(),h("div",{key:C.id,onClick:$=>y(C),class:R(["flex items-center px-3 py-2 rounded-md cursor-pointer",Z===s.value?"bg-primary-50":"hover:bg-gray-50"])},[a("div",La,[a("div",{class:R(x(C.type))},[E(V,{name:_(C.type),size:"sm"},null,8,["name"])],2)]),a("div",ja,[a("p",Oa,S(C.title),1),a("p",za,S(C.description),1)]),a("div",Na,S(k(C.type)),1)],10,Ra))),128))])])):o.value&&!u.value?(i(),h("div",Ba,c[8]||(c[8]=[a("p",{class:"text-sm text-gray-500"},"Nessun risultato trovato",-1)]))):o.value?T("",!0):(i(),h("div",Ha,c[9]||(c[9]=[a("p",{class:"text-xs text-gray-400"},"Inizia a digitare per cercare...",-1)])))])])])])):T("",!0)]))}},Za={class:"relative"},Ga={class:"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center"},Wa={class:"text-sm font-medium text-primary-700"},Qa={class:"py-1"},Ka={class:"px-4 py-2 border-b border-gray-100 dark:border-gray-700"},Ja={class:"text-sm font-medium text-gray-900 dark:text-white"},Ya={class:"text-xs text-gray-500 dark:text-gray-400"},Xa={class:"flex items-center"},eo={__name:"HeaderUserMenu",setup(t){const e=fe(),r=me(),o=q(!1),{isDarkMode:n,toggleDarkMode:s}=Oe(),u=w(()=>r.user&&(r.user.name||r.user.username)||"Utente"),f=w(()=>{var b;return((b=r.user)==null?void 0:b.email)||""}),p=w(()=>r.user?u.value.charAt(0).toUpperCase():"U");async function g(){o.value=!1,await r.logout(),e.push("/auth/login")}return(b,l)=>{const y=re("router-link");return i(),h("div",Za,[a("button",{onClick:l[0]||(l[0]=x=>o.value=!o.value),class:"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[l[5]||(l[5]=a("span",{class:"sr-only"},"Apri menu utente",-1)),a("div",Ga,[a("span",Wa,S(p.value),1)])]),o.value?(i(),h("div",{key:0,onClick:l[4]||(l[4]=x=>o.value=!1),class:"origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-50"},[a("div",Qa,[a("div",Ka,[a("p",Ja,S(u.value),1),a("p",Ya,S(f.value),1)]),E(y,{to:"/app/profile",class:"flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:l[1]||(l[1]=x=>o.value=!1)},{default:z(()=>[E(V,{name:"user",size:"sm",className:"mr-2"}),l[6]||(l[6]=N(" Il tuo profilo "))]),_:1,__:[6]}),E(y,{to:"/app/settings",class:"flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:l[2]||(l[2]=x=>o.value=!1)},{default:z(()=>[E(V,{name:"settings",size:"sm",className:"mr-2"}),l[7]||(l[7]=N(" Impostazioni "))]),_:1,__:[7]}),l[9]||(l[9]=a("div",{class:"border-t border-gray-100 dark:border-gray-700 my-1"},null,-1)),a("button",{onClick:l[3]||(l[3]=(...x)=>H(s)&&H(s)(...x)),class:"flex items-center justify-between w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"},[a("span",Xa,[E(V,{name:H(n)?"sun":"moon",size:"sm",className:"mr-2"},null,8,["name"]),N(" "+S(H(n)?"Modalità chiara":"Modalità scura"),1)])]),a("button",{onClick:g,class:"flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"},[E(V,{name:"arrow-right-on-rectangle",size:"sm",className:"mr-2"}),l[8]||(l[8]=N(" Esci "))])])])):T("",!0)])}}},to={class:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700"},ro={class:"flex justify-between items-center px-4 py-4 sm:px-6 lg:px-8"},ao={class:"flex items-center space-x-4"},oo={class:"flex flex-col"},no={class:"text-lg font-semibold text-gray-900 dark:text-white"},so={class:"flex items-center space-x-4"},io={class:"hidden md:flex items-center space-x-2"},lo={__name:"AppHeader",props:{pageTitle:{type:String,required:!0},breadcrumbs:{type:Array,default:()=>[]}},emits:["toggle-mobile-sidebar","quick-create-project","quick-add-task"],setup(t){return(e,r)=>(i(),h("header",to,[a("div",ro,[a("div",ao,[a("button",{onClick:r[0]||(r[0]=o=>e.$emit("toggle-mobile-sidebar")),class:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-500 dark:hover:text-gray-300 dark:hover:bg-gray-700"},[E(V,{name:"bars-3",size:"md"})]),a("div",oo,[a("h2",no,S(t.pageTitle),1),t.breadcrumbs.length>0?(i(),U(ia,{key:0,breadcrumbs:t.breadcrumbs},null,8,["breadcrumbs"])):T("",!0)])]),a("div",so,[a("div",io,[E(ua,{onQuickCreateProject:r[1]||(r[1]=o=>e.$emit("quick-create-project")),onQuickAddTask:r[2]||(r[2]=o=>e.$emit("quick-add-task"))})]),E($a),E(Ua),E(eo)])])]))}},co={__name:"LoadingSpinner",props:{size:{type:String,default:"md",validator:t=>["sm","md","lg","xl"].includes(t)},message:{type:String,default:""},centered:{type:Boolean,default:!0}},setup(t){const e=t,r=w(()=>{const u={sm:"20px",md:"32px",lg:"48px",xl:"64px"};return`width: ${u[e.size]}; height: ${u[e.size]};`}),o=w(()=>["flex",e.centered?"items-center justify-center":"","space-y-2"]),n=w(()=>["flex items-center justify-center"]),s=w(()=>["text-sm text-gray-600 text-center"]);return(u,f)=>(i(),h("div",{class:R(o.value)},[a("div",{class:R(n.value)},[a("div",{class:"animate-spin rounded-full border-2 border-gray-300 border-t-primary-600",style:Re(r.value)},null,4)],2),t.message?(i(),h("p",{key:0,class:R(s.value)},S(t.message),3)):T("",!0)],2))}},uo={class:"fixed bottom-0 right-0 z-[9999] p-6 space-y-4 pointer-events-none"},mo={class:"p-4"},po={class:"flex items-start"},ho={class:"flex-shrink-0"},go={class:"ml-3 w-0 flex-1 pt-0.5"},fo={class:"text-sm font-semibold text-gray-900 dark:text-white"},vo={class:"mt-1 text-sm text-gray-700 dark:text-gray-300"},_o={class:"ml-4 flex-shrink-0 flex"},wo=["onClick"],yo={__name:"NotificationManager",setup(t){const e=q([]);function r(f){const p=Date.now(),g={id:p,type:f.type||"info",title:f.title,message:f.message,duration:f.duration||5e3};e.value.push(g),g.duration>0&&setTimeout(()=>{o(p)},g.duration)}function o(f){const p=e.value.findIndex(g=>g.id===f);p>-1&&e.value.splice(p,1)}function n(f){const p={success:"border-l-4 border-green-500 dark:border-green-400",error:"border-l-4 border-red-500 dark:border-red-400",warning:"border-l-4 border-yellow-500 dark:border-yellow-400",info:"border-l-4 border-primary-500 dark:border-primary-400"};return p[f]||p.info}function s(f){const p={success:"h-8 w-8 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 flex items-center justify-center",error:"h-8 w-8 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 flex items-center justify-center",warning:"h-8 w-8 rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 flex items-center justify-center",info:"h-8 w-8 rounded-full bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 flex items-center justify-center"};return p[f]||p.info}function u(f){const p={success:"check-circle",error:"x-circle",warning:"exclamation-circle",info:"information-circle"};return p[f]||p.info}return window.showNotification=r,ce(()=>{}),(f,p)=>(i(),h("div",uo,[E(vt,{name:"notification",tag:"div",class:"space-y-4"},{default:z(()=>[(i(!0),h(ae,null,ie(e.value,g=>(i(),h("div",{key:g.id,class:R([n(g.type),"max-w-sm w-full bg-white dark:bg-gray-800 shadow-xl rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 dark:ring-white dark:ring-opacity-10 overflow-hidden border border-gray-200 dark:border-gray-700"])},[a("div",mo,[a("div",po,[a("div",ho,[a("div",{class:R(s(g.type))},[E(V,{name:u(g.type),size:"sm"},null,8,["name"])],2)]),a("div",go,[a("p",fo,S(g.title),1),a("p",vo,S(g.message),1)]),a("div",_o,[a("button",{onClick:b=>o(g.id),class:"bg-white dark:bg-gray-800 rounded-md inline-flex text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:focus:ring-offset-gray-800"},[p[0]||(p[0]=a("span",{class:"sr-only"},"Chiudi",-1)),E(V,{name:"close",size:"sm"})],8,wo)])])])],2))),128))]),_:1})]))}},bo=xe(yo,[["__scopeId","data-v-b1b3c3a3"]]);class xo{constructor(){this.isDev=!1}info(e,...r){this.isDev&&console.log(`ℹ️ [INFO] ${e}`,...r)}warn(e,...r){console.warn(`⚠️ [WARN] ${e}`,...r)}error(e,...r){console.error(`❌ [ERROR] ${e}`,...r)}debug(e,...r){this.isDev&&console.debug(`🔧 [DEBUG] ${e}`,...r)}}const Y=new xo,mt=ge("selfHealing",()=>{const t=q({current_score:0,timestamp:null,error_count_24h:0,critical_errors:0,pending_issues:0}),e=q([]),r=q([]),o=q([]),n=q({}),s=q([]),u=q({}),f=q({severities:[],error_types:[]}),p=q([]),g=q({}),b=q(!1),l=q(null),y=q(null),x=q(null),_=q(null),k=q(null),m=w(()=>t.value.current_score),c=w(()=>m.value>=80),C=w(()=>m.value<50),Z=w(()=>r.value.filter(v=>v.severity==="critical").length),$=w(()=>o.value.some(v=>new Date(v.started_at)>new Date(Date.now()-24*60*60*1e3)));async function I(){try{b.value=!0,l.value=null;const A=await se(ne.default).get("/api/self-healing/dashboard");if(A.data.success){const M=A.data.data;return t.value=M.system_health,e.value=M.health_trend,r.value=M.critical_patterns,o.value=M.recent_sessions,n.value=M.quick_stats,Y.info("Self-healing dashboard loaded successfully"),M}else throw new Error(A.data.message||"Failed to load dashboard")}catch(v){throw l.value=v.message,Y.error("Failed to fetch self-healing dashboard:",v),v}finally{b.value=!1}}async function B(v={}){try{b.value=!0,l.value=null;const A=se(ne.default),M=new URLSearchParams;Object.entries(v).forEach(([j,Q])=>{Q!=null&&Q!==""&&M.append(j,Q)});const F=await A.get(`/api/self-healing/patterns?${M}`);if(F.data.success){const j=F.data.data;return s.value=j.patterns,u.value=j.pagination,f.value=j.filter_options,Y.info(`Loaded ${j.patterns.length} error patterns`),j}else throw new Error(F.data.message||"Failed to load error patterns")}catch(A){throw l.value=A.message,Y.error("Failed to fetch error patterns:",A),A}finally{b.value=!1}}async function le(v,A=!1){try{y.value=v,l.value=null;const F=await se(ne.ai_analysis).post(`/api/self-healing/patterns/${v}/analyze`,{force_reanalysis:A});if(F.data.success){const j=F.data.data.analysis;_.value=j;const Q=s.value.findIndex(Me=>Me.id===v);return Q!==-1&&(s.value[Q].has_ai_analysis=!0),Y.info(`AI analysis completed for pattern ${v}`),j}else throw new Error(F.data.message||"AI analysis failed")}catch(M){throw l.value=M.message,Y.error("Failed to analyze pattern:",M),M}finally{y.value=null}}async function ee(v){try{x.value=v,l.value=null;const M=await se(ne.ai_analysis).post(`/api/self-healing/patterns/${v}/generate-prompt`,{include_context:!0});if(M.data.success){const F=M.data.data;return k.value={prompt:F.claude_prompt,healing_session_id:F.healing_session_id,analysis:F.pattern_analysis,usage_instructions:F.usage_instructions,generated_at:F.generated_at},Y.info(`Claude prompt generated for pattern ${v}`),k.value}else throw new Error(M.data.message||"Prompt generation failed")}catch(A){throw l.value=A.message,Y.error("Failed to generate Claude prompt:",A),A}finally{x.value=null}}async function ue(v){try{return await navigator.clipboard.writeText(v),Y.info("Claude prompt copied to clipboard"),!0}catch(A){throw Y.error("Failed to copy prompt to clipboard:",A),new Error("Impossibile copiare negli appunti")}}async function D(v={}){try{b.value=!0,l.value=null;const A=se(ne.default),M=new URLSearchParams;Object.entries(v).forEach(([j,Q])=>{Q!=null&&Q!==""&&M.append(j,Q)});const F=await A.get(`/api/self-healing/sessions?${M}`);if(F.data.success){const j=F.data.data;return p.value=j.sessions,g.value=j.pagination,Y.info(`Loaded ${j.sessions.length} healing sessions`),j}else throw new Error(F.data.message||"Failed to load healing sessions")}catch(A){throw l.value=A.message,Y.error("Failed to fetch healing sessions:",A),A}finally{b.value=!1}}async function G(v,A){try{b.value=!0,l.value=null;const F=await se(ne.default).put(`/api/self-healing/sessions/${v}/complete`,A);if(F.data.success){const j=p.value.findIndex(Q=>Q.id===v);return j!==-1&&(p.value[j]={...p.value[j],...F.data.data}),Y.info(`Healing session ${v} completed`),F.data.data}else throw new Error(F.data.message||"Failed to complete session")}catch(M){throw l.value=M.message,Y.error("Failed to complete healing session:",M),M}finally{b.value=!1}}async function J(){try{b.value=!0,l.value=null;const A=await se(ne.default).post("/api/self-healing/health/update");if(A.data.success)return t.value={current_score:A.data.data.health_score,timestamp:A.data.data.timestamp,error_count_24h:A.data.data.error_count_24h,critical_errors:A.data.data.critical_errors,pending_issues:A.data.data.pending_issues},Y.info("System health updated manually"),A.data.data;throw new Error(A.data.message||"Failed to update system health")}catch(v){throw l.value=v.message,Y.error("Failed to update system health:",v),v}finally{b.value=!1}}async function W(v){try{const M=await se(ne.default).post("/api/self-healing/submit-error",{...v,automatic:!1,manual_report:!0,timestamp:new Date().toISOString()});if(M.data.success)return Y.info("Manual error submitted successfully"),M.data.data;throw new Error(M.data.message||"Failed to submit error")}catch(A){throw Y.error("Failed to submit manual error:",A),A}}function K(){_.value=null,k.value=null,y.value=null,x.value=null,l.value=null}return{systemHealth:t,healthTrend:e,criticalPatterns:r,recentSessions:o,quickStats:n,errorPatterns:s,patternsPagination:u,filterOptions:f,healingSessions:p,sessionsPagination:g,loading:b,error:l,analyzingPattern:y,generatingPrompt:x,currentAnalysis:_,currentPrompt:k,healthScore:m,isHealthy:c,isUnhealthy:C,criticalIssuesCount:Z,hasRecentActivity:$,fetchDashboard:I,fetchErrorPatterns:B,analyzePattern:le,generateClaudePrompt:ee,copyPromptToClipboard:ue,fetchHealingSessions:D,completeHealingSession:G,updateSystemHealth:J,submitManualError:W,resetCurrentState:K,formatters:{healthScore:v=>`${v}/100`,severity:v=>{const A={critical:"text-red-600",high:"text-orange-600",medium:"text-yellow-600",low:"text-green-600"};return{label:v.toUpperCase(),class:A[v]||"text-gray-600"}},timeAgo:v=>{const A=new Date,M=new Date(v),F=A-M,j=Math.floor(F/(1e3*60*60));return j<1?"Meno di un'ora fa":j<24?`${j} ore fa`:`${Math.floor(j/24)} giorni fa`},duration:v=>v?v<60?`${v}s`:v<3600?`${Math.floor(v/60)}m ${v%60}s`:`${Math.floor(v/3600)}h ${Math.floor(v%3600/60)}m`:"N/A",formatDate:v=>{if(!v||typeof v!="string")return"N/A";try{return new Date(v).toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"})}catch{return"N/A"}}}}}),_e=q([]);let ko=0;function ze(){function t(f,p="info",g=5e3){const b=++ko,l={id:b,message:f,type:p,duration:g};return _e.value.push(l),g>0&&setTimeout(()=>{e(b)},g),b}function e(f){const p=_e.value.findIndex(g=>g.id===f);p>-1&&_e.value.splice(p,1)}function r(f,p=5e3){return t(f,"success",p)}function o(f,p=7e3){return t(f,"error",p)}function n(f,p=6e3){return t(f,"warning",p)}function s(f,p=5e3){return t(f,"info",p)}function u(){_e.value=[]}return{toasts:_e,showToast:t,removeToast:e,showSuccess:r,showError:o,showWarning:n,showInfo:s,clearToasts:u}}const Eo={class:"space-y-6"},Ao={class:"space-y-2"},Po={class:"text-sm text-gray-500 dark:text-gray-400 w-6"},Co=["onUpdate:modelValue"],qo=["onClick"],$o={class:"grid grid-cols-3 gap-3"},Io=["onClick"],Mo={class:"flex items-center justify-center mb-1"},To={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},So={class:"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4"},Fo={class:"text-center"},Vo={class:"text-sm text-gray-600 dark:text-gray-400"},Do={class:"cursor-pointer"},Ro={key:0,class:"mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-md"},Lo={class:"flex items-center justify-between"},jo={class:"flex items-center space-x-2"},Oo={class:"text-sm text-gray-600 dark:text-gray-400"},zo={class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-2"},No={class:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm"},Bo={class:"ml-2 text-gray-900 dark:text-white font-mono text-xs"},Ho={class:"ml-2 text-gray-900 dark:text-white"},Uo={class:"ml-2 text-gray-900 dark:text-white"},Zo={class:"ml-2 text-gray-900 dark:text-white"},Go={__name:"ErrorSubmissionForm",props:{modelValue:{type:Object,default:()=>({})}},emits:["update:modelValue"],setup(t,{expose:e,emit:r}){const o=t,n=r,s=Le({errorType:"",description:"",stepsToReproduce:[""],priority:"medium",expectedBehavior:"",actualBehavior:"",screenshot:null,additionalNotes:"",...o.modelValue}),u=[{value:"low",label:"Bassa",icon:"information-circle",color:"blue"},{value:"medium",label:"Media",icon:"exclamation-triangle",color:"yellow"},{value:"high",label:"Alta",icon:"exclamation-circle",color:"red"}],f=Le({url:"",browser:"",viewport:{width:0,height:0},timestamp:""});function p(){s.stepsToReproduce.push("")}function g(k){s.stepsToReproduce.length>1&&s.stepsToReproduce.splice(k,1)}function b(k){const m=k.target.files[0];if(m){if(m.size>10*1024*1024){alert("Il file è troppo grande. Massimo 10MB.");return}if(!m.type.startsWith("image/")){alert("Sono accettati solo file immagine.");return}s.screenshot=m}}function l(){s.screenshot=null}function y(){f.url=window.location.href,f.browser=navigator.userAgent,f.viewport.width=window.innerWidth,f.viewport.height=window.innerHeight,f.timestamp=new Date().toLocaleString("it-IT")}const x=w(()=>s.errorType&&s.description.trim());function _(){n("update:modelValue",{...s,systemInfo:f,isValid:x.value})}return ce(()=>{y(),_()}),te(s,_,{deep:!0}),te(f,_,{deep:!0}),e({isValid:x,formData:s,systemInfo:f}),(k,m)=>(i(),h("div",Eo,[a("div",null,[m[6]||(m[6]=a("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Tipo di Errore * ",-1)),de(a("select",{"onUpdate:modelValue":m[0]||(m[0]=c=>s.errorType=c),required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"},m[5]||(m[5]=[wt('<option value="">Seleziona tipo di errore</option><option value="javascript_error">Errore JavaScript</option><option value="vue_component_error">Errore Componente Vue</option><option value="api_error">Errore API</option><option value="database_error">Errore Database</option><option value="network_error">Errore di Rete</option><option value="authentication_error">Errore di Autenticazione</option><option value="css_loading_error">Errore Caricamento CSS</option><option value="memory_error">Errore di Memoria</option><option value="performance_issue">Problema di Performance</option><option value="ui_bug">Bug Interfaccia Utente</option><option value="other">Altro</option>',12)]),512),[[_t,s.errorType]])]),a("div",null,[m[7]||(m[7]=a("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Descrizione Errore * ",-1)),de(a("textarea",{"onUpdate:modelValue":m[1]||(m[1]=c=>s.description=c),required:"",rows:"4",placeholder:"Descrivi dettagliatamente l'errore che hai riscontrato...",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"},null,512),[[pe,s.description]])]),a("div",null,[m[9]||(m[9]=a("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Passaggi per Riprodurre ",-1)),a("div",Ao,[(i(!0),h(ae,null,ie(s.stepsToReproduce,(c,C)=>(i(),h("div",{key:C,class:"flex items-center space-x-2"},[a("span",Po,S(C+1)+".",1),de(a("input",{"onUpdate:modelValue":Z=>s.stepsToReproduce[C]=Z,type:"text",placeholder:"Descrivi il passaggio",class:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"},null,8,Co),[[pe,s.stepsToReproduce[C]]]),s.stepsToReproduce.length>1?(i(),h("button",{key:0,onClick:Z=>g(C),type:"button",class:"text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"},[E(V,{name:"x-mark",size:"sm"})],8,qo)):T("",!0)]))),128)),a("button",{onClick:p,type:"button",class:"inline-flex items-center px-3 py-1 text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300"},[E(V,{name:"plus",size:"sm",className:"mr-1"}),m[8]||(m[8]=N(" Aggiungi Passaggio "))])])]),a("div",null,[m[10]||(m[10]=a("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Priorità ",-1)),a("div",$o,[(i(),h(ae,null,ie(u,c=>a("button",{key:c.value,onClick:C=>s.priority=c.value,type:"button",class:R(["p-3 rounded-lg border-2 text-center transition-all duration-200",s.priority===c.value?`border-${c.color}-500 bg-${c.color}-50 dark:bg-${c.color}-900/20`:"border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"])},[a("div",Mo,[E(V,{name:c.icon,size:"sm",className:s.priority===c.value?`text-${c.color}-600 dark:text-${c.color}-400`:"text-gray-400 dark:text-gray-500"},null,8,["name","className"])]),a("span",{class:R(["text-sm font-medium",s.priority===c.value?`text-${c.color}-700 dark:text-${c.color}-300`:"text-gray-700 dark:text-gray-300"])},S(c.label),3)],10,Io)),64))])]),a("div",To,[a("div",null,[m[11]||(m[11]=a("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Comportamento Atteso ",-1)),de(a("textarea",{"onUpdate:modelValue":m[2]||(m[2]=c=>s.expectedBehavior=c),rows:"3",placeholder:"Cosa ti aspettavi che accadesse?",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"},null,512),[[pe,s.expectedBehavior]])]),a("div",null,[m[12]||(m[12]=a("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Comportamento Effettivo ",-1)),de(a("textarea",{"onUpdate:modelValue":m[3]||(m[3]=c=>s.actualBehavior=c),rows:"3",placeholder:"Cosa è successo realmente?",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"},null,512),[[pe,s.actualBehavior]])])]),a("div",null,[m[16]||(m[16]=a("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Screenshot (Opzionale) ",-1)),a("div",So,[a("div",Fo,[E(V,{name:"photo",size:"xl",class:"mx-auto text-gray-400 dark:text-gray-500 mb-2"}),a("div",Vo,[a("label",Do,[m[13]||(m[13]=a("span",{class:"text-primary-600 dark:text-primary-400 font-medium hover:text-primary-500 dark:hover:text-primary-300"}," Carica screenshot ",-1)),a("input",{type:"file",accept:"image/*",onChange:b,class:"hidden"},null,32)]),m[14]||(m[14]=a("span",{class:"pl-1"},"o trascina qui",-1))]),m[15]||(m[15]=a("p",{class:"text-xs text-gray-500 dark:text-gray-400 mt-1"}," PNG, JPG, GIF fino a 10MB ",-1))]),s.screenshot?(i(),h("div",Ro,[a("div",Lo,[a("div",jo,[E(V,{name:"photo",size:"sm",class:"text-gray-400 dark:text-gray-500"}),a("span",Oo,S(s.screenshot.name),1)]),a("button",{onClick:l,type:"button",class:"text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"},[E(V,{name:"x-mark",size:"sm"})])])])):T("",!0)])]),a("div",null,[m[21]||(m[21]=a("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Informazioni Sistema ",-1)),a("div",zo,[a("div",No,[a("div",null,[m[17]||(m[17]=a("span",{class:"text-gray-600 dark:text-gray-400"},"URL:",-1)),a("span",Bo,S(f.url),1)]),a("div",null,[m[18]||(m[18]=a("span",{class:"text-gray-600 dark:text-gray-400"},"Browser:",-1)),a("span",Ho,S(f.browser),1)]),a("div",null,[m[19]||(m[19]=a("span",{class:"text-gray-600 dark:text-gray-400"},"Risoluzione:",-1)),a("span",Uo,S(f.viewport.width)+"x"+S(f.viewport.height),1)]),a("div",null,[m[20]||(m[20]=a("span",{class:"text-gray-600 dark:text-gray-400"},"Timestamp:",-1)),a("span",Zo,S(f.timestamp),1)])])])]),a("div",null,[m[22]||(m[22]=a("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Note Aggiuntive ",-1)),de(a("textarea",{"onUpdate:modelValue":m[4]||(m[4]=c=>s.additionalNotes=c),rows:"3",placeholder:"Altre informazioni utili per la diagnosi...",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"},null,512),[[pe,s.additionalNotes]])])]))}},Wo={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between"},Qo={class:"flex items-center space-x-3"},Ko={class:"p-2 bg-red-100 dark:bg-red-900/20 rounded-lg"},Jo={class:"flex-1 overflow-y-auto p-6"},Yo={class:"px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900/50"},Xo={class:"flex items-center justify-between"},en={class:"flex items-center space-x-4"},tn={key:0,class:"flex items-center space-x-2"},rn={key:1,class:"flex items-center space-x-2"},an={class:"text-sm text-green-600 dark:text-green-400"},on={key:2,class:"flex items-center space-x-2"},nn={class:"text-sm text-red-600 dark:text-red-400"},sn={class:"flex items-center space-x-3"},ln=["disabled"],cn={key:0,class:"flex items-center space-x-2"},un={key:1,class:"flex items-center space-x-2"},dn={__name:"ManualErrorSubmissionModal",props:{show:{type:Boolean,default:!1}},emits:["close","submitted"],setup(t,{emit:e}){const r=t,o=e,n=mt(),{showToast:s}=ze(),u=q({}),f=q(!1),p=q(!1),g=q(!1),b=q(""),l=q(""),y=c=>{u.value=c,f.value=c.isValid};te(()=>r.show,c=>{c&&x()});function x(){u.value={},f.value=!1,p.value=!1,g.value=!1,b.value="",l.value=""}function _(){o("close")}async function k(){if(!(!f.value||p.value)){p.value=!0,b.value="",g.value=!1;try{const c={error_type:u.value.errorType,message:u.value.description,severity:u.value.priority,error_context:{steps_to_reproduce:u.value.stepsToReproduce.filter(Z=>Z.trim()),expected_behavior:u.value.expectedBehavior,actual_behavior:u.value.actualBehavior,additional_notes:u.value.additionalNotes,system_info:u.value.systemInfo},automatic:!1,manual_report:!0,user_reported:!0};u.value.screenshot&&(c.error_context.screenshot_filename=u.value.screenshot.name,c.error_context.screenshot_size=u.value.screenshot.size);const C=await n.submitManualError(c);C&&(g.value=!0,l.value=C.tracking_id||C.id||"N/A",s(`Errore segnalato con successo! ${C!=null&&C.health_updated?"Metriche sistema aggiornate. ":""}Il nostro team lo analizzerà presto.`,"success"),o("submitted",C),setTimeout(()=>{_()},3e3))}catch(c){console.error("Error submitting manual error:",c),b.value=c.message||"Errore durante l'invio della segnalazione",s("Errore durante l'invio della segnalazione. Riprova più tardi.","error")}finally{p.value=!1}}}function m(c){c.key==="Escape"?_():c.key==="Enter"&&(c.ctrlKey||c.metaKey)&&k()}return te(()=>r.show,c=>{c?document.addEventListener("keydown",m):document.removeEventListener("keydown",m)}),$e(()=>{document.removeEventListener("keydown",m)}),(c,C)=>t.show?(i(),h("div",{key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",onClick:_},[a("div",{class:"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col",onClick:C[1]||(C[1]=ct(()=>{},["stop"]))},[a("div",Wo,[a("div",Qo,[a("div",Ko,[E(V,{name:"exclamation-triangle",size:"md",class:"text-red-600 dark:text-red-400"})]),C[2]||(C[2]=a("div",null,[a("h2",{class:"text-xl font-semibold text-gray-900 dark:text-white"}," Segnala Errore "),a("p",{class:"text-sm text-gray-600 dark:text-gray-400"}," Aiutaci a migliorare il sistema segnalando errori o problemi ")],-1))]),a("button",{onClick:_,class:"text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 transition-colors"},[E(V,{name:"x-mark",size:"md"})])]),a("div",Jo,[E(Go,{modelValue:u.value,"onUpdate:modelValue":[C[0]||(C[0]=Z=>u.value=Z),y]},null,8,["modelValue"])]),a("div",Yo,[a("div",Xo,[a("div",en,[p.value?(i(),h("div",tn,[E(V,{name:"arrow-path",size:"sm",class:"text-primary-600 dark:text-primary-400 animate-spin"}),C[3]||(C[3]=a("span",{class:"text-sm text-gray-600 dark:text-gray-400"},"Invio in corso...",-1))])):T("",!0),g.value?(i(),h("div",rn,[E(V,{name:"check-circle",size:"sm",class:"text-green-600 dark:text-green-400"}),a("span",an," Errore inviato con successo! ID: "+S(l.value),1)])):T("",!0),b.value?(i(),h("div",on,[E(V,{name:"exclamation-circle",size:"sm",class:"text-red-600 dark:text-red-400"}),a("span",nn,S(b.value),1)])):T("",!0)]),a("div",sn,[a("button",{onClick:_,type:"button",class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors"}," Annulla "),a("button",{onClick:k,disabled:!f.value||p.value,type:"button",class:"px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors"},[p.value?(i(),h("span",cn,[E(V,{name:"arrow-path",size:"sm",class:"animate-spin"}),C[4]||(C[4]=a("span",null,"Invio...",-1))])):(i(),h("span",un,[E(V,{name:"paper-airplane",size:"sm"}),C[5]||(C[5]=a("span",null,"Invia Segnalazione",-1))]))],8,ln)])])])])])):T("",!0)}},mn={class:"fixed bottom-4 right-4 z-40"},pn={class:"relative"},hn=["title"],gn={key:0,class:"absolute -top-1 -right-1 w-4 h-4 bg-yellow-500 text-white rounded-full flex items-center justify-center text-xs font-bold animate-pulse"},fn={class:"p-4 space-y-2"},vn=["onClick"],_n={class:"flex items-center space-x-3"},wn={class:"flex-1"},yn={class:"font-medium text-gray-900 dark:text-white"},bn={class:"text-sm text-gray-500 dark:text-gray-400"},xn={class:"p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900/50 space-y-3"},kn={__name:"ErrorReportWidget",setup(t){const e=fe(),r=mt(),{showToast:o}=ze(),n=q(!1),s=q(!1),u=[{value:"javascript_error",label:"Errore JavaScript",description:"Errore nell'applicazione o pagina non carica",icon:"code-bracket",color:"red"},{value:"ui_bug",label:"Bug Interfaccia",description:"Elementi non funzionano o non appaiono",icon:"eye-slash",color:"orange"},{value:"performance_issue",label:"Lentezza",description:"Pagina lenta o non risponde",icon:"clock",color:"yellow"},{value:"network_error",label:"Errore Rete",description:"Problemi di connessione o timeout",icon:"wifi",color:"blue"}],f=w(()=>!1);function p(){n.value=!n.value}function g(){s.value=!0,n.value=!1}async function b(k){try{const m={error_type:k.value,message:`Quick report: ${k.label}`,severity:"medium",error_context:{quick_report:!0,error_category:k.label,description:k.description,url:window.location.href,timestamp:new Date().toISOString(),user_agent:navigator.userAgent},automatic:!1,manual_report:!0,user_reported:!0},c=await r.submitManualError(m);o(`Errore "${k.label}" segnalato con successo! ${c!=null&&c.health_updated?"Metriche sistema aggiornate.":""}`,"success"),n.value=!1}catch(m){console.error("Error submitting quick report:",m),o("Errore durante l'invio della segnalazione. Riprova più tardi.","error")}}function l(){e.push("/app/help/dashboard"),n.value=!1}function y(k){o("Errore segnalato con successo! Riceverai presto una risposta.","success")}function x(k){n.value&&!k.target.closest(".fixed.bottom-4.right-4")&&(n.value=!1)}function _(k){k.altKey&&k.key==="e"&&(k.preventDefault(),p()),k.key==="Escape"&&n.value&&(n.value=!1)}return ce(()=>{document.addEventListener("click",x),document.addEventListener("keydown",_)}),$e(()=>{document.removeEventListener("click",x),document.removeEventListener("keydown",_)}),(k,m)=>(i(),h("div",mn,[a("div",pn,[a("button",{onClick:p,class:R(["w-14 h-14 bg-red-600 hover:bg-red-700 text-white rounded-full shadow-lg hover:shadow-xl transform transition-all duration-300 ease-in-out flex items-center justify-center group",n.value?"rotate-45":"hover:scale-110"]),title:n.value?"Chiudi":"Segnala Errore"},[E(V,{name:n.value?"x-mark":"exclamation-triangle",size:"md",className:n.value?"text-white":"text-white group-hover:animate-pulse"},null,8,["name","className"])],10,hn),f.value?(i(),h("div",gn," ! ")):T("",!0)]),n.value?(i(),h("div",{key:0,class:R(["absolute bottom-16 right-0 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 transform transition-all duration-300 ease-in-out",n.value?"scale-100 opacity-100":"scale-95 opacity-0"])},[m[2]||(m[2]=a("div",{class:"p-4 border-b border-gray-200 dark:border-gray-700"},[a("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"}," Segnala Errore "),a("p",{class:"text-sm text-gray-600 dark:text-gray-400 mt-1"}," Seleziona il tipo di problema riscontrato ")],-1)),a("div",fn,[(i(),h(ae,null,ie(u,c=>a("button",{key:c.value,onClick:C=>b(c),class:"w-full p-3 text-left rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors group"},[a("div",_n,[a("div",{class:R(["p-2 rounded-lg",`bg-${c.color}-100 dark:bg-${c.color}-900/20`])},[E(V,{name:c.icon,size:"sm",className:`text-${c.color}-600 dark:text-${c.color}-400`},null,8,["name","className"])],2),a("div",wn,[a("div",yn,S(c.label),1),a("div",bn,S(c.description),1)]),E(V,{name:"chevron-right",size:"sm",class:"text-gray-400 dark:text-gray-500 group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors"})])],8,vn)),64))]),a("div",xn,[a("button",{onClick:g,class:"w-full flex items-center justify-center space-x-2 px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"},[E(V,{name:"document-text",size:"sm"}),m[1]||(m[1]=a("span",null,"Segnalazione Dettagliata",-1))]),a("div",{class:"flex items-center space-x-2"},[a("button",{onClick:p,class:"flex-1 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"}," Chiudi "),a("button",{onClick:l,class:"flex-1 px-4 py-2 text-sm font-medium text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800 rounded-lg hover:bg-primary-100 dark:hover:bg-primary-900/30 transition-colors"}," Help Center ")])])],2)):T("",!0),E(dn,{show:s.value,onClose:m[0]||(m[0]=c=>s.value=!1),onSubmitted:y},null,8,["show"])]))}},En={class:"flex items-center"},An={class:"flex-shrink-0 mr-4"},Pn={class:"flex-1"},Cn={__name:"Toast",props:{type:{type:String,default:"success",validator:t=>["success","error","warning","info"].includes(t)},title:{type:String,required:!0},message:{type:String,default:""},duration:{type:Number,default:5e3},closable:{type:Boolean,default:!0}},emits:["close"],setup(t,{expose:e,emit:r}){const o=t,n=r,s=q(!1);let u=null;const f={success:"bg-green-600 border-l-4 border-green-800",error:"bg-red-600 border-l-4 border-red-800",warning:"bg-yellow-500 border-l-4 border-yellow-700",info:"bg-primary-600 border-l-4 border-primary-800"},p=w(()=>{const y={success:"check-circle",error:"x-circle",warning:"exclamation-triangle",info:"information-circle"};return y[o.type]||y.success}),g=w(()=>{const y={success:"text-white",error:"text-white",warning:"text-gray-900",info:"text-white"};return y[o.type]||y.success}),b=()=>{s.value=!0,o.duration>0&&(u=setTimeout(()=>{l()},o.duration))},l=()=>{s.value=!1,u&&(clearTimeout(u),u=null),setTimeout(()=>{n("close")},300)};return ce(()=>{setTimeout(b,10)}),$e(()=>{u&&clearTimeout(u)}),e({show:b,close:l}),(y,x)=>(i(),U(yt,{to:"body"},[s.value?(i(),h("div",{key:0,class:R([["fixed top-4 right-4 z-50 p-6 rounded-lg shadow-2xl transition-all duration-300 transform",f[t.type]||f.success,s.value?"translate-x-0 opacity-100":"translate-x-full opacity-0"],"backdrop-blur-sm"]),style:{"min-width":"300px","max-width":"500px","box-shadow":"0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1)"}},[a("div",En,[a("div",An,[E(V,{name:p.value,size:"lg",variant:"solid"},null,8,["name"])]),a("div",Pn,[a("p",{class:R([g.value,"text-lg font-semibold"]),style:Re({color:o.type==="warning"?"#1f2937":"#ffffff"})},S(t.title),7),t.message?(i(),h("p",{key:0,class:R([g.value,"text-base mt-1 leading-relaxed opacity-90"]),style:Re({color:o.type==="warning"?"#1f2937":"#ffffff"})},S(t.message),7)):T("",!0)]),t.closable?(i(),h("button",{key:0,onClick:l,class:R([g.value,"flex-shrink-0 ml-3 opacity-70 hover:opacity-100 transition-opacity"])},[E(V,{name:"close",size:"sm"})],2)):T("",!0)])],2)):T("",!0)]))}},qn={class:"h-screen flex bg-gray-50 dark:bg-gray-900"},$n={class:"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900"},In={class:"py-6"},Mn={class:"px-4 sm:px-6 lg:px-8"},Tn={key:0,class:"mb-6"},Sn={key:1,class:"flex items-center justify-center h-64"},Fn={id:"toast-container",class:"fixed top-4 right-4 z-50 space-y-4"},Vn={__name:"AppLayout",setup(t){const e=qe(),r=fe(),o=be(),{toasts:n,removeToast:s}=ze(),u=q(!1),f=q(!1),p=q(!1);w(()=>o.config||{});const g=w(()=>o.config!==null),b=w(()=>{var C;if((C=e.meta)!=null&&C.title)return e.meta.title;const c=e.path;return c.startsWith("/app/dashboard")?"Dashboard":c.startsWith("/app/personnel")?"Personale":c.startsWith("/app/timesheet")||c.startsWith("/app/projects")?"Attività":c.startsWith("/app/crm")||c.startsWith("/app/invoicing")?"Sales":c.startsWith("/app/business-intelligence")?"Business Intelligence":c.startsWith("/app/funding")?"Bandi":c.startsWith("/app/communications")?"Comunicazioni":c.startsWith("/app/compliance")?"Certificazioni":c.startsWith("/app/ceo")?"CEO":c.startsWith("/app/agents")?"Agenti":c.startsWith("/app/admin")?"Amministrazione":c.startsWith("/app/examples")?"Design System":c.startsWith("/app/profile")?"Profilo":"Portale"}),l=w(()=>{var c;return(c=e.meta)!=null&&c.breadcrumbs?e.meta.breadcrumbs.map(C=>({label:C.label,to:C.to,icon:C.icon})):[]}),y=w(()=>{var c;return((c=e.meta)==null?void 0:c.hasActions)||!1});function x(){u.value=!u.value}function _(){u.value=!1}function k(c){f.value=c}function m(){r.push("/app/projects/create")}return ce(()=>{o.loadConfig()}),te(e,()=>{p.value=!0,setTimeout(()=>{p.value=!1},300)}),te(e,()=>{_()}),ce(()=>{g.value||o.loadConfig()}),(c,C)=>{const Z=re("router-view");return i(),h("div",qn,[u.value?(i(),h("div",{key:0,onClick:_,class:"fixed inset-0 z-20 bg-black bg-opacity-50 lg:hidden"})):T("",!0),E(ta,{"is-mobile-open":u.value,onClose:_,onToggleCollapsed:k},null,8,["is-mobile-open"]),a("div",{class:R(["flex flex-col flex-1 overflow-hidden transition-all duration-300",[f.value?"lg:ml-20":"lg:ml-64"]])},[E(lo,{"page-title":b.value,breadcrumbs:l.value,onToggleMobileSidebar:x,onQuickCreateProject:m},null,8,["page-title","breadcrumbs"]),a("main",$n,[a("div",In,[a("div",Mn,[y.value?(i(),h("div",Tn,[bt(c.$slots,"page-actions")])):T("",!0),p.value?(i(),h("div",Sn,[E(co)])):(i(),U(Z,{key:2}))])])])],2),E(bo),a("div",Fn,[(i(!0),h(ae,null,ie(H(n),$=>(i(),U(Cn,{key:$.id,type:$.type,title:$.message,closable:!0,duration:$.duration,onClose:I=>H(s)($.id)},null,8,["type","title","duration","onClose"]))),128))]),E(kn)])}}},O=Le({necessary:!0,preferences:!1,analytics:!1,marketing:!1,consentGiven:!1,consentDate:null,showBanner:!1,showSettings:!1}),Ve="cookie-consent",De="cookie-consent-date",Dn={necessary:{name:"Necessari",description:"Cookie tecnici essenziali per il funzionamento del sito",required:!0,color:"green"},preferences:{name:"Preferenze",description:"Cookie per ricordare le tue preferenze e personalizzare l'esperienza",required:!1,color:"blue"},analytics:{name:"Analitici",description:"Cookie per analizzare l'utilizzo del sito e migliorare le performance",required:!1,color:"yellow"},marketing:{name:"Marketing",description:"Cookie per pubblicità personalizzata e remarketing",required:!1,color:"purple"}};function pt(){const t=()=>{const I={necessary:O.necessary,preferences:O.preferences,analytics:O.analytics,marketing:O.marketing,consentGiven:!0,consentDate:new Date().toISOString()};localStorage.setItem(Ve,JSON.stringify(I)),localStorage.setItem(De,I.consentDate),Object.assign(O,I)},e=()=>{try{const I=localStorage.getItem(Ve),B=localStorage.getItem(De);if(I&&B){const le=JSON.parse(I),ee=new Date(B),ue=new Date,D=new Date(ue.setMonth(ue.getMonth()-12));if(ee>D)return Object.assign(O,{...le,consentDate:B,showBanner:!1}),r(),!0}}catch(I){console.error("Errore nel caricamento del consenso cookie:",I)}return O.showBanner=!0,!1},r=()=>{O.analytics?o():n(),O.marketing?s():u(),O.preferences||f()},o=()=>{typeof gtag<"u"&&gtag("consent","update",{analytics_storage:"granted"})},n=()=>{typeof gtag<"u"&&gtag("consent","update",{analytics_storage:"denied"})},s=()=>{typeof gtag<"u"&&gtag("consent","update",{ad_storage:"granted"})},u=()=>{typeof gtag<"u"&&gtag("consent","update",{ad_storage:"denied"})},f=()=>{document.cookie.split(";").forEach(function(I){document.cookie=I.replace(/^ +/,"").replace(/=.*/,"=;expires="+new Date().toUTCString()+";path=/")})},p=()=>{O.preferences=!0,O.analytics=!0,O.marketing=!0,t(),r(),y()},g=()=>{O.preferences=!1,O.analytics=!1,O.marketing=!1,t(),r(),y()},b=()=>{t(),r(),_(),y()},l=()=>{O.showBanner=!0},y=()=>{O.showBanner=!1},x=()=>{O.showSettings=!0},_=()=>{O.showSettings=!1},k=()=>{O.showSettings=!O.showSettings},m=I=>O[I]===!0,c=()=>!O.consentGiven||O.showBanner,C=()=>{localStorage.removeItem(Ve),localStorage.removeItem(De),Object.assign(O,{necessary:!0,preferences:!1,analytics:!1,marketing:!1,consentGiven:!1,consentDate:null,showBanner:!0,showSettings:!1})},Z=w(()=>({necessary:O.necessary,preferences:O.preferences,analytics:O.analytics,marketing:O.marketing,consentGiven:O.consentGiven,consentDate:O.consentDate})),$=w(()=>({showBanner:O.showBanner,showSettings:O.showSettings}));return{cookieConsent:xt(O),consentStatus:Z,uiState:$,COOKIE_CATEGORIES:Dn,loadConsent:e,saveConsent:t,applyCookieSettings:r,acceptAll:p,acceptNecessaryOnly:g,saveCustomSettings:b,resetConsent:C,showBanner:l,hideBanner:y,showSettings:x,hideSettings:_,toggleSettings:k,hasConsent:m,isConsentRequired:c}}const Rn={key:0,class:"fixed bottom-0 left-0 right-0 z-50 bg-white border-t-2 border-gray-200 shadow-lg",role:"banner","aria-label":"Cookie consent banner"},Ln={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4"},jn={class:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4"},On={class:"flex-1"},zn={class:"flex items-start"},Nn={class:"flex flex-wrap gap-4 mt-3 text-sm"},Bn={class:"flex flex-col sm:flex-row gap-3 lg:flex-shrink-0"},Hn={key:1,class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"cookie-settings-title",role:"dialog","aria-modal":"true"},Un={class:"flex min-h-full items-center justify-center p-4"},Zn={class:"relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden"},Gn={class:"px-6 py-4 border-b border-gray-200"},Wn={class:"flex items-center justify-between"},Qn={class:"px-6 py-4 max-h-96 overflow-y-auto"},Kn={class:"space-y-6"},Jn={class:"flex items-start justify-between"},Yn={class:"flex-1"},Xn={class:"flex items-center mb-2"},es={class:"text-lg font-medium text-gray-900"},ts={key:0,class:"ml-2 bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full"},rs={class:"text-sm text-gray-600"},as={class:"ml-4"},os={class:"relative inline-flex items-center cursor-pointer"},ns=["checked","disabled","onChange"],ss={class:"px-6 py-4 border-t border-gray-200 bg-gray-50"},is={class:"flex flex-col sm:flex-row gap-3 justify-end"},ls={__name:"CookieBanner",setup(t){const{cookieConsent:e,uiState:r,COOKIE_CATEGORIES:o,acceptAll:n,acceptNecessaryOnly:s,saveCustomSettings:u,showSettings:f,hideSettings:p}=pt(),g=(b,l)=>{o[b].required||(e[b]=l)};return(b,l)=>{const y=re("router-link");return i(),h(ae,null,[H(r).showBanner?(i(),h("div",Rn,[a("div",Ln,[a("div",jn,[a("div",On,[a("div",zn,[E(V,{name:"information-circle",class:"w-6 h-6 text-primary-600 mt-1 mr-3 flex-shrink-0"}),a("div",null,[l[9]||(l[9]=a("h3",{class:"text-lg font-semibold text-gray-900 mb-2"}," Utilizziamo i Cookie ",-1)),l[10]||(l[10]=a("p",{class:"text-sm text-gray-700 leading-relaxed"}," Utilizziamo cookie tecnici per garantire il funzionamento del sito e cookie opzionali per migliorare la tua esperienza. Puoi accettare tutti i cookie, solo quelli necessari, o personalizzare le tue preferenze. ",-1)),a("div",Nn,[E(y,{to:"/privacy",class:"text-primary-600 hover:text-primary-700 underline",target:"_blank"},{default:z(()=>l[7]||(l[7]=[N(" Privacy Policy ")])),_:1,__:[7]}),E(y,{to:"/cookie-policy",class:"text-primary-600 hover:text-primary-700 underline",target:"_blank"},{default:z(()=>l[8]||(l[8]=[N(" Cookie Policy ")])),_:1,__:[8]})])])])]),a("div",Bn,[a("button",{onClick:l[0]||(l[0]=(...x)=>H(f)&&H(f)(...x)),class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors"},[E(V,{name:"cog",class:"w-4 h-4 inline mr-2"}),l[11]||(l[11]=N(" Personalizza "))]),a("button",{onClick:l[1]||(l[1]=(...x)=>H(s)&&H(s)(...x)),class:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors"}," Solo Necessari "),a("button",{onClick:l[2]||(l[2]=(...x)=>H(n)&&H(n)(...x)),class:"px-6 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors"},[E(V,{name:"check",class:"w-4 h-4 inline mr-2"}),l[12]||(l[12]=N(" Accetta Tutti "))])])])])])):T("",!0),H(r).showSettings?(i(),h("div",Hn,[a("div",{class:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:l[3]||(l[3]=(...x)=>H(p)&&H(p)(...x))}),a("div",Un,[a("div",Zn,[a("div",Gn,[a("div",Wn,[l[13]||(l[13]=a("h2",{id:"cookie-settings-title",class:"text-xl font-semibold text-gray-900"}," Impostazioni Cookie ",-1)),a("button",{onClick:l[4]||(l[4]=(...x)=>H(p)&&H(p)(...x)),class:"text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500 rounded-md p-1"},[E(V,{name:"x-mark",class:"w-6 h-6"})])])]),a("div",Qn,[l[14]||(l[14]=a("p",{class:"text-sm text-gray-600 mb-6"}," Gestisci le tue preferenze sui cookie. I cookie necessari sono sempre attivi per garantire il funzionamento del sito. ",-1)),a("div",Kn,[(i(!0),h(ae,null,ie(H(o),(x,_)=>(i(),h("div",{key:_,class:"border border-gray-200 rounded-lg p-4"},[a("div",Jn,[a("div",Yn,[a("div",Xn,[a("div",{class:R(`w-3 h-3 rounded-full mr-3 bg-${x.color}-500`)},null,2),a("h3",es,S(x.name),1),x.required?(i(),h("span",ts," Necessari ")):T("",!0)]),a("p",rs,S(x.description),1)]),a("div",as,[a("label",os,[a("input",{type:"checkbox",checked:H(e)[_],disabled:x.required,onChange:k=>g(_,k.target.checked),class:"sr-only"},null,40,ns),a("div",{class:R(["w-11 h-6 rounded-full transition-colors duration-200 ease-in-out",H(e)[_]?"bg-primary-600":"bg-gray-200",x.required?"opacity-50 cursor-not-allowed":"cursor-pointer"])},[a("div",{class:R(["w-5 h-5 bg-white rounded-full shadow-md transform transition-transform duration-200 ease-in-out",H(e)[_]?"translate-x-5":"translate-x-0"])},null,2)],2)])])])]))),128))])]),a("div",ss,[a("div",is,[a("button",{onClick:l[5]||(l[5]=(...x)=>H(p)&&H(p)(...x)),class:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors"}," Annulla "),a("button",{onClick:l[6]||(l[6]=(...x)=>H(u)&&H(u)(...x)),class:"px-6 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors"},[E(V,{name:"check",class:"w-4 h-4 inline mr-2"}),l[15]||(l[15]=N(" Salva Preferenze "))])])])])])])):T("",!0)],64)}}},cs=xe(ls,[["__scopeId","data-v-516fe8dc"]]),us={class:"min-h-screen bg-gray-50"},ds={class:"bg-white shadow-sm border-b"},ms={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},ps={class:"flex justify-between h-16"},hs={class:"flex items-center"},gs={class:"text-xl font-semibold text-gray-900"},fs={class:"hidden md:flex items-center space-x-8"},vs={class:"flex items-center space-x-4 ml-8 pl-8 border-l border-gray-200"},_s={class:"md:hidden flex items-center"},ws={key:0,class:"md:hidden"},ys={class:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t"},bs={class:"flex-1"},xs={class:"bg-gray-900 text-white"},ks={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12"},Es={class:"grid grid-cols-1 md:grid-cols-4 gap-8"},As={class:"md:col-span-2"},Ps={class:"text-lg font-semibold mb-4"},Cs={class:"text-gray-300 mb-4"},qs={class:"flex space-x-4"},$s=["href"],Is=["href"],Ms={class:"space-y-2"},Ts={class:"space-y-2"},Ss={class:"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400"},Ee={__name:"PublicLayout",setup(t){const e=be(),{loadConsent:r,showSettings:o}=pt(),n=q(!1),s=w(()=>e.config||{}),u=w(()=>s.value.company||{}),f=w(()=>s.value.contact||{}),p=w(()=>{var x;return((x=s.value.company)==null?void 0:x.name)||"DatVinci"}),g=w(()=>{try{const x=localStorage.getItem("user");return!!(x&&x!=="null"&&x!=="undefined")}catch(x){return console.warn("Auth check error in PublicLayout:",x),!1}}),b=w(()=>g.value?"Dashboard":"Accedi"),l=w(()=>g.value?"/app":"/auth/login"),y=()=>{o()};return ce(()=>{e.loadConfig(),r()}),(x,_)=>{const k=re("router-link"),m=re("router-view");return i(),h("div",us,[a("nav",ds,[a("div",ms,[a("div",ps,[a("div",hs,[E(k,{to:"/",class:"flex items-center space-x-3"},{default:z(()=>[E(je,{variant:"primary",size:"md"}),a("span",gs,S(p.value),1)]),_:1})]),a("div",fs,[E(k,{to:"/",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:z(()=>_[1]||(_[1]=[N(" Home ")])),_:1,__:[1]}),E(k,{to:"/about",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:z(()=>_[2]||(_[2]=[N(" Chi Siamo ")])),_:1,__:[2]}),E(k,{to:"/services",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:z(()=>_[3]||(_[3]=[N(" Servizi ")])),_:1,__:[3]}),E(k,{to:"/careers",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:z(()=>_[4]||(_[4]=[N(" Posizioni Aperte ")])),_:1,__:[4]}),E(k,{to:"/contact",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:z(()=>_[5]||(_[5]=[N(" Contatti ")])),_:1,__:[5]}),a("div",vs,[E(k,{to:l.value,class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:z(()=>[N(S(b.value),1)]),_:1},8,["to"])])]),a("div",_s,[a("button",{onClick:_[0]||(_[0]=c=>n.value=!n.value),class:"text-gray-400 hover:text-gray-500"},[E(V,{name:"bars-3",size:"md"})])])])]),n.value?(i(),h("div",ws,[a("div",ys,[E(k,{to:"/",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:z(()=>_[6]||(_[6]=[N(" Home ")])),_:1,__:[6]}),E(k,{to:"/about",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:z(()=>_[7]||(_[7]=[N(" Chi Siamo ")])),_:1,__:[7]}),E(k,{to:"/services",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:z(()=>_[8]||(_[8]=[N(" Servizi ")])),_:1,__:[8]}),E(k,{to:"/careers",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:z(()=>_[9]||(_[9]=[N(" Posizioni Aperte ")])),_:1,__:[9]}),E(k,{to:"/contact",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:z(()=>_[10]||(_[10]=[N(" Contatti ")])),_:1,__:[10]}),_[11]||(_[11]=a("hr",{class:"my-2"},null,-1)),E(k,{to:l.value,class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:z(()=>[N(S(b.value),1)]),_:1},8,["to"])])])):T("",!0)]),a("main",bs,[E(m)]),a("footer",xs,[a("div",ks,[a("div",Es,[a("div",As,[a("h3",Ps,S(u.value.name),1),a("p",Cs,S(u.value.description),1),a("div",qs,[f.value.email?(i(),h("a",{key:0,href:`mailto:${f.value.email}`,class:"text-gray-300 hover:text-white"},[E(V,{name:"envelope",class:"w-5 h-5"})],8,$s)):T("",!0),f.value.phone?(i(),h("a",{key:1,href:`tel:${f.value.phone}`,class:"text-gray-300 hover:text-white"},[E(V,{name:"phone",class:"w-5 h-5"})],8,Is)):T("",!0)])]),a("div",null,[_[17]||(_[17]=a("h3",{class:"text-lg font-semibold mb-4"},"Link Utili",-1)),a("ul",Ms,[a("li",null,[E(k,{to:"/",class:"text-gray-300 hover:text-white"},{default:z(()=>_[12]||(_[12]=[N("Home")])),_:1,__:[12]})]),a("li",null,[E(k,{to:"/about",class:"text-gray-300 hover:text-white"},{default:z(()=>_[13]||(_[13]=[N("Chi Siamo")])),_:1,__:[13]})]),a("li",null,[E(k,{to:"/services",class:"text-gray-300 hover:text-white"},{default:z(()=>_[14]||(_[14]=[N("Servizi")])),_:1,__:[14]})]),a("li",null,[E(k,{to:"/careers",class:"text-gray-300 hover:text-white"},{default:z(()=>_[15]||(_[15]=[N("Posizioni Aperte")])),_:1,__:[15]})]),a("li",null,[E(k,{to:"/contact",class:"text-gray-300 hover:text-white"},{default:z(()=>_[16]||(_[16]=[N("Contatti")])),_:1,__:[16]})])])]),a("div",null,[_[20]||(_[20]=a("h3",{class:"text-lg font-semibold mb-4"},"Informazioni Legali",-1)),a("ul",Ts,[a("li",null,[E(k,{to:"/privacy",class:"text-gray-300 hover:text-white"},{default:z(()=>_[18]||(_[18]=[N("Privacy Policy")])),_:1,__:[18]})]),a("li",null,[E(k,{to:"/cookie-policy",class:"text-gray-300 hover:text-white"},{default:z(()=>_[19]||(_[19]=[N("Cookie Policy")])),_:1,__:[19]})]),a("li",null,[a("button",{onClick:y,class:"text-gray-300 hover:text-white text-left"}," Gestisci Cookie ")])])])]),a("div",Ss,[a("p",null,"© "+S(new Date().getFullYear())+" "+S(u.value.name)+". Tutti i diritti riservati.",1)])])]),E(cs)])}}},Fs=()=>d(()=>import("./Home.js"),__vite__mapDeps([5,1])),Vs=()=>d(()=>import("./About.js"),__vite__mapDeps([6,1])),Ds=()=>d(()=>import("./Contact.js"),__vite__mapDeps([7,1])),Rs=()=>d(()=>import("./Services.js"),__vite__mapDeps([8,1])),Ls=()=>d(()=>import("./Privacy.js"),__vite__mapDeps([9,1])),js=()=>d(()=>import("./CookiePolicy.js"),__vite__mapDeps([10,1])),nt=()=>d(()=>import("./Careers.js"),__vite__mapDeps([11,1,12])),Os=()=>d(()=>import("./Login.js"),__vite__mapDeps([13,1,14])),zs=()=>d(()=>import("./Register.js"),__vite__mapDeps([15,1])),Ns=()=>d(()=>import("./Dashboard.js"),__vite__mapDeps([16,1,17,18,19])),Bs=()=>d(()=>import("./Projects.js"),__vite__mapDeps([20,1,21,14,22,23,24,25,26])),st=()=>d(()=>import("./ErrorPage.js"),__vite__mapDeps([27,1,14])),Hs=[{path:"/",component:Ee,children:[{path:"",name:"home",component:Fs},{path:"about",name:"about",component:Vs},{path:"contact",name:"contact",component:Ds},{path:"services",name:"services",component:Rs},{path:"careers",name:"careers",component:nt},{path:"posizioni-aperte",name:"careers-alt",component:nt},{path:"privacy",name:"privacy",component:Ls},{path:"cookie-policy",name:"cookie-policy",component:js}]},{path:"/error",component:Ee,children:[{path:":code?",name:"error",component:st,props:!0}]},{path:"/auth",component:Ee,children:[{path:"login",name:"login",component:Os},{path:"register",name:"register",component:zs}]},{path:"/app",component:Vn,meta:{requiresAuth:!0},children:[{path:"",redirect:"/app/dashboard"},{path:"dashboard",name:"dashboard",component:Ns,meta:{requiresAuth:!0,requiredPermission:"view_dashboard",requiresFeature:"dashboard_module"}},{path:"projects",name:"projects",component:Bs,meta:{requiresAuth:!0,requiredPermission:"view_all_projects",requiresFeature:"timesheet_module"}},{path:"projects/create",name:"projects-create",component:()=>d(()=>import("./ProjectCreate.js"),__vite__mapDeps([28,1])),meta:{requiresAuth:!0,requiredPermission:"edit_project",requiresFeature:"timesheet_module"}},{path:"projects/:id",name:"project-view",component:()=>d(()=>import("./ProjectView.js"),__vite__mapDeps([29,1,21,14,22,23,4,30])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects",requiresFeature:"timesheet_module"}},{path:"projects/:id/edit",name:"project-edit",component:()=>d(()=>import("./ProjectEdit.js"),__vite__mapDeps([31,1])),meta:{requiresAuth:!0,requiredPermission:"edit_project",requiresFeature:"timesheet_module"}},{path:"timesheet",redirect:"/app/timesheet/entry"},{path:"timesheet/entry",name:"timesheet-entry",component:()=>d(()=>import("./TimesheetEntry.js"),__vite__mapDeps([32,1,25,33,34,35,36])),meta:{requiresAuth:!0,requiredPermission:"manage_timesheets",requiresFeature:"timesheet_module"}},{path:"timesheet/requests",name:"timesheet-requests",component:()=>d(()=>import("./TimesheetRequests.js"),__vite__mapDeps([37,1,36,38,14,23])),meta:{requiresAuth:!0,requiredPermission:"manage_timesheets",requiresFeature:"timesheet_module"}},{path:"timesheet/dashboard",name:"timesheet-dashboard",component:()=>d(()=>import("./TimesheetDashboard.js"),__vite__mapDeps([39,1,25,40,41,42,33,14,34,35,36,43,44])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects",requiresFeature:"timesheet_module"}},{path:"timesheet/analytics",name:"timesheet-analytics",component:()=>d(()=>import("./TimesheetAnalytics.js"),__vite__mapDeps([45,25,1,40,26,46,47,33,36,48])),meta:{requiresAuth:!0,requiredPermission:"admin_access",requiresFeature:"timesheet_module"}},{path:"communications",redirect:"/app/communications/dashboard"},{path:"communications/dashboard",name:"communications-dashboard",component:()=>d(()=>import("./CommunicationDashboard.js"),__vite__mapDeps([49,1,50,47,17,18,22,23,51])),meta:{requiresAuth:!0,requiredPermission:"view_communications",requiresFeature:"communications_module"}},{path:"communications/forum",name:"communications-forum",component:()=>d(()=>import("./ForumIndex.js"),__vite__mapDeps([52,1,50,47,53,54,55,14,24,23,56,57,58])),meta:{requiresAuth:!0,requiredPermission:"view_communications",requiresFeature:"communications_module"}},{path:"communications/forum/topics/:id",name:"communications-topic",component:()=>d(()=>import("./TopicView.js"),__vite__mapDeps([59,1,50,47,56,57,60])),meta:{requiresAuth:!0,requiredPermission:"view_communications",requiresFeature:"communications_module"}},{path:"communications/polls",name:"communications-polls",component:()=>d(()=>import("./PollsIndex.js"),__vite__mapDeps([61,1,50,47,53,54,55,22,23,62,63,57,64])),meta:{requiresAuth:!0,requiredPermission:"view_communications",requiresFeature:"communications_module"}},{path:"communications/polls/:id",name:"communications-poll",component:()=>d(()=>import("./PollView.js"),__vite__mapDeps([65,1,50,47,22,23,62,63,57,66])),meta:{requiresAuth:!0,requiredPermission:"view_communications",requiresFeature:"communications_module"}},{path:"communications/messages",name:"communications-messages",component:()=>d(()=>import("./MessagesIndex.js"),__vite__mapDeps([67,1,50,47,53,54,55,38,14,23,68,24,57,69])),meta:{requiresAuth:!0,requiredPermission:"view_communications",requiresFeature:"communications_module"}},{path:"communications/events",name:"communications-events",component:()=>d(()=>import("./EventsIndex.js"),__vite__mapDeps([70,1,50,47,53,54,55,14,22,23,71,72,57,73])),meta:{requiresAuth:!0,requiredPermission:"view_communications",requiresFeature:"communications_module"}},{path:"communications/events/:id",name:"communications-event-view",component:()=>d(()=>import("./EventView.js"),__vite__mapDeps([74,1,50,47,71,72,57])),meta:{requiresAuth:!0,requiredPermission:"view_communications",requiresFeature:"communications_module"}},{path:"communications/news",name:"communications-news",component:()=>d(()=>import("./NewsIndex.js"),__vite__mapDeps([75,1,50,47,53,54,55,14,22,23,76,33,77,57,78])),meta:{requiresAuth:!0,requiredPermission:"view_communications",requiresFeature:"communications_module"}},{path:"communications/news/:id",name:"communications-news-view",component:()=>d(()=>import("./NewsView.js"),__vite__mapDeps([79,1,50,47,22,23,80])),meta:{requiresAuth:!0,requiredPermission:"view_communications",requiresFeature:"communications_module"}},{path:"communications/hr-assistant",name:"hr-assistant-chat",component:()=>d(()=>import("./HRAssistantChat.js"),__vite__mapDeps([81,1,82])),meta:{requiresAuth:!0,requiredPermission:"view_communications",requiresFeature:"communications_module"}},{path:"communications/hr-knowledge-base",name:"hr-knowledge-base",component:()=>d(()=>import("./HRKnowledgeBase.js"),__vite__mapDeps([83,1,82,54,55])),meta:{requiresAuth:!0,requiredPermission:"view_communications",requiresFeature:"communications_module"}},{path:"examples/dashboard",name:"dashboard-example",component:()=>d(()=>import("./DashboardExample.js"),__vite__mapDeps([84,17,1,18,85])),meta:{requiresAuth:!0,requiredPermission:"view_dashboard"}},{path:"examples/timesheet-grid",name:"timesheet-grid-example",component:()=>d(()=>import("./TimesheetGridExample.js"),__vite__mapDeps([86,34,1,35])),meta:{requiresAuth:!0,requiredPermission:"view_dashboard"}},{path:"examples/components",name:"components-example",component:()=>d(()=>import("./ComponentsExample.js"),__vite__mapDeps([87,25,1,40,33,88,44,76,77,89,90,91,92,93])),meta:{requiresAuth:!0,requiredPermission:"view_dashboard"}},{path:"examples/form-builder",name:"form-builder-example",component:()=>d(()=>import("./FormBuilderExample.js"),__vite__mapDeps([94,76,1,33,77,95])),meta:{requiresAuth:!0,requiredPermission:"view_dashboard"}},{path:"examples/view-mode-toggle",name:"view-mode-toggle-example",component:()=>d(()=>import("./ViewModeToggleExample.js"),__vite__mapDeps([96,89,1,90,97])),meta:{requiresAuth:!0,requiredPermission:"view_dashboard"}},{path:"examples/kanban",name:"kanban-example",component:()=>d(()=>import("./KanbanExample.js"),__vite__mapDeps([98,1,25,91])),meta:{requiresAuth:!0,requiredPermission:"view_dashboard"}},{path:"examples/proposal-card",name:"proposal-card-example",component:()=>d(()=>import("./ProposalCardExample.js"),__vite__mapDeps([99,1,25,92,93])),meta:{requiresAuth:!0,requiredPermission:"view_dashboard"}},{path:"examples/icon-system",name:"icon-system-example",component:()=>d(()=>import("./IconSystemExample.js"),__vite__mapDeps([100,1])),meta:{requiresAuth:!0,requiredPermission:"view_dashboard"}},{path:"examples/wizard-container",name:"wizard-container-example",component:()=>d(()=>import("./WizardContainerExample.js"),__vite__mapDeps([101,1,102,103,104])),meta:{requiresAuth:!0,requiredPermission:"view_dashboard"}},{path:"personnel",redirect:"/app/personnel/admin"},{path:"personnel/orgchart",name:"personnel-orgchart",component:()=>d(()=>import("./PersonnelOrgChart.js"),__vite__mapDeps([105,1,25,89,90,40,106])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data",requiresFeature:"personnel_module"}},{path:"personnel/skills",name:"personnel-skills",component:()=>d(()=>import("./SkillsMatrix.js"),__vite__mapDeps([107,1,25,26,40,33,108])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data",requiresFeature:"personnel_module"}},{path:"personnel/job-levels",name:"personnel-job-levels",component:()=>d(()=>import("./JobLevels.js"),__vite__mapDeps([109,1,53,54,55,38,14,23,110])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data",requiresFeature:"personnel_module"}},{path:"personnel/inquadramenti",redirect:"/app/personnel/job-levels"},{path:"personnel/departments",redirect:"/app/personnel/admin"},{path:"personnel/departments/create",name:"department-create",component:()=>d(()=>import("./DepartmentCreate.js"),__vite__mapDeps([111,1,25,76,33,77])),meta:{requiresAuth:!0,requiredPermission:"manage_users",requiresFeature:"personnel_module"}},{path:"personnel/departments/:id",name:"department-view",component:()=>d(()=>import("./DepartmentView.js"),__vite__mapDeps([112,1,43,25,41,42,23])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data",requiresFeature:"personnel_module"}},{path:"personnel/departments/:id/edit",name:"department-edit",component:()=>d(()=>import("./DepartmentEdit.js"),__vite__mapDeps([113,1,25,76,33,77,23])),meta:{requiresAuth:!0,requiredPermission:"manage_users",requiresFeature:"personnel_module"}},{path:"personnel/allocation",name:"personnel-allocation",component:()=>d(()=>import("./PersonnelAllocation.js"),__vite__mapDeps([114,25,1,40,26,46,47,50])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data",requiresFeature:"personnel_module"}},{path:"personnel/admin",name:"personnel-admin",component:()=>d(()=>import("./PersonnelAdmin.js"),__vite__mapDeps([115,14,1,25,33,88,44,54,55,38,23,116])),meta:{requiresAuth:!0,requiredPermission:"admin_access",requiresFeature:"personnel_module"}},{path:"personnel/performance",name:"personnel-performance",component:()=>d(()=>import("./PersonnelPerformance.js"),__vite__mapDeps([117,1,118,43,57,22,23,14,119,17,18,88,44,46,47,120,40,121,122,123,124])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data",requiresFeature:"personnel_module"}},{path:"personnel/performance/reviews",name:"personnel-performance-reviews",component:()=>d(()=>import("./PersonnelPerformanceReviews.js"),__vite__mapDeps([125,1,118,43,57,22,23,14,119,53,54,55,120,40,121,122,123])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data",requiresFeature:"personnel_module"}},{path:"personnel/performance/reviews/:id",name:"personnel-performance-review-detail",component:()=>d(()=>import("./PersonnelPerformanceReviewDetail.js"),__vite__mapDeps([126,1,118,43,57,22,23,14,119])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data",requiresFeature:"personnel_module"}},{path:"personnel/:id",name:"personnel-profile",component:()=>d(()=>import("./PersonnelProfile.js"),__vite__mapDeps([127,1,43,24,23,14,41,42,54,55,22,128])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data",requiresFeature:"personnel_module"}},{path:"personnel/:id/performance/:year",name:"personnel-performance-personal",component:()=>d(()=>import("./PersonnelPerformancePersonal.js"),__vite__mapDeps([129,1,25,88,44,14,22,23,57,130,131])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data",requiresFeature:"personnel_module"}},{path:"recruiting",redirect:"/app/recruiting/dashboard"},{path:"recruiting/dashboard",name:"recruiting-dashboard",component:()=>d(()=>import("./RecruitingDashboard.js"),__vite__mapDeps([132,133,1,14,134])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data",requiresFeature:"recruiting_module"}},{path:"recruiting/job-postings",name:"job-postings-list",component:()=>d(()=>import("./JobPostingsList.js"),__vite__mapDeps([135,1,133,14,38,23,136])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data",requiresFeature:"recruiting_module"}},{path:"recruiting/candidates",name:"candidates-list",component:()=>d(()=>import("./CandidatesList.js"),__vite__mapDeps([137,1,133,14,68,38,23,130,138])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data",requiresFeature:"recruiting_module"}},{path:"recruiting/candidates/new",name:"candidate-create",component:()=>d(()=>import("./CandidateForm.js"),__vite__mapDeps([139,1,133,76,33,77,14,140])),meta:{requiresAuth:!0,requiredPermission:"edit_personnel_data",requiresFeature:"recruiting_module"}},{path:"recruiting/candidates/:id",name:"candidate-view",component:()=>d(()=>import("./CandidateView.js"),__vite__mapDeps([141,1,133,14,142])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data",requiresFeature:"recruiting_module"}},{path:"recruiting/candidates/:id/edit",name:"candidate-edit",component:()=>d(()=>import("./CandidateEdit.js"),__vite__mapDeps([143,1,133,76,33,77,14,144])),meta:{requiresAuth:!0,requiredPermission:"edit_personnel_data",requiresFeature:"recruiting_module"}},{path:"recruiting/applications",name:"applications-list",component:()=>d(()=>import("./ApplicationsList.js"),__vite__mapDeps([145,1,133,38,14,23,146])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data",requiresFeature:"recruiting_module"}},{path:"recruiting/applications/:id",name:"application-view",component:()=>d(()=>import("./ApplicationView.js"),__vite__mapDeps([147,1,133,14,148])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data",requiresFeature:"recruiting_module"}},{path:"recruiting/job-postings/new",name:"job-posting-create",component:()=>d(()=>import("./JobPostingForm.js"),__vite__mapDeps([149,1,133,76,33,77,14,150])),meta:{requiresAuth:!0,requiredPermission:"edit_personnel_data",requiresFeature:"recruiting_module"}},{path:"recruiting/job-postings/:id",name:"job-posting-view",component:()=>d(()=>import("./JobPostingView.js"),__vite__mapDeps([151,1,133,14,152])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data",requiresFeature:"recruiting_module"}},{path:"recruiting/job-postings/:id/edit",name:"job-posting-edit",component:()=>d(()=>import("./JobPostingForm.js"),__vite__mapDeps([149,1,133,76,33,77,14,150])),meta:{requiresAuth:!0,requiredPermission:"edit_personnel_data",requiresFeature:"recruiting_module"}},{path:"recruiting/pipeline",name:"recruiting-pipeline",component:()=>d(()=>import("./RecruitingPipeline.js"),__vite__mapDeps([153,1,133,68,14,38,23,154])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data",requiresFeature:"recruiting_module"}},{path:"recruiting/interviews",name:"interviews-calendar",component:()=>d(()=>import("./InterviewsCalendar.js"),__vite__mapDeps([155,1,133,14,156])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data",requiresFeature:"recruiting_module"}},{path:"recruiting/interviews/new",name:"interview-create",component:()=>d(()=>import("./InterviewForm.js"),__vite__mapDeps([157,1,133,76,33,77,14,158])),meta:{requiresAuth:!0,requiredPermission:"edit_personnel_data",requiresFeature:"recruiting_module"}},{path:"recruiting/interviews/:id",name:"interview-view",component:()=>d(()=>import("./InterviewView.js"),__vite__mapDeps([159,1,133,14,160])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data",requiresFeature:"recruiting_module"}},{path:"recruiting/interviews/:id/edit",name:"interview-edit",component:()=>d(()=>import("./InterviewForm.js"),__vite__mapDeps([157,1,133,76,33,77,14,158])),meta:{requiresAuth:!0,requiredPermission:"edit_personnel_data",requiresFeature:"recruiting_module"}},{path:"admin",redirect:"/app/admin/users"},{path:"admin/users",name:"admin-users",component:()=>d(()=>import("./Admin.js"),__vite__mapDeps([161,1,38,14,23])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"admin/settings",name:"admin-settings",component:()=>d(()=>import("./AdminSettings.js"),__vite__mapDeps([162,1])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"admin/kpi-templates",name:"admin-kpi-templates",component:()=>d(()=>import("./KPITemplates.js"),__vite__mapDeps([163,1])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"admin/feature-flags",name:"admin-feature-flags",component:()=>d(()=>import("./FeatureFlags.js"),__vite__mapDeps([164,1,68,14,38,23,22,54,55,57])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"admin/self-healing",name:"admin-self-healing",component:()=>d(()=>import("./SelfHealingDashboard.js"),__vite__mapDeps([165,1,14,50,47,166,130,121,122,167,168])),meta:{requiresAuth:!0,requiredPermission:"view_system_health"}},{path:"admin/self-healing/sessions",name:"admin-self-healing-sessions",component:()=>d(()=>import("./SelfHealingDashboard.js"),__vite__mapDeps([165,1,14,50,47,166,130,121,122,167,168])),meta:{requiresAuth:!0,requiredPermission:"view_system_health"}},{path:"admin/self-healing/patterns",name:"admin-self-healing-patterns",component:()=>d(()=>import("./SelfHealingPatterns.js"),__vite__mapDeps([169,1,50,47,14,54,55,166,130,121,122,167])),meta:{requiresAuth:!0,requiredPermission:"view_system_health"}},{path:"admin/security",name:"admin-security",component:()=>d(()=>import("./SecurityDashboard.js"),__vite__mapDeps([170,1,14])),meta:{requiresAuth:!0,requiredPermission:"admin_access",requiresFeature:"security_dashboard"}},{path:"profile",name:"profile",component:()=>d(()=>import("./Profile.js"),__vite__mapDeps([171,1])),meta:{requiresAuth:!0}},{path:"settings",name:"settings",component:()=>d(()=>import("./Settings.js"),__vite__mapDeps([172,1])),meta:{requiresAuth:!0}},{path:"crm",redirect:"/app/crm/dashboard"},{path:"crm/dashboard",name:"crm-dashboard",component:()=>d(()=>import("./CRMDashboard.js"),__vite__mapDeps([173,174,1,17,18,14,175])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects",requiresFeature:"sales_module"}},{path:"crm/clients",name:"crm-clients",component:()=>d(()=>import("./ClientsList.js"),__vite__mapDeps([176,1,174,177,53,54,55,38,14,23])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects",requiresFeature:"sales_module"}},{path:"crm/clients/create",name:"crm-clients-create",component:()=>d(()=>import("./ClientForm.js"),__vite__mapDeps([178,1,174,177,25,76,33,77])),meta:{requiresAuth:!0,requiredPermission:"edit_project",requiresFeature:"sales_module"}},{path:"crm/clients/:id",name:"crm-client-view",component:()=>d(()=>import("./ClientView.js"),__vite__mapDeps([179,1,174,177,25,40,41,42,22,23,77])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects",requiresFeature:"sales_module"}},{path:"crm/clients/:id/edit",name:"crm-client-edit",component:()=>d(()=>import("./ClientForm.js"),__vite__mapDeps([178,1,174,177,25,76,33,77])),meta:{requiresAuth:!0,requiredPermission:"edit_project",requiresFeature:"sales_module"}},{path:"crm/contacts",name:"crm-contacts",component:()=>d(()=>import("./ContactsList.js"),__vite__mapDeps([180,1,174,53,54,55,38,14,23])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects",requiresFeature:"sales_module"}},{path:"crm/proposals",name:"crm-proposals",component:()=>d(()=>import("./ProposalsPipeline.js"),__vite__mapDeps([181,1,174,50,47,25,26,40,91,92,93,182])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects",requiresFeature:"sales_module"}},{path:"crm/proposals/new",name:"crm-proposals-new",component:()=>d(()=>import("./ProposalForm.js"),__vite__mapDeps([183,1,174,25])),meta:{requiresAuth:!0,requiredPermission:"edit_project",requiresFeature:"sales_module"}},{path:"crm/proposals/create",name:"crm-proposals-create",component:()=>d(()=>import("./ProposalForm.js"),__vite__mapDeps([183,1,174,25])),meta:{requiresAuth:!0,requiredPermission:"edit_project",requiresFeature:"sales_module"}},{path:"crm/proposals/:id",name:"crm-proposal-view",component:()=>d(()=>import("./ProposalView.js"),__vite__mapDeps([184,1,174,22,23,25,40])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects",requiresFeature:"sales_module"}},{path:"crm/proposals/:id/edit",name:"crm-proposal-edit",component:()=>d(()=>import("./ProposalForm.js"),__vite__mapDeps([183,1,174,25])),meta:{requiresAuth:!0,requiredPermission:"edit_project",requiresFeature:"sales_module"}},{path:"crm/contracts",name:"crm-contracts",component:()=>d(()=>import("./ContractsList.js"),__vite__mapDeps([185,1,174,22,23,14,38,186,53,54,55])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects",requiresFeature:"sales_module"}},{path:"crm/contracts/new",name:"crm-contract-create",component:()=>d(()=>import("./ContractForm.js"),__vite__mapDeps([187,1,174,186,25])),meta:{requiresAuth:!0,requiredPermission:"manage_contracts",requiresFeature:"sales_module"}},{path:"crm/contracts/:id/edit",name:"crm-contract-edit",component:()=>d(()=>import("./ContractForm.js"),__vite__mapDeps([187,1,174,186,25])),meta:{requiresAuth:!0,requiredPermission:"manage_contracts",requiresFeature:"sales_module"}},{path:"crm/contracts/:id",name:"crm-contract-view",component:()=>d(()=>import("./ContractView.js"),__vite__mapDeps([188,1,174,22,23,25,40,186])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects",requiresFeature:"sales_module"}},{path:"invoicing",redirect:"/app/invoicing/pre-invoices"},{path:"invoicing/pre-invoices",name:"invoicing-pre-invoices",component:()=>d(()=>import("./PreInvoicesList.js"),__vite__mapDeps([189,1,38,14,23,25,26,40,46,47,54,55])),meta:{requiresAuth:!0,requiredPermission:"view_all_invoices"}},{path:"invoicing/pre-invoices/new",name:"invoicing-pre-invoice-create",component:()=>d(()=>import("./PreInvoiceForm.js"),__vite__mapDeps([190,1,25])),meta:{requiresAuth:!0,requiredPermission:"manage_invoices"}},{path:"invoicing/pre-invoices/:id",name:"invoicing-pre-invoice-view",component:()=>d(()=>import("./PreInvoiceView.js"),__vite__mapDeps([191,1,25])),meta:{requiresAuth:!0,requiredPermission:"view_all_invoices"}},{path:"business-intelligence",redirect:"/app/business-intelligence/dashboard"},{path:"business-intelligence/dashboard",name:"business-intelligence-dashboard",component:()=>d(()=>import("./BIDashboard.js"),__vite__mapDeps([192,17,1,18,48])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects",requiresFeature:"business_intelligence_module"}},{path:"business-intelligence/case-studies",name:"business-intelligence-case-studies",component:()=>d(()=>import("./CaseStudies.js"),__vite__mapDeps([193,1])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects",requiresFeature:"business_intelligence_module"}},{path:"business-intelligence/core-skills",name:"business-intelligence-core-skills",component:()=>d(()=>import("./CoreSkills.js"),__vite__mapDeps([194,1])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data",requiresFeature:"business_intelligence_module"}},{path:"business-intelligence/technical-offer",name:"business-intelligence-technical-offer",component:()=>d(()=>import("./TechnicalOffer.js"),__vite__mapDeps([195,1])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects",requiresFeature:"business_intelligence_module"}},{path:"business-intelligence/market-intel",name:"business-intelligence-market-intel",component:()=>d(()=>import("./MarketIntelligence.js"),__vite__mapDeps([196,1])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects",requiresFeature:"business_intelligence_module"}},{path:"business-intelligence/advanced-reports",name:"business-intelligence-advanced-reports",component:()=>d(()=>import("./AdvancedReports.js"),__vite__mapDeps([197,1])),meta:{requiresAuth:!0,requiredPermission:"view_reports",requiresFeature:"business_intelligence_module"}},{path:"certifications",redirect:"/app/certifications/dashboard"},{path:"certifications/dashboard",name:"certifications-dashboard",component:()=>d(()=>import("./CertificationsDashboard.js"),__vite__mapDeps([198,199,1,17,18,14])),meta:{requiresAuth:!0,requiredPermission:"view_compliance",requiresFeature:"certifications_module"}},{path:"certifications/list",name:"certifications-list",component:()=>d(()=>import("./CertificationsList.js"),__vite__mapDeps([200,199,1,38,14,23,24])),meta:{requiresAuth:!0,requiredPermission:"view_compliance",requiresFeature:"certifications_module"}},{path:"certifications/catalog",name:"certifications-catalog",component:()=>d(()=>import("./CertificationsCatalog.js"),__vite__mapDeps([201,1,199,25,202,14])),meta:{requiresAuth:!0,requiredPermission:"view_compliance",requiresFeature:"certifications_module"}},{path:"certifications/create",name:"certifications-create",component:()=>d(()=>import("./CertificationCreate.js"),__vite__mapDeps([203,1,199,25,14,24,23])),meta:{requiresAuth:!0,requiredPermission:"view_compliance",requiresFeature:"certifications_module"}},{path:"certifications/:id",name:"certification-view",component:()=>d(()=>import("./CertificationView.js"),__vite__mapDeps([204,1,199,14,25,202,23])),meta:{requiresAuth:!0,requiredPermission:"view_compliance",requiresFeature:"certifications_module"}},{path:"certifications/:id/edit",name:"certification-edit",component:()=>d(()=>import("./CertificationEdit.js"),__vite__mapDeps([205,1,199,14,24,23,25,202])),meta:{requiresAuth:!0,requiredPermission:"view_compliance",requiresFeature:"certifications_module"}},{path:"certifications/readiness",name:"certifications-readiness",component:()=>d(()=>import("./CertificationReadiness.js"),__vite__mapDeps([206,1,199,25,202,14,207])),meta:{requiresAuth:!0,requiredPermission:"view_compliance",requiresFeature:"certifications_module"}},{path:"ceo",redirect:"/app/ceo/dashboard"},{path:"ceo/dashboard",name:"ceo-dashboard",component:()=>d(()=>import("./CEODashboard.js"),__vite__mapDeps([208,1,17,18,14,209,210])),meta:{requiresAuth:!0,requiredPermission:"view_ceo",requiresFeature:"ceo_module"}},{path:"ceo/assistant",name:"ceo-assistant",component:()=>d(()=>import("./AIAssistant.js"),__vite__mapDeps([211,1,25,14,121,122,23])),meta:{requiresAuth:!0,requiredPermission:"view_ceo",requiresFeature:"ceo_module"}},{path:"ceo/insights",name:"ceo-insights",component:()=>d(()=>import("./InsightsReports.js"),__vite__mapDeps([212,1,25,209,213,214])),meta:{requiresAuth:!0,requiredPermission:"view_ceo",requiresFeature:"ceo_module"}},{path:"ceo/config",name:"ceo-config",component:()=>d(()=>import("./ResearchConfig.js"),__vite__mapDeps([215,1,25,14,23])),meta:{requiresAuth:!0,requiredPermission:"view_ceo",requiresFeature:"ceo_module"}},{path:"funding",redirect:"/app/funding/dashboard"},{path:"funding/dashboard",name:"funding-dashboard",component:()=>d(()=>import("./FundingDashboard.js"),__vite__mapDeps([216,1,217,17,18,14])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects",requiresFeature:"funding_module"}},{path:"funding/search",name:"funding-search",component:()=>d(()=>import("./FundingSearch.js"),__vite__mapDeps([218,1,217,26,25,219])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects",requiresFeature:"funding_module"}},{path:"funding/opportunities/:id",name:"funding-opportunity-view",component:()=>d(()=>import("./FundingOpportunityView.js"),__vite__mapDeps([220,1,217,25,22,23,14,221])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects",requiresFeature:"funding_module"}},{path:"funding/applications/:id",name:"funding-application-view",component:()=>d(()=>import("./FundingApplicationView.js"),__vite__mapDeps([222,1,217,50,47,25,22,23,14,223])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects",requiresFeature:"funding_module"}},{path:"funding/reporting",name:"funding-reporting",component:()=>d(()=>import("./FundingReporting.js"),__vite__mapDeps([224,1,217,50,47,25,22,23,38,14,24,225])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects",requiresFeature:"funding_module"}},{path:"funding/applications/new",name:"funding-application-form",component:()=>d(()=>import("./FundingApplicationForm.js"),__vite__mapDeps([226,1,217,50,47,24,23,14,102,103])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects",requiresFeature:"funding_module"}},{path:"funding/applications/new/:opportunityId",name:"funding-application-form-with-opportunity",component:()=>d(()=>import("./FundingApplicationForm.js"),__vite__mapDeps([226,1,217,50,47,24,23,14,102,103])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects",requiresFeature:"funding_module"}},{path:"funding/expenses/new",name:"funding-expense-form",component:()=>d(()=>import("./FundingExpenseForm.js"),__vite__mapDeps([227,1,217,25])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects",requiresFeature:"funding_module"}},{path:"funding/expenses/:id",name:"funding-expense-view",component:()=>d(()=>import("./FundingExpenseView.js"),__vite__mapDeps([228,1,217,47,25,22,23])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects",requiresFeature:"funding_module"}},{path:"funding/expenses/:id/edit",name:"funding-expense-edit",component:()=>d(()=>import("./FundingExpenseForm.js"),__vite__mapDeps([227,1,217,25])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects",requiresFeature:"funding_module"}},{path:"governance/compliance",name:"governance-compliance",component:()=>d(()=>import("./ComplianceDashboard.js"),__vite__mapDeps([229,230,1,23])),meta:{requiresAuth:!0,requiredPermission:"view_compliance",requiresFeature:"governance_module"}},{path:"governance/audit",name:"governance-audit",component:()=>d(()=>import("./AuditTrail.js"),__vite__mapDeps([231,1,230])),meta:{requiresAuth:!0,requiredPermission:"view_compliance",requiresFeature:"governance_module"}},{path:"governance/policies",name:"governance-policies",component:()=>d(()=>import("./PolicyCenter.js"),__vite__mapDeps([232,1,230])),meta:{requiresAuth:!0,requiredPermission:"view_compliance",requiresFeature:"governance_module"}},{path:"governance/events",name:"governance-events",component:()=>d(()=>import("./ComplianceEvents.js"),__vite__mapDeps([233,1,230])),meta:{requiresAuth:!0,requiredPermission:"view_compliance",requiresFeature:"governance_module"}},{path:"governance/risk",name:"governance-risk",component:()=>d(()=>import("./RiskAssessment.js"),__vite__mapDeps([234,1,230])),meta:{requiresAuth:!0,requiredPermission:"view_compliance",requiresFeature:"governance_module"}},{path:"engagement/dashboard",name:"engagement-dashboard",component:()=>d(()=>import("./EngagementDashboard.js"),__vite__mapDeps([235,1,236,237])),meta:{requiresAuth:!0,requiredPermission:"view_engagement",requiresFeature:"engagement_module"}},{path:"engagement/leaderboard",name:"engagement-leaderboard",component:()=>d(()=>import("./Leaderboard.js"),__vite__mapDeps([238,1,236,17,18,239])),meta:{requiresAuth:!0,requiredPermission:"view_engagement",requiresFeature:"engagement_module"}},{path:"engagement/campaigns",name:"engagement-campaigns",component:()=>d(()=>import("./EngagementCampaigns.js"),__vite__mapDeps([240,1,236,241])),meta:{requiresAuth:!0,requiredPermission:"view_engagement",requiresFeature:"engagement_module"}},{path:"engagement/campaigns/:id",name:"engagement-campaign-detail",component:()=>d(()=>import("./EngagementCampaignDetail.js"),__vite__mapDeps([242,1,236])),meta:{requiresAuth:!0,requiredPermission:"view_engagement",requiresFeature:"engagement_module"}},{path:"engagement/rewards",name:"engagement-rewards",component:()=>d(()=>import("./EngagementRewards.js"),__vite__mapDeps([243,1,236,244])),meta:{requiresAuth:!0,requiredPermission:"view_engagement",requiresFeature:"engagement_module"}},{path:"engagement/admin",name:"engagement-admin",component:()=>d(()=>import("./EngagementAdmin.js"),__vite__mapDeps([245,236,1,17,18,41,42,14,38,23,22])),meta:{requiresAuth:!0,requiredPermission:"manage_engagement_campaigns",requiresFeature:"engagement_module"}},{path:"engagement/points",name:"engagement-points",component:()=>d(()=>import("./EngagementPoints.js"),__vite__mapDeps([246,1,236])),meta:{requiresAuth:!0,requiredPermission:"view_engagement",requiresFeature:"engagement_module"}},{path:"help",redirect:"/app/help/dashboard"},{path:"help/dashboard",name:"help-dashboard",component:()=>d(()=>import("./HelpDashboard.js"),__vite__mapDeps([247,1,248,68,25,14])),meta:{requiresAuth:!0,requiredPermission:"view_help",requiresFeature:"help_module"}},{path:"help/content/:id",name:"help-content-view",component:()=>d(()=>import("./HelpContent.js"),__vite__mapDeps([249,1,248,121,122,22,23,14,202,250,130,251,252])),meta:{requiresAuth:!0,requiredPermission:"view_help",requiresFeature:"help_module"}},{path:"help/content",name:"help-content-list",component:()=>d(()=>import("./HelpContentList.js"),__vite__mapDeps([253,1,248,68,14,202,54,55,250,121,122,130,251,254])),meta:{requiresAuth:!0,requiredPermission:"view_help",requiresFeature:"help_module"}},{path:"help/faq",name:"help-faq",component:()=>d(()=>import("./HelpFAQ.js"),__vite__mapDeps([255,1,248,68,14,202,121,122,256])),meta:{requiresAuth:!0,requiredPermission:"view_help",requiresFeature:"help_module"}},{path:"help/contact",name:"help-contact",component:()=>d(()=>import("./HelpContact.js"),__vite__mapDeps([257,1,248,14,202,130,258])),meta:{requiresAuth:!0,requiredPermission:"view_help",requiresFeature:"help_module"}},{path:"help/report-error",name:"help-report-error",component:()=>d(()=>import("./ErrorReport.js"),__vite__mapDeps([259,1])),meta:{requiresAuth:!0,requiredPermission:"view_help",requiresFeature:"help_module"}},{path:"drafts",name:"drafts-dashboard",component:()=>d(()=>import("./DraftsDashboard.js"),__vite__mapDeps([260,1])),meta:{requiresAuth:!0,requiredPermission:"view_dashboard"}},{path:"drafts/module/:moduleKey",name:"drafts-module-details",component:()=>d(()=>import("./DraftsModuleDetails.js"),__vite__mapDeps([261,1,38,14,23,22])),meta:{requiresAuth:!0,requiredPermission:"view_dashboard"}},{path:"test/design-system",name:"test-design-system",component:()=>d(()=>import("./DesignSystemTest.js"),__vite__mapDeps([262,1,53,54,55])),meta:{requiresAuth:!0}}]},{path:"/:pathMatch(.*)*",name:"not-found",component:Ee,children:[{path:"",component:st,props:{code:"404"}}]}],we=kt({history:Et(),routes:Hs});let Ae=!1,Pe=null;we.beforeEach(async(t,e,r)=>{if(t.path.startsWith("/error/")){r();return}const o=me();try{if(t.meta.requiresAuth){if(!o.sessionChecked&&!Ae?(Ae=!0,Pe=o.initializeAuth(),await Pe,Ae=!1):Ae&&Pe&&await Pe,!o.isAuthenticated){console.debug(`[Router] Redirecting to login from ${t.path}`),r("/auth/login");return}if(t.meta.requiresFeature)try{const n=Ie();if(await n.ensureInitialized(),!n.isFeatureEnabled(t.meta.requiresFeature)){console.warn(`[Router] Feature disabled: ${t.meta.requiresFeature} for ${t.path}`),r("/error/404");return}}catch(n){console.warn(`[Router] Feature flag check failed for ${t.path}:`,n)}if(t.meta.requiredPermission&&!o.hasPermission(t.meta.requiredPermission)){console.warn(`[Router] Permission denied: ${t.meta.requiredPermission} for ${t.path}`),r("/error/403");return}}r()}catch(n){console.error("[Router] Navigation guard error:",n),e.path.startsWith("/error/")?r():r("/error/500")}});we.onError(t=>{console.error("Router error:",t),t.message.includes("Failed to fetch")?we.push("/error/network"):we.push("/error/500")});class Us{constructor(){this.errorQueue=[],this.isOnline=navigator.onLine,this.isInitialized=!1,this.batchTimeout=null,this.maxQueueSize=100,this.batchSendInterval=3e4,this.config={enableAutoSubmit:!1,enableCriticalAutoAnalysis:!1,ignoredErrors:[/Script error/,/ResizeObserver loop limit exceeded/,/Non-Error promise rejection captured/,/Loading chunk \d+ failed/,/ChunkLoadError/,/favicon\.ico/,/self-healing.*submit-error/,/api\/self-healing/,/CSRF.*token/,/Request failed.*400/],criticalPatterns:[/500/,/Network Error/,/TypeError.*undefined/,/ReferenceError/,/SyntaxError/,/Failed to fetch/,/timeout/i],userVisiblePatterns:[/Failed to load/,/Network Error/,/timeout/i,/400|401|403|404|500|502|503/]},this.onCriticalError=null,this.onUserVisibleError=null,this.onBatchSent=null}initialize(){this.isInitialized||(this.setupGlobalErrorHandlers(),this.setupNetworkMonitoring(),this.setupVueErrorHandler(),this.startBatchProcessor(),this.isInitialized=!0)}setupGlobalErrorHandlers(){window.addEventListener("error",e=>{var r,o,n;this.captureError({type:"javascript_error",message:e.message,filename:e.filename,lineno:e.lineno,colno:e.colno,stack:(r=e.error)==null?void 0:r.stack,timestamp:new Date().toISOString(),url:window.location.href,userAgent:navigator.userAgent,automatic:!0,error_context:{target:(o=e.target)==null?void 0:o.tagName,src:(n=e.target)==null?void 0:n.src,type:e.type}})}),window.addEventListener("unhandledrejection",e=>{var r,o;this.captureError({type:"unhandled_promise",message:((r=e.reason)==null?void 0:r.message)||String(e.reason),stack:(o=e.reason)==null?void 0:o.stack,timestamp:new Date().toISOString(),url:window.location.href,automatic:!0,error_context:{reason_type:typeof e.reason,is_error_object:e.reason instanceof Error}})})}setupNetworkMonitoring(){window.addEventListener("online",()=>{this.isOnline=!0,this.processPendingQueue()}),window.addEventListener("offline",()=>{this.isOnline=!1,this.captureError({type:"network_error",message:"Network went offline",timestamp:new Date().toISOString(),url:window.location.href,automatic:!0})})}setupVueErrorHandler(){this.setupVueHandler=e=>{if(e&&e.config){const r=e.config.errorHandler;e.config.errorHandler=(o,n,s)=>{this.captureError({type:"vue_error",message:o.message,stack:o.stack,timestamp:new Date().toISOString(),url:window.location.href,automatic:!0,error_context:{component_info:s,component_name:this.getComponentName(n),component_path:this.getComponentPath(n)}}),r&&r(o,n,s)}}}}captureError(e){try{if(!e.message&&!e.type||this.shouldIgnoreError(e))return;const r=this.enrichErrorData(e);this.errorQueue.push(r),this.errorQueue.length>this.maxQueueSize&&(this.errorQueue=this.errorQueue.slice(-this.maxQueueSize)),this.isCriticalError(r)&&this.handleCriticalError(r),this.isUserVisibleError(r)&&this.handleUserVisibleError(r),console.debug("📊 Error captured for self-healing:",{type:r.type,message:r.message.substring(0,100),queue_size:this.errorQueue.length})}catch(r){console.warn("Self-healing capture failed:",r)}}enrichErrorData(e){const r={...e,timestamp:e.timestamp||new Date().toISOString(),url:e.url||window.location.href,user_agent:e.userAgent||navigator.userAgent,session_id:this.getSessionId(),page_context:{title:document.title,pathname:window.location.pathname,search:window.location.search,hash:window.location.hash,referrer:document.referrer},browser_context:{viewport:{width:window.innerWidth,height:window.innerHeight},memory:this.getMemoryInfo(),connection:this.getConnectionInfo(),local_time:new Date().toLocaleString()}};return r.severity=this.classifyErrorSeverity(r),r}shouldIgnoreError(e){const r=e.message||"",o=e.type||"";return this.config.ignoredErrors.some(n=>n.test(r)||n.test(o))}isCriticalError(e){const r=e.message||"",o=e.type||"";return this.config.criticalPatterns.some(n=>n.test(r)||n.test(o))}isUserVisibleError(e){const r=e.message||"",o=e.type||"";return this.config.userVisiblePatterns.some(n=>n.test(r)||n.test(o))}classifyErrorSeverity(e){var o;if(this.isCriticalError(e))return"critical";if(this.isUserVisibleError(e))return"high";const r=((o=e.message)==null?void 0:o.toLowerCase())||"";return r.includes("warning")||r.includes("deprecated")?"low":"medium"}handleCriticalError(e){this.config.enableAutoSubmit&&this.sendErrorsToBackend([e]).then(()=>{console.log("🚨 Critical error sent immediately:",e.type)}).catch(r=>{console.warn("Failed to send critical error:",r)}),this.onCriticalError&&this.onCriticalError(e)}handleUserVisibleError(e){this.onUserVisibleError&&this.onUserVisibleError(e)}startBatchProcessor(){this.batchTimeout||(this.batchTimeout=setInterval(()=>{this.processPendingQueue()},this.batchSendInterval))}async processPendingQueue(){if(!this.isOnline||this.errorQueue.length===0||!this.config.enableAutoSubmit)return;const e=this.errorQueue.filter(r=>r.severity!=="critical");if(e.length!==0)try{await this.sendErrorsToBackend(e),this.errorQueue=this.errorQueue.filter(r=>!e.includes(r)),console.log(`📤 Sent ${e.length} non-critical errors to backend`),this.onBatchSent&&this.onBatchSent(e)}catch(r){console.warn("Failed to send error batch:",r)}}async sendErrorsToBackend(e){if(!e||e.length===0)return;const r=se(ne.default),o={errors:e,batch_info:{sent_at:new Date().toISOString(),batch_size:e.length,collector_version:"1.0.0"},session_metadata:{session_id:this.getSessionId(),page_loads:this.getPageLoadCount(),time_on_page:this.getTimeOnPage()}};return(await r.post("/api/self-healing/submit-error",o)).data}getSessionId(){let e=sessionStorage.getItem("datportal_session_id");return e||(e="sess_"+Date.now()+"_"+Math.random().toString(36).substr(2,9),sessionStorage.setItem("datportal_session_id",e)),e}getComponentName(e){var s,u,f,p,g,b;if(!e)return"Componente Non Disponibile";const r=((s=e.$options)==null?void 0:s.name)||e.__name||((u=e.$options)==null?void 0:u._componentTag)||((f=e.constructor)==null?void 0:f.name);if(r&&r!=="Object")return r;const o=((p=e.$vnode)==null?void 0:p.tag)||((g=e.$el)==null?void 0:g.tagName);if(o)return`<${o.toLowerCase()}>`;const n=(b=e.$route)==null?void 0:b.name;return n?`Route: ${n}`:"Componente Anonimo"}getComponentPath(e){if(!e)return"Percorso Non Disponibile";const r=[];let o=e,n=0;const s=10;for(;o&&n<s;){const u=this.getComponentName(o);r.unshift(u),o=o.$parent,n++}return r.length>0?r.join(" > "):"Percorso Sconosciuto"}getMemoryInfo(){return performance.memory?{used:Math.round(performance.memory.usedJSHeapSize/1024/1024),total:Math.round(performance.memory.totalJSHeapSize/1024/1024),limit:Math.round(performance.memory.jsHeapSizeLimit/1024/1024)}:null}getConnectionInfo(){return navigator.connection?{effective_type:navigator.connection.effectiveType,downlink:navigator.connection.downlink,rtt:navigator.connection.rtt,save_data:navigator.connection.saveData}:null}getPageLoadCount(){const e=parseInt(sessionStorage.getItem("datportal_page_loads")||"0");return sessionStorage.setItem("datportal_page_loads",String(e+1)),e+1}getTimeOnPage(){const e=parseInt(sessionStorage.getItem("datportal_page_start")||Date.now());return sessionStorage.getItem("datportal_page_start")||sessionStorage.setItem("datportal_page_start",String(Date.now())),Math.round((Date.now()-e)/1e3)}configure(e){this.config={...this.config,...e}}captureManualError(e){this.captureError({...e,automatic:!1,manual_report:!0})}async flushQueue(){return this.processPendingQueue()}getStats(){return{queue_size:this.errorQueue.length,is_online:this.isOnline,is_initialized:this.isInitialized,session_id:this.getSessionId(),time_on_page:this.getTimeOnPage()}}destroy(){this.batchTimeout&&(clearInterval(this.batchTimeout),this.batchTimeout=null),this.isInitialized=!1,this.errorQueue=[]}}const ye=new Us,he=At(It),Zs=Pt();he.use(Zs);he.use(we);function Gs(t){var n,s,u,f,p,g;if(!t)return"Componente Non Disponibile";const e=((n=t.$options)==null?void 0:n.name)||t.__name||((s=t.$options)==null?void 0:s._componentTag)||((u=t.constructor)==null?void 0:u.name);if(e&&e!=="Object")return e;const r=((f=t.$vnode)==null?void 0:f.tag)||((p=t.$el)==null?void 0:p.tagName);if(r)return`<${r.toLowerCase()}>`;const o=(g=t.$route)==null?void 0:g.name;return o?`Route: ${o}`:"Componente Anonimo"}he.config.errorHandler=(t,e,r)=>{var o;console.error("Global error:",t),console.error("Component instance:",e),console.error("Error info:",r),ye.isInitialized&&ye.captureError({type:"vue_error",message:t.message,stack:t.stack,file:((o=e==null?void 0:e.$options)==null?void 0:o.__file)||(e==null?void 0:e.__name)||"File Non Disponibile",error_context:{component_info:r,component_name:Gs(e)}})};window.addEventListener("unhandledrejection",t=>{console.error("Unhandled promise rejection:",t.reason),t.preventDefault()});const Ws=me(),Qs=Ie();ye.initialize();ye.setupVueHandler&&ye.setupVueHandler(he);Ws.initializeAuth().then(()=>Qs.ensureInitialized()).then(()=>{he.mount("#app")}).catch(t=>{console.error("Initialization failed:",t),he.mount("#app")});export{ur as $,Ze as A,Wt as B,Ge as C,We as D,Qe as E,Qt as F,Kt as G,V as H,Jt as I,Ke as J,Yt as K,Xt as L,Se as M,er as N,tr as O,Je as P,rr as Q,ar as R,Ye as S,or as T,nr as U,sr as V,Xe as W,ir as X,lr as Y,cr as Z,xe as _,me as a,dr as a0,mr as a1,pr as a2,hr as a3,gr as a4,et as a5,fr as a6,vr as a7,tt as a8,_r as a9,wr as aa,yr as ab,rt as ac,Fe as ad,br as ae,xr as af,kr as ag,Er as ah,Ar as ai,Js as aj,pt as b,P as c,ze as d,ma as e,Ie as f,Xs as g,jt as h,co as i,d as j,Ys as k,mt as l,St as m,Oe as n,Go as o,Ot as p,zt as q,Te as r,Nt as s,Bt as t,be as u,Ht as v,Ut as w,Ue as x,Zt as y,Gt as z};
