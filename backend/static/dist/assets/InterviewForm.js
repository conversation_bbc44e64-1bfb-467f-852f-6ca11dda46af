import{r as h,c as N,w as ne,b as r,o as n,l as b,j as e,e as g,s as m,t as s,B as M,H as te,I as ae,Q as W,C as ue,k as j,h as le,F as P,p as L,A as de,u as ce,x as pe,q as ve,n as ie}from"./vendor.js";import{u as me}from"./recruiting.js";import{_ as se,d as re,H as _,c as ge}from"./app.js";import{F as be}from"./FormBuilder.js";import{S as O}from"./StandardButton.js";import"./AlertsSection.js";/* empty css                                                           */const _e={class:"interview-questions-generator"},fe={class:"flex justify-between items-center mb-6"},xe={class:"flex items-center space-x-3"},ye={class:"flex-shrink-0"},he={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},we={class:"space-y-4"},ke={class:"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-700"},qe={class:"space-y-2 text-sm"},Ce={key:0},Ee={key:1},$e={key:0,class:"bg-green-50 dark:bg-green-900/20 rounded-lg p-4 border border-green-200 dark:border-green-700"},Ie={class:"space-y-2 text-sm"},Ae={key:0},Te={key:1},ze={class:"space-y-2"},Se={class:"flex items-center"},Pe={class:"flex items-center"},Le={class:"flex items-center"},je={class:"flex items-center"},De={class:"flex items-center justify-between pt-4"},Ge={class:"text-xs text-gray-500"},Me={key:1},Ne={class:"bg-gray-50 rounded-lg p-4 h-full"},Oe={key:0,class:"text-center text-gray-500 py-16"},Ue={key:1,class:"text-center py-16"},Re={key:2,class:"space-y-4"},Ve={class:"flex justify-between items-center"},Fe={class:"font-medium text-gray-900 flex items-center"},Qe={class:"flex space-x-2"},Be={class:"max-h-96 overflow-y-auto space-y-4"},He={key:0},Ze={class:"text-sm font-medium text-purple-900 mb-2 flex items-center"},Je={class:"bg-white p-3 rounded border space-y-2"},We={key:1},Ke={class:"text-sm font-medium text-purple-900 mb-2 flex items-center"},Xe={class:"bg-white p-3 rounded border space-y-2"},Ye={key:2},et={class:"text-sm font-medium text-purple-900 mb-2 flex items-center"},tt={class:"bg-white p-3 rounded border space-y-2"},at={key:3},it={class:"text-sm font-medium text-purple-900 mb-2 flex items-center"},ot={class:"bg-white p-3 rounded border space-y-2"},lt={key:4},nt={class:"text-sm font-medium text-purple-900 mb-2 flex items-center"},st={class:"bg-white p-3 rounded border space-y-2"},rt={key:5},ut={class:"text-sm font-medium text-purple-900 mb-2 flex items-center"},dt={class:"bg-white p-3 rounded border space-y-2"},ct={key:6,class:"border-t pt-3"},pt={class:"text-sm font-medium text-purple-900 mb-2 flex items-center"},vt={class:"bg-yellow-50 p-3 rounded border border-yellow-200"},mt={class:"text-sm text-yellow-800 whitespace-pre-wrap"},gt={key:7,class:"border-t pt-3"},bt={class:"flex items-center justify-between text-xs text-gray-500"},_t={key:3,class:"text-center py-16"},ft={class:"text-sm text-red-600 mb-4"},xt={class:"flex justify-between items-center mt-6 pt-6 border-t"},yt={class:"flex space-x-3"},ht={__name:"InterviewQuestionsGenerator",props:{show:{type:Boolean,default:!1},jobPosting:{type:Object,required:!0},candidate:{type:Object,default:null}},emits:["questions-generated","questions-applied","close"],setup(x,{emit:E}){const U=E,w=x,{showToast:S}=re(),v=h(!1),$=h(!1),k=h(null),l=h(null),p=h({interview_type:"",interview_duration:"60",seniority_level:"",include_technical:!0,include_situational:!0,include_cultural:!0,include_motivation:!0,additional_context:""}),D=N(()=>p.value.interview_type),H=async()=>{var o,t,I;if(!D.value){S("Seleziona il tipo di colloquio","warning");return}$.value=!0,k.value=null;try{const f={job_posting_id:w.jobPosting.id,candidate_id:((o=w.candidate)==null?void 0:o.id)||null,interview_type:p.value.interview_type,interview_duration:parseInt(p.value.interview_duration),seniority_level:p.value.seniority_level||null,focus_areas:F(),additional_context:p.value.additional_context||null},C=await ge.post("/api/recruiting/ai/interview/questions/generate",f);if(C.data.success)l.value=C.data.data.questions,U("questions-generated",C.data.data),S("Domande generate con successo!","success");else throw new Error(C.data.message||"Errore nella generazione")}catch(f){k.value=((I=(t=f.response)==null?void 0:t.data)==null?void 0:I.message)||f.message||"Errore nella generazione AI",console.error("AI Questions Generation Error:",f),S("Errore nella generazione domande","error")}finally{$.value=!1}},F=()=>{const o=[];return p.value.include_technical&&o.push("technical"),p.value.include_situational&&o.push("situational"),p.value.include_cultural&&o.push("cultural"),p.value.include_motivation&&o.push("motivation"),o},Z=()=>{l.value&&(U("questions-applied",l.value),S("Domande applicate al colloquio!","success"),V())},q=async()=>{var t,I,f,C,R,a;if(!l.value)return;let o=`DOMANDE COLLOQUIO AI - ${w.jobPosting.title}
`;o+=`Tipo: ${X(p.value.interview_type)}
`,o+=`Durata: ${p.value.interview_duration} minuti

`,(t=l.value.opening_questions)!=null&&t.length&&(o+=`APERTURA:
`,l.value.opening_questions.forEach((i,c)=>{o+=`${c+1}. ${i}
`}),o+=`
`),(I=l.value.experience_questions)!=null&&I.length&&(o+=`ESPERIENZA:
`,l.value.experience_questions.forEach((i,c)=>{o+=`${c+1}. ${i}
`}),o+=`
`),(f=l.value.technical_questions)!=null&&f.length&&(o+=`TECNICHE:
`,l.value.technical_questions.forEach((i,c)=>{o+=`${c+1}. ${i}
`}),o+=`
`),(C=l.value.behavioral_questions)!=null&&C.length&&(o+=`COMPORTAMENTALI:
`,l.value.behavioral_questions.forEach((i,c)=>{o+=`${c+1}. ${i}
`}),o+=`
`),(R=l.value.motivation_questions)!=null&&R.length&&(o+=`MOTIVAZIONE:
`,l.value.motivation_questions.forEach((i,c)=>{o+=`${c+1}. ${i}
`}),o+=`
`),(a=l.value.closing_questions)!=null&&a.length&&(o+=`CHIUSURA:
`,l.value.closing_questions.forEach((i,c)=>{o+=`${c+1}. ${i}
`}),o+=`
`),l.value.interview_tips&&(o+=`SUGGERIMENTI:
${l.value.interview_tips}
`);try{await navigator.clipboard.writeText(o),S("Domande copiate negli appunti","success")}catch(i){console.error("Copy failed:",i),S("Errore nella copia","error")}},J=()=>{k.value=null},V=()=>{v.value=!1,U("close")},Q=()=>{p.value={interview_type:"",interview_duration:"60",seniority_level:"",include_technical:!0,include_situational:!0,include_cultural:!0,include_motivation:!0,additional_context:""},l.value=null,k.value=null},B=o=>({full_time:"Tempo Pieno",part_time:"Part-time",contract:"Contratto",intern:"Stage"})[o]||o,K=o=>({website:"Sito Web",linkedin:"LinkedIn",referral:"Referral",agency:"Agenzia"})[o]||o,X=o=>({phone_screening:"Screening Telefonico",technical:"Colloquio Tecnico",behavioral:"Colloquio Comportamentale",final:"Colloquio Finale"})[o]||o,G=o=>`~${o*3}min`,Y=()=>{let o=0;return l.value.opening_questions&&(o+=l.value.opening_questions.length),l.value.experience_questions&&(o+=l.value.experience_questions.length),l.value.technical_questions&&(o+=l.value.technical_questions.length),l.value.behavioral_questions&&(o+=l.value.behavioral_questions.length),l.value.motivation_questions&&(o+=l.value.motivation_questions.length),l.value.closing_questions&&(o+=l.value.closing_questions.length),`~${o*3}min`},ee=o=>o?new Date(o).toLocaleString("it-IT"):"";return ne(()=>w.show,o=>{v.value=o,o&&Q()}),(o,t)=>{var I,f,C,R,a,i,c,y,A;return n(),r("div",_e,[v.value?(n(),r("div",{key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:V},[e("div",{class:"relative top-10 mx-auto p-6 border max-w-5xl shadow-lg rounded-md bg-white",onClick:t[8]||(t[8]=de(()=>{},["stop"]))},[e("div",fe,[e("div",xe,[e("div",ye,[g(_,{name:"chat-bubble-left-right",class:"w-6 h-6 text-purple-600"})]),t[9]||(t[9]=e("div",null,[e("h3",{class:"text-xl font-semibold text-gray-900"}," ✨ Generatore Domande Colloquio AI "),e("p",{class:"text-sm text-gray-600"}," Crea domande professionali e strutturate per colloqui di selezione ")],-1))]),e("button",{onClick:V,class:"text-gray-400 hover:text-gray-600"},[g(_,{name:"x-mark",class:"w-6 h-6"})])]),e("div",he,[e("div",we,[e("div",ke,[t[13]||(t[13]=e("h4",{class:"font-medium text-blue-900 dark:text-blue-300 mb-2"},"Posizione Lavorativa",-1)),e("div",qe,[e("div",null,[t[10]||(t[10]=e("strong",null,"Titolo:",-1)),m(" "+s(((I=x.jobPosting)==null?void 0:I.title)||"N/A"),1)]),(f=x.jobPosting)!=null&&f.department?(n(),r("div",Ce,[t[11]||(t[11]=e("strong",null,"Dipartimento:",-1)),m(" "+s(x.jobPosting.department.name),1)])):b("",!0),(C=x.jobPosting)!=null&&C.employment_type?(n(),r("div",Ee,[t[12]||(t[12]=e("strong",null,"Contratto:",-1)),m(" "+s(B(x.jobPosting.employment_type)),1)])):b("",!0)])]),x.candidate?(n(),r("div",$e,[t[17]||(t[17]=e("h4",{class:"font-medium text-green-900 dark:text-green-300 mb-2"},"Candidato",-1)),e("div",Ie,[e("div",null,[t[14]||(t[14]=e("strong",null,"Nome:",-1)),m(" "+s(x.candidate.full_name),1)]),x.candidate.location?(n(),r("div",Ae,[t[15]||(t[15]=e("strong",null,"Località:",-1)),m(" "+s(x.candidate.location),1)])):b("",!0),x.candidate.source?(n(),r("div",Te,[t[16]||(t[16]=e("strong",null,"Fonte:",-1)),m(" "+s(K(x.candidate.source)),1)])):b("",!0)])])):b("",!0),e("div",null,[t[19]||(t[19]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Tipo di Colloquio * ",-1)),M(e("select",{"onUpdate:modelValue":t[0]||(t[0]=u=>p.value.interview_type=u),class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500",required:""},t[18]||(t[18]=[ae('<option value="" data-v-6bc4ca0a>Seleziona tipo colloquio</option><option value="phone_screening" data-v-6bc4ca0a>Screening Telefonico</option><option value="technical" data-v-6bc4ca0a>Colloquio Tecnico</option><option value="behavioral" data-v-6bc4ca0a>Colloquio Comportamentale</option><option value="final" data-v-6bc4ca0a>Colloquio Finale</option>',5)]),512),[[te,p.value.interview_type]])]),e("div",null,[t[21]||(t[21]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Durata Prevista ",-1)),M(e("select",{"onUpdate:modelValue":t[1]||(t[1]=u=>p.value.interview_duration=u),class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500"},t[20]||(t[20]=[ae('<option value="30" data-v-6bc4ca0a>30 minuti</option><option value="45" data-v-6bc4ca0a>45 minuti</option><option value="60" data-v-6bc4ca0a>1 ora</option><option value="90" data-v-6bc4ca0a>1.5 ore</option><option value="120" data-v-6bc4ca0a>2 ore</option>',5)]),512),[[te,p.value.interview_duration]])]),e("div",null,[t[23]||(t[23]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Livello di Seniority ",-1)),M(e("select",{"onUpdate:modelValue":t[2]||(t[2]=u=>p.value.seniority_level=u),class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500"},t[22]||(t[22]=[ae('<option value="" data-v-6bc4ca0a>Auto-detect dalla posizione</option><option value="intern" data-v-6bc4ca0a>Stage/Intern</option><option value="junior" data-v-6bc4ca0a>Junior (0-2 anni)</option><option value="mid" data-v-6bc4ca0a>Mid-level (2-5 anni)</option><option value="senior" data-v-6bc4ca0a>Senior (5+ anni)</option><option value="lead" data-v-6bc4ca0a>Lead/Principal</option><option value="executive" data-v-6bc4ca0a>Executive/C-Level</option>',7)]),512),[[te,p.value.seniority_level]])]),e("div",null,[t[28]||(t[28]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Focus Specifici ",-1)),e("div",ze,[e("label",Se,[M(e("input",{type:"checkbox","onUpdate:modelValue":t[3]||(t[3]=u=>p.value.include_technical=u),class:"rounded border-gray-300 text-purple-600 focus:ring-purple-500"},null,512),[[W,p.value.include_technical]]),t[24]||(t[24]=e("span",{class:"ml-2 text-sm text-gray-700"},"Competenze tecniche",-1))]),e("label",Pe,[M(e("input",{type:"checkbox","onUpdate:modelValue":t[4]||(t[4]=u=>p.value.include_situational=u),class:"rounded border-gray-300 text-purple-600 focus:ring-purple-500"},null,512),[[W,p.value.include_situational]]),t[25]||(t[25]=e("span",{class:"ml-2 text-sm text-gray-700"},"Domande situazionali",-1))]),e("label",Le,[M(e("input",{type:"checkbox","onUpdate:modelValue":t[5]||(t[5]=u=>p.value.include_cultural=u),class:"rounded border-gray-300 text-purple-600 focus:ring-purple-500"},null,512),[[W,p.value.include_cultural]]),t[26]||(t[26]=e("span",{class:"ml-2 text-sm text-gray-700"},"Fit culturale",-1))]),e("label",je,[M(e("input",{type:"checkbox","onUpdate:modelValue":t[6]||(t[6]=u=>p.value.include_motivation=u),class:"rounded border-gray-300 text-purple-600 focus:ring-purple-500"},null,512),[[W,p.value.include_motivation]]),t[27]||(t[27]=e("span",{class:"ml-2 text-sm text-gray-700"},"Motivazione e obiettivi",-1))])])]),e("div",null,[t[29]||(t[29]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Note Aggiuntive ",-1)),M(e("textarea",{"onUpdate:modelValue":t[7]||(t[7]=u=>p.value.additional_context=u),rows:"3",placeholder:"Es: Focus su leadership, esperienza internazionale, capacità di problem solving...",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500"},null,512),[[ue,p.value.additional_context]])]),e("div",De,[e("div",Ge,[g(_,{name:"information-circle",class:"w-4 h-4 inline mr-1"}),t[30]||(t[30]=m(" La generazione richiede 10-20 secondi "))]),g(O,{onClick:H,disabled:!D.value||$.value,variant:"primary",class:"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"},{default:j(()=>[$.value?(n(),le(_,{key:0,name:"arrow-path",class:"animate-spin h-4 w-4 mr-2"})):(n(),r("span",Me,"✨")),m(" "+s($.value?"Generazione...":"Genera Domande"),1)]),_:1},8,["disabled"])])]),e("div",Ne,[!l.value&&!$.value?(n(),r("div",Oe,[g(_,{name:"chat-bubble-left-right",class:"mx-auto h-16 w-16 text-gray-400 mb-4"}),t[31]||(t[31]=e("h4",{class:"text-lg font-medium text-gray-900 mb-2"}," Domande Colloquio AI ",-1)),t[32]||(t[32]=e("p",{class:"text-sm"}," Le domande personalizzate appariranno qui ",-1)),t[33]||(t[33]=e("div",{class:"mt-4 text-xs text-gray-400"},[e("div",{class:"flex items-center justify-center space-x-4"},[e("span",null,"🎯 Specifiche"),e("span",null,"📋 Strutturate"),e("span",null,"⏱️ Temporizzate")])],-1))])):b("",!0),$.value?(n(),r("div",Ue,t[34]||(t[34]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"},null,-1),e("h4",{class:"text-lg font-medium text-gray-900 mb-2"}," Generazione in corso... ",-1),e("p",{class:"text-sm text-gray-600"}," L'AI sta creando domande personalizzate per il colloquio ",-1)]))):b("",!0),l.value&&!$.value?(n(),r("div",Re,[e("div",Ve,[e("h4",Fe,[g(_,{name:"sparkles",class:"w-4 h-4 text-purple-600 mr-2"}),t[35]||(t[35]=m(" Domande Generate "))]),e("div",Qe,[e("button",{onClick:q,class:"text-purple-600 hover:text-purple-800 text-sm flex items-center"},[g(_,{name:"clipboard",class:"w-4 h-4 mr-1"}),t[36]||(t[36]=m(" Copia "))]),e("button",{onClick:Z,class:"text-green-600 hover:text-green-800 text-sm flex items-center"},[g(_,{name:"check",class:"w-4 h-4 mr-1"}),t[37]||(t[37]=m(" Usa nel Colloquio "))])])]),e("div",Be,[(R=l.value.opening_questions)!=null&&R.length?(n(),r("div",He,[e("h5",Ze,[g(_,{name:"play",class:"w-4 h-4 mr-1"}),m(" Apertura ("+s(G(l.value.opening_questions.length))+") ",1)]),e("div",Je,[(n(!0),r(P,null,L(l.value.opening_questions,(u,d)=>(n(),r("div",{key:d,class:"text-sm text-gray-700 border-l-4 border-blue-200 pl-3"},[e("strong",null,s(d+1)+".",1),m(" "+s(u),1)]))),128))])])):b("",!0),(a=l.value.experience_questions)!=null&&a.length?(n(),r("div",We,[e("h5",Ke,[g(_,{name:"briefcase",class:"w-4 h-4 mr-1"}),m(" Esperienza ("+s(G(l.value.experience_questions.length))+") ",1)]),e("div",Xe,[(n(!0),r(P,null,L(l.value.experience_questions,(u,d)=>(n(),r("div",{key:d,class:"text-sm text-gray-700 border-l-4 border-green-200 pl-3"},[e("strong",null,s(d+1)+".",1),m(" "+s(u),1)]))),128))])])):b("",!0),(i=l.value.technical_questions)!=null&&i.length?(n(),r("div",Ye,[e("h5",et,[g(_,{name:"code-bracket",class:"w-4 h-4 mr-1"}),m(" Tecniche ("+s(G(l.value.technical_questions.length))+") ",1)]),e("div",tt,[(n(!0),r(P,null,L(l.value.technical_questions,(u,d)=>(n(),r("div",{key:d,class:"text-sm text-gray-700 border-l-4 border-purple-200 pl-3"},[e("strong",null,s(d+1)+".",1),m(" "+s(u),1)]))),128))])])):b("",!0),(c=l.value.behavioral_questions)!=null&&c.length?(n(),r("div",at,[e("h5",it,[g(_,{name:"users",class:"w-4 h-4 mr-1"}),m(" Comportamentali ("+s(G(l.value.behavioral_questions.length))+") ",1)]),e("div",ot,[(n(!0),r(P,null,L(l.value.behavioral_questions,(u,d)=>(n(),r("div",{key:d,class:"text-sm text-gray-700 border-l-4 border-orange-200 pl-3"},[e("strong",null,s(d+1)+".",1),m(" "+s(u),1)]))),128))])])):b("",!0),(y=l.value.motivation_questions)!=null&&y.length?(n(),r("div",lt,[e("h5",nt,[g(_,{name:"fire",class:"w-4 h-4 mr-1"}),m(" Motivazione ("+s(G(l.value.motivation_questions.length))+") ",1)]),e("div",st,[(n(!0),r(P,null,L(l.value.motivation_questions,(u,d)=>(n(),r("div",{key:d,class:"text-sm text-gray-700 border-l-4 border-red-200 pl-3"},[e("strong",null,s(d+1)+".",1),m(" "+s(u),1)]))),128))])])):b("",!0),(A=l.value.closing_questions)!=null&&A.length?(n(),r("div",rt,[e("h5",ut,[g(_,{name:"stop",class:"w-4 h-4 mr-1"}),m(" Chiusura ("+s(G(l.value.closing_questions.length))+") ",1)]),e("div",dt,[(n(!0),r(P,null,L(l.value.closing_questions,(u,d)=>(n(),r("div",{key:d,class:"text-sm text-gray-700 border-l-4 border-gray-200 pl-3"},[e("strong",null,s(d+1)+".",1),m(" "+s(u),1)]))),128))])])):b("",!0),l.value.interview_tips?(n(),r("div",ct,[e("h5",pt,[g(_,{name:"light-bulb",class:"w-4 h-4 mr-1"}),t[38]||(t[38]=m(" Suggerimenti per l'Intervistatore "))]),e("div",vt,[e("p",mt,s(l.value.interview_tips),1)])])):b("",!0),l.value.ai_metadata?(n(),r("div",gt,[e("div",bt,[e("span",null,"Generato: "+s(ee(l.value.ai_metadata.generated_at)),1),e("span",null,"Durata totale stimata: "+s(Y()),1)])])):b("",!0)])])):b("",!0),k.value?(n(),r("div",_t,[g(_,{name:"exclamation-triangle",class:"mx-auto h-12 w-12 text-red-500 mb-4"}),t[40]||(t[40]=e("h4",{class:"text-lg font-medium text-gray-900 mb-2"}," Errore nella generazione ",-1)),e("p",ft,s(k.value),1),g(O,{onClick:J,variant:"secondary",size:"sm"},{default:j(()=>t[39]||(t[39]=[m(" Riprova ")])),_:1,__:[39]})])):b("",!0)])]),e("div",xt,[t[43]||(t[43]=e("div",{class:"text-xs text-gray-500"}," Powered by OpenAI GPT-4o-mini ",-1)),e("div",yt,[g(O,{onClick:V,variant:"secondary"},{default:j(()=>t[41]||(t[41]=[m(" Chiudi ")])),_:1,__:[41]}),l.value?(n(),le(O,{key:0,onClick:Z,variant:"primary",class:"bg-green-600 hover:bg-green-700"},{default:j(()=>[g(_,{name:"check",class:"w-4 h-4 mr-2"}),t[42]||(t[42]=m(" Usa nel Colloquio "))]),_:1,__:[42]})):b("",!0)])])])])):b("",!0)])}}},wt=se(ht,[["__scopeId","data-v-6bc4ca0a"]]),kt={class:"interview-form"},qt={class:"mb-6"},Ct={class:"flex items-center space-x-3 mb-2"},Et={class:"text-2xl font-bold text-gray-900 dark:text-white"},$t={class:"text-sm text-gray-600 dark:text-gray-400"},It={class:"mb-6 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-700"},At={class:"flex items-center justify-between"},Tt={class:"flex items-center space-x-3"},zt={class:"flex-shrink-0"},St={key:0,class:"mt-2 text-xs text-gray-500"},Pt={class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},Lt=["value","onChange"],jt=["value"],Dt={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},Gt={class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},Mt=["value","onChange"],Nt=["value"],Ot={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},Ut={class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},Rt=["value","onChange"],Vt=["value"],Ft={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},Qt={class:"flex justify-end space-x-3"},Bt={key:0,class:"mt-6"},Ht={class:"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6"},Zt={class:"flex"},Jt={class:"flex-shrink-0"},Wt={__name:"InterviewForm",setup(x){const E=ce(),U=ve(),w=me(),{showToast:S}=re(),v=h({candidate_id:"",application_id:"",interviewer_id:"",scheduled_date:"",duration_minutes:60,interview_type:"technical",location:"",notes:""}),$=h({}),k=h(null),l=h(null),p=h([]),D=h([]),H=h([]),F=h(!1),Z=N(()=>w.loading),q=N(()=>!!E.params.id),J=N(()=>q.value?`/app/recruiting/interviews/${E.params.id}`:"/app/recruiting/interviews"),V=N(()=>v.value.candidate_id?D.value.filter(a=>a.candidate_id==v.value.candidate_id):D.value),Q=N(()=>v.value.candidate_id?p.value.find(a=>a.id==v.value.candidate_id):null),B=N(()=>{if(!v.value.application_id)return null;const a=D.value.find(i=>i.id==v.value.application_id);return(a==null?void 0:a.job_posting)||null}),K=N(()=>[{id:"candidate_id",type:"custom",label:"Candidato",required:!0},{id:"application_id",type:"custom",label:"Candidatura",required:!0},{id:"interviewer_id",type:"custom",label:"Intervistatore",required:!0},{id:"scheduled_date",type:"datetime-local",label:"Data e Ora",required:!0},{id:"duration_minutes",type:"number",label:"Durata (minuti)",placeholder:"60",min:15,max:480,required:!0},{id:"interview_type",type:"select",label:"Tipo Colloquio",required:!0,options:[{value:"phone",label:"Telefonico"},{value:"video",label:"Video Call"},{value:"in_person",label:"Di Persona"},{value:"technical",label:"Tecnico"},{value:"behavioral",label:"Comportamentale"},{value:"final",label:"Finale"}]},{id:"location",type:"text",label:"Luogo/Link",placeholder:"Ufficio principale o link video call"},{id:"notes",type:"textarea",label:"Note per il Colloquio",placeholder:"Argomenti da trattare, focus specifici...",rows:3}]),X=async()=>{if(q.value)try{const a=await w.fetchInterview(parseInt(E.params.id));a&&(l.value=a,v.value={candidate_id:a.candidate_id||"",application_id:a.application_id||"",interviewer_id:a.interviewer_id||"",scheduled_date:a.scheduled_date?a.scheduled_date.slice(0,16):"",duration_minutes:a.duration_minutes||60,interview_type:a.interview_type||"technical",location:a.location||"",notes:a.notes||""})}catch(a){console.error("Error loading interview:",a),k.value="Errore nel caricamento del colloquio"}},G=async()=>{try{await Promise.all([Y(),ee(),o()]),E.query.candidate_id&&(v.value.candidate_id=E.query.candidate_id),E.query.application_id&&(v.value.application_id=E.query.application_id)}catch(a){console.error("Error loading dropdown data:",a)}},Y=async()=>{const a=await w.fetchCandidates();p.value=(a==null?void 0:a.candidates)||[]},ee=async()=>{const a=await w.fetchApplications();D.value=(a==null?void 0:a.applications)||[]},o=async()=>{const a=await w.fetchInterviewers();H.value=a||[]},t=()=>{const a={};return v.value.candidate_id||(a.candidate_id="Il candidato è obbligatorio"),v.value.application_id||(a.application_id="La candidatura è obbligatoria"),v.value.interviewer_id||(a.interviewer_id="L'intervistatore è obbligatorio"),v.value.scheduled_date?new Date(v.value.scheduled_date)<=new Date&&(a.scheduled_date="La data deve essere futura"):a.scheduled_date="Data e ora sono obbligatorie",(!v.value.duration_minutes||v.value.duration_minutes<15)&&(a.duration_minutes="La durata minima è 15 minuti"),v.value.interview_type||(a.interview_type="Il tipo di colloquio è obbligatorio"),$.value=a,Object.keys(a).length===0},I=async()=>{if(k.value=null,!t()){k.value="Controlla i campi del modulo e riprova";return}try{const a={...v.value};if(a.scheduled_date&&(a.scheduled_date=new Date(a.scheduled_date).toISOString()),a.candidate_id=parseInt(a.candidate_id),a.application_id=parseInt(a.application_id),a.interviewer_id=parseInt(a.interviewer_id),a.duration_minutes=parseInt(a.duration_minutes),Object.keys(a).forEach(i=>{a[i]===""&&(a[i]=null)}),q.value)await w.updateInterview(parseInt(E.params.id),a),U.push(`/app/recruiting/interviews/${E.params.id}`);else{const i=await w.createInterview(a);U.push(`/app/recruiting/interviews/${i.id}`)}}catch(a){console.error("Error saving interview:",a),k.value=q.value?"Errore nell'aggiornamento del colloquio":"Errore nella programmazione del colloquio"}},f=()=>{U.push(J.value)},C=a=>{console.log("Questions generated:",a),S("Domande colloquio generate con successo!","success")},R=a=>{var c,y,A,u,d,oe;console.log("Questions applied:",a);let i=`DOMANDE COLLOQUIO GENERATE DA AI:

`;(c=a.opening_questions)!=null&&c.length&&(i+=`APERTURA:
`,a.opening_questions.forEach((T,z)=>{i+=`${z+1}. ${T}
`}),i+=`
`),(y=a.experience_questions)!=null&&y.length&&(i+=`ESPERIENZA:
`,a.experience_questions.forEach((T,z)=>{i+=`${z+1}. ${T}
`}),i+=`
`),(A=a.technical_questions)!=null&&A.length&&(i+=`TECNICHE:
`,a.technical_questions.forEach((T,z)=>{i+=`${z+1}. ${T}
`}),i+=`
`),(u=a.behavioral_questions)!=null&&u.length&&(i+=`COMPORTAMENTALI:
`,a.behavioral_questions.forEach((T,z)=>{i+=`${z+1}. ${T}
`}),i+=`
`),(d=a.motivation_questions)!=null&&d.length&&(i+=`MOTIVAZIONE:
`,a.motivation_questions.forEach((T,z)=>{i+=`${z+1}. ${T}
`}),i+=`
`),(oe=a.closing_questions)!=null&&oe.length&&(i+=`CHIUSURA:
`,a.closing_questions.forEach((T,z)=>{i+=`${z+1}. ${T}
`}),i+=`
`),a.interview_tips&&(i+=`SUGGERIMENTI PER L'INTERVISTATORE:
${a.interview_tips}
`),v.value.notes=i+`

--- NOTE AGGIUNTIVE ---
`+(v.value.notes||""),S("Domande applicate al colloquio!","success")};return ne(()=>v.value.candidate_id,a=>{if(a&&v.value.application_id){const i=D.value.find(c=>c.id==v.value.application_id);i&&i.candidate_id!=a&&(v.value.application_id="")}}),pe(async()=>{await Promise.all([G(),X()])}),(a,i)=>(n(),r("div",kt,[e("div",qt,[e("div",Ct,[g(O,{variant:"ghost",icon:"arrow-left",onClick:f,size:"sm"}),e("h1",Et,s(q.value?"Modifica Colloquio":"Programma Colloquio"),1)]),e("p",$t,s(q.value?"Aggiorna i dettagli del colloquio":"Pianifica un nuovo colloquio con il candidato"),1)]),e("div",It,[e("div",At,[e("div",Tt,[e("div",zt,[g(_,{name:"chat-bubble-left-right",class:"w-6 h-6 text-purple-600"})]),i[3]||(i[3]=e("div",null,[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"}," ✨ Generatore Domande AI "),e("p",{class:"text-sm text-gray-600 dark:text-gray-400"}," Genera domande professionali e strutturate per il colloquio utilizzando l'intelligenza artificiale ")],-1))]),g(O,{onClick:i[0]||(i[0]=c=>F.value=!0),disabled:!Q.value||!B.value,variant:"primary",class:"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700",icon:"sparkles"},{default:j(()=>i[4]||(i[4]=[m(" Genera Domande ")])),_:1,__:[4]},8,["disabled"])]),!Q.value||!B.value?(n(),r("div",St," Seleziona candidato e posizione nel form per abilitare la generazione AI ")):b("",!0)]),g(be,{fields:K.value,modelValue:v.value,"onUpdate:modelValue":i[1]||(i[1]=c=>v.value=c),errors:$.value,"global-error":k.value,loading:Z.value,"submit-label":q.value?"Aggiorna Colloquio":"Programma Colloquio","loading-label":q.value?"Aggiornamento...":"Programmazione...","cancel-label":"Annulla","cancel-route":J.value,onSubmit:I},{"field-candidate_id":j(({field:c,value:y,update:A,error:u})=>[e("div",null,[e("label",Pt,s(c.label)+" "+s(c.required?"*":""),1),e("select",{value:y,onChange:d=>A(d.target.value),class:ie(["w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",u?"border-red-300 dark:border-red-600":"border-gray-300 dark:border-gray-600"])},[i[5]||(i[5]=e("option",{value:""},"Seleziona candidato",-1)),(n(!0),r(P,null,L(p.value,d=>(n(),r("option",{key:d.id,value:d.id},s(d.full_name)+" - "+s(d.email),9,jt))),128))],42,Lt),u?(n(),r("p",Dt,s(u),1)):b("",!0)])]),"field-application_id":j(({field:c,value:y,update:A,error:u})=>[e("div",null,[e("label",Gt,s(c.label)+" "+s(c.required?"*":""),1),e("select",{value:y,onChange:d=>A(d.target.value),class:ie(["w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",u?"border-red-300 dark:border-red-600":"border-gray-300 dark:border-gray-600"])},[i[6]||(i[6]=e("option",{value:""},"Seleziona candidatura",-1)),(n(!0),r(P,null,L(V.value,d=>(n(),r("option",{key:d.id,value:d.id},s(d.job_posting.title)+" - "+s(d.candidate.full_name),9,Nt))),128))],42,Mt),u?(n(),r("p",Ot,s(u),1)):b("",!0)])]),"field-interviewer_id":j(({field:c,value:y,update:A,error:u})=>[e("div",null,[e("label",Ut,s(c.label)+" "+s(c.required?"*":""),1),e("select",{value:y,onChange:d=>A(d.target.value),class:ie(["w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",u?"border-red-300 dark:border-red-600":"border-gray-300 dark:border-gray-600"])},[i[7]||(i[7]=e("option",{value:""},"Seleziona intervistatore",-1)),(n(!0),r(P,null,L(H.value,d=>(n(),r("option",{key:d.id,value:d.id},s(d.full_name)+" ("+s(d.role)+") ",9,Vt))),128))],42,Rt),u?(n(),r("p",Ft,s(u),1)):b("",!0)])]),actions:j(({loading:c,submit:y})=>[e("div",Qt,[g(O,{variant:"secondary",text:"Annulla",onClick:f}),g(O,{variant:"primary",text:q.value?"Aggiorna Colloquio":"Programma Colloquio",loading:c,onClick:y,icon:"calendar"},null,8,["text","loading","onClick"])])]),_:1},8,["fields","modelValue","errors","global-error","loading","submit-label","loading-label","cancel-route"]),q.value?b("",!0):(n(),r("div",Bt,[e("div",Ht,[e("div",Zt,[e("div",Jt,[g(_,{name:"light-bulb",size:"md",class:"text-blue-400"})]),i[8]||(i[8]=e("div",{class:"ml-3"},[e("h3",{class:"text-sm font-medium text-blue-800 dark:text-blue-200"}," Suggerimenti per il Colloquio "),e("div",{class:"mt-2 text-sm text-blue-700 dark:text-blue-300"},[e("ul",{class:"list-disc pl-5 space-y-1"},[e("li",null,"Prepara domande specifiche basate sul CV del candidato"),e("li",null,"Assicurati di avere almeno 60 minuti per colloqui tecnici"),e("li",null,"Usa videochiamate per candidati remoti"),e("li",null,"Prepara una descrizione dettagliata del ruolo e dell'azienda"),e("li",null,"Lascia spazio per le domande del candidato")])])],-1))])])])),g(wt,{show:F.value,"job-posting":B.value,candidate:Q.value,onQuestionsGenerated:C,onQuestionsApplied:R,onClose:i[2]||(i[2]=c=>F.value=!1)},null,8,["show","job-posting","candidate"])]))}},oa=se(Wt,[["__scopeId","data-v-199e5838"]]);export{oa as default};
