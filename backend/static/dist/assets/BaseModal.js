import{c as n,b as d,l as a,o as l,j as t,h as p,t as y,K as c,A as h,n as g}from"./vendor.js";import{S as v}from"./StandardButton.js";const k={class:"p-5"},B={class:"flex items-center justify-between mb-4"},C={class:"text-lg font-medium text-gray-900 dark:text-white"},b={class:"mt-3"},z={key:0,class:"flex justify-end space-x-3 mt-6"},O={__name:"BaseModal",props:{show:{type:Boolean,default:!1},title:{type:String,required:!0},size:{type:String,default:"md",validator:e=>["xs","sm","md","lg","xl","2xl"].includes(e)},position:{type:String,default:"center",validator:e=>["top","center"].includes(e)},showCloseButton:{type:<PERSON><PERSON>an,default:!0},closeOnOverlay:{type:Boolean,default:!0}},emits:["close"],setup(e,{emit:m}){const o=e,u=m,x=n(()=>({xs:"w-80 max-w-xs",sm:"w-96 max-w-sm",md:"w-[32rem] max-w-md",lg:"w-[48rem] max-w-2xl",xl:"w-[64rem] max-w-4xl","2xl":"w-[80rem] max-w-6xl"})[o.size]),f=n(()=>o.position==="top"?"top-20":"top-1/2 -translate-y-1/2"),i=()=>{u("close")},w=()=>{o.closeOnOverlay&&i()};return(s,r)=>e.show?(l(),d("div",{key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:w},[t("div",{class:g(["relative mx-auto border shadow-lg rounded-md bg-white dark:bg-gray-800",[x.value,f.value]]),onClick:r[0]||(r[0]=h(()=>{},["stop"]))},[t("div",k,[t("div",B,[t("h3",C,y(e.title),1),e.showCloseButton?(l(),p(v,{key:0,variant:"ghost",icon:"x-mark",size:"sm",onClick:i})):a("",!0)]),t("div",b,[c(s.$slots,"default")]),s.$slots.footer?(l(),d("div",z,[c(s.$slots,"footer")])):a("",!0)])],2)])):a("",!0)}};export{O as _};
