import{r as v,x as ae,b as n,e as d,j as s,l,B as te,C as se,D as ie,h as C,s as p,t as o,F,p as O,n as A,q as ne,o as i,A as oe}from"./vendor.js";import{u as re}from"./funding.js";import{_ as le,u as ce,a as de,H as c,i as me,d as ue}from"./app.js";import{_ as ge}from"./FilterBar.js";import{_ as ye}from"./PageHeader.js";const pe={class:"funding-search"},xe={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6"},ve={class:"max-w-4xl mx-auto"},_e={class:"flex gap-4"},be={class:"flex-1"},he=["disabled"],fe={class:"mt-4"},ke={class:"flex flex-wrap gap-2"},Ae=["onClick"],ze={class:"mb-6"},we={class:"flex space-x-8","aria-label":"Tabs"},Ie={key:0,class:"ml-2 bg-primary-100 dark:bg-primary-900/50 text-primary-600 dark:text-primary-400 py-1 px-2 rounded-full text-xs"},Ce={key:0,class:"ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 py-1 px-2 rounded-full text-xs"},De={key:0,class:"text-center py-12"},Se={key:1},Re={key:0,class:"text-center py-12"},$e={key:1,class:"text-center py-12"},Ee={key:2,class:"space-y-4"},Te={class:"flex items-start justify-between mb-4"},Fe={class:"flex-1"},Oe={class:"text-lg font-semibold text-gray-900 dark:text-white mb-2"},Be={class:"flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mb-2"},Me={key:0,class:"flex items-center gap-1"},qe={key:1,class:"flex items-center gap-1"},Ne={key:2,class:"flex items-center gap-1"},Pe={key:0,class:"flex-shrink-0 ml-4"},Ve={class:"mb-4"},Le={class:"text-gray-700 dark:text-gray-300 text-sm leading-relaxed line-clamp-3"},je={key:0,class:"flex flex-wrap gap-4 mb-4 text-xs text-gray-500 dark:text-gray-400"},He={key:0,class:"flex items-center gap-1"},Ue={key:1,class:"flex items-center gap-1"},Ge={key:1,class:"mb-4 p-4 bg-secondary-50 dark:bg-secondary-900/20 rounded-lg border border-secondary-200 dark:border-secondary-700"},Je={class:"text-sm font-semibold text-secondary-900 dark:text-secondary-100 mb-3 flex items-center gap-2"},Ke={class:"grid grid-cols-2 md:grid-cols-4 gap-2 mb-3"},Qe={key:0,class:"text-center"},We={key:1,class:"text-center"},Xe={key:2,class:"text-center"},Ye={key:3,class:"text-center"},Ze={class:"space-y-2 text-sm"},ea={key:0,class:"text-purple-700"},aa={key:1},ta={class:"list-disc list-inside mt-1 text-purple-700 text-xs"},sa={key:0,class:"text-purple-600 italic"},ia={key:2,class:"flex gap-4 text-xs"},na={key:0,class:"flex-1"},oa={class:"text-green-600"},ra={key:0,class:"text-green-500"},la={key:1,class:"flex-1"},ca={class:"text-orange-600"},da={key:0,class:"text-orange-500"},ma={key:3,class:"text-xs text-purple-600 mt-2 pt-2 border-t border-purple-200"},ua={class:"flex gap-2 mt-4"},ga=["onClick"],ya=["onClick","disabled"],pa={key:2},xa={key:3},va=["onClick","disabled"],_a={key:2},ba={key:3},ha={key:4},fa={key:5},ka={key:2},Aa={class:"mb-4"},za={key:0,class:"text-center py-12"},wa={key:1,class:"space-y-4"},Ia=["onClick"],Ca={class:"flex items-start justify-between"},Da={class:"flex-1"},Sa={class:"flex items-center gap-3 mb-2"},Ra={class:"text-lg font-semibold text-gray-900"},$a={key:0,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800"},Ea={class:"text-gray-600 mb-3 line-clamp-2"},Ta={class:"flex items-center gap-6 text-sm text-gray-600"},Fa={class:"flex items-center gap-1"},Oa={class:"flex items-center gap-1"},Ba={class:"text-right ml-6"},Ma={class:"text-xl font-bold text-brand-primary-600"},qa=["onClick","disabled"],Na={key:0,class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Pa={key:1,class:"w-4 h-4 animate-spin",fill:"none",viewBox:"0 0 24 24"},Va={__name:"FundingSearch",setup(La){const N=ne(),z=re(),u=ce();de();const{showToast:b}=ue(),f=v("ai"),_=v(""),I=v(!1),P=v(!1),m=v([]),k=v([]),H=v(""),D=v({}),S=v({}),E=v({}),R=v({}),U=v(["digitalizzazione PMI","industria 4.0","innovazione tecnologica","startup innovative","ricerca e sviluppo","transizione ecologica","export internazionalizzazione"]),G=v([{key:"status",label:"Stato",type:"select",options:[{value:"",label:"Tutti"},{value:"open",label:"Aperti"},{value:"closed",label:"Chiusi"}]},{key:"ai_generated",label:"Origine",type:"select",options:[{value:"",label:"Tutti"},{value:"true",label:"Generati da AI"},{value:"false",label:"Inseriti manualmente"}]}]);async function B(){var t,a,e,r,g,y,w;if(_.value.trim()){I.value=!0,P.value=!0;try{const x={name:((t=u.company)==null?void 0:t.name)||"Azienda",sector:((a=u.company)==null?void 0:a.sector)||"ICT",size:((e=u.company)==null?void 0:e.size)||"PMI",location:((r=u.company)==null?void 0:r.location)||((g=u.contact)==null?void 0:g.address)||"Italia",activities:((y=u.company)==null?void 0:y.description)||((w=u.company)==null?void 0:w.tagline)||"Digitalizzazione e innovazione"},T={keywords:_.value,query:_.value,max_results:10,max_amount:"Qualsiasi",geographic_scope:"Nazionale/Regionale",target_sectors:[],deadline_limit:"6 mesi"};console.log("🔍 Ricerca AI:",_.value);const h=await z.searchOpportunitiesWithAI(x,T);if(!h.search_performed)throw new Error(h.error||"Ricerca AI non eseguita");m.value=h.opportunities||[],H.value=`Trovati ${m.value.length} risultati per "${_.value}"`,m.value.length>0&&await W(m.value),h.raw_ai_content&&console.log("🤖 Contenuto AI grezzo:",h.raw_ai_content.substring(0,200)+"..."),f.value="ai",b("success",`Ricerca completata: ${m.value.length} risultati trovati`)}catch(x){console.error("❌ Errore ricerca AI:",x),b("error","Errore nella ricerca AI: "+(x.message||"Errore sconosciuto")),m.value=[],x.fallback_content&&(m.value=[{title:"Ricerca AI - Risultato parziale",description:x.fallback_content,match_score:30,source_entity:"Sistema AI"}])}finally{I.value=!1}}}async function J(t,a){var e,r,g,y,w,x,T;try{R.value[a]=!0,console.log("🤖 Analisi AI per:",t.title);const h={name:((e=u.company)==null?void 0:e.name)||"Azienda",sector:((r=u.company)==null?void 0:r.sector)||"ICT",size:((g=u.company)==null?void 0:g.size)||"PMI",location:((y=u.company)==null?void 0:y.location)||((w=u.contact)==null?void 0:w.address)||"Italia",activities:((x=u.company)==null?void 0:x.description)||((T=u.company)==null?void 0:T.tagline)||"Digitalizzazione e innovazione"},q=await z.calculateAIMatchScore(t,h);console.log("🔍 Analisi ricevuta:",q),f.value==="ai"?m.value[a].aiAnalysis=q:k.value[a].aiAnalysis=q,console.log("✅ Risultato aggiornato:",f.value==="ai"?m.value[a]:k.value[a]),b("success","Analisi AI completata")}catch(h){console.error("❌ Errore analisi AI:",h),b("error","Errore nell'analisi AI: "+(h.message||"Errore sconosciuto"))}finally{R.value[a]=!1}}async function K(t){var a,e;try{if(D.value[t.id]=!0,t.saved||t.database_id){b("warning","Bando già salvato nel database");return}const r=await z.checkDuplicateOpportunity(t.title);if(r.exists){b("warning",`Esiste già un bando simile: "${r.existing_title}"`);return}const g=new Date(Date.now()+60*24*60*60*1e3).toISOString().split("T")[0],y={title:t.title||"Bando da ricerca AI",application_deadline:t.deadline||g,description:t.description||t.compatibility_reason||"Descrizione non disponibile",source_entity:t.source_entity||"Da verificare",max_grant_amount:parseFloat(t.max_grant_amount)||1e5,contribution_percentage:parseFloat(t.contribution_percentage)||50,status:"open",geographic_scope:t.geographic_scope||"nazionale",official_url:t.official_link||null,target_sectors:Array.isArray(t.target_sectors)?t.target_sectors:[],eligibility_criteria:t.requirements?[t.requirements]:[],ai_generated:!0,ai_search_query:_.value,ai_match_score:t.match_score||0,ai_content:JSON.stringify(t)};console.log("💾 Salvataggio dati:",y);const w=await z.createOpportunity(y);m.value=m.value.map(x=>x.id===t.id?{...x,saved:!0,database_id:w.id}:x),await M(),b("success",`Bando "${t.title}" salvato nel database`)}catch(r){console.error("❌ Errore salvataggio:",r);const g=((e=(a=r.response)==null?void 0:a.data)==null?void 0:e.message)||r.message||"Errore sconosciuto";b("error",`Errore nel salvataggio: ${g}`)}finally{D.value[t.id]=!1}}async function M(){try{const t=await z.fetchOpportunities();k.value=t.opportunities||[]}catch(t){console.error("❌ Errore caricamento database:",t)}}async function Q(t){var a,e;if(!S.value[t.id]&&confirm(`Sei sicuro di voler eliminare il bando "${t.title}"?`)){S.value[t.id]=!0;try{await z.deleteOpportunity(t.id),k.value=k.value.filter(r=>r.id!==t.id),b("success",`Bando "${t.title}" eliminato con successo`)}catch(r){console.error("❌ Errore eliminazione:",r);const g=((e=(a=r.response)==null?void 0:a.data)==null?void 0:e.message)||r.message||"Errore sconosciuto";b("error",`Errore nell'eliminazione: ${g}`)}finally{S.value[t.id]=!1}}}function V(t){var a;return!(t.saved||t.database_id||(a=E.value[t.title])!=null&&a.exists)}async function W(t){for(const a of t)if(!E.value[a.title]&&!a.saved&&!a.database_id)try{const e=await z.checkDuplicateOpportunity(a.title);E.value[a.title]=e}catch(e){console.error("Errore controllo duplicati per",a.title,":",e)}}function X(t){M()}function Y(t){N.push(`/app/funding/opportunities/${t}`)}function Z(t){const a={ai:"true",search:_.value,data:encodeURIComponent(JSON.stringify(t))};N.push({name:"funding-opportunity-view",params:{id:t.id||"ai"},query:a})}function L(t){return t?new Date(t).toLocaleDateString("it-IT",{day:"numeric",month:"long",year:"numeric"}):""}function ee(t){return t?new Date(t).toLocaleDateString("it-IT",{day:"numeric",month:"short",year:"numeric",hour:"2-digit",minute:"2-digit"}):""}function j(t){return t?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR",minimumFractionDigits:0,maximumFractionDigits:0}).format(t):"€0"}function $(t){return t<50?"bg-red-100 text-red-800":t<75?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800"}return ae(()=>{console.log("🔍 FundingSearch mounted - nuova versione"),M()}),(t,a)=>(i(),n("div",pe,[d(ye,{title:"Ricerca Bandi",subtitle:"Cerca opportunità di finanziamento con intelligenza artificiale"}),s("div",xe,[s("div",ve,[s("div",_e,[s("div",be,[te(s("input",{"onUpdate:modelValue":a[0]||(a[0]=e=>_.value=e),type:"text",placeholder:"Descrivi il tipo di bando che stai cercando (es: digitalizzazione PMI, industria 4.0, innovazione...)",class:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-400",onKeydown:ie(B,["enter"])},null,544),[[se,_.value]])]),s("button",{onClick:B,disabled:!_.value.trim()||I.value,class:"px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 dark:bg-primary-500 dark:hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"},[I.value?(i(),C(c,{key:0,name:"arrow-path",size:"sm",class:"animate-spin"})):(i(),C(c,{key:1,name:"magnifying-glass",size:"sm"})),p(" "+o(I.value?"Ricerca in corso...":"Cerca con AI"),1)],8,he)]),s("div",fe,[a[3]||(a[3]=s("p",{class:"text-sm text-gray-600 dark:text-gray-400 mb-2"},"Suggerimenti:",-1)),s("div",ke,[(i(!0),n(F,null,O(U.value,e=>(i(),n("button",{key:e,onClick:r=>{_.value=e,B()},class:"px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600"},o(e),9,Ae))),128))])])])]),s("div",ze,[s("nav",we,[s("button",{onClick:a[1]||(a[1]=e=>f.value="ai"),class:A(["py-2 px-1 border-b-2 font-medium text-sm",f.value==="ai"?"border-primary-500 text-primary-600 dark:text-primary-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"])},[d(c,{name:"sparkles",size:"sm",class:"inline mr-2"}),a[4]||(a[4]=p(" Risultati AI ")),m.value.length?(i(),n("span",Ie,o(m.value.length),1)):l("",!0)],2),s("button",{onClick:a[2]||(a[2]=e=>f.value="database"),class:A(["py-2 px-1 border-b-2 font-medium text-sm",f.value==="database"?"border-gray-500 text-gray-600 dark:text-gray-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"])},[d(c,{name:"database",size:"sm",class:"inline mr-2"}),a[5]||(a[5]=p(" Database Locale ")),k.value.length?(i(),n("span",Ce,o(k.value.length),1)):l("",!0)],2)])]),I.value?(i(),n("div",De,[d(me),a[6]||(a[6]=s("p",{class:"mt-4 text-gray-600 dark:text-gray-400"},"Ricerca in corso...",-1))])):f.value==="ai"?(i(),n("div",Se,[P.value?m.value.length===0?(i(),n("div",$e,[d(c,{name:"exclamation-triangle",size:"lg",class:"mx-auto text-amber-400 mb-4"}),a[9]||(a[9]=s("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Nessun risultato trovato",-1)),a[10]||(a[10]=s("p",{class:"text-gray-600 dark:text-gray-400"},"Prova con termini di ricerca diversi",-1))])):l("",!0):(i(),n("div",Re,[d(c,{name:"magnifying-glass",size:"lg",class:"mx-auto text-gray-400 mb-4"}),a[7]||(a[7]=s("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Inizia una ricerca",-1)),a[8]||(a[8]=s("p",{class:"text-gray-600 dark:text-gray-400"},"Inserisci una query per cercare bandi con l'intelligenza artificiale",-1))])),m.value.length>0?(i(),n("div",Ee,[(i(!0),n(F,null,O(m.value,(e,r)=>{var g;return i(),n("div",{key:`ai-${r}`,class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-secondary-200 dark:border-secondary-700 p-6"},[s("div",Te,[s("div",Fe,[s("h3",Oe,o(e.title||"Bando senza titolo"),1),s("div",Be,[e.source_entity?(i(),n("span",Me,[d(c,{name:"building-office",size:"xs"}),p(" "+o(e.source_entity),1)])):l("",!0),e.application_deadline?(i(),n("span",qe,[d(c,{name:"calendar-days",size:"xs"}),p(" Scadenza: "+o(L(e.application_deadline)),1)])):l("",!0),e.max_grant_amount?(i(),n("span",Ne,[d(c,{name:"currency-euro",size:"xs"}),p(" "+o(j(e.max_grant_amount)),1)])):l("",!0)])]),e.match_score?(i(),n("div",Pe,[s("div",{class:A(["inline-flex items-center px-3 py-1 rounded-full text-sm font-medium",$(e.match_score)])},o(e.match_score)+"% match ",3)])):l("",!0)]),s("div",Ve,[s("p",Le,o(e.description||"Descrizione non disponibile"),1)]),e.contribution_percentage||e.geographic_scope?(i(),n("div",je,[e.contribution_percentage?(i(),n("span",He,[d(c,{name:"calculator",size:"xs"}),p(" Finanziamento: "+o(e.contribution_percentage)+"% ",1)])):l("",!0),e.geographic_scope?(i(),n("span",Ue,[d(c,{name:"map-pin",size:"xs"}),p(" Ambito: "+o(e.geographic_scope),1)])):l("",!0)])):l("",!0),e.aiAnalysis?(i(),n("div",Ge,[s("h4",Je,[d(c,{name:"sparkles",size:"sm"}),a[11]||(a[11]=p(" Analisi AI Dettagliata "))]),s("div",Ke,[e.aiAnalysis.match_score?(i(),n("div",Qe,[a[12]||(a[12]=s("div",{class:"text-xs text-secondary-700 dark:text-secondary-300 mb-1"},"Compatibilità",-1)),s("div",{class:A(["px-2 py-1 rounded text-xs font-bold",$(e.aiAnalysis.match_score)])},o(e.aiAnalysis.match_score)+"% ",3)])):l("",!0),e.aiAnalysis.technical_compatibility?(i(),n("div",We,[a[13]||(a[13]=s("div",{class:"text-xs text-secondary-700 dark:text-secondary-300 mb-1"},"Tecnica",-1)),s("div",{class:A(["px-2 py-1 rounded text-xs font-bold",$(e.aiAnalysis.technical_compatibility)])},o(e.aiAnalysis.technical_compatibility)+"% ",3)])):l("",!0),e.aiAnalysis.financial_feasibility?(i(),n("div",Xe,[a[14]||(a[14]=s("div",{class:"text-xs text-purple-700 mb-1"},"Finanziaria",-1)),s("div",{class:A(["px-2 py-1 rounded text-xs font-bold",$(e.aiAnalysis.financial_feasibility)])},o(e.aiAnalysis.financial_feasibility)+"% ",3)])):l("",!0),e.aiAnalysis.success_probability?(i(),n("div",Ye,[a[15]||(a[15]=s("div",{class:"text-xs text-purple-700 mb-1"},"Successo",-1)),s("div",{class:A(["px-2 py-1 rounded text-xs font-bold",$(e.aiAnalysis.success_probability)])},o(e.aiAnalysis.success_probability)+"% ",3)])):l("",!0)]),s("div",Ze,[e.aiAnalysis.recommendation?(i(),n("div",ea,[a[16]||(a[16]=s("span",{class:"font-medium text-purple-800"},"Raccomandazione:",-1)),p(" "+o(e.aiAnalysis.recommendation),1)])):l("",!0),e.aiAnalysis.insights&&e.aiAnalysis.insights.length>0?(i(),n("div",aa,[a[17]||(a[17]=s("span",{class:"font-medium text-purple-800"},"Insights Chiave:",-1)),s("ul",ta,[(i(!0),n(F,null,O(e.aiAnalysis.insights.slice(0,2),y=>(i(),n("li",{key:y},o(y),1))),128)),e.aiAnalysis.insights.length>2?(i(),n("li",sa," +"+o(e.aiAnalysis.insights.length-2)+" altri insights... ",1)):l("",!0)])])):l("",!0),e.aiAnalysis.strengths&&e.aiAnalysis.strengths.length>0||e.aiAnalysis.weaknesses&&e.aiAnalysis.weaknesses.length>0?(i(),n("div",ia,[e.aiAnalysis.strengths&&e.aiAnalysis.strengths.length>0?(i(),n("div",na,[a[18]||(a[18]=s("span",{class:"font-medium text-green-700"},"Punti di forza:",-1)),s("span",oa,o(e.aiAnalysis.strengths[0]),1),e.aiAnalysis.strengths.length>1?(i(),n("span",ra,"+"+o(e.aiAnalysis.strengths.length-1),1)):l("",!0)])):l("",!0),e.aiAnalysis.weaknesses&&e.aiAnalysis.weaknesses.length>0?(i(),n("div",la,[a[19]||(a[19]=s("span",{class:"font-medium text-orange-700"},"Da migliorare:",-1)),s("span",ca,o(e.aiAnalysis.weaknesses[0]),1),e.aiAnalysis.weaknesses.length>1?(i(),n("span",da,"+"+o(e.aiAnalysis.weaknesses.length-1),1)):l("",!0)])):l("",!0)])):l("",!0),e.aiAnalysis.analysis_timestamp?(i(),n("div",ma," Analisi del "+o(ee(e.aiAnalysis.analysis_timestamp)),1)):l("",!0)])])):l("",!0),s("div",ua,[s("button",{onClick:y=>Z(e),class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-blue-700 bg-blue-100 rounded-md hover:bg-blue-200"},[d(c,{name:"eye",size:"sm",class:"mr-1"}),a[20]||(a[20]=p(" Visualizza Dettaglio "))],8,ga),s("button",{onClick:y=>J(e,r),disabled:R.value[r],class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-purple-700 bg-purple-100 rounded-md hover:bg-purple-200 disabled:opacity-50"},[R.value[r]?(i(),C(c,{key:0,name:"arrow-path",size:"sm",class:"mr-1 animate-spin"})):(i(),C(c,{key:1,name:"sparkles",size:"sm",class:"mr-1"})),R.value[r]?(i(),n("span",pa,"Analisi...")):(i(),n("span",xa,o(e.aiAnalysis?"Rianalizza":"Analizza con AI"),1))],8,ya),s("button",{onClick:y=>K(e),disabled:D.value[e.id]||e.saved||!V(e),class:A(["inline-flex items-center px-3 py-1.5 text-sm font-medium rounded-md disabled:opacity-50",e.saved||!V(e)?"text-gray-500 bg-gray-100 cursor-not-allowed":"text-green-700 bg-green-100 hover:bg-green-200"])},[D.value[e.id]?(i(),C(c,{key:0,name:"arrow-path",size:"sm",class:"mr-1 animate-spin"})):(i(),C(c,{key:1,name:"archive-box",size:"sm",class:"mr-1"})),D.value[e.id]?(i(),n("span",_a,"Salvataggio...")):e.saved?(i(),n("span",ba,"Già salvato")):(g=E.value[e.title])!=null&&g.exists?(i(),n("span",ha,"Duplicato")):(i(),n("span",fa,"Salva nel DB"))],10,va)])])}),128))])):l("",!0)])):f.value==="database"?(i(),n("div",ka,[s("div",Aa,[d(ge,{filters:G.value,onFilterChange:X},null,8,["filters"])]),k.value.length===0?(i(),n("div",za,[d(c,{name:"database",size:"lg",class:"mx-auto text-gray-400 mb-4"}),a[21]||(a[21]=s("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Nessun bando nel database",-1)),a[22]||(a[22]=s("p",{class:"text-gray-600"},"I bandi salvati appariranno qui",-1))])):(i(),n("div",wa,[(i(!0),n(F,null,O(k.value,e=>(i(),n("div",{key:e.id,class:"bg-white border rounded-lg p-6 hover:shadow-md transition-shadow cursor-pointer",onClick:r=>Y(e.id)},[s("div",Ca,[s("div",Da,[s("div",Sa,[s("h3",Ra,o(e.title),1),e.ai_generated?(i(),n("span",$a,[d(c,{name:"sparkles",size:"xs",class:"mr-1"}),a[23]||(a[23]=p(" Da AI "))])):l("",!0)]),s("p",Ea,o(e.description),1),s("div",Ta,[s("span",Fa,[d(c,{name:"building-office",size:"xs"}),p(" "+o(e.source_entity),1)]),s("span",Oa,[d(c,{name:"calendar",size:"xs"}),p(" "+o(L(e.application_deadline)),1)])])]),s("div",Ba,[s("div",Ma,o(j(e.max_grant_amount)),1),s("button",{onClick:oe(r=>Q(e),["stop"]),disabled:S.value[e.id],class:"p-1 text-red-600 hover:text-red-800 hover:bg-red-50 rounded transition-colors mt-2",title:"Elimina bando"},[S.value[e.id]?(i(),n("svg",Pa,a[25]||(a[25]=[s("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),s("path",{class:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(i(),n("svg",Na,a[24]||(a[24]=[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"},null,-1)])))],8,qa)])])],8,Ia))),128))]))])):l("",!0)]))}},Ka=le(Va,[["__scopeId","data-v-5a4a1742"]]);export{Ka as default};
