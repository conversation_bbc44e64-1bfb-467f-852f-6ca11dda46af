import{H as _}from"./app.js";import{f as v}from"./formatters.js";import{b as r,o as d,l as k,j as i,K as h,t as y,F as u,p as b,n as f,E as w,e as C}from"./vendor.js";const $={class:"bg-white dark:bg-gray-800 rounded-lg shadow"},K={key:0,class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},S={class:"text-lg font-medium text-gray-900 dark:text-white"},N={class:"overflow-x-auto"},V={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},D={class:"bg-gray-50 dark:bg-gray-700"},B={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},I={key:0,class:"text-sm text-gray-900 dark:text-white"},j={key:2,class:"flex items-center"},z={class:"text-sm font-medium text-gray-900 dark:text-white mr-2"},F={class:"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2"},L={key:3,class:"flex items-center"},M={class:"w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center"},T={class:"text-xs font-medium text-blue-600 dark:text-blue-400"},A={class:"ml-3"},E={class:"text-sm font-medium text-gray-900 dark:text-white"},H={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},q={key:4,class:"text-sm text-gray-900 dark:text-white"},O={key:1,class:"text-center py-12"},R={class:"text-sm font-medium text-gray-900 dark:text-white"},U={key:2,class:"px-6 py-4 border-t border-gray-200 dark:border-gray-700"},Q={__name:"DataTable",props:{title:{type:String,default:""},columns:{type:Array,required:!0,validator:o=>o.every(n=>n.key&&n.label)},data:{type:Array,default:()=>[]},emptyMessage:{type:String,default:"Nessun dato disponibile"},rowKey:{type:[String,Function],default:"id"}},setup(o){const n=o,x=(t,a)=>typeof n.rowKey=="function"?n.rowKey(t,a):t[n.rowKey]||a,l=(t,a)=>a.split(".").reduce((s,c)=>s==null?void 0:s[c],t),p=t=>t?t.split(" ").map(a=>a[0]).join("").toUpperCase():"??",g=(t,a)=>{if(t==null)return"-";if(["hours","percentage","currency","number"].includes(a.format))return v(t,a.format,a.formatOptions||{});switch(a.format){case"date":return new Date(t).toLocaleDateString("it-IT");case"datetime":return new Date(t).toLocaleString("it-IT");default:return t}},m=(t,a)=>{const s="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium";return a.badgeColors&&a.badgeColors[t]?`${s} ${a.badgeColors[t]}`:`${s} bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200`};return(t,a)=>(d(),r("div",$,[o.title||t.$slots.header?(d(),r("div",K,[h(t.$slots,"header",{},()=>[i("h3",S,y(o.title),1)])])):k("",!0),i("div",N,[i("table",V,[i("thead",D,[i("tr",null,[(d(!0),r(u,null,b(o.columns,s=>(d(),r("th",{key:s.key,class:f(["px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",s.headerClass])},y(s.label),3))),128))])]),i("tbody",B,[(d(!0),r(u,null,b(o.data,(s,c)=>(d(),r("tr",{key:x(s,c)},[(d(!0),r(u,null,b(o.columns,e=>(d(),r("td",{key:e.key,class:f(["px-6 py-4",e.cellClass||"whitespace-nowrap"])},[h(t.$slots,`cell-${e.key}`,{row:s,column:e,value:l(s,e.key),index:c},()=>[e.type==="text"?(d(),r("span",I,y(g(l(s,e.key),e)),1)):e.type==="badge"?(d(),r("span",{key:1,class:f(m(l(s,e.key),e))},y(g(l(s,e.key),e)),3)):e.type==="progress"?(d(),r("div",j,[i("div",z,y(g(l(s,e.key),e)),1),i("div",F,[i("div",{class:f([e.progressColor||"bg-blue-500","h-2 rounded-full"]),style:w({width:Math.min(l(s,e.key),100)+"%"})},null,6)])])):e.type==="avatar"?(d(),r("div",L,[i("div",M,[i("span",T,y(p(l(s,e.key))),1)]),i("div",A,[i("div",E,y(l(s,e.key)),1),e.subKey?(d(),r("div",H,y(l(s,e.subKey)),1)):k("",!0)])])):(d(),r("span",q,y(g(l(s,e.key),e)),1))])],2))),128))]))),128))])])]),!o.data||o.data.length===0?(d(),r("div",O,[h(t.$slots,"empty",{},()=>[C(_,{name:"table-cells",size:"lg",class:"mx-auto text-gray-400 mb-4"}),i("h3",R,y(o.emptyMessage||"Nessun dato disponibile"),1)])])):k("",!0),t.$slots.footer?(d(),r("div",U,[h(t.$slots,"footer")])):k("",!0)]))}};export{Q as _};
