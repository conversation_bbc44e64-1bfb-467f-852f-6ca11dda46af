import{r as A,c as l,x as mt,b as n,e as c,j as s,t as o,l as i,k as b,n as f,F as j,p as C,s as g,v as p,u as pt,q as gt,o as a,h as S}from"./vendor.js";import{u as vt}from"./funding.js";import{u as yt}from"./useFormatters.js";import{_ as _t}from"./PageHeader.js";import{_ as xt,i as bt,H as v}from"./app.js";import{S as K}from"./StatusBadge.js";import{S as h}from"./StandardButton.js";import"./formatters.js";const ft={key:0,class:"flex justify-center items-center min-h-64"},ht={key:1,class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},kt={class:"text-center py-12"},wt={class:"text-gray-600 dark:text-gray-400"},At={key:2},jt={key:0,class:"mb-6"},Ct={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},St={class:"lg:col-span-2 space-y-6"},Ft={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},$t={class:"prose prose-sm max-w-none"},Bt={class:"text-gray-700 dark:text-gray-300 whitespace-pre-wrap"},Dt={key:0,class:"bg-white rounded-lg shadow-sm border p-6"},zt={class:"space-y-3"},Et={class:"text-sm font-medium text-gray-900 dark:text-white"},Pt={class:"text-sm text-gray-600 dark:text-gray-400"},It={key:1,class:"bg-white rounded-lg shadow-sm border p-6"},Ot={class:"space-y-3"},qt={class:"text-sm text-gray-900 dark:text-white"},Nt={class:"text-sm font-medium text-gray-900"},Rt={key:2,class:"bg-white rounded-lg shadow-sm border p-6"},Vt={class:"prose prose-sm max-w-none"},Tt={class:"text-gray-700 whitespace-pre-wrap"},Lt={key:3,class:"bg-red-50 border border-red-200 rounded-lg p-6"},Jt={class:"prose prose-sm max-w-none"},Ut={class:"text-red-700 whitespace-pre-wrap"},Ht={class:"space-y-6"},Mt={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},Gt={class:"space-y-4"},Kt={class:"flex items-center"},Qt={key:0},Wt={class:"flex items-center text-sm font-medium text-gray-500 mb-1"},Xt={class:"text-sm text-gray-900"},Yt={key:1},Zt={class:"flex items-center text-sm font-medium text-gray-500 mb-1"},te={class:"text-sm text-gray-900"},ee={key:2},se={class:"text-sm text-gray-900"},re={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},ae={class:"space-y-4"},ne={class:"text-sm text-gray-900"},oe={key:0},ie={class:"text-sm text-gray-900"},de={class:"text-sm text-gray-900"},ue={key:1},le={class:"text-sm font-semibold text-green-600"},ce={key:2},me={class:"text-sm text-gray-900"},pe={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},ge={class:"space-y-4"},ve={key:0},ye={class:"flex items-center text-sm font-medium text-gray-500 mb-1"},_e={class:"text-sm text-gray-900"},xe={key:1},be={class:"text-sm text-gray-900"},fe={key:2},he={class:"text-sm text-gray-900"},ke={key:0,class:"bg-white rounded-lg shadow-sm border p-6"},we={class:"space-y-4"},Ae={class:"text-sm text-gray-900"},je={key:0},Ce={class:"text-sm text-gray-900"},Se={key:1},Fe={class:"flex items-center text-sm font-medium text-gray-500 mb-1"},$e={class:"text-sm text-gray-900"},Be={key:1,class:"bg-white rounded-lg shadow-sm border p-6"},De={class:"space-y-3"},ze={class:"flex items-center"},Ee={class:"text-sm text-gray-900"},Pe={__name:"FundingApplicationView",setup(Ie){const Q=pt(),x=gt(),F=vt(),{formatDate:k,formatCurrency:y}=yt(),w=A(!1),_=A(null),e=A(null),W=l(()=>{if(!e.value)return"View application details and status";const r=e.value.opportunity||{};return r.source_entity&&r.title?`${r.source_entity} - ${r.title}`:"Application Details"}),X=l(()=>{var r,t;return((r=e.value)==null?void 0:r.status)==="draft"||((t=e.value)==null?void 0:t.status)==="pending"}),Y=l(()=>{if(!e.value)return 0;const r=e.value.requested_amount||0,t=e.value.co_financing_amount||0;return r+t}),$=l(()=>{var r;if(!((r=e.value)!=null&&r.team_composition))return[];if(typeof e.value.team_composition!="string")return Array.isArray(e.value.team_composition)?e.value.team_composition:[];try{const t=JSON.parse(e.value.team_composition);return Array.isArray(t)?t:[]}catch(t){return console.error("Error parsing team_composition:",t),[]}}),B=l(()=>{var r;if(!((r=e.value)!=null&&r.budget_breakdown))return[];if(typeof e.value.budget_breakdown!="string"){const t=e.value.budget_breakdown;return Array.isArray(t)?t:Object.entries(t).map(([u,m])=>({category:u,amount:m}))}try{const t=JSON.parse(e.value.budget_breakdown);return t?Array.isArray(t)?t:Object.entries(t).map(([u,m])=>({category:u,amount:m})):[]}catch(t){return console.error("Error parsing budget_breakdown:",t),[]}}),Z=l(()=>{var r;return((r=e.value)==null?void 0:r.documents_checklist)&&D.value.length>0}),D=l(()=>{var r;if(!((r=e.value)!=null&&r.documents_checklist))return[];if(typeof e.value.documents_checklist!="string"){const t=e.value.documents_checklist;return Array.isArray(t)?t:Object.entries(t).map(([u,m])=>({name:u,status:m}))}try{const t=JSON.parse(e.value.documents_checklist);return t?Array.isArray(t)?t:Object.entries(t).map(([u,m])=>({name:u,status:m})):[]}catch(t){return console.error("Error parsing documents_checklist:",t),[]}}),tt=l(()=>{var u;if(!((u=e.value)!=null&&u.status))return"bg-gray-50 border-gray-200";const r=e.value.status,t="border";switch(r){case"draft":return`${t} bg-gray-50 border-gray-200`;case"submitted":return`${t} bg-blue-50 border-blue-200`;case"under_evaluation":return`${t} bg-yellow-50 border-yellow-200`;case"approved":return`${t} bg-green-50 border-green-200`;case"rejected":return`${t} bg-red-50 border-red-200`;case"funded":return`${t} bg-emerald-50 border-emerald-200`;default:return`${t} bg-gray-50 border-gray-200`}}),et=l(()=>{var t;if(!((t=e.value)!=null&&t.status))return"document-text";switch(e.value.status){case"draft":return"pencil-square";case"submitted":return"paper-airplane";case"under_evaluation":return"clock";case"approved":return"check-circle";case"rejected":return"exclamation-triangle";case"funded":return"currency-euro";default:return"document-text"}}),st=l(()=>{var t;if(!((t=e.value)!=null&&t.status))return"text-gray-500";switch(e.value.status){case"draft":return"text-gray-500";case"submitted":return"text-blue-500";case"under_evaluation":return"text-yellow-500";case"approved":return"text-green-500";case"rejected":return"text-red-500";case"funded":return"text-emerald-500";default:return"text-gray-500"}}),rt=l(()=>{var t;if(!((t=e.value)!=null&&t.status))return"text-gray-800";switch(e.value.status){case"draft":return"text-gray-800";case"submitted":return"text-blue-800";case"under_evaluation":return"text-yellow-800";case"approved":return"text-green-800";case"rejected":return"text-red-800";case"funded":return"text-emerald-800";default:return"text-gray-800"}}),at=l(()=>{var t;if(!((t=e.value)!=null&&t.status))return"text-gray-600";switch(e.value.status){case"draft":return"text-gray-600";case"submitted":return"text-blue-600";case"under_evaluation":return"text-yellow-600";case"approved":return"text-green-600";case"rejected":return"text-red-600";case"funded":return"text-emerald-600";default:return"text-gray-600"}}),nt=l(()=>{var t;if(!((t=e.value)!=null&&t.status))return"Unknown Status";switch(e.value.status){case"draft":return"Draft Application";case"submitted":return"Application Submitted";case"under_evaluation":return"Under Evaluation";case"approved":return"Application Approved";case"rejected":return"Application Rejected";case"funded":return"Funding Awarded";default:return"Unknown Status"}}),ot=l(()=>{var t;if(!((t=e.value)!=null&&t.status))return"";switch(e.value.status){case"draft":return"Application is being prepared and can be edited";case"submitted":return"Application has been submitted and is awaiting review";case"under_evaluation":return"Application is currently being evaluated";case"approved":return"Application has been approved for funding";case"rejected":return"Application was not successful";case"funded":return"Funding has been awarded and project can begin";default:return""}});async function it(){const r=Q.params.id;if(!r){_.value="No application ID provided";return}w.value=!0,_.value=null;try{await F.fetchApplications();const t=F.applications.find(u=>u.id===parseInt(r));if(!t){_.value="Application not found",e.value=null;return}console.log("Application loaded:",t),e.value=t}catch(t){console.error("Failed to load application:",t),_.value=t.message||"Failed to load application",e.value=null}finally{w.value=!1}}function dt(){x.push("/app/funding/dashboard")}function ut(){e.value&&x.push(`/app/funding/applications/${e.value.id}/edit`)}function z(){var r,t;(t=(r=e.value)==null?void 0:r.linked_project)!=null&&t.id&&x.push(`/app/projects/${e.value.linked_project.id}`)}function lt(){var r,t;(t=(r=e.value)==null?void 0:r.opportunity)!=null&&t.id&&x.push(`/app/funding/opportunities/${e.value.opportunity.id}`)}function ct(){var r;(r=e.value)!=null&&r.id&&x.push(`/app/funding/reporting?applicationId=${e.value.id}`)}return mt(()=>{it()}),(r,t)=>{var u,m,E,P,I,O,q,N,R,V,T,L,J,U,H,M;return w.value?(a(),n("div",ft,[c(bt)])):_.value?(a(),n("div",ht,[s("div",kt,[c(v,{name:"exclamation-triangle",size:"xl",class:"text-red-400 mx-auto mb-4"}),t[0]||(t[0]=s("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Error Loading Application",-1)),s("p",wt,o(_.value),1)])])):(a(),n("div",At,[c(_t,{title:((u=e.value)==null?void 0:u.project_title)||"Funding Application",subtitle:W.value},{actions:b(()=>{var d,G;return[c(h,{onClick:dt,variant:"secondary",icon:"arrow-left"},{default:b(()=>t[1]||(t[1]=[g(" Back to Dashboard ")])),_:1,__:[1]}),X.value?(a(),S(h,{key:0,onClick:ut,variant:"secondary",icon:"pencil-square"},{default:b(()=>t[2]||(t[2]=[g(" Edit ")])),_:1,__:[2]})):i("",!0),(d=e.value)!=null&&d.linked_project_id?(a(),S(h,{key:1,onClick:z,variant:"secondary",icon:"eye"},{default:b(()=>t[3]||(t[3]=[g(" Progetto ")])),_:1,__:[3]})):i("",!0),(G=e.value)!=null&&G.linked_project_id?(a(),S(h,{key:2,onClick:ct,variant:"primary",icon:"document-chart-bar"},{default:b(()=>t[4]||(t[4]=[g(" Rendicontazione ")])),_:1,__:[4]})):i("",!0)]}),_:1},8,["title","subtitle"]),e.value?(a(),n("div",jt,[s("div",{class:f([tt.value,"rounded-lg p-4 flex items-center"])},[c(v,{name:et.value,size:"sm",class:f([st.value,"mr-3"])},null,8,["name","class"]),s("div",null,[s("p",{class:f(["text-sm font-medium",rt.value])},o(nt.value),3),s("p",{class:f(["text-sm",at.value])},o(ot.value),3)])],2)])):i("",!0),s("div",Ct,[s("div",St,[s("div",Ft,[t[5]||(t[5]=s("h2",{class:"text-lg font-semibold text-gray-900 dark:text-white mb-4"},"Project Description",-1)),s("div",$t,[s("p",Bt,o((m=e.value)==null?void 0:m.project_description),1)])]),$.value.length?(a(),n("div",Dt,[t[6]||(t[6]=s("h2",{class:"text-lg font-semibold text-gray-900 dark:text-white mb-4"},"Team Composition",-1)),s("div",zt,[(a(!0),n(j,null,C($.value,d=>(a(),n("div",{key:d.name||d.role,class:"flex items-center p-3 border border-gray-200 dark:border-gray-700 rounded-lg"},[c(v,{name:"user-group",size:"sm",class:"text-gray-400 mr-3"}),s("div",null,[s("p",Et,o(d.name||d.role),1),s("p",Pt,o(d.role||d.description),1)])]))),128))])])):i("",!0),B.value.length?(a(),n("div",It,[t[7]||(t[7]=s("h2",{class:"text-lg font-semibold text-gray-900 dark:text-white mb-4"},"Budget Breakdown",-1)),s("div",Ot,[(a(!0),n(j,null,C(B.value,d=>(a(),n("div",{key:d.category,class:"flex items-center justify-between"},[s("div",qt,o(d.category),1),s("div",Nt,o(d.amount?p(y)(d.amount):p(y)(0)),1)]))),128))])])):i("",!0),(E=e.value)!=null&&E.evaluation_feedback?(a(),n("div",Rt,[t[8]||(t[8]=s("h2",{class:"text-lg font-semibold text-gray-900 dark:text-white mb-4"},"Evaluation Feedback",-1)),s("div",Vt,[s("p",Tt,o(e.value.evaluation_feedback),1)])])):i("",!0),(P=e.value)!=null&&P.rejection_reason?(a(),n("div",Lt,[t[9]||(t[9]=s("h2",{class:"text-lg font-semibold text-red-900 mb-4"},"Rejection Reason",-1)),s("div",Jt,[s("p",Ut,o(e.value.rejection_reason),1)])])):i("",!0)]),s("div",Ht,[s("div",Mt,[t[14]||(t[14]=s("h2",{class:"text-lg font-semibold text-gray-900 dark:text-white mb-4"},"Application Status",-1)),s("dl",Gt,[s("div",null,[t[10]||(t[10]=s("dt",{class:"text-sm font-medium text-gray-500 mb-1"},"Current Status",-1)),s("dd",Kt,[c(K,{status:(I=e.value)==null?void 0:I.status,type:"funding_application"},null,8,["status"])])]),(O=e.value)!=null&&O.submission_date?(a(),n("div",Qt,[s("dt",Wt,[c(v,{name:"calendar-days",size:"sm",class:"mr-2"}),t[11]||(t[11]=g(" Submitted "))]),s("dd",Xt,o(p(k)(e.value.submission_date)),1)])):i("",!0),(q=e.value)!=null&&q.approval_date?(a(),n("div",Yt,[s("dt",Zt,[c(v,{name:"check-circle",size:"sm",class:"mr-2"}),t[12]||(t[12]=g(" Approved "))]),s("dd",te,o(p(k)(e.value.approval_date)),1)])):i("",!0),(N=e.value)!=null&&N.priority_score?(a(),n("div",ee,[t[13]||(t[13]=s("dt",{class:"text-sm font-medium text-gray-500 mb-1"},"Priority Score",-1)),s("dd",se,o(e.value.priority_score)+"/10 ",1)])):i("",!0)])]),s("div",re,[t[20]||(t[20]=s("h2",{class:"text-lg font-semibold text-gray-900 dark:text-white mb-4"},"Financial Information",-1)),s("dl",ae,[s("div",null,[t[15]||(t[15]=s("dt",{class:"text-sm font-medium text-gray-500 mb-1"},"Requested Amount",-1)),s("dd",ne,o((R=e.value)!=null&&R.requested_amount?p(y)(e.value.requested_amount):p(y)(0)),1)]),(V=e.value)!=null&&V.co_financing_amount?(a(),n("div",oe,[t[16]||(t[16]=s("dt",{class:"text-sm font-medium text-gray-500 mb-1"},"Co-financing Amount",-1)),s("dd",ie,o(p(y)(e.value.co_financing_amount)),1)])):i("",!0),s("div",null,[t[17]||(t[17]=s("dt",{class:"text-sm font-medium text-gray-500 mb-1"},"Total Project Cost",-1)),s("dd",de,o(p(y)(Y.value)),1)]),(T=e.value)!=null&&T.approved_amount?(a(),n("div",ue,[t[18]||(t[18]=s("dt",{class:"text-sm font-medium text-green-600 mb-1"}," Approved Amount ",-1)),s("dd",le,o(p(y)(e.value.approved_amount)),1)])):i("",!0),(L=e.value)!=null&&L.funding_percentage?(a(),n("div",ce,[t[19]||(t[19]=s("dt",{class:"text-sm font-medium text-gray-500 mb-1"},"Funding Percentage",-1)),s("dd",me,o(e.value.funding_percentage)+"% ",1)])):i("",!0)])]),s("div",pe,[t[24]||(t[24]=s("h2",{class:"text-lg font-semibold text-gray-900 dark:text-white mb-4"},"Project Information",-1)),s("dl",ge,[(J=e.value)!=null&&J.project_duration_months?(a(),n("div",ve,[s("dt",ye,[c(v,{name:"clock",size:"sm",class:"mr-2"}),t[21]||(t[21]=g(" Duration "))]),s("dd",_e,o(e.value.project_duration_months)+" months ",1)])):i("",!0),(U=e.value)!=null&&U.project_manager?(a(),n("div",xe,[t[22]||(t[22]=s("dt",{class:"text-sm font-medium text-gray-500 mb-1"},"Project Manager",-1)),s("dd",be,o(e.value.project_manager.first_name)+" "+o(e.value.project_manager.last_name),1)])):i("",!0),(H=e.value)!=null&&H.linked_project?(a(),n("div",fe,[t[23]||(t[23]=s("dt",{class:"text-sm font-medium text-gray-500 mb-1"},"Linked Project",-1)),s("dd",he,[s("button",{onClick:z,class:"text-blue-600 hover:text-blue-800 underline"},o(e.value.linked_project.name),1)])])):i("",!0)])]),(M=e.value)!=null&&M.opportunity?(a(),n("div",ke,[t[28]||(t[28]=s("h2",{class:"text-lg font-semibold text-gray-900 dark:text-white mb-4"},"Funding Opportunity",-1)),s("dl",we,[s("div",null,[t[25]||(t[25]=s("dt",{class:"text-sm font-medium text-gray-500 mb-1"},"Title",-1)),s("dd",Ae,[s("button",{onClick:lt,class:"text-blue-600 hover:text-blue-800 underline"},o(e.value.opportunity.title),1)])]),e.value.opportunity.source_entity?(a(),n("div",je,[t[26]||(t[26]=s("dt",{class:"text-sm font-medium text-gray-500 mb-1"},"Source Entity",-1)),s("dd",Ce,o(e.value.opportunity.source_entity),1)])):i("",!0),e.value.opportunity.deadline?(a(),n("div",Se,[s("dt",Fe,[c(v,{name:"calendar-days",size:"sm",class:"mr-2"}),t[27]||(t[27]=g(" Deadline "))]),s("dd",$e,o(p(k)(e.value.opportunity.deadline)),1)])):i("",!0)])])):i("",!0),Z.value?(a(),n("div",Be,[t[29]||(t[29]=s("h2",{class:"text-lg font-semibold text-gray-900 dark:text-white mb-4"},"Documents",-1)),s("div",De,[(a(!0),n(j,null,C(D.value,d=>(a(),n("div",{key:d.name,class:"flex items-center justify-between"},[s("div",ze,[c(v,{name:"document-text",size:"sm",class:"text-gray-400 mr-2"}),s("span",Ee,o(d.name),1)]),c(K,{status:d.status,size:"sm"},null,8,["status"])]))),128))])])):i("",!0)])])]))}}},Ue=xt(Pe,[["__scopeId","data-v-f7ff2165"]]);export{Ue as default};
