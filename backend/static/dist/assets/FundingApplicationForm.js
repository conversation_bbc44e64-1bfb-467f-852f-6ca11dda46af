import{r as g,c as h,x as ee,h as te,k as v,u as oe,q as se,o as r,j as t,b as d,l as U,e as l,s as c,t as n,F as b,p as y,v as z,B as ae,Q as le}from"./vendor.js";import{u as ie}from"./funding.js";import{u as ne}from"./useFormatters.js";import{_ as re,c as R,d as de,H as u}from"./app.js";import{S as p}from"./StandardInput.js";import{S as T}from"./StandardButton.js";import{W as ue}from"./WizardContainer.js";import"./formatters.js";/* empty css                                                             */const me={class:"space-y-6"},ce={class:"block text-sm font-medium text-gray-700 mb-2"},pe=["value"],ge={class:"block text-sm font-medium text-gray-700 mb-2"},ve={class:"block text-sm font-medium text-gray-700 mb-2"},fe={class:"text-xs text-gray-500 mt-1"},be={class:"block text-sm font-medium text-gray-700 mb-2"},ye={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},_e={class:"block text-sm font-medium text-gray-700 mb-2"},xe=["value"],ze={class:"block text-sm font-medium text-gray-700 mb-2"},Ve=["value"],ke={class:"space-y-6"},je={class:"flex items-center justify-between mb-4"},he={class:"block text-sm font-medium text-gray-700"},we={class:"space-y-3"},Ce={class:"flex-1 grid grid-cols-1 md:grid-cols-3 gap-3"},Se=["value"],Ue=["onClick"],Be={key:0,class:"text-center py-8 text-gray-500"},De={class:"space-y-6"},Pe={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Fe={class:"block text-sm font-medium text-gray-700 mb-2"},Ie={class:"block text-sm font-medium text-gray-700 mb-2"},Me={class:"bg-gray-50 rounded-lg p-4"},qe={class:"space-y-2 text-sm"},Ae={class:"flex justify-between"},Re={class:"font-medium"},Te={class:"flex justify-between"},Ne={class:"font-medium"},Ee={class:"border-t border-gray-300 pt-2 flex justify-between"},Oe={class:"font-semibold text-lg"},$e={class:"flex justify-between text-primary-600"},Je={class:"font-medium"},He={class:"flex items-center justify-between mb-4"},Le={class:"block text-sm font-medium text-gray-700"},We={class:"space-y-3"},Qe={class:"flex-1 grid grid-cols-1 md:grid-cols-2 gap-3"},Ge=["onClick"],Ke={key:0,class:"text-center py-6 text-gray-500"},Xe={class:"space-y-6"},Ye={class:"bg-gray-50 rounded-lg p-6 text-center"},Ze={class:"space-y-6"},et={class:"space-y-6"},tt={class:"bg-white border border-gray-200 rounded-lg p-4"},ot={class:"text-md font-medium text-gray-900 mb-3 flex items-center"},st={class:"space-y-2 text-sm"},at={class:"flex justify-between"},lt={class:"font-medium"},it={class:"flex justify-between"},nt={class:"font-medium"},rt={key:0,class:"flex justify-between"},dt={class:"font-medium"},ut={key:0,class:"bg-white border border-gray-200 rounded-lg p-4"},mt={class:"text-md font-medium text-gray-900 mb-3 flex items-center"},ct={class:"space-y-2"},pt={class:"text-gray-600"},gt={class:"bg-white border border-gray-200 rounded-lg p-4"},vt={class:"text-md font-medium text-gray-900 mb-3 flex items-center"},ft={class:"space-y-2 text-sm"},bt={class:"flex justify-between"},yt={class:"font-medium"},_t={class:"flex justify-between"},xt={class:"font-medium"},zt={class:"border-t pt-2 flex justify-between"},Vt={class:"font-semibold"},kt={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},jt={class:"flex"},ht={__name:"FundingApplicationForm",setup(wt){const N=oe(),E=se(),w=ie(),{formatCurrency:_}=ne(),{showToast:V}=de(),I=g([{id:"basic",title:"Informazioni Base",description:"Progetto e dettagli"},{id:"team",title:"Team",description:"Composizione e competenze"},{id:"budget",title:"Budget",description:"Finanziamenti e costi"},{id:"documents",title:"Documenti",description:"Allegati richiesti"},{id:"review",title:"Riepilogo",description:"Verifica e invio"}]),k=g(0),j=g(!1),C=g(!1),s=g({opportunity_id:"",project_title:"",project_description:"",project_duration_months:12,project_manager_id:"",linked_project_id:"",requested_amount:0,co_financing_amount:0}),f=g([]),x=g([]),B=g([]),D=g([]),P=g([]),F=g([]),O=h(()=>{var i;return(i=I.value[k.value])==null?void 0:i.id}),M=h(()=>B.value.find(i=>i.id===s.value.opportunity_id)),S=h(()=>(s.value.requested_amount||0)+(s.value.co_financing_amount||0)),$=h(()=>S.value===0?0:Math.round((s.value.requested_amount||0)/S.value*100));function q(i,e){switch(e){case"basic":return s.value.opportunity_id&&s.value.project_title&&s.value.project_description&&s.value.project_description.length>=200&&s.value.project_duration_months&&s.value.project_manager_id;case"team":return!0;case"budget":return s.value.requested_amount>0;case"documents":return!0;case"review":return C.value;default:return!1}}const J=h(()=>q(k.value,O.value));function A(){return J.value&&C.value}function H(i){k.value=i}function L(){f.value.push({user_id:"",name:"",role:"",skills:""})}function W(i){f.value.splice(i,1)}function Q(i){const e=f.value[i],a=F.value.find(o=>o.id===e.user_id);a&&(e.name=`${a.first_name} ${a.last_name}`,a.skills&&(e.skills=a.skills))}function G(){x.value.push({category:"",amount:0})}function K(i){x.value.splice(i,1)}async function X(){j.value=!0;try{const i={...s.value,team_composition:JSON.stringify(f.value),budget_breakdown:JSON.stringify(x.value),status:"draft"};console.log("Sending application data:",i),await w.createApplication(i),V("Bozza salvata con successo","success")}catch(i){console.error("Error saving draft:",i),V("Errore nel salvataggio della bozza","error")}finally{j.value=!1}}async function Y(){if(A.value){j.value=!0;try{const i={...s.value,team_composition:JSON.stringify(f.value),budget_breakdown:JSON.stringify(x.value),status:"submitted"};console.log("Submitting application data:",i);const e=await w.createApplication(i);V("Candidatura sottomessa con successo!","success"),E.push(`/app/funding/applications/${e.application.id}`)}catch(i){console.error("Error submitting application:",i),V("Errore nella sottomissione della candidatura","error")}finally{j.value=!1}}}async function Z(){try{await w.fetchOpportunities(),B.value=w.opportunities.filter(e=>e.status==="open"&&new Date(e.application_deadline)>new Date);try{const e=await R.get("/api/personnel/users");if(e.data.success){const a=e.data.data.users||[];D.value=a.filter(o=>o.role==="manager"||o.role==="admin"||o.is_active),F.value=a.filter(o=>o.is_active)}}catch{console.warn("Could not load users, using fallback"),D.value=[{id:1,first_name:"Mario",last_name:"Rossi"},{id:2,first_name:"Laura",last_name:"Bianchi"}]}try{const e=await R.get("/api/projects");e.data.success&&(P.value=e.data.data.projects||[])}catch{console.warn("Could not load projects, using fallback"),P.value=[{id:1,name:"Progetto Alpha"},{id:2,name:"Progetto Beta"}]}const i=N.params.opportunityId;i&&(s.value.opportunity_id=parseInt(i))}catch(i){console.error("Error loading form data:",i),V("Errore nel caricamento dei dati","error")}}return ee(()=>{Z()}),(i,e)=>(r(),te(ue,{steps:I.value,"current-step-index":k.value,"onUpdate:currentStepIndex":e[9]||(e[9]=a=>k.value=a),"is-step-valid":q,"is-form-valid":A,loading:j.value,"show-save-draft":!0,"can-save-draft":!0,"submit-button-text":"Sottometti Candidatura","save-draft-text":"Salva Bozza",onSubmit:Y,onSaveDraft:X,onStepChange:H},{"step-basic":v(()=>{var a;return[t("div",me,[e[20]||(e[20]=t("div",{class:"border-b border-gray-200 pb-4 mb-6"},[t("h2",{class:"text-lg font-semibold text-gray-900"},"Informazioni Base del Progetto"),t("p",{class:"text-sm text-gray-600 mt-1"}," Descrivi il progetto per cui stai richiedendo il finanziamento ")],-1)),t("div",null,[t("label",ce,[l(u,{name:"clipboard-document",size:"sm",class:"inline mr-2"}),e[10]||(e[10]=c(" Bando di Riferimento * "))]),l(p,{modelValue:s.value.opportunity_id,"onUpdate:modelValue":e[0]||(e[0]=o=>s.value.opportunity_id=o),type:"select",required:""},{default:v(()=>[e[11]||(e[11]=t("option",{value:""},"Seleziona un bando",-1)),(r(!0),d(b,null,y(B.value,o=>(r(),d("option",{key:o.id,value:o.id},n(o.title)+" - "+n(o.source_entity),9,pe))),128))]),_:1,__:[11]},8,["modelValue"])]),t("div",null,[t("label",ge,[l(u,{name:"document-text",size:"sm",class:"inline mr-2"}),e[12]||(e[12]=c(" Titolo del Progetto * "))]),l(p,{modelValue:s.value.project_title,"onUpdate:modelValue":e[1]||(e[1]=o=>s.value.project_title=o),type:"text",required:"",placeholder:"Es. Sviluppo di una piattaforma AI per l'automazione dei processi"},null,8,["modelValue"])]),t("div",null,[t("label",ve,[l(u,{name:"document-text",size:"sm",class:"inline mr-2"}),e[13]||(e[13]=c(" Descrizione del Progetto * "))]),l(p,{modelValue:s.value.project_description,"onUpdate:modelValue":e[2]||(e[2]=o=>s.value.project_description=o),type:"textarea",required:"",rows:"6",placeholder:"Descrivi dettagliatamente il progetto, gli obiettivi, la metodologia e i risultati attesi..."},null,8,["modelValue"]),t("p",fe," Almeno 200 caratteri. Attuale: "+n(((a=s.value.project_description)==null?void 0:a.length)||0),1)]),t("div",null,[t("label",be,[l(u,{name:"clock",size:"sm",class:"inline mr-2"}),e[14]||(e[14]=c(" Durata Progetto (mesi) * "))]),l(p,{modelValue:s.value.project_duration_months,"onUpdate:modelValue":e[3]||(e[3]=o=>s.value.project_duration_months=o),modelModifiers:{number:!0},type:"number",min:"1",max:"60",required:""},null,8,["modelValue"])]),t("div",ye,[t("div",null,[t("label",_e,[l(u,{name:"user",size:"sm",class:"inline mr-2"}),e[15]||(e[15]=c(" Project Manager * "))]),l(p,{modelValue:s.value.project_manager_id,"onUpdate:modelValue":e[4]||(e[4]=o=>s.value.project_manager_id=o),type:"select",required:""},{default:v(()=>[e[16]||(e[16]=t("option",{value:""},"Seleziona PM",-1)),(r(!0),d(b,null,y(D.value,o=>(r(),d("option",{key:o.id,value:o.id},n(o.first_name)+" "+n(o.last_name),9,xe))),128))]),_:1,__:[16]},8,["modelValue"])]),t("div",null,[t("label",ze,[l(u,{name:"link",size:"sm",class:"inline mr-2"}),e[17]||(e[17]=c(" Progetto Collegato (opzionale) "))]),l(p,{modelValue:s.value.linked_project_id,"onUpdate:modelValue":e[5]||(e[5]=o=>s.value.linked_project_id=o),type:"select"},{default:v(()=>[e[18]||(e[18]=t("option",{value:""},"Nessun progetto collegato",-1)),(r(!0),d(b,null,y(P.value,o=>(r(),d("option",{key:o.id,value:o.id},n(o.name),9,Ve))),128))]),_:1,__:[18]},8,["modelValue"]),e[19]||(e[19]=t("p",{class:"text-xs text-gray-500 mt-1"}," Per rendicontazione automatica ore e costi ",-1))])])])]}),"step-team":v(()=>[t("div",ke,[e[26]||(e[26]=t("div",{class:"border-b border-gray-200 pb-4 mb-6"},[t("h2",{class:"text-lg font-semibold text-gray-900"},"Team e Competenze"),t("p",{class:"text-sm text-gray-600 mt-1"}," Definisci il team che lavorerà al progetto e le competenze richieste ")],-1)),t("div",null,[t("div",je,[t("label",he,[l(u,{name:"user-group",size:"sm",class:"inline mr-2"}),e[21]||(e[21]=c(" Composizione del Team "))]),l(T,{variant:"secondary",icon:"plus",onClick:L,size:"sm"},{default:v(()=>e[22]||(e[22]=[c(" Aggiungi Membro ")])),_:1,__:[22]})]),t("div",we,[(r(!0),d(b,null,y(f.value,(a,o)=>(r(),d("div",{key:o,class:"flex items-center space-x-3 p-3 border border-gray-200 rounded-lg"},[t("div",Ce,[l(p,{modelValue:a.user_id,"onUpdate:modelValue":m=>a.user_id=m,type:"select",onChange:m=>Q(o)},{default:v(()=>[e[23]||(e[23]=t("option",{value:""},"Seleziona dipendente",-1)),(r(!0),d(b,null,y(F.value,m=>(r(),d("option",{key:m.id,value:m.id},n(m.first_name)+" "+n(m.last_name),9,Se))),128))]),_:2,__:[23]},1032,["modelValue","onUpdate:modelValue","onChange"]),l(p,{modelValue:a.role,"onUpdate:modelValue":m=>a.role=m,type:"text",placeholder:"Ruolo nel progetto"},null,8,["modelValue","onUpdate:modelValue"]),l(p,{modelValue:a.skills,"onUpdate:modelValue":m=>a.skills=m,type:"text",placeholder:"Competenze chiave"},null,8,["modelValue","onUpdate:modelValue"])]),t("button",{type:"button",onClick:m=>W(o),class:"text-red-500 hover:text-red-700 p-1"},[l(u,{name:"trash",size:"sm"})],8,Ue)]))),128))]),f.value.length===0?(r(),d("div",Be,[l(u,{name:"user-group",size:"lg",class:"mx-auto mb-2 text-gray-300"}),e[24]||(e[24]=t("p",null,"Nessun membro del team aggiunto",-1)),e[25]||(e[25]=t("p",{class:"text-sm"},"Aggiungi i membri che lavoreranno al progetto",-1))])):U("",!0)])])]),"step-budget":v(()=>[t("div",De,[e[38]||(e[38]=t("div",{class:"border-b border-gray-200 pb-4 mb-6"},[t("h2",{class:"text-lg font-semibold text-gray-900"},"Budget e Finanziamenti"),t("p",{class:"text-sm text-gray-600 mt-1"}," Definisci il budget del progetto e i dettagli del finanziamento richiesto ")],-1)),t("div",Pe,[t("div",null,[t("label",Fe,[l(u,{name:"currency-euro",size:"sm",class:"inline mr-2"}),e[27]||(e[27]=c(" Importo Richiesto * "))]),l(p,{modelValue:s.value.requested_amount,"onUpdate:modelValue":e[6]||(e[6]=a=>s.value.requested_amount=a),modelModifiers:{number:!0},type:"number",min:"0",step:"0.01",required:"",placeholder:"50000.00"},null,8,["modelValue"])]),t("div",null,[t("label",Ie,[l(u,{name:"banknotes",size:"sm",class:"inline mr-2"}),e[28]||(e[28]=c(" Co-finanziamento Aziendale "))]),l(p,{modelValue:s.value.co_financing_amount,"onUpdate:modelValue":e[7]||(e[7]=a=>s.value.co_financing_amount=a),modelModifiers:{number:!0},type:"number",min:"0",step:"0.01",placeholder:"25000.00"},null,8,["modelValue"])])]),t("div",Me,[e[33]||(e[33]=t("h3",{class:"text-sm font-medium text-gray-900 mb-3"},"Riepilogo Finanziario",-1)),t("div",qe,[t("div",Ae,[e[29]||(e[29]=t("span",null,"Finanziamento richiesto:",-1)),t("span",Re,n(z(_)(s.value.requested_amount||0)),1)]),t("div",Te,[e[30]||(e[30]=t("span",null,"Co-finanziamento aziendale:",-1)),t("span",Ne,n(z(_)(s.value.co_financing_amount||0)),1)]),t("div",Ee,[e[31]||(e[31]=t("span",{class:"font-medium"},"Costo totale progetto:",-1)),t("span",Oe,n(z(_)(S.value)),1)]),t("div",$e,[e[32]||(e[32]=t("span",null,"Percentuale finanziamento:",-1)),t("span",Je,n($.value)+"%",1)])])]),t("div",null,[t("div",He,[t("label",Le,[l(u,{name:"calculator",size:"sm",class:"inline mr-2"}),e[34]||(e[34]=c(" Ripartizione Budget (opzionale) "))]),l(T,{variant:"secondary",icon:"plus",onClick:G,size:"sm"},{default:v(()=>e[35]||(e[35]=[c(" Aggiungi Voce ")])),_:1,__:[35]})]),t("div",We,[(r(!0),d(b,null,y(x.value,(a,o)=>(r(),d("div",{key:o,class:"flex items-center space-x-3 p-3 border border-gray-200 rounded-lg"},[t("div",Qe,[l(p,{modelValue:a.category,"onUpdate:modelValue":m=>a.category=m,type:"text",placeholder:"Es. Personale, Attrezzature, Consulenze..."},null,8,["modelValue","onUpdate:modelValue"]),l(p,{modelValue:a.amount,"onUpdate:modelValue":m=>a.amount=m,modelModifiers:{number:!0},type:"number",min:"0",step:"0.01",placeholder:"0.00"},null,8,["modelValue","onUpdate:modelValue"])]),t("button",{type:"button",onClick:m=>K(o),class:"text-red-500 hover:text-red-700 p-1"},[l(u,{name:"trash",size:"sm"})],8,Ge)]))),128))]),x.value.length===0?(r(),d("div",Ke,[l(u,{name:"calculator",size:"lg",class:"mx-auto mb-2 text-gray-300"}),e[36]||(e[36]=t("p",null,"Nessuna voce di budget aggiunta",-1)),e[37]||(e[37]=t("p",{class:"text-sm"},"Opzionale: aggiungi dettagli sulla ripartizione del budget",-1))])):U("",!0)])])]),"step-documents":v(()=>[t("div",Xe,[e[42]||(e[42]=t("div",{class:"border-b border-gray-200 pb-4 mb-6"},[t("h2",{class:"text-lg font-semibold text-gray-900"},"Documenti e Allegati"),t("p",{class:"text-sm text-gray-600 mt-1"}," Carica i documenti richiesti per completare la candidatura ")],-1)),t("div",Ye,[l(u,{name:"document-arrow-up",size:"xl",class:"mx-auto mb-4 text-gray-400"}),e[39]||(e[39]=t("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Upload Documenti",-1)),e[40]||(e[40]=t("p",{class:"text-gray-600 mb-4"}," Il componente di upload documenti sarà implementato nella prossima fase ",-1)),e[41]||(e[41]=t("div",{class:"text-sm text-gray-500"},[t("p",null,"Documenti tipicamente richiesti:"),t("ul",{class:"mt-2 space-y-1"},[t("li",null,"• Visura camerale"),t("li",null,"• Bilancio ultimi 2 anni"),t("li",null,"• CV del team"),t("li",null,"• Piano di progetto dettagliato")])],-1))])])]),"step-review":v(()=>[t("div",Ze,[e[52]||(e[52]=t("div",{class:"border-b border-gray-200 pb-4 mb-6"},[t("h2",{class:"text-lg font-semibold text-gray-900"},"Riepilogo Candidatura"),t("p",{class:"text-sm text-gray-600 mt-1"}," Verifica tutti i dati prima di sottomettere la candidatura ")],-1)),t("div",et,[t("div",tt,[t("h3",ot,[l(u,{name:"document-text",size:"sm",class:"mr-2"}),e[43]||(e[43]=c(" Informazioni Progetto "))]),t("dl",st,[t("div",at,[e[44]||(e[44]=t("dt",{class:"text-gray-600"},"Titolo:",-1)),t("dd",lt,n(s.value.project_title),1)]),t("div",it,[e[45]||(e[45]=t("dt",{class:"text-gray-600"},"Durata:",-1)),t("dd",nt,n(s.value.project_duration_months)+" mesi",1)]),M.value?(r(),d("div",rt,[e[46]||(e[46]=t("dt",{class:"text-gray-600"},"Bando:",-1)),t("dd",dt,n(M.value.title),1)])):U("",!0)])]),f.value.length>0?(r(),d("div",ut,[t("h3",mt,[l(u,{name:"user-group",size:"sm",class:"mr-2"}),c(" Team ("+n(f.value.length)+" membri) ",1)]),t("div",ct,[(r(!0),d(b,null,y(f.value,a=>(r(),d("div",{key:a.name,class:"text-sm flex justify-between"},[t("span",null,n(a.name),1),t("span",pt,n(a.role),1)]))),128))])])):U("",!0),t("div",gt,[t("h3",vt,[l(u,{name:"currency-euro",size:"sm",class:"mr-2"}),e[47]||(e[47]=c(" Riepilogo Finanziario "))]),t("dl",ft,[t("div",bt,[e[48]||(e[48]=t("dt",{class:"text-gray-600"},"Finanziamento richiesto:",-1)),t("dd",yt,n(z(_)(s.value.requested_amount||0)),1)]),t("div",_t,[e[49]||(e[49]=t("dt",{class:"text-gray-600"},"Co-finanziamento:",-1)),t("dd",xt,n(z(_)(s.value.co_financing_amount||0)),1)]),t("div",zt,[e[50]||(e[50]=t("dt",{class:"font-medium"},"Costo totale:",-1)),t("dd",Vt,n(z(_)(S.value)),1)])])])]),t("div",kt,[t("div",jt,[ae(t("input",{"onUpdate:modelValue":e[8]||(e[8]=a=>C.value=a),type:"checkbox",id:"accept-terms",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[le,C.value]]),e[51]||(e[51]=t("label",{for:"accept-terms",class:"ml-3 text-sm text-gray-700"},[c(" Dichiaro di aver letto e accettato i "),t("a",{href:"#",class:"text-primary-600 hover:text-primary-800 underline"}," termini e condizioni "),c(" del bando e di essere consapevole che la candidatura sarà valutata secondo i criteri specificati. ")],-1))])])])]),_:1},8,["steps","current-step-index","loading"]))}},qt=re(ht,[["__scopeId","data-v-dfd6a138"]]);export{qt as default};
