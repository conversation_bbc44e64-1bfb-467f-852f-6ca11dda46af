import os
import logging
from flask import Flask, session, redirect, url_for, request, flash, make_response
from flask_login import logout_user, current_user
from flask_cors import CORS
from werkzeug.middleware.proxy_fix import ProxyFix
from datetime import datetime, timedelta
from config import Config
import time
from extensions import db, login_manager, migrate, csrf, mail

# Configure logging
logging.basicConfig(level=logging.INFO) # Modificato INFO per produzione, DEBUG per sviluppo
logger = logging.getLogger(__name__)

PUBLIC_ENDPOINTS = [
    'static',
    'public_api.get_public_config', 'public_api.get_featured_services',
    'public_api.get_services', 'public_api.get_service_detail',
    'tenants_api.api_tenant_config',  # API per configurazione tenant
    'api_auth.login', 'api_auth.logout', 'api_auth.debug',  # API di autenticazione Vue.js
    'api_oauth.oauth_login', 'api_oauth.oauth_callback', 'api_oauth.get_oauth_providers',  # OAuth endpoints
    'api_recruiting.get_public_job_postings', 'api_recruiting.submit_public_application',  # Recruiting pubblico
    'api_feature_flags.get_feature_flags',  # Feature flags pubblici per frontend
    'swagger_json.swagger_json',  # Swagger JSON
    'swagger_ui.show',  # Swagger UI
    'spa'  # SPA catch-all route - Vue.js gestisce tutto
]

# Fix MIME types for ES6 modules GLOBALLY
import mimetypes
mimetypes.add_type('application/javascript', '.js')
mimetypes.add_type('application/javascript', '.mjs')
mimetypes.add_type('application/javascript', '.vue')

def create_app(config_object='config.Config', config_overrides=None):
    """Factory function to create and configure the Flask app."""
    app = Flask(__name__)
    app.secret_key = os.environ.get("SESSION_SECRET", os.urandom(24))
    app.wsgi_app = ProxyFix(app.wsgi_app, x_proto=1, x_host=1)

    # Load configuration
    app.config.from_object(config_object)
    if config_overrides:
        app.config.from_mapping(config_overrides)

    # Initialize extensions with app
    db.init_app(app)
    login_manager.init_app(app)
    # login_manager.login_view = None  # Non serve più con Vue.js SPA
    migrate.init_app(app, db)
    mail.init_app(app)

    # CORS will be handled manually in before_request and after_request hooks

    # Configure CSRF protection
    csrf.init_app(app)
    # Make csrf_token available in templates without function call
    app.jinja_env.globals['csrf_token'] = lambda: csrf.generate_csrf()

    with app.app_context():
        # Import models first to ensure they're registered
        from models import User # Spostato import qui per evitare importazioni circolari

        # Add datetime utility for templates
        @app.context_processor
        def utility_processor():
            return {'current_year': datetime.now().year}

        # Import blueprints for Vue.js SPA
        from blueprints.api.public import public_api_bp
        from blueprints.swagger import register_swagger_blueprints

        # Register blueprints - SOLO API per Vue.js SPA
        app.register_blueprint(public_api_bp)

        # Auth API
        from blueprints.api.auth import api_auth
        app.register_blueprint(api_auth, url_prefix='/api/auth')

        # Dashboard API
        from blueprints.api.dashboard import api_dashboard
        app.register_blueprint(api_dashboard, url_prefix='/api/dashboard')

        # Projects API
        from blueprints.api.projects import api_projects
        app.register_blueprint(api_projects, url_prefix='/api/projects')

        # Tasks API
        from blueprints.api.tasks import api_tasks
        app.register_blueprint(api_tasks, url_prefix='/api/tasks')

        # Timesheets API
        from blueprints.api.timesheets import api_timesheets
        app.register_blueprint(api_timesheets, url_prefix='/api/timesheets')

        # Time-off Requests API
        from blueprints.api.timeoff_requests import api_timeoff_requests
        app.register_blueprint(api_timeoff_requests, url_prefix='/api/time-off-requests')

        # Monthly Timesheets API
        from blueprints.api.monthly_timesheets import api_monthly_timesheets
        app.register_blueprint(api_monthly_timesheets, url_prefix='/api/monthly-timesheets')

        # Contracts API
        from blueprints.api.contracts import api_contracts
        app.register_blueprint(api_contracts, url_prefix='/api/contracts')

        # Invoices API
        from blueprints.api.invoices import api_invoices
        app.register_blueprint(api_invoices, url_prefix='/api/invoices')

        # Pre-invoices API
        from blueprints.api.pre_invoices import api_pre_invoices
        app.register_blueprint(api_pre_invoices, url_prefix='/api/pre-invoices')

        # Clients API
        from blueprints.api.clients import api_clients
        app.register_blueprint(api_clients, url_prefix='/api/clients')

        # Contacts API
        from blueprints.api.contacts import api_contacts
        app.register_blueprint(api_contacts, url_prefix='/api/contacts')

        # Proposals API
        from blueprints.api.proposals import api_proposals
        app.register_blueprint(api_proposals, url_prefix='/api/proposals')

        # Personnel API
        from blueprints.api.personnel import api_personnel
        app.register_blueprint(api_personnel, url_prefix='/api/personnel')

        # Performance API
        from blueprints.api.performance import api_performance
        app.register_blueprint(api_performance, url_prefix='/api/performance')
        
        # Performance Personal API
        from blueprints.api.performance_personal import performance_personal_bp
        app.register_blueprint(performance_personal_bp)

        # Personnel Allocation API (registrato prima per evitare conflitti)
        from blueprints.api.personnel_allocation import api_personnel_allocation
        app.register_blueprint(api_personnel_allocation, url_prefix='/api')

        # Expenses API
        from blueprints.api.expenses import api_expenses
        app.register_blueprint(api_expenses)

        # KPIs API
        from blueprints.api.kpis import api_kpis
        app.register_blueprint(api_kpis, url_prefix='/api/kpis')

        # Project KPIs API
        from blueprints.api.project_kpis import api_project_kpis
        app.register_blueprint(api_project_kpis, url_prefix='/api/project-kpis')

        # Resources API
        from blueprints.api.resources import api_resources
        app.register_blueprint(api_resources, url_prefix='/api/resources')

        # Task Dependencies API
        from blueprints.api.task_dependencies import api_task_dependencies
        app.register_blueprint(api_task_dependencies, url_prefix='/api/task-dependencies')

        # Tenant API (senza prefix per mantenere /api/config/tenant)
        from blueprints.api.tenants import tenants_api
        app.register_blueprint(tenants_api, url_prefix='/api')

        # Admin API
        from blueprints.api.admin import api_admin
        app.register_blueprint(api_admin, url_prefix='/api/admin')

        # Admin Settings API
        from blueprints.api.admin_settings import api_admin_settings
        app.register_blueprint(api_admin_settings, url_prefix='/api/admin/settings')

        # Validation & Data Integrity API
        from blueprints.api.validation import validation_bp
        app.register_blueprint(validation_bp, url_prefix='/api/admin')

        # AI Resources API
        from blueprints.api.ai_resources import api_ai_resources
        app.register_blueprint(api_ai_resources, url_prefix='/api/ai-resources')

        # Business Intelligence API
        from blueprints.api.business_intelligence import bi_api
        app.register_blueprint(bi_api, url_prefix='/api/business-intelligence')

        # Case Studies API
        from blueprints.api.case_studies import case_studies_api
        app.register_blueprint(case_studies_api, url_prefix='/api/business-intelligence/case-studies')

        # Notifications API
        from blueprints.api.notifications import notifications_bp
        app.register_blueprint(notifications_bp, url_prefix='/api')

        # Funding API
        from blueprints.api.funding import api_funding
        app.register_blueprint(api_funding)
        
        # Communication API
        from blueprints.api.communication import communication_bp
        app.register_blueprint(communication_bp, url_prefix='/api/communication')
        
        # Certifications API
        from blueprints.api.certifications import api_certifications
        app.register_blueprint(api_certifications, url_prefix='/api/certifications')
        
        # Human CEO AI Assistant API
        from blueprints.api.ceo import api_ceo
        app.register_blueprint(api_ceo, url_prefix='/api/ceo')
        
        # Drafts Management API
        from blueprints.api.drafts import api_drafts
        app.register_blueprint(api_drafts, url_prefix='/api/drafts')
        
        # Governance & Compliance API
        from blueprints.api.governance import api_governance
        app.register_blueprint(api_governance, url_prefix='/api/governance')
        
        # Help System API
        from blueprints.api.help import api_help
        app.register_blueprint(api_help, url_prefix='/api/help')
        
        # Engagement & Gamification API
        from blueprints.api.engagement import api_engagement
        app.register_blueprint(api_engagement, url_prefix='/api/engagement')
        
        # Recruiting API
        from blueprints.api.recruiting import api_recruiting
        app.register_blueprint(api_recruiting, url_prefix='/api/recruiting')
        
        # Recruiting AI API
        from blueprints.api.recruiting_ai import api_recruiting_ai
        app.register_blueprint(api_recruiting_ai, url_prefix='/api/recruiting/ai')
        
        # Feature Flags API
        from blueprints.api.feature_flags import api_feature_flags
        app.register_blueprint(api_feature_flags, url_prefix='/api/feature-flags')
        
        # OAuth API
        from blueprints.api.oauth import api_oauth
        app.register_blueprint(api_oauth, url_prefix='/api/auth')
        
        # Self-Healing System API
        from blueprints.api.self_healing import api_self_healing
        app.register_blueprint(api_self_healing, url_prefix='/api/self-healing')
        
        # Security Dashboard API
        from blueprints.api.security import api_security
        app.register_blueprint(api_security, url_prefix='/api/security')
        
        # Global Search API
        from blueprints.api.search import search_bp
        app.register_blueprint(search_bp, url_prefix='/api')
        
        # Funding AI API (integrato in funding.py)

        # Register Swagger blueprints
        register_swagger_blueprints(app)
        
        # Initialize Report Scheduler (opzionale)
        from services.scheduler import report_scheduler
        report_scheduler.start()  # Ora è solo un placeholder che legge la config
        
        # Initialize Compliance Tracker (opzionale)
        from middleware.compliance_tracker import compliance_tracker
        compliance_tracker.init_app(app)

        # Handle CORS preflight requests
        @app.before_request
        def handle_preflight():
            if request.method == "OPTIONS":
                response = make_response()
                origin = request.headers.get('Origin')
                if origin in ['http://localhost:3000', 'http://127.0.0.1:3000', 'http://localhost:5000', 'http://127.0.0.1:5000']:
                    response.headers.add("Access-Control-Allow-Origin", origin)
                    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization,X-CSRFToken,X-Requested-With')
                    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
                    response.headers.add('Access-Control-Allow-Credentials', "true")
                return response

        # Configure static file serving with correct MIME types
        @app.after_request
        def after_request(response):
            # Add CORS headers to all responses
            origin = request.headers.get('Origin')
            if origin in ['http://localhost:3000', 'http://127.0.0.1:3000', 'http://localhost:5000', 'http://127.0.0.1:5000']:
                response.headers.add('Access-Control-Allow-Origin', origin)
                response.headers.add('Access-Control-Allow-Credentials', 'true')
                response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization,X-CSRFToken,X-Requested-With')
                response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')

            # Fix MIME type for JavaScript modules and Vue files
            if request.path.endswith('.js') or request.path.endswith('.mjs'):
                response.content_type = 'application/javascript'
                response.headers['Cache-Control'] = 'public, max-age=********'
            elif request.path.endswith('.vue'):
                response.content_type = 'application/javascript'
            elif request.path.endswith('.css'):
                response.content_type = 'text/css'
                response.headers['Cache-Control'] = 'public, max-age=********'
            return response



        # SPA Route - Catch-all for Vue.js routing
        @app.route('/')
        @app.route('/<path:path>')
        def spa(path=''):
            """
            Serve the Vue.js SPA for all routes except API and auth routes.
            This allows Vue Router to handle client-side routing.
            """
            # Don't serve SPA for API routes
            if path.startswith('api/'):
                from flask import abort
                abort(404)


            # Don't serve SPA for static files - let Flask serve them directly
            if path.startswith('static/'):
                from flask import abort
                abort(404)

            # Don't serve SPA for swagger routes
            if path.startswith('swagger') or path.startswith('docs') or path.startswith('api/swagger'):
                from flask import abort
                abort(404)

            # Load tenant configuration
            from blueprints.api.tenants import load_tenant_config
            tenant_config = load_tenant_config()

            # Serve the Vue.js SPA template for all other routes
            from flask import render_template
            return render_template('vue_app.html', tenant_config=tenant_config)

        # Setup user loader for Flask-Login
        @login_manager.user_loader
        def load_user(user_id):
            return User.query.get(int(user_id))

        # Create database tables if they don't exist
        try:
            db.create_all()
        except Exception as e:
            # Log but don't fail if tables already exist
            logger.warning(f"Database table creation warning: {str(e)}")

        logger.info("Flask app created and configured.")

        @app.before_request
        def session_management():
            # Simplified session management for Vue.js SPA
            if current_user.is_authenticated:
                session['last_activity'] = time.time()

        @app.before_request
        def global_auth_enforcement():
            endpoint = request.endpoint
            # Log ogni accesso
            user = getattr(current_user, 'username', 'anonymous')

            # Skip enforcement per endpoint pubblici
            if not endpoint or endpoint.startswith('static') or endpoint in PUBLIC_ENDPOINTS:
                return

            # Enforcement autenticazione globale solo per endpoint protetti
            if not current_user.is_authenticated:
                logger.warning(f"Tentativo accesso non autenticato a {endpoint} da IP {request.remote_addr}")
                logger.warning(f"Path: {request.path}, Method: {request.method}")

                # Per le API, restituisci JSON 401
                if endpoint and (endpoint.startswith('api_') or endpoint.startswith('api.')):
                    logger.warning(f"Endpoint API non autenticato, ritorno JSON 401: {endpoint}")
                    from flask import jsonify
                    return jsonify({
                        'success': False,
                        'message': 'Autenticazione richiesta'
                    }), 401

                # Per le pagine web/SPA, restituisci JSON 401 (Vue.js gestirà il redirect)
                logger.warning(f"Endpoint WEB non autenticato, ritorno JSON 401: {endpoint}")
                from flask import jsonify
                return jsonify({'error': 'Authentication required'}), 401
            else:
                pass

        @app.errorhandler(403)
        def forbidden(e):
            user = getattr(current_user, 'username', 'anonymous')
            logger.warning(f"403 Forbidden: user={user}, endpoint={request.endpoint}, ip={request.remote_addr}")
            flash('Accesso negato: non hai i permessi necessari.', 'danger')
            return redirect('/')

        # Registra i filtri personalizzati
        try:
            from utils.filters import register_filters
            register_filters(app)
        except ImportError:
            # Filtri non trovati, continua senza
            pass

        # Inizializza il servizio di validazione per prevenire bug critici
        try:
            from services.validation_service import init_validation_service
            init_validation_service(app)
            logger.info("Validation service initialized - bug prevention active")
        except ImportError as e:
            logger.warning(f"Validation service not available: {str(e)}")
        except Exception as e:
            logger.error(f"Error initializing validation service: {str(e)}")

    return app
