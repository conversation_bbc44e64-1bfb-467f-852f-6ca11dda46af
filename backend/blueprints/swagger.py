"""
Blueprint per la documentazione Swagger delle API.
"""
from flask import Blueprint, jsonify, current_app, send_from_directory
from flask_login import login_required
from flask_swagger_ui import get_swaggerui_blueprint
import os
import json

# Blueprint per servire il file swagger.json
swagger_json_bp = Blueprint('swagger_json', __name__, url_prefix='/api')


@swagger_json_bp.route('/swagger.json')
def swagger_json():
    """
    Serve il file swagger.json per la documentazione delle API.
    Richiede autenticazione per proteggere la documentazione.
    """
    swagger_path = os.path.join(current_app.root_path, 'static', 'swagger',
                                'swagger.json')

    # Se il file non esiste, restituisci un template vuoto
    if not os.path.exists(swagger_path):
        return jsonify({
            "openapi": "3.0.0",
            "info": {
                "title": "DatPortal API",
                "description": "API per DatPortal",
                "version": "1.0.0"
            },
            "paths": {}
        })

    # Altrimenti, carica il file JSON
    with open(swagger_path, 'r') as f:
        swagger_data = json.load(f)

    return jsonify(swagger_data)


# Crea il blueprint per l'interfaccia Swagger UI
swagger_ui_blueprint = get_swaggerui_blueprint('/api/docs',
                                               '/api/swagger.json',
                                               config={
                                                   'app_name': "DatPortal API",
                                                   'dom_id': '#swagger-ui',
                                                   'layout': 'BaseLayout',
                                                   'deepLinking': True,
                                                   'persistAuthorization':
                                                   True,
                                                   'displayRequestDuration':
                                                   True,
                                                   'docExpansion': 'none'
                                               })


# Funzione per registrare i blueprint Swagger nell'app
def register_swagger_blueprints(app):
    """
    Registra i blueprint Swagger nell'app Flask.
    
    Args:
        app: L'istanza dell'app Flask
    """
    # Crea la directory per i file Swagger se non esiste
    swagger_dir = os.path.join(app.root_path, 'static', 'swagger')
    if not os.path.exists(swagger_dir):
        os.makedirs(swagger_dir)

    # Crea un file swagger.json vuoto se non esiste
    swagger_file = os.path.join(swagger_dir, 'swagger.json')
    if not os.path.exists(swagger_file):
        with open(swagger_file, 'w') as f:
            json.dump(
                {
                    "openapi": "3.0.0",
                    "info": {
                        "title": "DatPortal API",
                        "description": "API per DatPortal",
                        "version": "1.0.0"
                    },
                    "paths": {}
                },
                f,
                indent=2)

    # Registra i blueprint
    app.register_blueprint(swagger_json_bp)
    app.register_blueprint(swagger_ui_blueprint)
