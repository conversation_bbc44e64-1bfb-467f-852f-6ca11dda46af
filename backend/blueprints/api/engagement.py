"""
API endpoints per sistema engagement e gamification
Gestisce campagne, punti, premi, classifiche e dashboard engagement
"""

from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from datetime import datetime, date, timedelta
from sqlalchemy import desc, func, and_, or_
from utils.api_utils import api_response, api_permission_required, get_pagination_params
from utils.permissions import (
    PERMISSION_VIEW_ENGAGEMENT, PERMISSION_PARTICIPATE_ENGAGEMENT,
    PERMISSION_MANAGE_ENGAGEMENT_CAMPAIGNS, PERMISSION_MANAGE_ENGAGEMENT_REWARDS,
    PERMISSION_VIEW_ENGAGEMENT_ANALYTICS
)
from extensions import db
from models import (
    EngagementCampaign, EngagementLevel, EngagementPoint, EngagementReward,
    EngagementUserReward, EngagementLeaderboard, EngagementUserProfile,
    User, ComplianceAuditLog, Department
)
from services.engagement_points_service import EngagementPointsService

api_engagement = Blueprint('api_engagement', __name__)

@api_engagement.route('/dashboard', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_ENGAGEMENT)
def get_engagement_dashboard():
    """Dashboard engagement con overview punti, livello, campagne attive"""
    try:
        user_id = current_user.id
        
        # Get o crea profilo utente
        user_profile = EngagementUserProfile.query.filter_by(user_id=user_id).first()
        if not user_profile:
            user_profile = create_user_profile(user_id)
        
        # Campagne attive
        active_campaigns = EngagementCampaign.query.filter(
            EngagementCampaign.status == 'active',
            EngagementCampaign.start_date <= date.today(),
            EngagementCampaign.end_date >= date.today()
        ).order_by(desc(EngagementCampaign.created_at)).limit(3).all()
        
        # Punti recenti (ultimi 30 giorni)
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        recent_points = EngagementPoint.query.filter(
            EngagementPoint.user_id == user_id,
            EngagementPoint.earned_at >= thirty_days_ago
        ).order_by(desc(EngagementPoint.earned_at)).limit(10).all()
        
        # Premi disponibili (top 5 per punti necessari)
        available_rewards = EngagementReward.query.filter(
            EngagementReward.is_active == True,
            EngagementReward.points_cost <= user_profile.available_points,
            or_(
                EngagementReward.available_until.is_(None),
                EngagementReward.available_until >= date.today()
            )
        ).order_by(EngagementReward.points_cost).limit(5).all()
        
        # Posizione in classifica generale
        leaderboard_position = get_user_leaderboard_position(user_id)
        
        # Statistiche attività ultima settimana
        week_ago = datetime.utcnow() - timedelta(days=7)
        week_activity = {
            'total_points': EngagementPoint.query.filter(
                EngagementPoint.user_id == user_id,
                EngagementPoint.earned_at >= week_ago
            ).with_entities(func.sum(EngagementPoint.points_earned)).scalar() or 0,
            
            'total_actions': EngagementPoint.query.filter(
                EngagementPoint.user_id == user_id,
                EngagementPoint.earned_at >= week_ago
            ).count()
        }
        
        dashboard_data = {
            'user_profile': user_profile.to_dict(),
            'active_campaigns': [campaign.to_dict() for campaign in active_campaigns],
            'recent_points': [point.to_dict() for point in recent_points],
            'available_rewards': [reward.to_dict() for reward in available_rewards],
            'leaderboard_position': leaderboard_position,
            'week_activity': week_activity,
            'engagement_enabled': current_app.config.get('ENGAGEMENT_SYSTEM_ENABLED', True)
        }
        
        return api_response(data=dashboard_data, message="Dashboard engagement caricata")
        
    except Exception as e:
        current_app.logger.error(f"Errore caricamento dashboard engagement: {e}")
        return api_response(message="Errore caricamento dashboard", status_code=500)

@api_engagement.route('/campaigns', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_ENGAGEMENT)
def get_campaigns():
    """Lista campagne con filtri opzionali"""
    try:
        status_filter = request.args.get('status', 'all')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        
        query = EngagementCampaign.query
        
        if status_filter == 'active':
            query = query.filter(
                EngagementCampaign.status == 'active',
                EngagementCampaign.start_date <= date.today(),
                EngagementCampaign.end_date >= date.today()
            )
        elif status_filter == 'upcoming':
            query = query.filter(EngagementCampaign.start_date > date.today())
        elif status_filter == 'past':
            query = query.filter(EngagementCampaign.end_date < date.today())
        elif status_filter != 'all':
            query = query.filter(EngagementCampaign.status == status_filter)
        
        campaigns = query.order_by(desc(EngagementCampaign.created_at)).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return api_response(
            data={
                'campaigns': [campaign.to_dict() for campaign in campaigns.items],
                'pagination': {
                    'page': campaigns.page,
                    'pages': campaigns.pages,
                    'per_page': campaigns.per_page,
                    'total': campaigns.total,
                    'has_next': campaigns.has_next,
                    'has_prev': campaigns.has_prev
                }
            }
        )
        
    except Exception as e:
        current_app.logger.error(f"Errore caricamento campagne: {e}")
        return api_response(message="Errore caricamento campagne", status_code=500)

@api_engagement.route('/campaigns/<int:campaign_id>', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_ENGAGEMENT)
def get_campaign_detail(campaign_id):
    """Dettaglio campagna con classifica dedicata"""
    try:
        campaign = EngagementCampaign.query.get_or_404(campaign_id)
        
        # Classifica per questa campagna (top 20)
        campaign_leaderboard = EngagementLeaderboard.query.filter_by(
            campaign_id=campaign_id
        ).order_by(EngagementLeaderboard.ranking_position).limit(20).all()
        
        # Punti utente corrente per questa campagna
        user_campaign_points = EngagementPoint.query.filter(
            EngagementPoint.user_id == current_user.id,
            EngagementPoint.campaign_id == campaign_id
        ).with_entities(func.sum(EngagementPoint.points_earned)).scalar() or 0
        
        # Posizione utente corrente in questa campagna
        user_position = EngagementLeaderboard.query.filter_by(
            campaign_id=campaign_id,
            user_id=current_user.id
        ).first()
        
        # Premi specifici della campagna
        campaign_rewards = EngagementReward.query.filter_by(
            campaign_id=campaign_id,
            is_active=True
        ).order_by(EngagementReward.points_cost).all()
        
        # Partecipanti totali
        total_participants = EngagementPoint.query.filter_by(
            campaign_id=campaign_id
        ).with_entities(func.count(func.distinct(EngagementPoint.user_id))).scalar() or 0
        
        campaign_detail = {
            'campaign': campaign.to_dict(),
            'leaderboard': [entry.to_dict() for entry in campaign_leaderboard],
            'user_points': user_campaign_points,
            'user_position': user_position.to_dict() if user_position else None,
            'campaign_rewards': [reward.to_dict() for reward in campaign_rewards],
            'total_participants': total_participants
        }
        
        return api_response(data=campaign_detail, message="Dettaglio campagna caricato")
        
    except Exception as e:
        current_app.logger.error(f"Errore caricamento dettaglio campagna: {e}")
        return api_response(message="Errore caricamento campagna", status_code=500)

@api_engagement.route('/points', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_ENGAGEMENT)
def get_user_points():
    """Storico punti utente con filtri"""
    try:
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        campaign_id = request.args.get('campaign_id')
        source_type = request.args.get('source_type')
        
        query = EngagementPoint.query.filter_by(user_id=current_user.id)
        
        if campaign_id:
            query = query.filter_by(campaign_id=campaign_id)
        if source_type:
            query = query.filter_by(source_type=source_type)
        
        points = query.order_by(desc(EngagementPoint.earned_at)).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        # Statistiche riassuntive
        total_points = EngagementPoint.query.filter_by(
            user_id=current_user.id
        ).with_entities(func.sum(EngagementPoint.points_earned)).scalar() or 0
        
        points_by_source = db.session.query(
            EngagementPoint.source_type,
            func.sum(EngagementPoint.points_earned).label('total'),
            func.count(EngagementPoint.id).label('count')
        ).filter_by(user_id=current_user.id).group_by(EngagementPoint.source_type).all()
        
        return api_response(data={
                'points': [point.to_dict() for point in points.items],
                'pagination': {
                    'page': points.page,
                    'pages': points.pages,
                    'per_page': points.per_page,
                    'total': points.total,
                    'has_next': points.has_next,
                    'has_prev': points.has_prev
                },
                'statistics': {
                    'total_points': total_points,
                    'points_by_source': [
                        {'source_type': row[0], 'total_points': row[1], 'count': row[2]}
                        for row in points_by_source
                    ]
                }
            },
            message="Storico punti caricato"
        )
        
    except Exception as e:
        current_app.logger.error(f"Errore caricamento punti utente: {e}")
        return api_response(message="Errore caricamento punti", status_code=500)

@api_engagement.route('/rewards', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_ENGAGEMENT)
def get_available_rewards():
    """Catalogo premi disponibili per riscatto"""
    try:
        campaign_id = request.args.get('campaign_id')
        reward_type = request.args.get('reward_type')
        max_cost = request.args.get('max_cost', type=int)
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 12))
        
        query = EngagementReward.query.filter_by(is_active=True)
        
        # Filtri opzionali
        if campaign_id:
            query = query.filter_by(campaign_id=campaign_id)
        if reward_type:
            query = query.filter_by(reward_type=reward_type)
        if max_cost:
            query = query.filter(EngagementReward.points_cost <= max_cost)
        
        # Filtro per disponibilità date
        today = date.today()
        query = query.filter(
            or_(
                EngagementReward.available_from.is_(None),
                EngagementReward.available_from <= today
            ),
            or_(
                EngagementReward.available_until.is_(None),
                EngagementReward.available_until >= today
            )
        )
        
        rewards = query.order_by(EngagementReward.points_cost).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        # Aggiungi info su riscatti precedenti per ogni premio
        rewards_data = []
        for reward in rewards.items:
            reward_dict = reward.to_dict()
            
            # Controlla se l'utente ha già riscattato questo premio
            user_redemptions = EngagementUserReward.query.filter_by(
                user_id=current_user.id,
                reward_id=reward.id
            ).count()
            
            # Get user profile
            user_profile = EngagementUserProfile.query.filter_by(user_id=current_user.id).first()
            
            reward_dict['user_redemptions'] = user_redemptions
            reward_dict['can_redeem'] = (
                reward.is_available and
                user_redemptions < reward.per_user_limit and
                user_profile is not None and
                user_profile.available_points >= reward.points_cost
            )
            
            rewards_data.append(reward_dict)
        
        return api_response(data={
                'rewards': rewards_data,
                'pagination': {
                    'page': rewards.page,
                    'pages': rewards.pages,
                    'per_page': rewards.per_page,
                    'total': rewards.total,
                    'has_next': rewards.has_next,
                    'has_prev': rewards.has_prev
                }
            },
            message="Catalogo premi caricato"
        )
        
    except Exception as e:
        current_app.logger.error(f"Errore caricamento premi: {e}")
        return api_response(message="Errore caricamento premi", status_code=500)

@api_engagement.route('/rewards/<int:reward_id>/redeem', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_PARTICIPATE_ENGAGEMENT)
def redeem_reward(reward_id):
    """Riscatta un premio"""
    try:
        data = request.get_json() or {}
        redemption_notes = data.get('notes', '')
        
        reward = EngagementReward.query.get_or_404(reward_id)
        
        # Verifica disponibilità premio
        if not reward.is_available:
            return api_response(message="Premio non più disponibile", status_code=400)
        
        # Get profilo utente
        user_profile = EngagementUserProfile.query.filter_by(user_id=current_user.id).first()
        if not user_profile:
            user_profile = create_user_profile(current_user.id)
        
        # Verifica punti sufficienti
        if user_profile.available_points < reward.points_cost:
            return api_response(message=f"Punti insufficienti. Servono {reward.points_cost} punti, ne hai {user_profile.available_points}"
            )
        
        # Verifica limite per utente
        user_redemptions = EngagementUserReward.query.filter_by(
            user_id=current_user.id,
            reward_id=reward_id
        ).count()
        
        if user_redemptions >= reward.per_user_limit:
            return api_response(message="Hai già raggiunto il limite di riscatti per questo premio", status_code=400)
        
        # Crea riscatto
        user_reward = EngagementUserReward(
            user_id=current_user.id,
            reward_id=reward_id,
            points_spent=reward.points_cost,
            redemption_status='pending',
            redemption_notes=redemption_notes
        )
        
        db.session.add(user_reward)
        
        # Aggiorna profilo utente
        user_profile.available_points -= reward.points_cost
        user_profile.total_points_spent += reward.points_cost
        
        # Aggiorna contatore riscatti premio
        reward.current_redemptions += 1
        
        db.session.commit()
        
        return api_response(data=user_reward.to_dict(),
            message=f"Premio '{reward.name}' riscattato con successo!"
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore riscatto premio: {e}")
        return api_response(message="Errore durante il riscatto", status_code=500)

@api_engagement.route('/leaderboard', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_ENGAGEMENT)
def get_leaderboard():
    """Classifica generale con filtri e paginazione"""
    try:
        # Parametri paginazione
        page, per_page = get_pagination_params()
        
        # Filtri
        period = request.args.get('period', 'month')  # week, month, quarter, year, all
        category = request.args.get('category', 'points')  # points, activities, streak, achievements
        department_id = request.args.get('department_id', type=int)
        
        # Query base con join a User e Department
        base_query = db.session.query(
            EngagementUserProfile,
            User.id.label('user_id'),
            User.username,
            User.email,
            Department.name.label('department_name')
        ).join(User, EngagementUserProfile.user_id == User.id)\
         .outerjoin(Department, User.department_id == Department.id)
        
        # Applica filtro dipartimento
        if department_id:
            base_query = base_query.filter(User.department_id == department_id)
        
        # Determina campo di ordinamento e calcolo score
        if category == 'points':
            if period == 'all':
                order_field = desc(EngagementUserProfile.total_points)
                score_field = EngagementUserProfile.total_points
            else:
                # Per periodi specifici, calcola punti dal database
                period_filter = get_period_filter(period)
                points_subquery = db.session.query(
                    EngagementPoint.user_id,
                    func.coalesce(func.sum(EngagementPoint.points_earned), 0).label('period_points')
                ).filter(period_filter).group_by(EngagementPoint.user_id).subquery()
                
                base_query = base_query.outerjoin(
                    points_subquery, EngagementUserProfile.user_id == points_subquery.c.user_id
                )
                score_field = func.coalesce(points_subquery.c.period_points, 0)
                order_field = desc(score_field)
        elif category == 'activities':
            # Conta attività completate
            order_field = desc(EngagementUserProfile.activities_completed)
            score_field = EngagementUserProfile.activities_completed
        elif category == 'streak':
            order_field = desc(EngagementUserProfile.current_streak_days)
            score_field = EngagementUserProfile.current_streak_days
        elif category == 'achievements':
            order_field = desc(EngagementUserProfile.achievements_count)
            score_field = EngagementUserProfile.achievements_count
        else:
            # Default: total points
            order_field = desc(EngagementUserProfile.total_points)
            score_field = EngagementUserProfile.total_points
        
        # Aggiungi score field alla query se non è già incluso
        if category == 'points' and period != 'all':
            query = base_query.add_columns(score_field.label('score'))
        else:
            query = base_query.add_columns(score_field.label('score'))
        
        # Ordina e pagina
        query = query.order_by(order_field)
        paginated = query.paginate(page=page, per_page=per_page, error_out=False)
        
        # Formatta risultati
        users = []
        for row in paginated.items:
            if len(row) == 6:  # profile, user_id, username, email, department_name, score
                profile, user_id, username, email, department_name, score = row
            else:  # fallback per query diverse
                profile = row[0]
                user_id = row[1] if len(row) > 1 else profile.user_id
                username = row[2] if len(row) > 2 else 'N/A'
                email = row[3] if len(row) > 3 else None
                department_name = row[4] if len(row) > 4 else None
                score = row[5] if len(row) > 5 else 0
            
            user_data = {
                'user_id': user_id,
                'username': username,
                'email': email,
                'department_name': department_name,
                'score': int(score) if score else 0,
                'current_level': profile.current_level_name if profile and hasattr(profile, 'current_level_name') else 'Novizio',
                'streak_days': profile.current_streak_days if profile and hasattr(profile, 'current_streak_days') else 0,
                'achievements_count': profile.achievements_count if profile and hasattr(profile, 'achievements_count') else 0
            }
            users.append(user_data)
        
        return api_response(data={
                'users': users,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': paginated.total,
                    'pages': paginated.pages,
                    'has_prev': paginated.has_prev,
                    'has_next': paginated.has_next
                }
            }
        )
        
    except Exception as e:
        current_app.logger.error(f"Errore caricamento leaderboard: {e}")
        return api_response(message="Errore caricamento leaderboard", status_code=500)

def get_period_filter(period):
    """Helper per creare filtri temporali"""
    now = datetime.utcnow()
    
    if period == 'week':
        start_date = now - timedelta(days=7)
    elif period == 'month':
        start_date = now - timedelta(days=30)
    elif period == 'quarter':
        start_date = now - timedelta(days=90)
    elif period == 'year':
        start_date = now - timedelta(days=365)
    else:  # 'all'
        return True  # No filter
    
    return EngagementPoint.earned_at >= start_date

# === ADMIN ENDPOINTS ===

@api_engagement.route('/admin/campaigns', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_MANAGE_ENGAGEMENT_CAMPAIGNS)
def admin_get_campaigns():
    """Admin: Lista completa campagne"""
    try:
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        
        campaigns = EngagementCampaign.query.order_by(
            desc(EngagementCampaign.created_at)
        ).paginate(page=page, per_page=per_page, error_out=False)
        
        return api_response(data={
                'campaigns': [campaign.to_dict() for campaign in campaigns.items],
                'pagination': {
                    'page': campaigns.page,
                    'pages': campaigns.pages,
                    'per_page': campaigns.per_page,
                    'total': campaigns.total,
                    'has_next': campaigns.has_next,
                    'has_prev': campaigns.has_prev
                }
            },
            message="Campagne caricate (admin)"
        )
        
    except Exception as e:
        current_app.logger.error(f"Errore admin caricamento campagne: {e}")
        return api_response(message="Errore caricamento campagne", status_code=500)

@api_engagement.route('/admin/campaigns', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_MANAGE_ENGAGEMENT_CAMPAIGNS)
def admin_create_campaign():
    """Admin: Crea nuova campagna"""
    try:
        data = request.get_json()
        
        campaign = EngagementCampaign(
            name=data['name'],
            description=data.get('description', ''),
            start_date=datetime.strptime(data['start_date'], '%Y-%m-%d').date(),
            end_date=datetime.strptime(data['end_date'], '%Y-%m-%d').date(),
            status=data.get('status', 'draft'),
            points_multiplier=data.get('points_multiplier', 1.0),
            objectives_config=data.get('objectives_config', {}),
            points_rules=data.get('points_rules', {}),
            created_by_id=current_user.id
        )
        
        db.session.add(campaign)
        db.session.commit()
        
        return api_response(data=campaign.to_dict(),
            message="Campagna creata con successo"
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore creazione campagna: {e}")
        return api_response(message="Errore creazione campagna", status_code=500)

@api_engagement.route('/admin/rewards', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_MANAGE_ENGAGEMENT_REWARDS)
def admin_get_rewards():
    """Admin: Lista completa premi"""
    try:
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        
        rewards = EngagementReward.query.order_by(
            desc(EngagementReward.created_at)
        ).paginate(page=page, per_page=per_page, error_out=False)
        
        return api_response(data={
                'rewards': [reward.to_dict() for reward in rewards.items],
                'pagination': {
                    'page': rewards.page,
                    'pages': rewards.pages,
                    'per_page': rewards.per_page,
                    'total': rewards.total,
                    'has_next': rewards.has_next,
                    'has_prev': rewards.has_prev
                }
            },
            message="Premi caricati (admin)"
        )
        
    except Exception as e:
        current_app.logger.error(f"Errore admin caricamento premi: {e}")
        return api_response(message="Errore caricamento premi", status_code=500)

@api_engagement.route('/admin/rewards', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_MANAGE_ENGAGEMENT_REWARDS)
def admin_create_reward():
    """Admin: Crea nuovo premio"""
    try:
        data = request.get_json()
        
        reward = EngagementReward(
            name=data['name'],
            description=data.get('description', ''),
            points_cost=data['points_cost'],
            reward_type=data['reward_type'],
            campaign_id=data.get('campaign_id'),
            available_from=datetime.strptime(data['available_from'], '%Y-%m-%d').date() if data.get('available_from') else None,
            available_until=datetime.strptime(data['available_until'], '%Y-%m-%d').date() if data.get('available_until') else None,
            max_redemptions=data.get('max_redemptions'),
            per_user_limit=data.get('per_user_limit', 1),
            image_url=data.get('image_url'),
            external_url=data.get('external_url'),
            is_active=data.get('is_active', True),
            created_by_id=current_user.id
        )
        
        db.session.add(reward)
        db.session.commit()
        
        return api_response(data=reward.to_dict(),
            message="Premio creato con successo"
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore creazione premio: {e}")
        return api_response(message="Errore creazione premio", status_code=500)

@api_engagement.route('/admin/points/generate', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_MANAGE_ENGAGEMENT_CAMPAIGNS)
def admin_generate_points():
    """Admin: Genera punti da audit logs"""
    try:
        data = request.get_json() or {}
        hours_back = data.get('hours_back', 24)
        
        points_service = EngagementPointsService()
        stats = points_service.process_audit_logs_for_points(hours_back=hours_back)
        
        return api_response(data=stats,
            message=f"Processati {stats['logs_processed']} logs, generati {stats['points_generated']} record punti"
        )
        
    except Exception as e:
        current_app.logger.error(f"Errore generazione punti: {e}")
        return api_response(message="Errore generazione punti", status_code=500)

@api_engagement.route('/admin/points/recalculate', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_MANAGE_ENGAGEMENT_CAMPAIGNS)
def admin_recalculate_points():
    """Admin: Ricalcola tutti i punti da zero"""
    try:
        data = request.get_json() or {}
        user_id = data.get('user_id')
        
        points_service = EngagementPointsService()
        result = points_service.recalculate_all_points(user_id=user_id)
        
        if result['success']:
            return api_response(data=result,
                message="Ricalcolo punti completato con successo"
            )
        else:
            return api_response(message=result['error'], status_code=500)
        
    except Exception as e:
        current_app.logger.error(f"Errore ricalcolo punti: {e}")
        return api_response(message="Errore ricalcolo punti", status_code=500)

@api_engagement.route('/admin/stats', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_ENGAGEMENT_ANALYTICS)
def admin_get_stats():
    """Admin: Statistiche sistema engagement"""
    try:
        # Statistiche generali sistema
        total_users = User.query.count()
        users_with_engagement = EngagementUserProfile.query.count()
        
        # Statistiche punti
        total_points_issued = EngagementPoint.query.with_entities(
            func.sum(EngagementPoint.points_earned)
        ).scalar() or 0
        
        total_points_spent = EngagementUserProfile.query.with_entities(
            func.sum(EngagementUserProfile.total_points_spent)
        ).scalar() or 0
        
        # Statistiche campagne
        total_campaigns = EngagementCampaign.query.count()
        active_campaigns = EngagementCampaign.query.filter_by(status='active').count()
        
        # Top 5 utenti per punti
        top_users = db.session.query(
            EngagementUserProfile,
            User.username,
            User.email
        ).join(User, EngagementUserProfile.user_id == User.id)\
         .order_by(desc(EngagementUserProfile.total_points))\
         .limit(5).all()
        
        # Statistiche premi
        total_rewards = EngagementReward.query.count()
        active_rewards = EngagementReward.query.filter_by(is_active=True).count()
        total_redemptions = EngagementUserReward.query.count()
        
        # Distribuzione per livello
        level_distribution = db.session.query(
            EngagementLevel.name,
            func.count(EngagementUserProfile.id).label('user_count')
        ).outerjoin(EngagementUserProfile, EngagementLevel.id == EngagementUserProfile.current_level_id)\
         .group_by(EngagementLevel.id, EngagementLevel.name)\
         .order_by(EngagementLevel.level_order).all()
        
        # Punti per fonte (ultimi 30 giorni)
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        points_by_source = db.session.query(
            EngagementPoint.source_type,
            func.sum(EngagementPoint.points_earned).label('total_points'),
            func.count(EngagementPoint.id).label('total_actions')
        ).filter(EngagementPoint.earned_at >= thirty_days_ago)\
         .group_by(EngagementPoint.source_type).all()
        
        stats_data = {
            'overview': {
                'total_users': total_users,
                'users_with_engagement': users_with_engagement,
                'engagement_adoption_rate': round((users_with_engagement / total_users * 100) if total_users > 0 else 0, 1),
                'total_points_issued': total_points_issued,
                'total_points_spent': total_points_spent,
                'points_in_circulation': total_points_issued - total_points_spent
            },
            'campaigns': {
                'total_campaigns': total_campaigns,
                'active_campaigns': active_campaigns
            },
            'rewards': {
                'total_rewards': total_rewards,
                'active_rewards': active_rewards,
                'total_redemptions': total_redemptions
            },
            'top_users': [
                {
                    'user_id': profile.user_id,
                    'username': username,
                    'email': email,
                    'total_points': profile.total_points,
                    'available_points': profile.available_points,
                    'current_level_name': profile.current_level.name if profile.current_level else 'Novizio'
                }
                for profile, username, email in top_users
            ],
            'level_distribution': [
                {
                    'level_name': name,
                    'user_count': user_count
                }
                for name, user_count in level_distribution
            ],
            'points_by_source_30d': [
                {
                    'source_type': source_type,
                    'total_points': int(total_points),
                    'total_actions': total_actions
                }
                for source_type, total_points, total_actions in points_by_source
            ]
        }
        
        return api_response(data=stats_data, message="Statistiche engagement caricate")
        
    except Exception as e:
        current_app.logger.error(f"Errore caricamento statistiche engagement: {e}")
        return api_response(message="Errore caricamento statistiche", status_code=500)

@api_engagement.route('/admin/points/config', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_ENGAGEMENT_ANALYTICS)
def admin_get_points_config():
    """Admin: Ottieni configurazione punti"""
    try:
        points_service = EngagementPointsService()
        
        config_data = {
            'base_points_config': points_service.BASE_POINTS_CONFIG,
            'resource_multipliers': points_service.RESOURCE_MULTIPLIERS,
            'system_stats': {
                'total_points_in_system': EngagementPoint.query.with_entities(
                    func.sum(EngagementPoint.points_earned)
                ).scalar() or 0,
                'total_users_with_points': EngagementUserProfile.query.filter(
                    EngagementUserProfile.total_points > 0
                ).count(),
                'points_from_audit_logs': EngagementPoint.query.filter_by(
                    source_type='audit_log'
                ).count()
            }
        }
        
        return api_response(data=config_data, message="Configurazione punti caricata")
        
    except Exception as e:
        current_app.logger.error(f"Errore caricamento config punti: {e}")
        return api_response(message="Errore caricamento configurazione", status_code=500)

# === UTILITY FUNCTIONS ===

def create_user_profile(user_id):
    """Crea profilo engagement per nuovo utente"""
    # Trova il primo livello (Novizio)
    first_level = EngagementLevel.query.filter_by(level_order=1).first()
    next_level = EngagementLevel.query.filter_by(level_order=2).first()
    
    user_profile = EngagementUserProfile(
        user_id=user_id,
        total_points=0,
        available_points=0,
        current_level_id=first_level.id if first_level else None,
        next_level_id=next_level.id if next_level else None,
        last_activity_date=date.today()
    )
    
    db.session.add(user_profile)
    db.session.commit()
    
    return user_profile

def get_user_leaderboard_position(user_id):
    """Ottieni posizione utente in classifica generale"""
    # Query per trovare posizione basata su total_points
    position_query = db.session.query(
        func.row_number().over(order_by=desc(EngagementUserProfile.total_points)).label('position'),
        EngagementUserProfile.user_id,
        EngagementUserProfile.total_points
    ).filter(EngagementUserProfile.total_points > 0).subquery()
    
    user_position = db.session.query(position_query).filter(
        position_query.c.user_id == user_id
    ).first()
    
    if user_position:
        return {
            'position': user_position.position,
            'total_points': user_position.total_points
        }
    
    return {'position': None, 'total_points': 0}