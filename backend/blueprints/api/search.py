"""
Global Search API for DatPortal
Provides unified search across projects, personnel, tasks, and other entities
"""

from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user
from sqlalchemy import or_, and_, func
from extensions import db
from utils.api_utils import api_response
# Search doesn't need special permissions - just login_required
from models_split.projects import Project, Task
from models_split.user import User
from models_split.hr import UserProfile
import logging

logger = logging.getLogger(__name__)

search_bp = Blueprint('search', __name__)

@search_bp.route('/search', methods=['GET'])
@login_required
def global_search():
    """
    Global search endpoint che cerca tra progetti, persone, task e altri contenuti.
    
    Query Parameters:
    - q: Search query (required)
    - type: Filter by entity type (optional): project, person, task, all
    - limit: Number of results per type (default: 5, max: 20)
    """
    try:
        # Get search parameters
        query = request.args.get('q', '').strip()
        entity_type = request.args.get('type', 'all')
        limit = min(int(request.args.get('limit', 5)), 20)
        
        if not query:
            return api_response([], "Search query is required", False)
        
        if len(query) < 2:
            return api_response([], "Search query must be at least 2 characters", False)
        
        logger.info(f"🔍 GLOBAL SEARCH: '{query}' (type: {entity_type}, limit: {limit}) by user {current_user.id}")
        
        results = []
        
        # Search projects
        if entity_type in ['all', 'project']:
            project_results = _search_projects(query, limit)
            results.extend(project_results)
        
        # Search personnel
        if entity_type in ['all', 'person']:
            person_results = _search_personnel(query, limit)
            results.extend(person_results)
        
        # Search tasks
        if entity_type in ['all', 'task']:
            task_results = _search_tasks(query, limit)
            results.extend(task_results)
        
        # Sort by relevance (exact matches first, then partial matches)
        results.sort(key=lambda x: (
            x['title'].lower().startswith(query.lower()),  # Exact start match (highest priority)
            query.lower() in x['title'].lower(),           # Contains in title
            query.lower() in x['description'].lower()      # Contains in description
        ), reverse=True)
        
        logger.info(f"✅ SEARCH COMPLETED: Found {len(results)} results for '{query}'")
        
        return api_response(results)
        
    except Exception as e:
        logger.error(f"❌ SEARCH ERROR: {str(e)}")
        return api_response([], f"Search failed: {str(e)}", False)

def _search_projects(query, limit):
    """Search projects by name and description."""
    try:
        # Search condition
        search_condition = or_(
            Project.name.ilike(f'%{query}%'),
            Project.description.ilike(f'%{query}%')
        )
        
        # Base query with permission filtering
        query_builder = Project.query.filter(search_condition)
        
        # Add permission-based filtering if needed
        # For now, show all projects the user can access
        
        projects = query_builder.limit(limit).all()
        
        results = []
        for project in projects:
            # Generate appropriate path based on project status/type
            path = f'/app/projects/{project.id}'
            
            results.append({
                'id': project.id,
                'type': 'project',
                'title': project.name,
                'description': project.description or 'Progetto senza descrizione',
                'path': path,
                'metadata': {
                    'status': project.status,
                    'client_name': project.client.name if project.client else None,
                    'start_date': project.start_date.isoformat() if project.start_date else None
                }
            })
        
        logger.info(f"🏗️ PROJECT SEARCH: Found {len(results)} projects matching '{query}'")
        return results
        
    except Exception as e:
        logger.error(f"❌ PROJECT SEARCH ERROR: {str(e)}")
        return []

def _search_personnel(query, limit):
    """Search personnel by name, email, and job title."""
    try:
        # Search condition across User and UserProfile
        search_condition = or_(
            func.concat(User.first_name, ' ', User.last_name).ilike(f'%{query}%'),
            User.username.ilike(f'%{query}%'),
            User.email.ilike(f'%{query}%')
        )
        
        # Join with UserProfile for additional search fields
        query_builder = db.session.query(User, UserProfile).outerjoin(
            UserProfile, User.id == UserProfile.user_id
        ).filter(search_condition)
        
        # Also search in job_title if UserProfile exists
        profile_search = or_(
            UserProfile.job_title.ilike(f'%{query}%'),
            UserProfile.employee_id.ilike(f'%{query}%')
        )
        
        # Combine search conditions
        combined_condition = or_(search_condition, profile_search)
        query_builder = query_builder.filter(combined_condition)
        
        results_data = query_builder.limit(limit).all()
        
        results = []
        for user_data in results_data:
            user = user_data[0] if isinstance(user_data, tuple) else user_data
            profile = user_data[1] if isinstance(user_data, tuple) and len(user_data) > 1 else None
            
            # Build full name
            full_name = f"{user.first_name or ''} {user.last_name or ''}".strip()
            if not full_name:
                full_name = user.username
            
            # Build description
            description_parts = []
            if profile and profile.job_title:
                description_parts.append(profile.job_title)
            if profile and profile.department:
                description_parts.append(f"Dept: {profile.department.name}")
            if not description_parts:
                description_parts.append("Dipendente")
            
            description = " • ".join(description_parts)
            
            # Generate path to personnel directory
            path = f'/app/personnel/directory/{user.id}'
            
            results.append({
                'id': user.id,
                'type': 'person',
                'title': full_name,
                'description': description,
                'path': path,
                'metadata': {
                    'username': user.username,
                    'email': user.email,
                    'job_title': profile.job_title if profile else None,
                    'employee_id': profile.employee_id if profile else None,
                    'is_active': user.is_active
                }
            })
        
        logger.info(f"👥 PERSONNEL SEARCH: Found {len(results)} people matching '{query}'")
        return results
        
    except Exception as e:
        logger.error(f"❌ PERSONNEL SEARCH ERROR: {str(e)}")
        return []

def _search_tasks(query, limit):
    """Search tasks by name and description."""
    try:
        # Search condition
        search_condition = or_(
            Task.name.ilike(f'%{query}%'),
            Task.description.ilike(f'%{query}%')
        )
        
        # Join with Project for additional context
        query_builder = db.session.query(Task, Project).join(
            Project, Task.project_id == Project.id
        ).filter(search_condition)
        
        results_data = query_builder.limit(limit).all()
        
        results = []
        for task_data in results_data:
            task = task_data[0]
            project = task_data[1]
            
            # Build description with project context
            description = task.description or 'Task senza descrizione'
            if project:
                description = f"{description} (Progetto: {project.name})"
            
            # Generate path to task detail
            path = f'/app/projects/{project.id}/tasks/{task.id}' if project else f'/app/tasks/{task.id}'
            
            results.append({
                'id': task.id,
                'type': 'task',
                'title': task.name,
                'description': description,
                'path': path,
                'metadata': {
                    'status': task.status,
                    'project_id': task.project_id,
                    'project_name': project.name if project else None,
                    'assignee_id': task.assignee_id,
                    'priority': task.priority,
                    'due_date': task.due_date.isoformat() if task.due_date else None
                }
            })
        
        logger.info(f"📋 TASK SEARCH: Found {len(results)} tasks matching '{query}'")
        return results
        
    except Exception as e:
        logger.error(f"❌ TASK SEARCH ERROR: {str(e)}")
        return []

@search_bp.route('/search/suggestions', methods=['GET'])
@login_required 
def search_suggestions():
    """
    Provides search suggestions for autocomplete.
    Returns top matches for quick suggestions.
    """
    try:
        query = request.args.get('q', '').strip()
        
        if not query or len(query) < 2:
            return api_response([])
        
        # Get quick suggestions (limit to 3 per type for fast response)
        suggestions = []
        
        # Quick project search
        projects = Project.query.filter(
            Project.name.ilike(f'%{query}%')
        ).limit(3).all()
        
        for project in projects:
            suggestions.append({
                'text': project.name,
                'type': 'project',
                'id': project.id
            })
        
        # Quick personnel search
        users = User.query.filter(
            or_(
                func.concat(User.first_name, ' ', User.last_name).ilike(f'%{query}%'),
                User.username.ilike(f'%{query}%')
            )
        ).limit(3).all()
        
        for user in users:
            full_name = f"{user.first_name or ''} {user.last_name or ''}".strip()
            if not full_name:
                full_name = user.username
                
            suggestions.append({
                'text': full_name,
                'type': 'person', 
                'id': user.id
            })
        
        # Quick task search
        tasks = Task.query.filter(
            Task.name.ilike(f'%{query}%')
        ).limit(3).all()
        
        for task in tasks:
            suggestions.append({
                'text': task.name,
                'type': 'task',
                'id': task.id
            })
        
        return api_response(suggestions)
        
    except Exception as e:
        logger.error(f"❌ SEARCH SUGGESTIONS ERROR: {str(e)}")
        return api_response([], f"Suggestions failed: {str(e)}", False)