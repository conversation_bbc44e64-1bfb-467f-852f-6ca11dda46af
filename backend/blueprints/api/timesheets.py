"""
API Blueprint per la gestione dei timesheet.
Task 3.1 - Timesheet Management System
"""

from flask import Blueprint, request, jsonify
from flask_login import current_user, login_required
from sqlalchemy import and_, extract, func, case
from datetime import datetime, date
from calendar import monthrange

from models import TimesheetEntry, User, Project, Task, MonthlyTimesheet
from utils.api_utils import api_response, handle_api_error
from utils.permissions import user_has_permission
from extensions import db, csrf
from middleware.audit_logger import audit_export_operation

# Crea il blueprint per le API dei timesheet
api_timesheets = Blueprint('api_timesheets', __name__)

@api_timesheets.route('/', methods=['GET'])
@csrf.exempt
@login_required
def get_timesheets():
    """Recupera i timesheet con filtri generali."""
    try:
        # Parametri query
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        user_id = request.args.get('user_id', type=int)
        project_id = request.args.get('project_id', type=int)
        task_id = request.args.get('task_id', type=int)
        limit = request.args.get('limit', type=int, default=50)
        offset = request.args.get('offset', type=int, default=0)

        # Query base
        query = TimesheetEntry.query

        # Verifica permessi
        if not user_has_permission(current_user.role, 'view_all_timesheets'):
            # L'utente può vedere solo i propri timesheet
            query = query.filter(TimesheetEntry.user_id == current_user.id)

        # Applica filtri con validazione date
        if start_date:
            try:
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
                query = query.filter(TimesheetEntry.date >= start_date_obj)
            except ValueError:
                return api_response(
                    False,
                    f'Formato data di inizio non valido: {start_date}. Utilizzare YYYY-MM-DD',
                    status_code=400
                )
        if end_date:
            try:
                end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
                query = query.filter(TimesheetEntry.date <= end_date_obj)
            except ValueError:
                return api_response(
                    False,
                    f'Formato data di fine non valido: {end_date}. Utilizzare YYYY-MM-DD',
                    status_code=400
                )
        if user_id:
            # Verifica permessi per vedere timesheet di altri utenti
            if not user_has_permission(current_user.role, 'view_all_timesheets') and user_id != current_user.id:
                return api_response(False, 'Non puoi visualizzare timesheet di altri utenti', status_code=403)
            query = query.filter(TimesheetEntry.user_id == user_id)
        if project_id:
            query = query.filter(TimesheetEntry.project_id == project_id)
        if task_id:
            query = query.filter(TimesheetEntry.task_id == task_id)

        # Conta totale
        total = query.count()

        # Applica paginazione e ordina per data
        timesheets = query.order_by(TimesheetEntry.date.desc()).offset(offset).limit(limit).all()

        # Prepara dati
        timesheets_data = []
        for ts in timesheets:
            timesheets_data.append({
                'id': ts.id,
                'user_id': ts.user_id,
                'project_id': ts.project_id,
                'task_id': ts.task_id,
                'date': ts.date.isoformat(),
                'hours': ts.hours,
                'description': ts.description,
                'status': ts.status,
                'user_name': f"{ts.user.first_name} {ts.user.last_name}" if ts.user else None,
                'project_name': ts.project.name if ts.project else None,
                'task_name': ts.task.name if ts.task else None,
                'created_at': ts.created_at.isoformat() if ts.created_at else None
            })

        return api_response(
            data=timesheets_data,
            message=f"Recuperati {len(timesheets_data)} timesheet",
            meta={
                'total': total,
                'limit': limit,
                'offset': offset,
                'has_more': offset + limit < total
            }
        )

    except Exception as e:
        return handle_api_error(e)

@api_timesheets.route('/project/<int:project_id>/monthly', methods=['GET'])
@csrf.exempt
@login_required
def get_project_monthly_timesheet(project_id):
    """Recupera i timesheet mensili per un progetto con layout tabellare."""
    try:
        # Parametri query
        year = int(request.args.get('year', datetime.now().year))
        month = int(request.args.get('month', datetime.now().month))
        member_id = request.args.get('member_id', type=int)

        # Verifica permessi progetto
        project = Project.query.get_or_404(project_id)
        if not user_has_permission(current_user.role, 'view_all_projects'):
            # Verifica se l'utente è membro del team
            if not any(member.id == current_user.id for member in project.team_members):
                return api_response(False, 'Accesso negato al progetto', status_code=403)

        # Calcola giorni del mese
        days_in_month = monthrange(year, month)[1]

        # Query timesheet del progetto per il mese
        query = TimesheetEntry.query.filter(
            TimesheetEntry.project_id == project_id,
            extract('year', TimesheetEntry.date) == year,
            extract('month', TimesheetEntry.date) == month
        )

        # Filtra per membro se specificato
        if member_id:
            query = query.filter(TimesheetEntry.user_id == member_id)

        timesheets = query.all()

        # Query task del progetto
        tasks = Task.query.filter(Task.project_id == project_id).all()

        # Organizza dati per task e giorni
        task_daily_data = {}
        task_daily_billing = {}
        task_totals = {}

        for task in tasks:
            task_daily_data[task.id] = {day: 0 for day in range(1, days_in_month + 1)}
            task_daily_billing[task.id] = {day: False for day in range(1, days_in_month + 1)}
            task_totals[task.id] = 0

        # Popola dati timesheet
        for ts in timesheets:
            day = ts.date.day
            task_id = ts.task_id
            if task_id and task_id in task_daily_data:
                task_daily_data[task_id][day] += ts.hours
                task_totals[task_id] += ts.hours
                # Se almeno una entry del giorno è fatturabile, segna il giorno come fatturabile
                if ts.billable:
                    task_daily_billing[task_id][day] = True

        # Prepara dati task con timesheet
        tasks_data = []
        for task in tasks:
            # Trova lavoratori per questo task nel mese
            workers = db.session.query(User).join(TimesheetEntry).filter(
                TimesheetEntry.task_id == task.id,
                TimesheetEntry.project_id == project_id,
                extract('year', TimesheetEntry.date) == year,
                extract('month', TimesheetEntry.date) == month
            ).distinct().all()

            tasks_data.append({
                'id': task.id,
                'name': task.name,
                'description': task.description,
                'daily_hours': task_daily_data[task.id],
                'daily_billing': task_daily_billing[task.id],
                'total_hours': task_totals[task.id],
                'workers': [f"{w.first_name} {w.last_name}" for w in workers]
            })

        # Calcola totali giornalieri
        daily_totals = {day: 0 for day in range(1, days_in_month + 1)}
        for task_id, daily_data in task_daily_data.items():
            for day, hours in daily_data.items():
                daily_totals[day] += hours

        return api_response(
            data={
                'year': year,
                'month': month,
                'days_in_month': days_in_month,
                'tasks': tasks_data,
                'daily_totals': daily_totals,
                'grand_total': sum(daily_totals.values()),
                'project': {
                    'id': project.id,
                    'name': project.name
                }
            },
            message="Timesheet mensile recuperato con successo"
        )

    except Exception as e:
        return handle_api_error(e)


@api_timesheets.route('/project/<int:project_id>', methods=['GET'])
@csrf.exempt
@login_required
def get_project_timesheets(project_id):
    """Recupera i timesheet per un progetto con filtri."""
    try:
        # Parametri query
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        task_id = request.args.get('task_id', type=int)
        user_id = request.args.get('user_id', type=int)

        # Verifica permessi progetto
        project = Project.query.get_or_404(project_id)
        if not user_has_permission(current_user.role, 'view_all_projects'):
            if not any(member.id == current_user.id for member in project.team_members):
                return api_response(False, 'Accesso negato al progetto', status_code=403)

        # Query base
        query = TimesheetEntry.query.filter(TimesheetEntry.project_id == project_id)

        # Applica filtri con validazione date
        if start_date:
            try:
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
                query = query.filter(TimesheetEntry.date >= start_date_obj)
            except ValueError:
                return api_response(
                    False,
                    f'Formato data di inizio non valido: {start_date}. Utilizzare YYYY-MM-DD',
                    status_code=400
                )
        if end_date:
            try:
                end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
                query = query.filter(TimesheetEntry.date <= end_date_obj)
            except ValueError:
                return api_response(
                    False,
                    f'Formato data di fine non valido: {end_date}. Utilizzare YYYY-MM-DD',
                    status_code=400
                )
        if task_id:
            query = query.filter(TimesheetEntry.task_id == task_id)
        if user_id:
            query = query.filter(TimesheetEntry.user_id == user_id)

        # Ordina per data
        timesheets = query.order_by(TimesheetEntry.date.desc()).all()

        # Prepara dati
        timesheets_data = []
        for ts in timesheets:
            timesheets_data.append({
                'id': ts.id,
                'user_id': ts.user_id,
                'task_id': ts.task_id,
                'date': ts.date.isoformat(),
                'hours': ts.hours,
                'description': ts.description,
                'status': ts.status,
                'billable': ts.billable,
                'billing_rate': ts.billing_rate,
                'user': {
                    'id': ts.user.id,
                    'first_name': ts.user.first_name,
                    'last_name': ts.user.last_name
                } if ts.user else None,
                'task': {
                    'id': ts.task.id,
                    'name': ts.task.name
                } if ts.task else None
            })

        return api_response(
            data=timesheets_data,
            message="Timesheet recuperati con successo"
        )

    except Exception as e:
        return handle_api_error(e)


@api_timesheets.route('/', methods=['POST'])
@csrf.exempt
@login_required
def create_timesheet():
    """Crea un nuovo timesheet."""
    try:
        data = request.get_json()

        # Validazione campi richiesti
        required_fields = ['project_id', 'date', 'hours']
        for field in required_fields:
            if field not in data:
                return api_response(
                    False,
                    f'Campo {field} richiesto',
                    status_code=400
                )

        # Verifica permessi progetto
        project = Project.query.get_or_404(data['project_id'])
        if not user_has_permission(current_user.role, 'manage_timesheets'):
            # Verifica se può inserire timesheet per se stesso
            user_id = data.get('user_id', current_user.id)
            if user_id != current_user.id:
                return api_response(False, 'Non puoi inserire timesheet per altri utenti', status_code=403)

        # Validazione e parsing data
        try:
            date_obj = datetime.strptime(data['date'], '%Y-%m-%d').date()
        except ValueError:
            return api_response(
                False,
                f'Formato data non valido: {data["date"]}. Utilizzare YYYY-MM-DD',
                status_code=400
            )

        # Validazione ore
        try:
            hours = float(data['hours'])
            if hours < 0:
                return api_response(
                    False,
                    'Le ore non possono essere negative',
                    status_code=400
                )
            if hours > 24:
                return api_response(
                    False,
                    'Le ore non possono superare 24 in un giorno',
                    status_code=400
                )
        except (ValueError, TypeError):
            return api_response(
                False,
                'Formato ore non valido',
                status_code=400
            )

        # Crea timesheet
        timesheet = TimesheetEntry(
            user_id=data.get('user_id', current_user.id),
            project_id=data['project_id'],
            task_id=data.get('task_id'),
            date=date_obj,
            hours=hours,
            description=data.get('description', ''),
            status=data.get('status', 'pending'),
            billable=data.get('billable', False),
            billing_rate=float(data['billing_rate']) if data.get('billing_rate') else None
        )

        db.session.add(timesheet)
        db.session.commit()

        return api_response(
            data={'id': timesheet.id},
            message='Timesheet creato con successo'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)


@api_timesheets.route('/<int:timesheet_id>', methods=['PUT'])
@csrf.exempt
@login_required
def update_timesheet(timesheet_id):
    """Aggiorna un timesheet esistente."""
    try:
        timesheet = TimesheetEntry.query.get_or_404(timesheet_id)

        # Verifica permessi
        if not user_has_permission(current_user.role, 'manage_timesheets'):
            if timesheet.user_id != current_user.id:
                return api_response(False, 'Non puoi modificare timesheet di altri utenti', status_code=403)

        data = request.get_json()

        # Aggiorna campi con validazione
        if 'date' in data:
            try:
                timesheet.date = datetime.strptime(data['date'], '%Y-%m-%d').date()
            except ValueError:
                return api_response(
                    False,
                    f'Formato data non valido: {data["date"]}. Utilizzare YYYY-MM-DD',
                    status_code=400
                )
        if 'hours' in data:
            try:
                hours = float(data['hours'])
                if hours < 0:
                    return api_response(
                        False,
                        'Le ore non possono essere negative',
                        status_code=400
                    )
                if hours > 24:
                    return api_response(
                        False,
                        'Le ore non possono superare 24 in un giorno',
                        status_code=400
                    )
                timesheet.hours = hours
            except (ValueError, TypeError):
                return api_response(
                    False,
                    'Formato ore non valido',
                    status_code=400
                )
        if 'description' in data:
            timesheet.description = data['description']
        if 'status' in data:
            timesheet.status = data['status']
        if 'task_id' in data:
            timesheet.task_id = data['task_id']
        if 'billable' in data:
            timesheet.billable = data['billable']
        if 'billing_rate' in data:
            timesheet.billing_rate = float(data['billing_rate']) if data['billing_rate'] else None

        db.session.commit()

        return api_response(
            data={},
            message='Timesheet aggiornato con successo'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)


@api_timesheets.route('/<int:timesheet_id>', methods=['DELETE'])
@csrf.exempt
@login_required
def delete_timesheet(timesheet_id):
    """Elimina un timesheet."""
    try:
        timesheet = TimesheetEntry.query.get_or_404(timesheet_id)

        # Verifica permessi
        if not user_has_permission(current_user.role, 'manage_timesheets'):
            if timesheet.user_id != current_user.id:
                return api_response(False, 'Non puoi eliminare timesheet di altri utenti', status_code=403)

        db.session.delete(timesheet)
        db.session.commit()

        return api_response(
            data={},
            message='Timesheet eliminato con successo'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

@api_timesheets.route('/status', methods=['GET'])
@csrf.exempt
@login_required
def get_timesheet_status():
    """
    Ottiene lo stato del timesheet mensile di un utente.
    
    Query Parameters:
        year_month (str): Anno e mese nel formato 'YYYY-MM'
        user_id (int): ID dell'utente (opzionale, default: utente corrente)
        
    Returns:
        JSON con lo stato del timesheet mensile (pending, confirmed, approved)
    """
    try:
        # Ottieni parametri dalla query
        year_month = request.args.get('year_month')
        user_id = request.args.get('user_id', type=int) or current_user.id
        
        # Verifica che year_month sia fornito
        if not year_month:
            return api_response(
                False,
                "Parametro year_month richiesto (formato: 'YYYY-MM')",
                status_code=400
            )
        
        # Verifica formato year_month
        try:
            year, month = year_month.split('-')
            year = int(year)
            month = int(month)
        except (ValueError, TypeError):
            return api_response(
                False,
                "Formato year_month non valido. Usa 'YYYY-MM'",
                status_code=400
            )
        
        # Verifica permessi
        if user_id != current_user.id and not user_has_permission(current_user.role, 'view_all_timesheets'):
            return api_response(
                False,
                "Non hai i permessi per visualizzare lo stato del timesheet di altri utenti",
                status_code=403
            )
        
        # Cerca il monthly timesheet nel database
        monthly_timesheet = MonthlyTimesheet.query.filter(
            and_(
                MonthlyTimesheet.user_id == user_id,
                MonthlyTimesheet.year == year,
                MonthlyTimesheet.month == month
            )
        ).first()
        
        if monthly_timesheet:
            # Se esiste, restituisci lo stato
            status = monthly_timesheet.status
        else:
            # Se non esiste, lo stato è 'pending'
            status = 'pending'
        
        return api_response(
            data={
                'status': status,
                'year': year,
                'month': month,
                'user_id': user_id
            },
            message="Stato timesheet mensile recuperato con successo"
        )
        
    except Exception as e:
        return handle_api_error(e)

@api_timesheets.route('/confirm', methods=['POST'])
@csrf.exempt
@login_required
def confirm_timesheet():
    """
    Conferma il timesheet mensile di un utente.
    
    JSON Body:
        user_id (int): ID dell'utente (opzionale, default: utente corrente)
        year_month (str): Anno e mese nel formato 'YYYY-MM'
        
    Returns:
        JSON con esito della conferma
    """
    try:
        data = request.get_json()
        
        if not data:
            return api_response(
                False,
                "Dati JSON richiesti",
                status_code=400
            )
        
        # Ottieni parametri dal body
        user_id = int(data.get('user_id', current_user.id))
        year_month = data.get('year_month')
        
        # Verifica che year_month sia fornito
        if not year_month:
            return api_response(
                False,
                "Parametro year_month richiesto (formato: 'YYYY-MM')",
                status_code=400
            )
        
        # Verifica formato year_month
        try:
            year, month = year_month.split('-')
            year = int(year)
            month = int(month)
        except (ValueError, TypeError):
            return api_response(
                False,
                "Formato year_month non valido. Usa 'YYYY-MM'",
                status_code=400
            )
        
        # Verifica permessi
        if user_id != current_user.id and not user_has_permission(current_user.role, 'manage_timesheets'):
            return api_response(
                False,
                "Non hai i permessi per confermare il timesheet di altri utenti",
                status_code=403
            )
        
        # Cerca il monthly timesheet nel database
        monthly_timesheet = MonthlyTimesheet.query.filter(
            and_(
                MonthlyTimesheet.user_id == user_id,
                MonthlyTimesheet.year == year,
                MonthlyTimesheet.month == month
            )
        ).first()
        
        if monthly_timesheet:
            # Se esiste, aggiorna lo stato
            if monthly_timesheet.status in ['approved', 'confirmed']:
                return api_response(
                    False,
                    f"Il timesheet è già stato {monthly_timesheet.status}",
                    status_code=400
                )
            
            monthly_timesheet.status = 'confirmed'
            monthly_timesheet.submission_date = datetime.utcnow()
        else:
            # Se non esiste, creane uno nuovo
            
            # Verifica che ci siano timesheet entries per il mese
            entries_count = TimesheetEntry.query.filter(
                and_(
                    TimesheetEntry.user_id == user_id,
                    extract('year', TimesheetEntry.date) == year,
                    extract('month', TimesheetEntry.date) == month
                )
            ).count()
            
            if entries_count == 0:
                return api_response(
                    False,
                    f"Nessuna entry timesheet trovata per {month}/{year}",
                    status_code=400
                )
            
            # Crea nuovo monthly timesheet
            monthly_timesheet = MonthlyTimesheet(
                user_id=user_id,
                year=year,
                month=month,
                status='confirmed',
                submission_date=datetime.utcnow()
            )
            
            db.session.add(monthly_timesheet)
            
            # Dopo il flush per ottenere l'ID
            db.session.flush()
            
            # Collega le entries esistenti al monthly timesheet
            TimesheetEntry.query.filter(
                and_(
                    TimesheetEntry.user_id == user_id,
                    extract('year', TimesheetEntry.date) == year,
                    extract('month', TimesheetEntry.date) == month
                )
            ).update({'monthly_timesheet_id': monthly_timesheet.id})
        
        db.session.commit()
        
        return api_response(
            data={
                'id': monthly_timesheet.id,
                'status': monthly_timesheet.status,
                'year': year,
                'month': month,
                'user_id': user_id,
                'submission_date': monthly_timesheet.submission_date.isoformat() if monthly_timesheet.submission_date else None
            },
            message="Timesheet mensile confermato con successo"
        )
        
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

@api_timesheets.route('/analytics/', methods=['GET'])
@csrf.exempt
@login_required
def get_analytics():
    """Recupera dati analytics aggregati per timesheet con metriche di performance."""
    try:
        # Parametri query
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        department_id = request.args.get('department_id', type=int)
        project_id = request.args.get('project_id', type=int)
        analysis_type = request.args.get('analysis_type', 'productivity')

        # Verifica permessi
        if not user_has_permission(current_user.role, 'view_all_timesheets'):
            return api_response(False, 'Accesso negato ai dati analytics', status_code=403)

        # Query base per timesheet con join alle relazioni
        query = db.session.query(
            TimesheetEntry.user_id,
            User.first_name,
            User.last_name,
            User.department,
            func.sum(TimesheetEntry.hours).label('total_hours'),
            func.sum(case((TimesheetEntry.billable == True, TimesheetEntry.hours), else_=0)).label('billable_hours'),
            func.count(func.distinct(TimesheetEntry.project_id)).label('active_projects'),
            func.sum(case((TimesheetEntry.billable == True, TimesheetEntry.hours * func.coalesce(TimesheetEntry.billing_rate, 50)), else_=0)).label('revenue')
        ).join(User, TimesheetEntry.user_id == User.id)

        # Applica filtri temporali
        if start_date:
            try:
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
                query = query.filter(TimesheetEntry.date >= start_date_obj)
            except ValueError:
                return api_response(False, 'Formato data di inizio non valido', status_code=400)
        
        if end_date:
            try:
                end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
                query = query.filter(TimesheetEntry.date <= end_date_obj)
            except ValueError:
                return api_response(False, 'Formato data di fine non valido', status_code=400)

        # Filtri aggiuntivi
        if department_id:
            query = query.filter(User.department_id == department_id)
        if project_id:
            query = query.filter(TimesheetEntry.project_id == project_id)

        # Raggruppa per utente
        results = query.group_by(
            TimesheetEntry.user_id,
            User.first_name,
            User.last_name,
            User.department
        ).all()

        # Processa risultati
        analytics_data = []
        for result in results:
            total_hours = float(result.total_hours or 0)
            billable_hours = float(result.billable_hours or 0)
            revenue = float(result.revenue or 0)
            
            # Calcola produttività (% ore fatturabili)
            productivity = (billable_hours / total_hours * 100) if total_hours > 0 else 0
            
            # Se non c'è billing_rate, usa una tariffa standard per il calcolo revenue
            if revenue == 0 and billable_hours > 0:
                revenue = billable_hours * 50  # Tariffa standard €50/ora

            analytics_data.append({
                'id': result.user_id,
                'full_name': f"{result.first_name} {result.last_name}",
                'department': result.department or 'N/A',
                'total_hours': round(total_hours, 2),
                'billable_hours': round(billable_hours, 2),
                'productivity': round(productivity, 1),
                'revenue': round(revenue, 2),
                'active_projects': int(result.active_projects or 0)
            })

        # Calcola metriche di confronto se possibile
        # TODO: Implementare logica per calcolare trend vs periodo precedente
        
        return api_response(
            data=analytics_data,
            message=f"Analytics recuperati per {len(analytics_data)} utenti",
            meta={
                'analysis_type': analysis_type,
                'date_range': {
                    'start_date': start_date,
                    'end_date': end_date
                },
                'filters': {
                    'department_id': department_id,
                    'project_id': project_id
                }
            }
        )

    except Exception as e:
        return handle_api_error(e)

@api_timesheets.route('/export/', methods=['GET'])
@csrf.exempt
@login_required
@audit_export_operation('timesheet', 'data_export')
def export_timesheet_data():
    """Esporta dati timesheet in formato Excel con audit logging."""
    try:
        from utils.export_utils import export_timesheet_data, generate_filename, ExportError
        from flask import send_file
        import tempfile
        import os
        
        # Verifica permessi
        if not user_has_permission(current_user.role, 'view_all_timesheets'):
            return api_response(False, 'Accesso negato all\'export dei dati', status_code=403)

        # Parametri di filtro
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        user_id = request.args.get('user_id')
        project_id = request.args.get('project_id')
        export_format = request.args.get('format', 'excel').lower()
        
        # Costruisci query base
        query = TimesheetEntry.query
        
        # Applica filtri
        if start_date:
            try:
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
                query = query.filter(TimesheetEntry.date >= start_date_obj)
            except ValueError:
                return api_response(False, 'Formato data inizio non valido (YYYY-MM-DD)', status_code=400)
        
        if end_date:
            try:
                end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
                query = query.filter(TimesheetEntry.date <= end_date_obj)
            except ValueError:
                return api_response(False, 'Formato data fine non valido (YYYY-MM-DD)', status_code=400)
        
        if user_id:
            query = query.filter(TimesheetEntry.user_id == user_id)
        
        if project_id:
            query = query.filter(TimesheetEntry.project_id == project_id)
        
        # Esegui query con join per includere nomi
        entries = query.join(User, TimesheetEntry.user_id == User.id)\
                      .join(Project, TimesheetEntry.project_id == Project.id, isouter=True)\
                      .add_columns(
                          User.first_name, User.last_name,
                          Project.name.label('project_name')
                      ).all()
        
        if not entries:
            return api_response(False, 'Nessun dato trovato per i filtri specificati', status_code=404)
        
        # Prepara dati per export
        timesheet_data = []
        total_hours = 0
        total_entries = len(entries)
        
        for entry_data in entries:
            entry = entry_data[0]  # TimesheetEntry object
            user_first_name = entry_data[1]
            user_last_name = entry_data[2]
            project_name = entry_data[3] or 'Nessun progetto'
            
            timesheet_data.append({
                'data': entry.date,
                'dipendente': f"{user_first_name} {user_last_name}",
                'progetto': project_name,
                'ore': entry.hours,
                'descrizione': entry.description or '',
                'stato': entry.status or 'pending',
                'creato_il': entry.created_at
            })
            total_hours += entry.hours
        
        # Calcola statistiche di riepilogo
        unique_users = len(set(f"{data['dipendente']}" for data in timesheet_data))
        unique_projects = len(set(data['progetto'] for data in timesheet_data))
        
        summary = {
            'periodo': f"{start_date or 'Inizio'} - {end_date or 'Fine'}",
            'totale_ore': total_hours,
            'totale_voci': total_entries,
            'dipendenti_coinvolti': unique_users,
            'progetti_coinvolti': unique_projects,
            'media_ore_per_voce': round(total_hours / total_entries, 2) if total_entries > 0 else 0
        }
        
        # Genera export
        try:
            export_data = export_timesheet_data(timesheet_data, summary, export_format)
            
            # Genera nome file
            filename = generate_filename("timesheet_export", 
                                       'xlsx' if export_format == 'excel' else export_format)
            
            # Crea file temporaneo
            temp_path = os.path.join(tempfile.gettempdir(), filename)
            with open(temp_path, 'wb') as f:
                f.write(export_data.read())
            
            # Restituisci file
            return send_file(
                temp_path,
                as_attachment=True,
                download_name=filename,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' if export_format == 'excel' else 'application/pdf'
            )
            
        except ExportError as e:
            current_app.logger.error(f"Errore export timesheet: {str(e)}")
            return api_response(False, f'Errore durante export: {str(e)}', status_code=500)
        
    except Exception as e:
        current_app.logger.error(f"Errore endpoint export timesheet: {str(e)}")
        return handle_api_error(e, "Errore durante l'export dei timesheet")