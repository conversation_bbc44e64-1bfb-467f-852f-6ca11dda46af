from flask import Blueprint, jsonify, current_app
from sqlalchemy import desc
from models import Service, News
import os
import json

public_api_bp = Blueprint('public_api', __name__, url_prefix='/api/public')

@public_api_bp.route('/config')
def get_public_config():
    """Get public configuration for the tenant"""
    try:
        # Load tenant configuration from file or database
        config_path = os.path.join(current_app.root_path, 'config', 'tenant_config.json')

        # Default configuration
        default_config = {
            'company': {
                'name': 'DatVinci',
                'tagline': 'Innovazione per il futuro',
                'description': 'Supportiamo le aziende nel loro percorso di innovazione attraverso soluzioni tecnologiche all\'avanguardia.',
                'mission': 'Supportare le aziende nel loro percorso di innovazione attraverso soluzioni tecnologiche all\'avanguardia.',
                'vision': 'Diventare il partner di riferimento per l\'innovazione tecnologica in Italia.',
                'founded': '2018',
                'team_size': '25+ professionisti',
                'expertise': [
                    'Sviluppo Software',
                    'Intelligenza Artificiale',
                    'Consulenza IT',
                    'Gestione Progetti Innovativi',
                    'Supporto su Bandi e Finanziamenti'
                ]
            },
            'contact': {
                'address': 'Via dell\'Innovazione 123, Milano',
                'email': '<EMAIL>',
                'phone': '+39 02 1234567',
                'hours': 'Lun-Ven: 9:00 - 18:00',
                'social': {
                    'linkedin': '#',
                    'twitter': '#',
                    'facebook': '#'
                }
            },
            'pages': {
                'home': {
                    'hero_title': 'Innovazione per il futuro',
                    'hero_subtitle': 'Supportiamo le aziende nel loro percorso di crescita attraverso soluzioni tecnologiche all\'avanguardia',
                    'cta_primary': 'Scopri i nostri servizi',
                    'cta_secondary': 'Contattaci',
                    'services_title': 'I nostri servizi',
                    'services_subtitle': 'Soluzioni innovative per ogni esigenza aziendale',
                    'news_title': 'Ultime notizie',
                    'final_cta_title': 'Pronto a innovare la tua azienda?',
                    'final_cta_subtitle': 'Contattaci per una consulenza gratuita',
                    'final_cta_button': 'Inizia ora'
                },
                'about': {
                    'title': 'Chi Siamo',
                    'subtitle': 'La nostra storia e i nostri valori',
                    'content': 'Siamo un team di professionisti appassionati di tecnologia e innovazione.',
                    'team_title': 'Il nostro team',
                    'values_title': 'I nostri valori'
                },
                'services': {
                    'title': 'I nostri servizi',
                    'subtitle': 'Soluzioni complete per la tua azienda',
                    'content': 'Offriamo una gamma completa di servizi per supportare la crescita della tua azienda.'
                },
                'contact': {
                    'title': 'Contattaci',
                    'subtitle': 'Siamo qui per aiutarti',
                    'content': 'Non esitare a contattarci per qualsiasi informazione o per richiedere una consulenza gratuita.',
                    'form_title': 'Invia un messaggio',
                    'success_message': 'Messaggio inviato con successo! Ti contatteremo presto.',
                    'error_message': 'Tutti i campi sono obbligatori'
                }
            }
        }

        # Try to load custom configuration
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                custom_config = json.load(f)
                # Merge with default config
                default_config.update(custom_config)

        return jsonify({
            'success': True,
            'data': default_config
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@public_api_bp.route('/services/featured')
def featured_services():
    """Get featured services for homepage"""
    try:
        services = Service.query.filter_by(status='active').limit(4).all()

        services_data = []
        for service in services:
            services_data.append({
                'id': service.id,
                'name': service.name,
                'description': service.description[:150] + '...' if len(service.description) > 150 else service.description,
                'category': service.category,
                'icon': get_service_icon(service.category)
            })

        return jsonify({
            'success': True,
            'data': services_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@public_api_bp.route('/news/recent')
def recent_news():
    """Get recent news for homepage"""
    try:
        news = News.query.filter_by(is_published=True).order_by(desc(News.created_at)).limit(3).all()

        news_data = []
        for item in news:
            news_data.append({
                'id': item.id,
                'title': item.title,
                'excerpt': item.content[:200] + '...' if len(item.content) > 200 else item.content,
                'created_at': item.created_at.isoformat()
            })

        return jsonify({
            'success': True,
            'data': news_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@public_api_bp.route('/services')
def all_services():
    """Get all services"""
    try:
        services = Service.query.filter_by(status='active').order_by(Service.name).all()

        services_data = []
        for service in services:
            services_data.append({
                'id': service.id,
                'name': service.name,
                'description': service.description,
                'category': service.category,
                'icon': get_service_icon(service.category)
            })

        return jsonify({
            'success': True,
            'data': services_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@public_api_bp.route('/services/<int:service_id>')
def service_detail(service_id):
    """Get service detail"""
    try:
        service = Service.query.get_or_404(service_id)

        # Get related services
        related_services = Service.query.filter(
            Service.category == service.category,
            Service.id != service.id,
            Service.status == 'active'
        ).limit(3).all()

        related_data = []
        for related in related_services:
            related_data.append({
                'id': related.id,
                'name': related.name,
                'description': related.description[:100] + '...' if len(related.description) > 100 else related.description,
                'category': related.category
            })

        service_data = {
            'id': service.id,
            'name': service.name,
            'description': service.description,
            'category': service.category,
            'icon': get_service_icon(service.category),
            'related_services': related_data
        }

        return jsonify({
            'success': True,
            'data': service_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def get_service_icon(category):
    """Get icon for service category"""
    icons = {
        'Sviluppo Software': 'code-bracket',
        'Intelligenza Artificiale': 'cpu-chip',
        'Consulenza IT': 'computer-desktop',
        'Gestione Progetti': 'briefcase',
        'Gestione Progetti Innovativi': 'briefcase',
        'Supporto su Bandi e Finanziamenti': 'banknotes',
        'Finanziamenti': 'banknotes',
        'default': 'wrench-screwdriver'
    }
    return icons.get(category, icons['default'])
