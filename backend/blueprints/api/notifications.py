from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user
from extensions import db
from models import Notification, User
from utils.decorators import admin_required
from utils.permissions import user_has_permission, PERMISSION_VIEW_ALL_PROJECTS
from datetime import datetime

notifications_bp = Blueprint('notifications', __name__)

@notifications_bp.route('/notifications', methods=['GET'])
@login_required
def get_notifications():
    """Recupera le notifiche dell'utente corrente"""
    try:
        # Parametri query
        limit = request.args.get('limit', 50, type=int)
        unread_only = request.args.get('unread_only', 'false').lower() == 'true'
        
        # Query base
        query = Notification.query.filter_by(user_id=current_user.id)
        
        # Filtro per non lette
        if unread_only:
            query = query.filter_by(is_read=False)
        
        # Ordine e limite
        notifications = query.order_by(Notification.created_at.desc()).limit(limit).all()
        
        # Serializzazione
        notifications_data = [{
            'id': n.id,
            'title': n.title,
            'message': n.message,
            'type': n.type,
            'link': n.link,
            'is_read': n.is_read,
            'created_at': n.created_at.isoformat()
        } for n in notifications]
        
        return jsonify({
            'notifications': notifications_data,
            'unread_count': Notification.query.filter_by(user_id=current_user.id, is_read=False).count()
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@notifications_bp.route('/notifications/<int:notification_id>/read', methods=['PUT'])
@login_required
def mark_notification_read(notification_id):
    """Segna una notifica come letta"""
    try:
        notification = Notification.query.filter_by(
            id=notification_id, 
            user_id=current_user.id
        ).first()
        
        if not notification:
            return jsonify({'error': 'Notifica non trovata'}), 404
        
        notification.is_read = True
        db.session.commit()
        
        return jsonify({'message': 'Notifica segnata come letta'})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@notifications_bp.route('/notifications/mark-all-read', methods=['PUT'])
@login_required
def mark_all_read():
    """Segna tutte le notifiche come lette"""
    try:
        Notification.query.filter_by(
            user_id=current_user.id, 
            is_read=False
        ).update({'is_read': True})
        
        db.session.commit()
        
        return jsonify({'message': 'Tutte le notifiche sono state segnate come lette'})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@notifications_bp.route('/notifications/unread-count', methods=['GET'])
@login_required
def get_unread_count():
    """Restituisce il numero di notifiche non lette"""
    try:
        count = Notification.query.filter_by(
            user_id=current_user.id, 
            is_read=False
        ).count()
        
        return jsonify({'unread_count': count})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@notifications_bp.route('/notifications', methods=['POST'])
@login_required
@admin_required
def create_notification():
    """Crea una nuova notifica (solo admin)"""
    try:
        data = request.get_json()
        
        # Validazione campi richiesti
        required_fields = ['user_id', 'title', 'message']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Campo obbligatorio mancante: {field}'}), 400
        
        # Verifica che l'utente esista
        user = User.query.get(data['user_id'])
        if not user:
            return jsonify({'error': 'Utente non trovato'}), 404
        
        # Creazione notifica
        notification = Notification(
            user_id=data['user_id'],
            title=data['title'],
            message=data['message'],
            type=data.get('type', 'info'),
            link=data.get('link')
        )
        
        db.session.add(notification)
        db.session.commit()
        
        return jsonify({
            'message': 'Notifica creata con successo',
            'notification_id': notification.id
        }), 201
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def create_notification_for_user(user_id, title, message, notification_type='info', link=None):
    """Funzione helper per creare notifiche programmaticamente"""
    try:
        notification = Notification(
            user_id=user_id,
            title=title,
            message=message,
            type=notification_type,
            link=link
        )
        
        db.session.add(notification)
        db.session.commit()
        
        return notification
        
    except Exception as e:
        print(f"Errore creazione notifica: {e}")
        db.session.rollback()
        return None

def notify_admins(title, message, notification_type='info', link=None):
    """Funzione helper per notificare tutti gli admin"""
    try:
        # Trova tutti gli admin
        admins = User.query.filter_by(role='admin', is_active=True).all()
        
        for admin in admins:
            create_notification_for_user(
                admin.id, 
                title, 
                message, 
                notification_type, 
                link
            )
            
    except Exception as e:
        print(f"Errore notifica admin: {e}")