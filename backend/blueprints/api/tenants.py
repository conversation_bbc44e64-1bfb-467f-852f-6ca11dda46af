from flask import Blueprint, render_template, jsonify
import json
import os

tenants_api = Blueprint('tenants_api', __name__)

def load_tenant_config():
    """Carica la configurazione tenant"""
    config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'tenant_config.json')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Errore caricamento tenant config: {e}")
        return {
            "company": {"name": "DatPortal", "tagline": "Enterprise Intranet"},
            "navigation": {"home": "Home", "about": "Chi Siamo", "services": "Servizi", "contact": "Contatti", "login": "Accedi"},
            "footer": {"copyright": "© {current_year} DatPortal. Tutti i diritti riservati."}
        }

@tenants_api.route('/vue')
@tenants_api.route('/vue/')
def vue_app():
    """Serve l'applicazione Vue.js compilata"""
    tenant_config = load_tenant_config()
    return render_template('vue_app.html', tenant_config=tenant_config)

@tenants_api.route('/config/tenant')
def api_tenant_config():
    """API per ottenere la configurazione tenant"""
    tenant_config = load_tenant_config()
    return jsonify(tenant_config)