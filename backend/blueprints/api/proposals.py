"""
API Blueprint per la gestione delle proposte/opportunità.
Task 4 - CRM Integration
"""

from flask import Blueprint, request, jsonify
from flask_login import current_user, login_required
from sqlalchemy import and_, or_, extract
from datetime import datetime, date

from models import Proposal, Client, User, Project, Contract
from utils.api_utils import api_response, handle_api_error
from utils.permissions import user_has_permission
from extensions import db, csrf
from services.ai import analyze_text_with_openai

api_proposals = Blueprint('api_proposals', __name__)


@api_proposals.route('/', methods=['GET'])
@login_required
def get_proposals():
    """Recupera lista proposte con filtri"""
    try:
        # Parametri filtro
        client_id = request.args.get('client_id', type=int)
        status = request.args.get('status')  # draft, sent, negotiating, accepted, rejected
        created_by = request.args.get('created_by', type=int)
        start_date = request.args.get('start_date')  # YYYY-MM-DD
        end_date = request.args.get('end_date')  # YYYY-MM-DD
        search = request.args.get('search')  # Ricerca in titolo
        
        # Paginazione
        page = request.args.get('page', type=int, default=1)
        per_page = request.args.get('per_page', type=int, default=50)
        
        # Query base
        query = Proposal.query
        
        # Controllo permessi
        if not user_has_permission(current_user.role, 'view_all_proposals'):
            # L'utente può vedere solo le proprie proposte
            query = query.filter(Proposal.created_by == current_user.id)
        
        # Applica filtri
        if client_id:
            query = query.filter(Proposal.client_id == client_id)
            
        if status:
            query = query.filter(Proposal.status == status)
            
        if created_by:
            query = query.filter(Proposal.created_by == created_by)
            
        if start_date:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            query = query.filter(Proposal.created_at >= start_date_obj)
            
        if end_date:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(Proposal.created_at <= end_date_obj)
            
        if search:
            search_pattern = f"%{search}%"
            query = query.filter(Proposal.title.ilike(search_pattern))
        
        # Ordina per data di creazione (più recenti prima)
        query = query.order_by(Proposal.created_at.desc())
        
        # Applica paginazione
        paginated = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        # Prepara dati
        proposals_data = []
        for proposal in paginated.items:
            proposals_data.append({
                'id': proposal.id,
                'client_id': proposal.client_id,
                'client': {
                    'id': proposal.client.id,
                    'name': proposal.client.name,
                    'company': getattr(proposal.client, 'company', None)
                } if proposal.client else None,
                'title': proposal.title,
                'description': proposal.description,
                'value': proposal.value,
                'status': proposal.status,
                'created_by': proposal.created_by,
                'creator': {
                    'id': proposal.creator.id,
                    'first_name': proposal.creator.first_name,
                    'last_name': proposal.creator.last_name
                } if proposal.creator else None,
                'sent_date': proposal.sent_date.isoformat() if proposal.sent_date else None,
                'expiry_date': proposal.expiry_date.isoformat() if proposal.expiry_date else None,
                'created_at': proposal.created_at.isoformat(),
                'updated_at': proposal.updated_at.isoformat()
            })
        
        return api_response(
            data={
                'proposals': proposals_data,
                'pagination': {
                    'page': paginated.page,
                    'pages': paginated.pages,
                    'per_page': paginated.per_page,
                    'total': paginated.total,
                    'has_next': paginated.has_next,
                    'has_prev': paginated.has_prev
                }
            },
            message=f"Recuperate {len(proposals_data)} proposte"
        )
        
    except Exception as e:
        return handle_api_error(e)


@api_proposals.route('/', methods=['POST'])
@csrf.exempt
@login_required
def create_proposal():
    """Crea una nuova proposta"""
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, 'manage_proposals'):
            return api_response(False, 'Non hai i permessi per creare proposte', status_code=403)
        
        data = request.get_json()
        
        # Validazione campi richiesti
        required_fields = ['client_id', 'title']
        for field in required_fields:
            if field not in data:
                return api_response(
                    False,
                    f'Campo {field} richiesto',
                    status_code=400
                )
        
        # Verifica che il cliente esista
        client = Client.query.get(data['client_id'])
        if not client:
            return api_response(
                False,
                'Cliente non trovato',
                status_code=404
            )
        
        # Validazione status
        valid_statuses = ['draft', 'sent', 'negotiating', 'accepted', 'rejected']
        status = data.get('status', 'draft')
        if status not in valid_statuses:
            return api_response(
                False,
                f'Status non valido. Valori ammessi: {", ".join(valid_statuses)}',
                status_code=400
            )
        
        # Parsing date
        sent_date = None
        expiry_date = None
        
        if 'sent_date' in data and data['sent_date']:
            try:
                sent_date = datetime.strptime(data['sent_date'], '%Y-%m-%d').date()
            except ValueError:
                return api_response(
                    False,
                    'Formato sent_date non valido. Utilizzare YYYY-MM-DD',
                    status_code=400
                )
        
        if 'expiry_date' in data and data['expiry_date']:
            try:
                expiry_date = datetime.strptime(data['expiry_date'], '%Y-%m-%d').date()
            except ValueError:
                return api_response(
                    False,
                    'Formato expiry_date non valido. Utilizzare YYYY-MM-DD',
                    status_code=400
                )
        
        # Validazione logica date
        if sent_date and expiry_date and sent_date > expiry_date:
            return api_response(
                False,
                'La data di invio non può essere successiva alla data di scadenza',
                status_code=400
            )
        
        # Crea nuova proposta
        proposal = Proposal(
            client_id=data['client_id'],
            title=data['title'],
            description=data.get('description', ''),
            value=data.get('value'),
            status=status,
            created_by=current_user.id,
            sent_date=sent_date,
            expiry_date=expiry_date
        )
        
        db.session.add(proposal)
        db.session.commit()
        
        return api_response(
            data={
                'id': proposal.id,
                'client_id': proposal.client_id,
                'client': {
                    'id': proposal.client.id,
                    'name': proposal.client.name
                },
                'title': proposal.title,
                'description': proposal.description,
                'value': proposal.value,
                'status': proposal.status,
                'created_by': proposal.created_by,
                'creator': {
                    'id': current_user.id,
                    'first_name': current_user.first_name,
                    'last_name': current_user.last_name
                },
                'sent_date': proposal.sent_date.isoformat() if proposal.sent_date else None,
                'expiry_date': proposal.expiry_date.isoformat() if proposal.expiry_date else None,
                'created_at': proposal.created_at.isoformat()
            },
            message='Proposta creata con successo'
        )
        
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)


@api_proposals.route('/<int:proposal_id>', methods=['GET'])
@login_required
def get_proposal(proposal_id):
    """Recupera dettaglio singola proposta"""
    try:
        proposal = Proposal.query.get_or_404(proposal_id)
        
        # Controllo permessi
        if not user_has_permission(current_user.role, 'view_all_proposals'):
            if proposal.created_by != current_user.id:
                return api_response(False, 'Non hai i permessi per visualizzare questa proposta', status_code=403)
        
        return api_response(
            data={
                'id': proposal.id,
                'client_id': proposal.client_id,
                'client': {
                    'id': proposal.client.id,
                    'name': proposal.client.name,
                    'company': getattr(proposal.client, 'company', None),
                    'industry': proposal.client.industry,
                    'website': proposal.client.website,
                    'address': proposal.client.address
                } if proposal.client else None,
                'title': proposal.title,
                'description': proposal.description,
                'value': proposal.value,
                'status': proposal.status,
                'created_by': proposal.created_by,
                'creator': {
                    'id': proposal.creator.id,
                    'first_name': proposal.creator.first_name,
                    'last_name': proposal.creator.last_name,
                    'email': proposal.creator.email
                } if proposal.creator else None,
                'sent_date': proposal.sent_date.isoformat() if proposal.sent_date else None,
                'expiry_date': proposal.expiry_date.isoformat() if proposal.expiry_date else None,
                'created_at': proposal.created_at.isoformat(),
                'updated_at': proposal.updated_at.isoformat()
            },
            message="Dettaglio proposta recuperato con successo"
        )
        
    except Exception as e:
        return handle_api_error(e)


@api_proposals.route('/<int:proposal_id>', methods=['PUT'])
@csrf.exempt
@login_required
def update_proposal(proposal_id):
    """Aggiorna una proposta esistente"""
    try:
        proposal = Proposal.query.get_or_404(proposal_id)

        # Controllo permessi
        if not user_has_permission(current_user.role, 'manage_proposals'):
            if proposal.created_by != current_user.id:
                return api_response(False, 'Non hai i permessi per modificare questa proposta', status_code=403)

        data = request.get_json()

        # Non permettere modifiche a proposte accettate/rifiutate
        if proposal.status in ['accepted', 'rejected'] and 'status' not in data:
            return api_response(
                False,
                'Non è possibile modificare proposte già accettate o rifiutate',
                status_code=400
            )

        # Aggiorna campi se forniti
        if 'title' in data:
            proposal.title = data['title']

        if 'description' in data:
            proposal.description = data['description']

        if 'value' in data:
            proposal.value = data['value']

        if 'status' in data:
            valid_statuses = ['draft', 'sent', 'negotiating', 'accepted', 'rejected']
            if data['status'] not in valid_statuses:
                return api_response(
                    False,
                    f'Status non valido. Valori ammessi: {", ".join(valid_statuses)}',
                    status_code=400
                )
            proposal.status = data['status']

            # Se status diventa 'sent' e non c'è sent_date, impostala a oggi
            if data['status'] == 'sent' and not proposal.sent_date:
                proposal.sent_date = date.today()

        # Aggiorna date se fornite
        if 'sent_date' in data:
            if data['sent_date']:
                try:
                    proposal.sent_date = datetime.strptime(data['sent_date'], '%Y-%m-%d').date()
                except ValueError:
                    return api_response(
                        False,
                        'Formato sent_date non valido. Utilizzare YYYY-MM-DD',
                        status_code=400
                    )
            else:
                proposal.sent_date = None

        if 'expiry_date' in data:
            if data['expiry_date']:
                try:
                    proposal.expiry_date = datetime.strptime(data['expiry_date'], '%Y-%m-%d').date()
                except ValueError:
                    return api_response(
                        False,
                        'Formato expiry_date non valido. Utilizzare YYYY-MM-DD',
                        status_code=400
                    )
            else:
                proposal.expiry_date = None

        # Validazione logica date
        if proposal.sent_date and proposal.expiry_date and proposal.sent_date > proposal.expiry_date:
            return api_response(
                False,
                'La data di invio non può essere successiva alla data di scadenza',
                status_code=400
            )

        db.session.commit()

        return api_response(
            data={
                'id': proposal.id,
                'title': proposal.title,
                'value': proposal.value,
                'status': proposal.status,
                'sent_date': proposal.sent_date.isoformat() if proposal.sent_date else None,
                'expiry_date': proposal.expiry_date.isoformat() if proposal.expiry_date else None,
                'updated_at': proposal.updated_at.isoformat()
            },
            message='Proposta aggiornata con successo'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)


@api_proposals.route('/<int:proposal_id>', methods=['DELETE'])
@csrf.exempt
@login_required
def delete_proposal(proposal_id):
    """Elimina una proposta"""
    try:
        proposal = Proposal.query.get_or_404(proposal_id)

        # Controllo permessi
        if not user_has_permission(current_user.role, 'manage_proposals'):
            if proposal.created_by != current_user.id:
                return api_response(False, 'Non hai i permessi per eliminare questa proposta', status_code=403)

        # Non permettere eliminazione di proposte accettate
        if proposal.status == 'accepted':
            return api_response(
                False,
                'Non è possibile eliminare proposte accettate',
                status_code=400
            )

        proposal_title = proposal.title
        db.session.delete(proposal)
        db.session.commit()

        return api_response(
            message=f'Proposta "{proposal_title}" eliminata con successo'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)


@api_proposals.route('/generate-ai', methods=['POST'])
@csrf.exempt
@login_required
def generate_proposal_with_ai():
    """Genera una proposta usando AI"""
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, 'manage_proposals'):
            return api_response(False, 'Non hai i permessi per generare proposte', status_code=403)

        data = request.get_json()

        # Validazione campi richiesti
        required_fields = ['client_id', 'project_type', 'requirements']
        for field in required_fields:
            if field not in data:
                return api_response(
                    False,
                    f'Campo {field} richiesto',
                    status_code=400
                )

        # Verifica che il cliente esista
        client = Client.query.get(data['client_id'])
        if not client:
            return api_response(
                False,
                'Cliente non trovato',
                status_code=404
            )

        # Raccogli dati del cliente
        client_context = {
            "name": client.name,
            "industry": client.industry,
            "company": getattr(client, 'company', None),
            "website": client.website,
            "description": client.description
        }

        # Raccogli progetti similari completati con successo (per contesto)
        similar_projects = Project.query.filter(
            Project.status.in_(['completed', 'delivered']),
            Project.project_type == data['project_type']
        ).limit(3).all()

        similar_projects_context = []
        for project in similar_projects:
            similar_projects_context.append({
                "name": project.name,
                "description": project.description,
                "budget": project.budget,
                "duration": f"{project.start_date} - {project.end_date}" if project.start_date and project.end_date else None,
                "success_metrics": f"Budget: €{project.budget}, Status: {project.status}"
            })

        # Prepara prompt per AI
        ai_prompt = f"""
        Sei un esperto commerciale che deve creare una proposta professionale. 

        DATI CLIENTE:
        - Nome: {client_context['name']}
        - Settore: {client_context['industry']}
        - Descrizione: {client_context['description']}

        RICHIESTA PROGETTO:
        - Tipo: {data['project_type']}
        - Requisiti dettagliati: {data['requirements']}
        - Budget range: {data.get('budget_range', 'Non specificato')}
        - Timeline desiderata: {data.get('timeline', 'Non specificata')}

        PROGETTI SIMILARI COMPLETATI (per riferimento prezzi/timeline):
        {chr(10).join([f"- {p['name']}: {p['description']} (Budget: €{p['budget']})" for p in similar_projects_context]) if similar_projects_context else "- Nessun progetto simile nel database"}

        ISTRUZIONI:
        1. Crea un titolo accattivante e professionale
        2. Scrivi una descrizione articolata (300-500 parole) che includa:
           - Analisi delle esigenze del cliente
           - Soluzione proposta con dettagli tecnici
           - Benefici e risultati attesi
           - Approccio metodologico
        3. Stima un valore realistico in euro basato sui progetti simili
        4. Calcola timeline in giorni lavorativi
        5. Elenca deliverable concreti e misurabili
        6. Identifica punti di forza della proposta

        FORMATO RICHIESTO - Restituisci SOLO questo JSON valido senza testo aggiuntivo:
        {{
            "title": "Titolo professionale e accattivante",
            "description": "Descrizione dettagliata e professionale di 300-500 parole che spiega la soluzione, i benefici e l'approccio metodologico",
            "estimated_value": numero_intero_euro_senza_decimali,
            "timeline_days": numero_giorni_lavorativi,
            "key_deliverables": [
                "Deliverable concreto 1",
                "Deliverable concreto 2", 
                "Deliverable concreto 3"
            ],
            "value_proposition": [
                "Punto di forza specifico 1",
                "Punto di forza specifico 2",
                "Punto di forza specifico 3"
            ],
            "methodology": "Descrizione concisa dell'approccio metodologico (es. Agile, Waterfall, iterativo)"
        }}
        """

        # Chiama servizio AI con formato JSON forzato
        from services.ai import get_openai_client
        import json
        
        try:
            client = get_openai_client()
            response = client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": "Sei un esperto commerciale. Rispondi SEMPRE con JSON valido senza testo aggiuntivo."},
                    {"role": "user", "content": ai_prompt}
                ],
                temperature=0.2,
                response_format={"type": "json_object"},
            )
            
            ai_response = response.choices[0].message.content
            ai_data = json.loads(ai_response)
            
            # Validazione e pulizia dati
            ai_data = {
                "title": ai_data.get("title", f"Proposta {data['project_type']} per {client_context['name']}"),
                "description": ai_data.get("description", "Descrizione non disponibile"),
                "estimated_value": int(ai_data.get("estimated_value", 0)) if ai_data.get("estimated_value") else None,
                "timeline_days": int(ai_data.get("timeline_days", 0)) if ai_data.get("timeline_days") else None,
                "key_deliverables": ai_data.get("key_deliverables", []),
                "value_proposition": ai_data.get("value_proposition", []),
                "methodology": ai_data.get("methodology", "Metodologia agile")
            }
            
        except Exception as e:
            print(f"AI Generation Error: {str(e)}")
            # Fallback structure
            ai_data = {
                "title": f"Proposta {data['project_type']} per {client_context['name']}",
                "description": f"Proposta per lo sviluppo di {data['project_type']} basata sui requisiti: {data['requirements'][:200]}...",
                "estimated_value": None,
                "timeline_days": None,
                "key_deliverables": ["Analisi requisiti", "Sviluppo soluzione", "Testing e deployment"],
                "value_proposition": ["Soluzione personalizzata", "Supporto tecnico", "Metodologia agile"],
                "methodology": "Metodologia agile con iterazioni settimanali"
            }

        # Calcola date suggerite
        from datetime import timedelta
        today = date.today()
        
        # Data invio: oggi
        # Data scadenza: 30 giorni da oggi (standard commerciale)
        suggested_dates = {
            "sent_date": today.isoformat(),
            "expiry_date": (today + timedelta(days=30)).isoformat()
        }
        
        # Se abbiamo una timeline, aggiungi info utili
        if ai_data.get('timeline_days'):
            # Data di fine progetto stimata (da data di possibile accettazione)
            start_date = today + timedelta(days=35)  # 5 giorni dopo scadenza per negoziazione
            end_date = start_date + timedelta(days=ai_data['timeline_days'])
            
            suggested_dates.update({
                "estimated_start_date": start_date.isoformat(),
                "estimated_end_date": end_date.isoformat()
            })

        return api_response(
            data={
                "generated_proposal": {
                    "title": ai_data.get("title", ""),
                    "description": ai_data.get("description", ""),
                    "value": ai_data.get("estimated_value"),
                    "key_deliverables": ai_data.get("key_deliverables", []),
                    "value_proposition": ai_data.get("value_proposition", []),
                    "methodology": ai_data.get("methodology", ""),
                    "timeline_days": ai_data.get("timeline_days")
                },
                "suggested_dates": suggested_dates,
                "client_context": client_context,
                "similar_projects_used": len(similar_projects_context)
            },
            message="Proposta generata con AI con successo"
        )

    except Exception as e:
        return handle_api_error(e)


@api_proposals.route('/<int:proposal_id>/convert-to-project', methods=['POST'])
@csrf.exempt
@login_required
def convert_proposal_to_project(proposal_id):
    """Converte una proposta accettata in un progetto"""
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, 'manage_proposals'):
            return api_response(False, 'Non hai i permessi per convertire proposte', status_code=403)

        # Recupera la proposta
        proposal = Proposal.query.get_or_404(proposal_id)
        
        # Controllo permessi sulla proposta specifica
        if not user_has_permission(current_user.role, 'view_all_proposals'):
            if proposal.created_by != current_user.id:
                return api_response(False, 'Non hai i permessi per convertire questa proposta', status_code=403)

        # Verifica che la proposta sia accettata
        if proposal.status != 'accepted':
            return api_response(
                False,
                f'Solo le proposte accettate possono essere convertite in progetti. Stato attuale: {proposal.status}',
                status_code=400
            )

        # Verifica che non sia già stata convertita
        existing_project = Project.query.filter(
            Project.name == proposal.title,
            Project.client_id == proposal.client_id
        ).first()
        
        if existing_project:
            return api_response(
                False,
                f'Esiste già un progetto con lo stesso nome per questo cliente: {existing_project.name}',
                status_code=409
            )

        # Dati della richiesta
        data = request.get_json() or {}
        
        # Calcola date progetto
        from datetime import timedelta
        today = date.today()
        
        # Data inizio: data odierna o quella specificata
        start_date = today
        if data.get('start_date'):
            try:
                start_date = datetime.strptime(data['start_date'], '%Y-%m-%d').date()
            except ValueError:
                return api_response(
                    False,
                    'Formato start_date non valido. Utilizzare YYYY-MM-DD',
                    status_code=400
                )

        # Data fine: calcolata da timeline o specificata
        end_date = None
        if data.get('end_date'):
            try:
                end_date = datetime.strptime(data['end_date'], '%Y-%m-%d').date()
            except ValueError:
                return api_response(
                    False,
                    'Formato end_date non valido. Utilizzare YYYY-MM-DD',
                    status_code=400
                )
        elif data.get('timeline_days'):
            try:
                timeline_days = int(data['timeline_days'])
                end_date = start_date + timedelta(days=timeline_days)
            except (ValueError, TypeError):
                return api_response(
                    False,
                    'Timeline days deve essere un numero intero',
                    status_code=400
                )

        # Crea il nuovo progetto
        project = Project(
            name=proposal.title,
            description=proposal.description,
            client_id=proposal.client_id,
            budget=proposal.value,
            start_date=start_date,
            end_date=end_date,
            status='planning',
            project_type=data.get('project_type', 'service'),
            is_billable=True,
            expenses=0.0,
            markup_percentage=data.get('markup_percentage', 0.0),
            client_daily_rate=data.get('client_daily_rate')
        )

        db.session.add(project)
        db.session.flush()  # Per ottenere l'ID del progetto

        # Log della conversione
        print(f"Converting proposal {proposal_id} ({proposal.title}) to project {project.id}")

        db.session.commit()

        return api_response(
            data={
                'project': {
                    'id': project.id,
                    'name': project.name,
                    'description': project.description,
                    'client_id': project.client_id,
                    'client': {
                        'id': project.client.id,
                        'name': project.client.name
                    } if project.client else None,
                    'budget': project.budget,
                    'start_date': project.start_date.isoformat() if project.start_date else None,
                    'end_date': project.end_date.isoformat() if project.end_date else None,
                    'status': project.status,
                    'project_type': project.project_type,
                    'created_at': project.created_at.isoformat()
                },
                'original_proposal': {
                    'id': proposal.id,
                    'title': proposal.title,
                    'status': proposal.status
                }
            },
            message=f'Proposta "{proposal.title}" convertita con successo in progetto'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)


@api_proposals.route('/<int:proposal_id>/create-contract', methods=['POST'])
@csrf.exempt
@login_required
def create_contract_from_proposal(proposal_id):
    """Crea un contratto da una proposta accettata"""
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, 'manage_proposals'):
            return api_response(False, 'Non hai i permessi per creare contratti da proposte', status_code=403)

        # Recupera la proposta
        proposal = Proposal.query.get_or_404(proposal_id)
        
        # Controllo permessi sulla proposta specifica
        if not user_has_permission(current_user.role, 'view_all_proposals'):
            if proposal.created_by != current_user.id:
                return api_response(False, 'Non hai i permessi per creare un contratto da questa proposta', status_code=403)

        # Verifica che la proposta sia accettata
        if proposal.status != 'accepted':
            return api_response(
                False,
                f'Solo le proposte accettate possono essere convertite in contratti. Stato attuale: {proposal.status}',
                status_code=400
            )

        # Verifica che non esista già un contratto per questa proposta
        existing_contract = Contract.query.filter(
            Contract.title == proposal.title,
            Contract.client_id == proposal.client_id
        ).first()
        
        if existing_contract:
            return api_response(
                False,
                f'Esiste già un contratto con lo stesso titolo per questo cliente: {existing_contract.title}',
                status_code=409
            )

        # Dati della richiesta
        data = request.get_json() or {}
        
        # Validazione tipo contratto
        contract_type = data.get('contract_type')
        if not contract_type:
            return api_response(False, 'Tipo contratto obbligatorio', status_code=400)
        
        valid_types = ['hourly', 'fixed', 'retainer', 'milestone', 'subscription']
        if contract_type not in valid_types:
            return api_response(
                False,
                f'Tipo contratto non valido. Valori accettati: {", ".join(valid_types)}',
                status_code=400
            )

        # Calcola date contratto
        from datetime import timedelta
        today = date.today()
        
        # Data inizio: data odierna o quella specificata
        start_date = today
        if data.get('start_date'):
            try:
                start_date = datetime.strptime(data['start_date'], '%Y-%m-%d').date()
            except ValueError:
                return api_response(
                    False,
                    'Formato start_date non valido. Utilizzare YYYY-MM-DD',
                    status_code=400
                )

        # Data fine: opzionale
        end_date = None
        if data.get('end_date'):
            try:
                end_date = datetime.strptime(data['end_date'], '%Y-%m-%d').date()
                if end_date <= start_date:
                    return api_response(
                        False,
                        'La data fine deve essere successiva alla data inizio',
                        status_code=400
                    )
            except ValueError:
                return api_response(
                    False,
                    'Formato end_date non valido. Utilizzare YYYY-MM-DD',
                    status_code=400
                )

        # Genera numero contratto automatico se non fornito
        contract_number = data.get('contract_number')
        if not contract_number:
            year = today.year
            # Conta contratti esistenti per l'anno corrente
            contracts_count = Contract.query.filter(
                Contract.contract_number.like(f'{year}-%')
            ).count()
            contract_number = f'{year}-{contracts_count + 1:04d}'

        # Crea il nuovo contratto
        contract = Contract(
            title=proposal.title,
            description=proposal.description,
            client_id=proposal.client_id,
            contract_type=contract_type,
            contract_number=contract_number,
            status='active',
            start_date=start_date,
            end_date=end_date
        )

        # Aggiungi campi specifici per tipo contratto
        if contract_type == 'hourly':
            hourly_rate = data.get('hourly_rate')
            if not hourly_rate or hourly_rate <= 0:
                return api_response(False, 'Tariffa oraria obbligatoria per contratti orari', status_code=400)
            contract.hourly_rate = float(hourly_rate)
            contract.budget_hours = float(data.get('budget_hours', 0)) if data.get('budget_hours') else None
            
        elif contract_type == 'fixed':
            budget_amount = data.get('budget_amount')
            if not budget_amount or budget_amount <= 0:
                return api_response(False, 'Importo fisso obbligatorio per contratti a prezzo fisso', status_code=400)
            contract.total_budget = float(budget_amount)
            
        elif contract_type == 'retainer':
            retainer_amount = data.get('retainer_amount')
            retainer_frequency = data.get('retainer_frequency')
            if not retainer_amount or retainer_amount <= 0:
                return api_response(False, 'Importo retainer obbligatorio', status_code=400)
            if not retainer_frequency:
                return api_response(False, 'Frequenza retainer obbligatoria', status_code=400)
            contract.retainer_amount = float(retainer_amount)
            contract.retainer_frequency = retainer_frequency
            
        elif contract_type == 'milestone':
            milestone_amount = data.get('milestone_amount')
            milestone_count = data.get('milestone_count')
            if not milestone_amount or milestone_amount <= 0:
                return api_response(False, 'Importo per milestone obbligatorio', status_code=400)
            if not milestone_count or milestone_count <= 0:
                return api_response(False, 'Numero milestone obbligatorio', status_code=400)
            contract.milestone_amount = float(milestone_amount)
            contract.milestone_count = int(milestone_count)
            
        elif contract_type == 'subscription':
            subscription_amount = data.get('subscription_amount')
            subscription_frequency = data.get('subscription_frequency')
            if not subscription_amount or subscription_amount <= 0:
                return api_response(False, 'Importo abbonamento obbligatorio', status_code=400)
            if not subscription_frequency:
                return api_response(False, 'Frequenza abbonamento obbligatoria', status_code=400)
            contract.subscription_amount = float(subscription_amount)
            contract.subscription_frequency = subscription_frequency

        db.session.add(contract)
        db.session.flush()  # Per ottenere l'ID del contratto

        # Log della creazione
        print(f"Creating contract {contract.id} ({contract.title}) from proposal {proposal_id}")

        db.session.commit()

        return api_response(
            data={
                'contract': {
                    'id': contract.id,
                    'title': contract.title,
                    'description': contract.description,
                    'client_id': contract.client_id,
                    'client': {
                        'id': contract.client.id,
                        'name': contract.client.name
                    } if contract.client else None,
                    'contract_type': contract.contract_type,
                    'contract_number': contract.contract_number,
                    'status': contract.status,
                    'start_date': contract.start_date.isoformat() if contract.start_date else None,
                    'end_date': contract.end_date.isoformat() if contract.end_date else None,
                    'hourly_rate': contract.hourly_rate,
                    'total_budget': contract.total_budget,
                    'retainer_amount': contract.retainer_amount,
                    'retainer_frequency': contract.retainer_frequency,
                    'milestone_amount': contract.milestone_amount,
                    'milestone_count': contract.milestone_count,
                    'subscription_amount': contract.subscription_amount,
                    'subscription_frequency': contract.subscription_frequency,
                    'created_at': contract.created_at.isoformat()
                },
                'original_proposal': {
                    'id': proposal.id,
                    'title': proposal.title,
                    'status': proposal.status
                }
            },
            message=f'Contratto "{contract.title}" creato con successo dalla proposta'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)
