"""
API RESTful per la gestione delle certificazioni aziendali.
"""
from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from sqlalchemy import desc, and_
from datetime import datetime, timedelta, date
import json
import os
from models import (
    CertificationStandard, CompanyCertification, CertificationAudit,
    ReadinessTask, CertificationDocument, User, Project
)
from utils.api_utils import (
    api_response, handle_api_error, get_pagination_params,
    format_pagination, api_permission_required
)
from utils.permissions import (
    PERMISSION_VIEW_ALL_PROJECTS, PERMISSION_EDIT_PROJECT,
    PERMISSION_DELETE_PROJECT, PERMISSION_VIEW_COMPLIANCE,
    ROLE_ADMIN, ROLE_MANAGER
)
from extensions import db
from services.ai import (
    analyze_platform_compliance, generate_certification_insights
)

# Crea il blueprint per le API delle certificazioni
api_certifications = Blueprint('api_certifications', __name__)

def load_standards_catalog():
    """
    Carica il catalogo degli standard da file JSON di configurazione.
    """
    try:
        # Path corretto verso backend/config
        catalog_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'certifications_catalog.json')
        catalog_path = os.path.abspath(catalog_path)
        if not os.path.exists(catalog_path):
            current_app.logger.warning(f"Certification catalog not found at {catalog_path}")
            return {}
        
        with open(catalog_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        current_app.logger.error(f"Error loading standards catalog: {str(e)}")
        return {}

def sync_standards_catalog():
    """
    Sincronizza il catalogo JSON con il database degli standard.
    """
    try:
        catalog = load_standards_catalog()
        
        for category, standards in catalog.items():
            for code, standard_data in standards.items():
                existing_standard = db.session.get(CertificationStandard, code)
                
                if existing_standard:
                    # Aggiorna standard esistente
                    existing_standard.name = standard_data.get('name')
                    existing_standard.version = standard_data.get('version')
                    existing_standard.category = standard_data.get('category')
                    existing_standard.description = standard_data.get('description')
                    existing_standard.requirements = standard_data.get('requirements', [])
                    existing_standard.preparatory_tasks = standard_data.get('preparatory_tasks', [])
                    existing_standard.documentation_required = standard_data.get('documentation_required', [])
                else:
                    # Crea nuovo standard
                    cost_data = standard_data.get('estimated_cost', {})
                    new_standard = CertificationStandard(
                        code=code,
                        name=standard_data.get('name'),
                        version=standard_data.get('version'),
                        category=standard_data.get('category'),
                        industry_sector=', '.join(standard_data.get('industry_sectors', [])),
                        typical_validity_years=standard_data.get('typical_validity_years', 3),
                        renewal_notice_months=standard_data.get('renewal_notice_months', 6),
                        audit_frequency_months=standard_data.get('audit_frequency_months', 12),
                        estimated_cost_min=cost_data.get('min'),
                        estimated_cost_max=cost_data.get('max'),
                        currency=cost_data.get('currency', 'EUR'),
                        requirements=standard_data.get('requirements', []),
                        preparatory_tasks=standard_data.get('preparatory_tasks', []),
                        documentation_required=standard_data.get('documentation_required', []),
                        description=standard_data.get('description'),
                        issuing_body=standard_data.get('issuing_body'),
                        website_url=standard_data.get('website_url')
                    )
                    db.session.add(new_standard)
        
        db.session.commit()
        return True
    except Exception as e:
        current_app.logger.error(f"Error syncing standards catalog: {str(e)}")
        db.session.rollback()
        return False

def calculate_compliance_metrics():
    """
    Calcola le metriche di compliance aggregate.
    """
    try:
        active_certs = CompanyCertification.query.filter_by(status='active').all()
        
        total_count = len(active_certs)
        expiring_soon_count = len([c for c in active_certs if c.is_expiring_soon])
        total_annual_cost = sum(c.annual_maintenance_cost or 0 for c in active_certs)
        average_health_score = sum(c.health_score or 0 for c in active_certs) / total_count if total_count > 0 else 0
        
        return {
            'total_certifications': total_count,
            'active_certifications': total_count,
            'expiring_soon': expiring_soon_count,
            'total_annual_cost': round(total_annual_cost, 2),
            'average_health_score': round(average_health_score, 1),
            'compliance_score': round(average_health_score, 1)  # Simplified compliance score
        }
    except Exception as e:
        current_app.logger.error(f"Error calculating compliance metrics: {str(e)}")
        return {
            'total_certifications': 0,
            'active_certifications': 0,
            'expiring_soon': 0,
            'total_annual_cost': 0,
            'average_health_score': 0,
            'compliance_score': 0
        }

def assess_compliance_risks():
    """
    Valuta i rischi di compliance per tutte le certificazioni.
    """
    try:
        risks = []
        certifications = CompanyCertification.query.filter_by(status='active').all()
        
        for cert in certifications:
            risk_level = 'low'
            risk_factors = []
            
            # Calcola giorni alla scadenza
            if cert.days_to_expiry is not None:
                if cert.days_to_expiry <= 90:
                    risk_level = 'high'
                    risk_factors.append(f"Scade tra {cert.days_to_expiry} giorni")
                elif cert.days_to_expiry <= 180:
                    risk_level = 'medium'
                    risk_factors.append(f"Scade tra {cert.days_to_expiry} giorni")
            
            # Valuta health score
            if cert.health_score < 70:
                risk_level = 'high' if risk_level != 'high' else risk_level
                risk_factors.append(f"Health score basso: {cert.health_score}%")
            elif cert.health_score < 80:
                risk_level = 'medium' if risk_level == 'low' else risk_level
                risk_factors.append(f"Health score medio: {cert.health_score}%")
            
            # Aggiungi solo se ci sono rischi
            if risk_level != 'low':
                risks.append({
                    'certification_name': cert.standard.name if cert.standard else cert.standard_code,
                    'certification_id': cert.id,
                    'risk_level': risk_level,
                    'risk_factors': risk_factors,
                    'days_to_expiry': cert.days_to_expiry,
                    'health_score': cert.health_score
                })
        
        # Ordina per livello di rischio
        risk_priority = {'high': 3, 'medium': 2, 'low': 1}
        risks.sort(key=lambda x: risk_priority.get(x['risk_level'], 0), reverse=True)
        
        return risks
    except Exception as e:
        current_app.logger.error(f"Error assessing compliance risks: {str(e)}")
        return []

@api_certifications.route('/sync-catalog', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMPLIANCE)
def sync_catalog():
    """
    Sincronizza il catalogo degli standard dal file JSON.
    ---
    tags:
      - certifications
    """
    try:
        success = sync_standards_catalog()
        if success:
            return api_response(
                message="Catalogo standard sincronizzato con successo",
                status_code=200
            )
        else:
            return api_response(
                message="Errore durante la sincronizzazione del catalogo",
                status_code=500
            )
    except Exception as e:
        return handle_api_error(e, "Errore durante la sincronizzazione del catalogo")

@api_certifications.route('/overview', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMPLIANCE)
def get_overview():
    """
    Ottiene una panoramica completa delle certificazioni aziendali.
    ---
    tags:
      - certifications
    """
    try:
        current_app.logger.info(f"🔍 [Certifications API] Overview requested by user {current_user.id}")
        current_app.logger.info(f"🔍 [Certifications API] User role: {current_user.role}")
        current_app.logger.info(f"🔍 [Certifications API] Starting overview calculation...")
        # Carica metriche di compliance
        current_app.logger.info(f"📊 [Certifications API] Calculating compliance metrics...")
        metrics = calculate_compliance_metrics()
        current_app.logger.info(f"📊 [Certifications API] Metrics calculated: {metrics}")
        
        # Carica certificazioni attive
        current_app.logger.info(f"📊 [Certifications API] Loading active certifications...")
        active_certs = CompanyCertification.query.filter_by(status='active').all()
        current_app.logger.info(f"📊 [Certifications API] Found {len(active_certs)} active certifications")
        certifications_data = []
        
        for cert in active_certs:
            cert_data = {
                'id': cert.id,
                'standard_code': cert.standard_code,
                'standard_name': cert.standard.name if cert.standard else cert.standard_code,
                'certificate_number': cert.certificate_number,
                'certifying_body': cert.certifying_body,
                'issue_date': cert.issue_date.isoformat() if cert.issue_date else None,
                'expiry_date': cert.expiry_date.isoformat() if cert.expiry_date else None,
                'days_to_expiry': cert.days_to_expiry,
                'is_expiring_soon': cert.is_expiring_soon,
                'status': cert.status,
                'health_score': cert.health_score,
                'readiness_score': cert.readiness_score,
                'responsible_person': {
                    'id': cert.responsible_person.id,
                    'name': f"{cert.responsible_person.first_name} {cert.responsible_person.last_name}"
                } if cert.responsible_person else None,
                'annual_cost': cert.annual_maintenance_cost,
                'project': {
                    'id': cert.project.id,
                    'name': cert.project.name,
                    'status': cert.project.status
                } if cert.project else None
            }
            certifications_data.append(cert_data)
        
        # Carica azioni imminenti (scadenze e audit)
        upcoming_actions = []
        
        # Certificazioni in scadenza
        expiring_certs = [c for c in active_certs if c.is_expiring_soon]
        for cert in expiring_certs:
            upcoming_actions.append({
                'type': 'renewal_due',
                'title': f"{cert.standard.name if cert.standard else cert.standard_code} - Rinnovo necessario",
                'description': f"Certificazione scade tra {cert.days_to_expiry} giorni",
                'due_date': cert.expiry_date.isoformat() if cert.expiry_date else None,
                'priority': 'high' if cert.days_to_expiry <= 90 else 'medium',
                'certification_id': cert.id
            })
        
        # Audit in programma
        upcoming_audits = CertificationAudit.query.filter(
            and_(
                CertificationAudit.planned_date >= date.today(),
                CertificationAudit.planned_date <= date.today() + timedelta(days=90),
                CertificationAudit.status == 'scheduled'
            )
        ).order_by(CertificationAudit.planned_date).limit(5).all()
        
        for audit in upcoming_audits:
            upcoming_actions.append({
                'type': 'audit_scheduled',
                'title': f"{audit.certification.standard.name if audit.certification.standard else audit.certification.standard_code} - {audit.audit_type.title()} audit",
                'description': f"Audit programmato con {audit.lead_auditor}",
                'due_date': audit.planned_date.isoformat(),
                'priority': 'medium',
                'certification_id': audit.certification_id,
                'audit_id': audit.id
            })
        
        # Ordina azioni per data
        upcoming_actions.sort(key=lambda x: x.get('due_date', ''))
        
        current_app.logger.info(f"✅ [Certifications API] Overview data prepared successfully")
        current_app.logger.info(f"✅ [Certifications API] Returning data with {len(certifications_data)} certifications")
        
        response_data = {
            'metrics': metrics,
            'certifications': certifications_data,
            'upcoming_actions': upcoming_actions[:10],  # Limite a 10 azioni
            'compliance_risks': assess_compliance_risks()
        }
        
        current_app.logger.info(f"✅ [Certifications API] Final response data: {response_data}")
        
        return api_response(data=response_data)
        
    except Exception as e:
        current_app.logger.error(f"💥 [Certifications API] Overview error: {str(e)}")
        current_app.logger.error(f"💥 [Certifications API] Exception type: {type(e)}")
        import traceback
        current_app.logger.error(f"💥 [Certifications API] Traceback: {traceback.format_exc()}")
        return handle_api_error(e, "Errore nel recupero della panoramica certificazioni")

@api_certifications.route('/standards/catalog', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMPLIANCE)
def get_standards_catalog():
    """
    Ottiene il catalogo degli standard disponibili.
    ---
    tags:
      - certifications
    """
    try:
        current_app.logger.info(f"🔍 [Certifications API] Catalog requested by user {current_user.id}")
        current_app.logger.info(f"🔍 [Certifications API] Loading standards catalog...")
        # Carica tutti gli standard dal database
        standards = CertificationStandard.query.filter_by(is_active=True).all()
        current_app.logger.info(f"📊 [Certifications API] Found {len(standards)} standards in database")
        
        # Se non ci sono standard in database, prova a sincronizzare dal catalog JSON
        if not standards:
            current_app.logger.info(f"📊 [Certifications API] No standards found, attempting sync...")
            sync_success = sync_standards_catalog()
            if sync_success:
                standards = CertificationStandard.query.filter_by(is_active=True).all()
                current_app.logger.info(f"📊 [Certifications API] After sync: {len(standards)} standards loaded")
            else:
                current_app.logger.error(f"❌ [Certifications API] Sync failed, loading empty catalog")
        
        # Carica certificazioni aziendali esistenti per mostrare lo stato
        company_certs = {cert.standard_code: cert for cert in CompanyCertification.query.all()}
        
        catalog_data = {}
        
        for standard in standards:
            # Determina lo stato per l'azienda
            company_cert = company_certs.get(standard.code)
            if company_cert:
                if company_cert.status == 'active':
                    if company_cert.is_expiring_soon:
                        status = 'renewal_due'
                    else:
                        status = 'active'
                elif company_cert.status == 'in_renewal':
                    status = 'renewal_in_progress'
                else:
                    status = 'expired'
            else:
                status = 'available'
            
            # Organizza per categoria
            category = standard.category
            if category not in catalog_data:
                catalog_data[category] = []
            
            standard_data = {
                'code': standard.code,
                'name': standard.name,
                'version': standard.version,
                'description': standard.description,
                'industry_sectors': standard.industry_sector.split(', ') if standard.industry_sector else [],
                'typical_validity_years': standard.typical_validity_years,
                'estimated_cost': {
                    'min': standard.estimated_cost_min,
                    'max': standard.estimated_cost_max,
                    'currency': standard.currency
                },
                'issuing_body': standard.issuing_body,
                'website_url': standard.website_url,
                'status': status,
                'company_certification': {
                    'id': company_cert.id,
                    'certificate_number': company_cert.certificate_number,
                    'expiry_date': company_cert.expiry_date.isoformat() if company_cert.expiry_date else None,
                    'health_score': company_cert.health_score,
                    'days_to_expiry': company_cert.days_to_expiry
                } if company_cert else None,
                'requirements_count': len(standard.requirements) if standard.requirements else 0,
                'tasks_count': len(standard.preparatory_tasks) if standard.preparatory_tasks else 0
            }
            
            catalog_data[category].append(standard_data)
        
        # Ordina ogni categoria per priorità e nome
        for category in catalog_data:
            catalog_data[category].sort(key=lambda x: (
                {'active': 0, 'renewal_due': 1, 'renewal_in_progress': 2, 'expired': 3, 'available': 4}[x['status']],
                x['name']
            ))
        
        return api_response(data={
            'catalog': catalog_data,
            'categories': list(catalog_data.keys())
        })
        
    except Exception as e:
        return handle_api_error(e, "Errore nel recupero del catalogo standard")

@api_certifications.route('/', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMPLIANCE)
def get_certifications():
    """
    Ottiene la lista delle certificazioni aziendali con filtri e paginazione.
    ---
    tags:
      - certifications
    """
    try:
        # Parametri di filtro
        status = request.args.get('status')
        category = request.args.get('category')
        expiring_soon = request.args.get('expiring_soon', '').lower() == 'true'
        responsible_person_id = request.args.get('responsible_person_id', type=int)
        
        # Parametri di paginazione
        page, per_page = get_pagination_params()
        
        # Costruisci query
        query = CompanyCertification.query
        
        if status:
            query = query.filter(CompanyCertification.status == status)
        
        if category:
            query = query.join(CertificationStandard).filter(CertificationStandard.category == category)
        
        if responsible_person_id:
            query = query.filter(CompanyCertification.responsible_person_id == responsible_person_id)
        
        if expiring_soon:
            # Filtra certificazioni che scadono entro i prossimi 6 mesi
            six_months_from_now = date.today() + timedelta(days=180)
            query = query.filter(CompanyCertification.expiry_date <= six_months_from_now)
        
        # Ordina per data di scadenza
        query = query.order_by(CompanyCertification.expiry_date.asc())
        
        # Paginazione
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        
        # Formatta i risultati
        certifications = []
        for cert in pagination.items:
            cert_data = {
                'id': cert.id,
                'standard_code': cert.standard_code,
                'standard_name': cert.standard.name if cert.standard else cert.standard_code,
                'certificate_number': cert.certificate_number,
                'certifying_body': cert.certifying_body,
                'issue_date': cert.issue_date.isoformat() if cert.issue_date else None,
                'expiry_date': cert.expiry_date.isoformat() if cert.expiry_date else None,
                'days_to_expiry': cert.days_to_expiry,
                'is_expiring_soon': cert.is_expiring_soon,
                'status': cert.status,
                'health_score': cert.health_score,
                'readiness_score': cert.readiness_score,
                'responsible_person': {
                    'id': cert.responsible_person.id,
                    'name': f"{cert.responsible_person.first_name} {cert.responsible_person.last_name}"
                } if cert.responsible_person else None,
                'annual_cost': cert.annual_maintenance_cost,
                'category': cert.standard.category if cert.standard else None,
                'created_at': cert.created_at.isoformat(),
                'updated_at': cert.updated_at.isoformat()
            }
            certifications.append(cert_data)
        
        return api_response(
            data=certifications,
            pagination=format_pagination(pagination)
        )
        
    except Exception as e:
        return handle_api_error(e, "Errore nel recupero delle certificazioni")

@api_certifications.route('/<int:cert_id>', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMPLIANCE)
def get_certification_detail(cert_id):
    """
    Ottiene i dettagli di una certificazione specifica.
    ---
    tags:
      - certifications
    """
    try:
        cert = CompanyCertification.query.get_or_404(cert_id)
        
        # Carica task di readiness
        readiness_tasks = ReadinessTask.query.filter_by(certification_id=cert_id).all()
        tasks_data = []
        for task in readiness_tasks:
            task_data = {
                'id': task.id,
                'task_name': task.task_name,
                'task_description': task.task_description,
                'task_category': task.task_category,
                'completion_percentage': task.completion_percentage,
                'status': task.status,
                'priority': task.priority,
                'due_date': task.due_date.isoformat() if task.due_date else None,
                'assigned_to': {
                    'id': task.assigned_to.id,
                    'name': f"{task.assigned_to.first_name} {task.assigned_to.last_name}"
                } if task.assigned_to else None,
                'estimated_hours': task.estimated_hours,
                'actual_hours': task.actual_hours
            }
            tasks_data.append(task_data)
        
        # Carica audit events
        audit_events = CertificationAudit.query.filter_by(certification_id=cert_id).order_by(CertificationAudit.planned_date.desc()).all()
        audits_data = []
        for audit in audit_events:
            audit_data = {
                'id': audit.id,
                'audit_type': audit.audit_type,
                'planned_date': audit.planned_date.isoformat(),
                'actual_date': audit.actual_date.isoformat() if audit.actual_date else None,
                'status': audit.status,
                'result': audit.result,
                'lead_auditor': audit.lead_auditor,
                'audit_cost': audit.audit_cost,
                'major_findings': audit.major_findings,
                'minor_findings': audit.minor_findings,
                'overall_score': audit.overall_score
            }
            audits_data.append(audit_data)
        
        cert_data = {
            'id': cert.id,
            'standard_code': cert.standard_code,
            'standard': {
                'code': cert.standard.code,
                'name': cert.standard.name,
                'version': cert.standard.version,
                'category': cert.standard.category,
                'description': cert.standard.description,
                'requirements': cert.standard.requirements,
                'documentation_required': cert.standard.documentation_required
            } if cert.standard else None,
            'certificate_number': cert.certificate_number,
            'certifying_body': cert.certifying_body,
            'certifying_body_contact': cert.certifying_body_contact,
            'issue_date': cert.issue_date.isoformat() if cert.issue_date else None,
            'expiry_date': cert.expiry_date.isoformat() if cert.expiry_date else None,
            'next_audit_date': cert.next_audit_date.isoformat() if cert.next_audit_date else None,
            'days_to_expiry': cert.days_to_expiry,
            'is_expiring_soon': cert.is_expiring_soon,
            'status': cert.status,
            'health_score': cert.health_score,
            'readiness_score': cert.readiness_score,
            'initial_cost': cert.initial_cost,
            'annual_maintenance_cost': cert.annual_maintenance_cost,
            'last_audit_cost': cert.last_audit_cost,
            'responsible_person': {
                'id': cert.responsible_person.id,
                'name': f"{cert.responsible_person.first_name} {cert.responsible_person.last_name}",
                'email': cert.responsible_person.email
            } if cert.responsible_person else None,
            'backup_person': {
                'id': cert.backup_person.id,
                'name': f"{cert.backup_person.first_name} {cert.backup_person.last_name}",
                'email': cert.backup_person.email
            } if cert.backup_person else None,
            'project': {
                'id': cert.project.id,
                'name': cert.project.name,
                'status': cert.project.status,
                'description': cert.project.description
            } if cert.project else None,
            'readiness_tasks': tasks_data,
            'audit_events': audits_data,
            'created_at': cert.created_at.isoformat(),
            'updated_at': cert.updated_at.isoformat()
        }
        
        return api_response(data=cert_data)
        
    except Exception as e:
        return handle_api_error(e, "Errore nel recupero dei dettagli della certificazione")

@api_certifications.route('/<int:cert_id>/projects', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMPLIANCE)
def get_certification_projects(cert_id):
    """
    Ottiene lo storico dei progetti collegati a una certificazione.
    ---
    tags:
      - certifications
    """
    try:
        # Verifica che la certificazione esista
        cert = CompanyCertification.query.get_or_404(cert_id)
        
        # Query per tutti i progetti collegati alla certificazione
        # Include progetto attuale e storico progetti passati
        projects_query = Project.query.filter_by(id=cert.project_id) if cert.project_id else Project.query.filter(False)
        
        # TODO: In futuro, quando avremo uno storico progetti per rinnovi,
        # aggiungeremo una query per progetti storici collegati
        
        projects_data = []
        
        # Progetto attuale
        if cert.project_id:
            current_project = Project.query.get(cert.project_id)
            if current_project:
                project_data = {
                    'id': current_project.id,
                    'name': current_project.name,
                    'description': current_project.description,
                    'status': current_project.status,
                    'start_date': current_project.start_date.isoformat() if current_project.start_date else None,
                    'end_date': current_project.end_date.isoformat() if current_project.end_date else None,
                    'budget': current_project.budget,
                    'completion_percentage': getattr(current_project, 'completion_percentage', 0),
                    'is_current': True,
                    'project_type': 'current',
                    'created_at': current_project.created_at.isoformat(),
                    'updated_at': current_project.updated_at.isoformat()
                }
                projects_data.append(project_data)
        
        # Statistiche
        stats = {
            'total_projects': len(projects_data),
            'active_projects': len([p for p in projects_data if p['status'] in ['active', 'in_progress']]),
            'completed_projects': len([p for p in projects_data if p['status'] == 'completed']),
            'total_budget': sum([p['budget'] or 0 for p in projects_data])
        }
        
        return api_response(data={
            'projects': projects_data,
            'stats': stats,
            'certification': {
                'id': cert.id,
                'standard_name': cert.standard.name if cert.standard else cert.standard_code,
                'status': cert.status
            }
        })
        
    except Exception as e:
        return handle_api_error(e, "Errore nel recupero dei progetti della certificazione")

@api_certifications.route('/<int:cert_id>/status', methods=['PUT'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMPLIANCE)
def update_certification_status(cert_id):
    """
    Aggiorna lo status di una certificazione.
    ---
    tags:
      - certifications
    """
    try:
        data = request.get_json()
        new_status = data.get('status')
        
        # Validazione status
        valid_statuses = ['planning', 'active', 'in_renewal', 'expired', 'suspended']
        if new_status not in valid_statuses:
            return api_response(
                success=False,
                message=f"Status non valido. Valori consentiti: {', '.join(valid_statuses)}",
                status_code=400
            )
        
        cert = CompanyCertification.query.get_or_404(cert_id)
        
        # Verifica permessi - solo admin, manager o responsabile della certificazione
        if (current_user.role not in [ROLE_ADMIN, ROLE_MANAGER] and 
            cert.responsible_person_id != current_user.id and 
            cert.created_by != current_user.id):
            return api_response(
                success=False,
                message="Non hai i permessi per modificare lo status di questa certificazione",
                status_code=403
            )
        
        old_status = cert.status
        
        # Logica di transizione status
        if new_status == 'active':
            if old_status == 'planning':
                # Planning → Active: Certificazione ottenuta
                cert.issue_date = data.get('issue_date', date.today())
                cert.certificate_number = data.get('certificate_number', cert.certificate_number)
                cert.certifying_body = data.get('certifying_body', cert.certifying_body)
                cert.health_score = data.get('health_score', 100)
                
                # Calcola data di scadenza se non fornita
                if not cert.expiry_date or data.get('expiry_date'):
                    if data.get('expiry_date'):
                        cert.expiry_date = datetime.strptime(data['expiry_date'], '%Y-%m-%d').date()
                    else:
                        # Default basato sulla validità tipica dello standard
                        validity_years = cert.standard.typical_validity_years if cert.standard else 3
                        cert.expiry_date = cert.issue_date + timedelta(days=365 * validity_years)
                
            elif old_status == 'in_renewal':
                # In Renewal → Active: Rinnovo completato
                cert.issue_date = data.get('issue_date', date.today())
                cert.certificate_number = data.get('certificate_number', cert.certificate_number)
                cert.health_score = data.get('health_score', cert.health_score or 100)
                
                if data.get('expiry_date'):
                    cert.expiry_date = datetime.strptime(data['expiry_date'], '%Y-%m-%d').date()
        
        elif new_status == 'in_renewal':
            if old_status == 'active':
                # Active → In Renewal: Processo di rinnovo avviato
                pass  # Mantiene tutti i dati esistenti
        
        elif new_status == 'expired':
            if old_status == 'active':
                # Active → Expired: Certificazione scaduta
                cert.health_score = 0
        
        elif new_status == 'suspended':
            # Qualsiasi status → Suspended
            cert.health_score = data.get('health_score', 50)  # Score ridotto
        
        # Aggiorna lo status
        cert.status = new_status
        
        # Aggiorna costi se forniti
        if data.get('initial_cost'):
            cert.initial_cost = data['initial_cost']
        if data.get('annual_maintenance_cost'):
            cert.annual_maintenance_cost = data['annual_maintenance_cost']
        
        # Log dell'operazione
        current_app.logger.info(f"📊 [Certifications API] Status updated for cert {cert_id}: {old_status} → {new_status} by user {current_user.id}")
        
        db.session.commit()
        
        # Preparare response con dati aggiornati
        response_data = {
            'id': cert.id,
            'old_status': old_status,
            'new_status': new_status,
            'issue_date': cert.issue_date.isoformat() if cert.issue_date else None,
            'expiry_date': cert.expiry_date.isoformat() if cert.expiry_date else None,
            'certificate_number': cert.certificate_number,
            'health_score': cert.health_score
        }
        
        return api_response(
            data=response_data,
            message=f"Status certificazione aggiornato da '{old_status}' a '{new_status}'",
            status_code=200
        )
        
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e, "Errore nell'aggiornamento dello status della certificazione")

@api_certifications.route('/', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMPLIANCE)
def create_certification():
    """
    Crea una nuova certificazione aziendale.
    ---
    tags:
      - certifications
    """
    try:
        data = request.get_json()
        
        # Validazione dati richiesti
        required_fields = ['standard_code', 'certifying_body', 'issue_date', 'expiry_date', 'responsible_person_id']
        for field in required_fields:
            if field not in data:
                return api_response(
                    message=f"Campo obbligatorio mancante: {field}",
                    status_code=400
                )
        
        # Verifica che lo standard esista
        standard = CertificationStandard.query.get(data['standard_code'])
        if not standard:
            return api_response(
                message="Standard di certificazione non trovato",
                status_code=404
            )
        
        # Verifica che la persona responsabile esista
        responsible_person = User.query.get(data['responsible_person_id'])
        if not responsible_person:
            return api_response(
                message="Persona responsabile non trovata",
                status_code=404
            )
        
        # Crea la certificazione
        certification = CompanyCertification(
            standard_code=data['standard_code'],
            certificate_number=data.get('certificate_number'),
            certifying_body=data['certifying_body'],
            certifying_body_contact=data.get('certifying_body_contact'),
            issue_date=datetime.strptime(data['issue_date'], '%Y-%m-%d').date(),
            expiry_date=datetime.strptime(data['expiry_date'], '%Y-%m-%d').date(),
            next_audit_date=datetime.strptime(data['next_audit_date'], '%Y-%m-%d').date() if data.get('next_audit_date') else None,
            status=data.get('status', 'active'),
            health_score=data.get('health_score', 100),
            initial_cost=data.get('initial_cost'),
            annual_maintenance_cost=data.get('annual_maintenance_cost'),
            responsible_person_id=data['responsible_person_id'],
            backup_person_id=data.get('backup_person_id'),
            created_by=current_user.id
        )
        
        db.session.add(certification)
        db.session.commit()
        
        # Crea task di readiness automatici dal catalogo
        if standard.preparatory_tasks:
            for task_template in standard.preparatory_tasks:
                readiness_task = ReadinessTask(
                    certification_id=certification.id,
                    task_name=task_template.get('name'),
                    task_description=task_template.get('description'),
                    task_category=task_template.get('category', 'process'),
                    requirement_reference=task_template.get('requirement_reference'),
                    priority=task_template.get('priority', 'medium'),
                    estimated_hours=task_template.get('estimated_hours'),
                    created_by=current_user.id
                )
                db.session.add(readiness_task)
        
        db.session.commit()
        
        return api_response(
            data={'id': certification.id},
            message="Certificazione creata con successo",
            status_code=201
        )
        
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e, "Errore nella creazione della certificazione")

@api_certifications.route('/platform-compliance/<standard_code>', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMPLIANCE)
def get_platform_compliance(standard_code):
    """
    Analizza la compliance della piattaforma DatPortal per una specifica certificazione.
    """
    try:
        current_app.logger.info(f"🔍 [Certifications API] Platform compliance analysis requested for {standard_code} by user {current_user.id}")
        
        # Analizza compliance usando AI service con dati reali aggregati
        # Il servizio AI raccoglierà automaticamente i dati reali se non forniti
        current_app.logger.info(f"📊 [Certifications API] Using real aggregated data for compliance analysis")
        analysis_result = analyze_platform_compliance(standard_code)
        
        return api_response(data=analysis_result)
        
    except Exception as e:
        return handle_api_error(e, "Errore nell'analisi platform compliance")

@api_certifications.route('/ai-insights', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMPLIANCE)
def get_certification_ai_insights():
    """
    Genera insights AI per la creazione di una nuova certificazione.
    """
    try:
        data = request.get_json()
        standard_code = data.get('standard_code')
        
        if not standard_code:
            return api_response(
                success=False,
                message="standard_code è richiesto",
                status_code=400
            )
        
        current_app.logger.info(f"🤖 [Certifications API] AI insights requested for {standard_code} by user {current_user.id}")
        
        # TODO: Raccogliere dati reali del profilo aziendale
        # Per ora utilizziamo dati mock
        mock_company_profile = {
            "industry": "Technology Services",
            "employees_count": 25,
            "revenue": "2M EUR",
            "main_services": ["Software Development", "AI Consulting", "Project Management", "Digital Innovation"]
        }
        
        mock_platform_usage = {
            "project_management": {"adoption_score": 85},
            "personnel_management": {"adoption_score": 90}, 
            "time_tracking": {"adoption_score": 75},
            "document_management": {"adoption_score": 60},
            "crm_system": {"adoption_score": 70},
            "financial_management": {"adoption_score": 80},
            "communication_platform": {"adoption_score": 65},
            "analytics_dashboard": {"adoption_score": 55},
            "ai_integration": {"adoption_score": 40},
            "access_control": {"adoption_score": 95}
        }
        
        # Genera insights usando AI
        insights_result = generate_certification_insights(
            standard_code, 
            mock_company_profile, 
            mock_platform_usage
        )
        
        return api_response(data=insights_result)
        
    except Exception as e:
        return handle_api_error(e, "Errore nella generazione degli insights AI")

@api_certifications.route('/available-projects', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMPLIANCE)
def get_available_projects():
    """
    Ottiene la lista dei progetti disponibili per collegamento a certificazioni.
    """
    try:
        # Carica progetti attivi
        projects = Project.query.filter(
            Project.status.in_(['planning', 'active', 'on_hold'])
        ).order_by(Project.name).all()
        
        projects_data = []
        for project in projects:
            projects_data.append({
                'id': project.id,
                'name': project.name,
                'status': project.status,
                'description': project.description,
                'start_date': project.start_date.isoformat() if project.start_date else None,
                'certifications_count': len(project.certifications) if hasattr(project, 'certifications') else 0
            })
        
        return api_response(data=projects_data)
        
    except Exception as e:
        return handle_api_error(e, "Errore nel recupero dei progetti disponibili")

@api_certifications.route('/create-certification', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMPLIANCE)
def create_certification_with_ai():
    """
    Crea una nuova certificazione aziendale con supporto AI.
    """
    try:
        data = request.get_json()
        
        # Validazione dati richiesti
        required_fields = ['standard_code']
        for field in required_fields:
            if not data.get(field):
                return api_response(
                    success=False,
                    message=f"Campo {field} è richiesto",
                    status_code=400
                )
        
        standard_code = data.get('standard_code')
        
        # Verifica che lo standard esista
        standard = CertificationStandard.query.get(standard_code)
        if not standard:
            return api_response(
                success=False,
                message="Standard di certificazione non trovato",
                status_code=404
            )
        
        current_app.logger.info(f"📋 [Certifications API] Creating certification for {standard_code} by user {current_user.id}")
        
        # Gestione progetto
        project_id = None
        project_action = data.get('project_action', 'none')  # 'none', 'existing', 'create'
        
        if project_action == 'existing':
            # Collega a progetto esistente
            project_id = data.get('existing_project_id')
            if project_id:
                project = Project.query.get(project_id)
                if not project:
                    return api_response(
                        success=False,
                        message="Progetto selezionato non trovato",
                        status_code=404
                    )
        
        elif project_action == 'create':
            # Crea nuovo progetto
            project_name = data.get('project_name')
            if not project_name:
                return api_response(
                    success=False,
                    message="Nome progetto è richiesto per creare un nuovo progetto",
                    status_code=400
                )
            
            new_project = Project(
                name=project_name,
                description=f"Progetto per certificazione {standard.name}",
                status='planning',
                start_date=datetime.utcnow().date(),
                project_manager_id=data.get('team_lead_id', current_user.id),
                created_by=current_user.id
            )
            db.session.add(new_project)
            db.session.flush()  # Per ottenere l'ID
            project_id = new_project.id
        
        # Crea la certificazione aziendale
        certification = CompanyCertification(
            standard_code=standard_code,
            project_id=project_id,
            certifying_body=data.get('certifying_body', 'Da definire'),
            issue_date=datetime.utcnow().date(),
            expiry_date=(datetime.utcnow() + timedelta(days=365*3)).date(),  # Default 3 anni
            status='planning',
            health_score=data.get('initial_score', 0),
            initial_cost=data.get('estimated_budget'),
            responsible_person_id=data.get('team_lead_id', current_user.id),
            created_by=current_user.id
        )
        
        db.session.add(certification)
        db.session.flush()  # Per ottenere l'ID
        
        # Se sono fornite AI insights, crea task automaticamente
        ai_insights = data.get('ai_insights')
        if ai_insights and ai_insights.get('timeline_prediction', {}).get('phases'):
            phases = ai_insights['timeline_prediction']['phases']
            for phase in phases:
                readiness_task = ReadinessTask(
                    certification_id=certification.id,
                    task_name=phase.get('name'),
                    task_description=phase.get('description'),
                    task_category='planning',
                    priority='medium',
                    estimated_hours=phase.get('duration_weeks', 1) * 40,  # Converti settimane in ore
                    status='pending',
                    created_by=current_user.id
                )
                db.session.add(readiness_task)
        
        db.session.commit()
        
        return api_response(
            data={
                'id': certification.id,
                'standard_code': standard_code,
                'status': certification.status,
                'project': {
                    'id': certification.project.id,
                    'name': certification.project.name,
                    'status': certification.project.status
                } if certification.project else None
            },
            message="Certificazione creata con successo con insights AI",
            status_code=201
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore nella creazione della certificazione AI: {str(e)}")
        return handle_api_error(e)

@api_certifications.route('/<int:cert_id>', methods=['DELETE'])
@login_required
@api_permission_required(PERMISSION_DELETE_PROJECT)
def delete_certification(cert_id):
    """
    Elimina (soft delete) una certificazione aziendale.
    ---
    tags:
      - certifications
    """
    try:
        current_app.logger.info(f"🗑️ [Certifications API] Delete request for certification {cert_id} by user {current_user.id}")
        
        cert = CompanyCertification.query.get_or_404(cert_id)
        
        # Verifica permessi - solo admin, manager o responsabile della certificazione
        if (current_user.role not in [ROLE_ADMIN, ROLE_MANAGER] and 
            cert.responsible_person_id != current_user.id and 
            cert.created_by != current_user.id):
            current_app.logger.warning(f"⚠️ [Certifications API] Unauthorized delete attempt for cert {cert_id} by user {current_user.id}")
            return api_response(
                success=False,
                message="Non hai i permessi per eliminare questa certificazione",
                status_code=403
            )
        
        # Soft delete - cambia solo lo status
        cert.status = 'deleted'
        cert.updated_at = datetime.utcnow()
        
        # Log dell'operazione per audit trail
        current_app.logger.info(f"🗑️ [Certifications API] Soft deleting certification {cert_id} ({cert.standard_code}) by user {current_user.id}")
        
        db.session.commit()
        
        return api_response(
            data={'id': cert_id, 'status': 'deleted'},
            message="Certificazione eliminata con successo",
            status_code=200
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"💥 [Certifications API] Error deleting certification {cert_id}: {str(e)}")
        return handle_api_error(e, "Errore nell'eliminazione della certificazione")

@api_certifications.route('/<int:cert_id>', methods=['PUT'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMPLIANCE)
def update_certification(cert_id):
    """
    Aggiorna una certificazione aziendale.
    ---
    tags:
      - certifications
    """
    try:
        current_app.logger.info(f"✏️ [Certifications API] Update request for certification {cert_id} by user {current_user.id}")
        
        cert = CompanyCertification.query.get_or_404(cert_id)
        data = request.get_json()
        
        # Verifica permessi
        if (current_user.role not in [ROLE_ADMIN, ROLE_MANAGER] and 
            cert.responsible_person_id != current_user.id and 
            cert.created_by != current_user.id):
            return api_response(
                success=False,
                message="Non hai i permessi per modificare questa certificazione",
                status_code=403
            )
        
        # Aggiorna i campi consentiti
        updatable_fields = [
            'certifying_body', 'certificate_number', 'issue_date', 'expiry_date',
            'status', 'health_score', 'readiness_score', 'responsible_person_id',
            'annual_maintenance_cost', 'initial_cost', 'notes'
        ]
        
        for field in updatable_fields:
            if field in data:
                if field in ['issue_date', 'expiry_date'] and data[field]:
                    # Converte le date da string ISO
                    setattr(cert, field, datetime.fromisoformat(data[field].replace('Z', '+00:00')).date())
                else:
                    setattr(cert, field, data[field])
        
        cert.updated_at = datetime.utcnow()
        
        current_app.logger.info(f"✏️ [Certifications API] Updated certification {cert_id} fields: {list(data.keys())}")
        
        db.session.commit()
        
        return api_response(
            data={
                'id': cert.id,
                'standard_code': cert.standard_code,
                'status': cert.status,
                'updated_at': cert.updated_at.isoformat()
            },
            message="Certificazione aggiornata con successo",
            status_code=200
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"💥 [Certifications API] Error updating certification {cert_id}: {str(e)}")
        return handle_api_error(e, "Errore nell'aggiornamento della certificazione")

# Inizializza il catalogo standard al primo caricamento del modulo
try:
    # Skip sync durante i test
    if not current_app.config.get('DISABLE_STARTUP_SYNC', False):
        sync_standards_catalog()
except Exception as e:
    current_app.logger.warning(f"Could not sync standards catalog on module load: {str(e)}")