"""
API endpoints for feature flags management.
Provides REST API for system feature toggle administration.
"""

from datetime import datetime
from flask import Blueprint, request, current_app
from flask_login import current_user
from sqlalchemy import or_

from extensions import db
from models_split.settings import FeatureFlag
from utils.api_utils import (
    api_response, get_pagination_params, api_permission_required,
    handle_api_error, api_login_required
)
from utils.permissions import PERMISSION_MANAGE_USERS

# Create blueprint
api_feature_flags = Blueprint('api_feature_flags', __name__)

@api_feature_flags.route('', methods=['GET'])
def get_feature_flags():
    """
    Get list of feature flags.
    Pubblic endpoint for frontend to check enabled features.
    
    Query Parameters:
    - enabled_only: Return only enabled flags (default: false)
    """
    try:
        # Get query parameters
        enabled_only = request.args.get('enabled_only', 'false').lower() == 'true'
        
        # Build query
        query = FeatureFlag.query
        
        if enabled_only:
            query = query.filter(FeatureFlag.is_enabled == True)
            
        # Get all feature flags (no pagination for frontend consumption)
        feature_flags = query.order_by(FeatureFlag.display_name).all()
        
        # Convert to dict format
        flags_dict = {}
        for flag in feature_flags:
            flags_dict[flag.feature_key] = flag.is_enabled
            
        current_app.logger.info(f"Feature flags requested (public), enabled_only={enabled_only}")
        
        return api_response({
            'flags': flags_dict,
            'details': [flag.to_dict() for flag in feature_flags] if not enabled_only else None,
            'total': len(feature_flags)
        })
        
    except Exception as e:
        return handle_api_error(e, 'Errore nel recupero feature flags')

@api_feature_flags.route('/admin', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_MANAGE_USERS)  
def get_admin_feature_flags():
    """
    Get detailed feature flags list for admin management.
    
    Query Parameters:
    - page: Page number (default: 1)
    - per_page: Items per page (default: 50)
    - search: Search in feature_key, display_name, description
    """
    try:
        # Get pagination and search parameters
        page, per_page = get_pagination_params()
        search = request.args.get('search', '').strip()
        
        # Build query
        query = FeatureFlag.query
        
        # Apply search filter
        if search:
            search_filter = or_(
                FeatureFlag.feature_key.ilike(f'%{search}%'),
                FeatureFlag.display_name.ilike(f'%{search}%'),
                FeatureFlag.description.ilike(f'%{search}%')
            )
            query = query.filter(search_filter)
            
        # Execute paginated query
        pagination = query.order_by(
            FeatureFlag.display_name
        ).paginate(
            page=page, 
            per_page=per_page,
            error_out=False
        )
        
        current_app.logger.info(f"Admin feature flags requested by {current_user.username}, page={page}")
        
        return api_response({
            'feature_flags': [flag.to_dict() for flag in pagination.items],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            },
            'search': search
        })
        
    except Exception as e:
        return handle_api_error(e, 'Errore nel recupero feature flags admin')

@api_feature_flags.route('/<int:flag_id>', methods=['PUT'])
@api_login_required
@api_permission_required(PERMISSION_MANAGE_USERS)
def update_feature_flag(flag_id):
    """
    Update a feature flag (primarily for toggling enabled state).
    
    Body Parameters:
    - is_enabled: Boolean to enable/disable feature
    - display_name: Optional display name update
    - description: Optional description update
    """
    try:
        # Get feature flag
        flag = FeatureFlag.query.get_or_404(flag_id)
        
        # Get request data
        data = request.get_json()
        if not data:
            return api_response({'error': 'Dati richiesti nel body della richiesta'}, 400)
            
        # Update fields
        if 'is_enabled' in data:
            old_status = flag.is_enabled
            flag.is_enabled = bool(data['is_enabled'])
            
            # Log the change
            status_change = f"{'enabled' if flag.is_enabled else 'disabled'}"
            current_app.logger.info(
                f"Feature flag {flag.feature_key} {status_change} by {current_user.username}"
            )
            
        if 'display_name' in data:
            flag.display_name = data['display_name'].strip()
            
        if 'description' in data:
            flag.description = data['description'].strip() if data['description'] else None
            
        # Update audit fields
        flag.updated_at = datetime.utcnow()
        flag.updated_by = current_user.id
        
        # Save changes
        db.session.commit()
        
        return api_response({
            'feature_flag': flag.to_dict(),
            'message': f'Feature flag {flag.feature_key} aggiornato con successo'
        })
        
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e, 'Errore nell\'aggiornamento feature flag')

@api_feature_flags.route('', methods=['POST'])
@api_login_required
@api_permission_required(PERMISSION_MANAGE_USERS)
def create_feature_flag():
    """
    Create a new feature flag.
    
    Body Parameters:
    - feature_key: Unique key for the feature (required)
    - display_name: Human readable name (required)
    - description: Optional description
    - is_enabled: Optional enabled state (default: true)
    """
    try:
        # Get request data
        data = request.get_json()
        if not data:
            return api_response({'error': 'Dati richiesti nel body della richiesta'}, 400)
            
        # Validate required fields
        if not data.get('feature_key') or not data.get('display_name'):
            return api_response({
                'error': 'feature_key e display_name sono obbligatori'
            }, 400)
            
        # Check if feature key already exists
        existing_flag = FeatureFlag.query.filter_by(feature_key=data['feature_key']).first()
        if existing_flag:
            return api_response({
                'error': f'Feature flag con chiave {data["feature_key"]} già esistente'
            }, 400)
            
        # Create new feature flag
        flag = FeatureFlag(
            feature_key=data['feature_key'].strip(),
            display_name=data['display_name'].strip(),
            description=data.get('description', '').strip() if data.get('description') else None,
            is_enabled=data.get('is_enabled', True),
            updated_by=current_user.id
        )
        
        # Save to database
        db.session.add(flag)
        db.session.commit()
        
        current_app.logger.info(f"New feature flag {flag.feature_key} created by {current_user.username}")
        
        return api_response({
            'feature_flag': flag.to_dict(),
            'message': f'Feature flag {flag.feature_key} creato con successo'
        }, 201)
        
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e, 'Errore nella creazione feature flag')

@api_feature_flags.route('/<int:flag_id>', methods=['DELETE'])
@api_login_required
@api_permission_required(PERMISSION_MANAGE_USERS)
def delete_feature_flag(flag_id):
    """
    Delete a feature flag.
    WARNING: This will permanently remove the feature flag.
    """
    try:
        # Get feature flag
        flag = FeatureFlag.query.get_or_404(flag_id)
        
        # Store info for logging
        feature_key = flag.feature_key
        
        # Delete from database
        db.session.delete(flag)
        db.session.commit()
        
        current_app.logger.warning(f"Feature flag {feature_key} deleted by {current_user.username}")
        
        return api_response({
            'message': f'Feature flag {feature_key} eliminato con successo'
        })
        
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e, 'Errore nell\'eliminazione feature flag')

@api_feature_flags.route('/check/<feature_key>', methods=['GET'])
@api_login_required
def check_feature_flag(feature_key):
    """
    Check if a specific feature is enabled.
    This is a convenience endpoint for frontend feature checking.
    
    Returns:
    - enabled: Boolean indicating if feature is enabled
    - exists: Boolean indicating if feature flag exists
    """
    try:
        # Use the static method from FeatureFlag model
        is_enabled = FeatureFlag.is_feature_enabled(feature_key)
        
        # Check if flag actually exists
        flag_exists = FeatureFlag.query.filter_by(feature_key=feature_key).first() is not None
        
        return api_response({
            'feature_key': feature_key,
            'enabled': is_enabled,
            'exists': flag_exists
        })
        
    except Exception as e:
        return handle_api_error(e, f'Errore nel controllo feature flag {feature_key}')