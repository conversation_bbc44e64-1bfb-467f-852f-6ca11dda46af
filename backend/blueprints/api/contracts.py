"""
API Blueprint per la gestione dei contratti clienti.
Task 3.1 + 4 - CRM/Billing Integration
"""

from flask import Blueprint, request, jsonify
from flask_login import current_user, login_required
from sqlalchemy import and_, or_, extract
from datetime import datetime, date

from models import Contract, Client, Project, User
from utils.api_utils import api_response, handle_api_error
from utils.permissions import user_has_permission
from extensions import db, csrf

api_contracts = Blueprint('api_contracts', __name__)


@api_contracts.route('/', methods=['GET'])
@login_required
def get_contracts():
    """Recupera lista contratti con filtri"""
    try:
        # Parametri filtro
        client_id = request.args.get('client_id', type=int)
        contract_type = request.args.get('type')  # fixed, hourly
        status = request.args.get('status')  # active, completed, cancelled
        start_date = request.args.get('start_date')  # YYYY-MM-DD
        end_date = request.args.get('end_date')  # YYYY-MM-DD
        search = request.args.get('search')  # Ricerca in titolo/numero
        
        # Paginazione
        page = request.args.get('page', type=int, default=1)
        per_page = request.args.get('per_page', type=int, default=50)
        
        # Query base
        query = Contract.query
        
        # Controllo permessi
        if not user_has_permission(current_user.role, 'view_all_contracts'):
            return api_response(False, 'Non hai i permessi per visualizzare i contratti', status_code=403)
        
        # Applica filtri
        if client_id:
            query = query.filter(Contract.client_id == client_id)
            
        if contract_type:
            query = query.filter(Contract.contract_type == contract_type)
            
        if status:
            query = query.filter(Contract.status == status)
            
        if start_date:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            query = query.filter(Contract.start_date >= start_date_obj)
            
        if end_date:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(Contract.end_date <= end_date_obj)
            
        if search:
            search_pattern = f"%{search}%"
            query = query.filter(
                or_(
                    Contract.contract_number.ilike(search_pattern),
                    Contract.title.ilike(search_pattern)
                )
            )
        
        # Ordina per data di creazione (più recenti prima)
        query = query.order_by(Contract.created_at.desc())
        
        # Applica paginazione
        paginated = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        # Prepara dati
        contracts_data = []
        for contract in paginated.items:
            contracts_data.append({
                'id': contract.id,
                'client_id': contract.client_id,
                'client': {
                    'id': contract.client.id,
                    'name': contract.client.name,
                    'company': contract.client.name
                } if contract.client else None,
                'contract_number': contract.contract_number,
                'title': contract.title,
                'contract_type': contract.contract_type,
                'status': contract.status,
                'hourly_rate': contract.hourly_rate,
                'budget_hours': contract.budget_hours,
                'total_budget': contract.budget_amount,
                'start_date': contract.start_date.isoformat() if contract.start_date else None,
                'end_date': contract.end_date.isoformat() if contract.end_date else None,
                'projects_count': len(contract.projects) if contract.projects else 0,
                'created_at': contract.created_at.isoformat() if contract.created_at else None,
                'updated_at': contract.updated_at.isoformat() if contract.updated_at else None
            })
        
        return api_response(
            data={'contracts': contracts_data},
            pagination={
                'page': paginated.page,
                'pages': paginated.pages,
                'per_page': paginated.per_page,
                'total': paginated.total,
                'has_next': paginated.has_next,
                'has_prev': paginated.has_prev
            },
            message=f"Recuperati {len(contracts_data)} contratti"
        )
        
    except Exception as e:
        return handle_api_error(e)


@api_contracts.route('/', methods=['POST'])
@csrf.exempt
@login_required
def create_contract():
    """Crea un nuovo contratto"""
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, 'manage_contracts'):
            return api_response(False, 'Non hai i permessi per creare contratti', status_code=403)
        
        data = request.get_json()
        
        # Validazione campi richiesti
        required_fields = ['client_id', 'title', 'contract_type']
        for field in required_fields:
            if field not in data:
                return api_response(
                    False,
                    f'Campo {field} richiesto',
                    status_code=400
                )
        
        # Validazione tipo contratto
        valid_types = ['hourly', 'fixed', 'retainer', 'milestone', 'subscription']
        if data['contract_type'] not in valid_types:
            return api_response(
                False,
                f'Tipo contratto non valido. Valori ammessi: {", ".join(valid_types)}',
                status_code=400
            )
        
        # Verifica che il cliente esista
        client = Client.query.get(data['client_id'])
        if not client:
            return api_response(
                False,
                'Cliente non trovato',
                status_code=404
            )
        
        # Parsing date
        start_date = None
        end_date = None
        if 'start_date' in data and data['start_date']:
            try:
                start_date = datetime.strptime(data['start_date'], '%Y-%m-%d').date()
            except ValueError:
                return api_response(
                    False,
                    'Formato start_date non valido. Utilizzare YYYY-MM-DD',
                    status_code=400
                )
        
        if 'end_date' in data and data['end_date']:
            try:
                end_date = datetime.strptime(data['end_date'], '%Y-%m-%d').date()
            except ValueError:
                return api_response(
                    False,
                    'Formato end_date non valido. Utilizzare YYYY-MM-DD',
                    status_code=400
                )
        
        # Validazione logica date
        if start_date and end_date and start_date > end_date:
            return api_response(
                False,
                'La data di inizio non può essere successiva alla data di fine',
                status_code=400
            )
        
        # Genera numero contratto se non fornito
        contract_number = data.get('contract_number')
        if not contract_number:
            # Genera numero automatico: YYYY-NNNN
            year = datetime.now().year
            last_contract = Contract.query.filter(
                Contract.contract_number.like(f'{year}-%')
            ).order_by(Contract.contract_number.desc()).first()
            
            if last_contract:
                try:
                    last_num = int(last_contract.contract_number.split('-')[1])
                    next_num = last_num + 1
                except:
                    next_num = 1
            else:
                next_num = 1
            
            contract_number = f"{year}-{next_num:04d}"
        
        # Verifica unicità numero contratto
        existing = Contract.query.filter(Contract.contract_number == contract_number).first()
        if existing:
            return api_response(
                False,
                f'Numero contratto {contract_number} già esistente',
                status_code=400
            )
        
        # Crea nuovo contratto
        contract = Contract(
            client_id=data['client_id'],
            contract_number=contract_number,
            title=data['title'],
            description=data.get('description', ''),
            contract_type=data['contract_type'],
            status=data.get('status', 'active'),
            hourly_rate=data.get('hourly_rate'),
            budget_hours=data.get('budget_hours'),
            budget_amount=data.get('total_budget'),
            start_date=start_date,
            end_date=end_date
        )
        
        # Aggiungi i nuovi campi dopo la creazione
        if 'retainer_amount' in data:
            contract.retainer_amount = data['retainer_amount']
        if 'retainer_frequency' in data:
            contract.retainer_frequency = data['retainer_frequency']
        if 'milestone_amount' in data:
            contract.milestone_amount = data['milestone_amount']
        if 'milestone_count' in data:
            contract.milestone_count = data['milestone_count']
        if 'subscription_amount' in data:
            contract.subscription_amount = data['subscription_amount']
        if 'subscription_frequency' in data:
            contract.subscription_frequency = data['subscription_frequency']
        
        db.session.add(contract)
        db.session.commit()
        
        return api_response(
            data={
                'id': contract.id,
                'contract_number': contract.contract_number,
                'title': contract.title,
                'contract_type': contract.contract_type,
                'status': contract.status,
                'client': {
                    'id': contract.client.id,
                    'name': contract.client.name,
                    'company': contract.client.name
                }
            },
            message='Contratto creato con successo'
        )
        
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)


@api_contracts.route('/<int:contract_id>', methods=['GET'])
@login_required
def get_contract(contract_id):
    """Recupera dettaglio singolo contratto"""
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, 'view_all_contracts'):
            return api_response(False, 'Non hai i permessi per visualizzare i contratti', status_code=403)
        
        contract = Contract.query.get_or_404(contract_id)
        
        # Calcola statistiche progetti collegati
        projects_stats = {
            'total': len(contract.projects),
            'active': len([p for p in contract.projects if p.status == 'active']),
            'completed': len([p for p in contract.projects if p.status == 'completed'])
        }
        
        return api_response(
            data={'contract': {
                'id': contract.id,
                'client_id': contract.client_id,
                'client': {
                    'id': contract.client.id,
                    'name': contract.client.name,
                    'company': contract.client.name,
                    'industry': contract.client.industry,
                    'website': contract.client.website,
                    'email': contract.client.email,
                    'phone': contract.client.phone
                } if contract.client else None,
                'contract_number': contract.contract_number,
                'title': contract.title,
                'description': contract.description,
                'contract_type': contract.contract_type,
                'status': contract.status,
                'hourly_rate': contract.hourly_rate,
                'budget_hours': contract.budget_hours,
                'total_budget': contract.budget_amount,
                # Campi per retainer
                'retainer_amount': contract.retainer_amount,
                'retainer_frequency': contract.retainer_frequency,
                # Campi per milestone
                'milestone_amount': contract.milestone_amount,
                'milestone_count': contract.milestone_count,
                # Campi per subscription
                'subscription_amount': contract.subscription_amount,
                'subscription_frequency': contract.subscription_frequency,
                'start_date': contract.start_date.isoformat() if contract.start_date else None,
                'end_date': contract.end_date.isoformat() if contract.end_date else None,
                'projects': [{
                    'id': p.id,
                    'name': p.name,
                    'status': p.status,
                    'start_date': p.start_date.isoformat() if p.start_date else None,
                    'end_date': p.end_date.isoformat() if p.end_date else None
                } for p in contract.projects] if contract.projects else [],
                'projects_stats': projects_stats,
                'created_at': contract.created_at.isoformat() if contract.created_at else None,
                'updated_at': contract.updated_at.isoformat() if contract.updated_at else None
            }},
            message="Dettaglio contratto recuperato con successo"
        )
        
    except Exception as e:
        return handle_api_error(e)


@api_contracts.route('/<int:contract_id>', methods=['PUT'])
@csrf.exempt
@login_required
def update_contract(contract_id):
    """Aggiorna un contratto esistente"""
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, 'manage_contracts'):
            return api_response(False, 'Non hai i permessi per modificare contratti', status_code=403)

        contract = Contract.query.get_or_404(contract_id)
        data = request.get_json()

        # Aggiorna campi se forniti
        if 'title' in data:
            contract.title = data['title']

        if 'description' in data:
            contract.description = data['description']

        if 'contract_type' in data:
            valid_types = ['hourly', 'fixed', 'retainer', 'milestone', 'subscription']
            if data['contract_type'] not in valid_types:
                return api_response(
                    False,
                    f'Tipo contratto non valido. Valori ammessi: {", ".join(valid_types)}',
                    status_code=400
                )
            contract.contract_type = data['contract_type']

        if 'status' in data:
            valid_statuses = ['active', 'completed', 'cancelled']
            if data['status'] not in valid_statuses:
                return api_response(
                    False,
                    f'Status non valido. Valori ammessi: {", ".join(valid_statuses)}',
                    status_code=400
                )
            contract.status = data['status']

        if 'hourly_rate' in data:
            contract.hourly_rate = data['hourly_rate']

        if 'budget_hours' in data:
            contract.budget_hours = data['budget_hours']

        if 'total_budget' in data:
            contract.budget_amount = data['total_budget']

        # Nuovi campi per retainer
        if 'retainer_amount' in data:
            contract.retainer_amount = data['retainer_amount']

        if 'retainer_frequency' in data:
            contract.retainer_frequency = data['retainer_frequency']

        # Nuovi campi per milestone
        if 'milestone_amount' in data:
            contract.milestone_amount = data['milestone_amount']

        if 'milestone_count' in data:
            contract.milestone_count = data['milestone_count']

        # Nuovi campi per subscription
        if 'subscription_amount' in data:
            contract.subscription_amount = data['subscription_amount']

        if 'subscription_frequency' in data:
            contract.subscription_frequency = data['subscription_frequency']

        # Aggiorna date se fornite
        if 'start_date' in data:
            if data['start_date']:
                try:
                    contract.start_date = datetime.strptime(data['start_date'], '%Y-%m-%d').date()
                except ValueError:
                    return api_response(
                        False,
                        'Formato start_date non valido. Utilizzare YYYY-MM-DD',
                        status_code=400
                    )
            else:
                contract.start_date = None

        if 'end_date' in data:
            if data['end_date']:
                try:
                    contract.end_date = datetime.strptime(data['end_date'], '%Y-%m-%d').date()
                except ValueError:
                    return api_response(
                        False,
                        'Formato end_date non valido. Utilizzare YYYY-MM-DD',
                        status_code=400
                    )
            else:
                contract.end_date = None

        # Validazione logica date
        if contract.start_date and contract.end_date and contract.start_date > contract.end_date:
            return api_response(
                False,
                'La data di inizio non può essere successiva alla data di fine',
                status_code=400
            )

        db.session.commit()

        return api_response(
            data={
                'id': contract.id,
                'contract_number': contract.contract_number,
                'title': contract.title,
                'contract_type': contract.contract_type,
                'status': contract.status,
                'updated_at': contract.updated_at.isoformat() if contract.updated_at else None
            },
            message='Contratto aggiornato con successo'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)


@api_contracts.route('/<int:contract_id>/create-project', methods=['POST'])
@csrf.exempt
@login_required
def create_project_from_contract(contract_id):
    """Converte un contratto in progetto"""
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, 'manage_projects'):
            return api_response(False, 'Non hai i permessi per creare progetti', status_code=403)
        
        # Verifica che il contratto esista
        contract = Contract.query.get(contract_id)
        if not contract:
            return api_response(False, 'Contratto non trovato', status_code=404)
        
        # Verifica che il contratto sia attivo
        if contract.status != 'active':
            return api_response(False, 'Solo i contratti attivi possono essere convertiti in progetti', status_code=400)
        
        # Verifica che non esista già un progetto per questo contratto
        existing_project = Project.query.filter_by(contract_id=contract_id).first()
        if existing_project:
            return api_response(False, 'Esiste già un progetto per questo contratto', status_code=400)
        
        data = request.get_json()
        
        # Validazione campi richiesti
        required_fields = ['name']
        for field in required_fields:
            if field not in data or not data[field]:
                return api_response(
                    False,
                    f'Campo {field} richiesto',
                    status_code=400
                )
        
        # Parsing date
        start_date = None
        end_date = None
        if 'start_date' in data and data['start_date']:
            try:
                start_date = datetime.strptime(data['start_date'], '%Y-%m-%d').date()
            except ValueError:
                return api_response(
                    False,
                    'Formato start_date non valido. Utilizzare YYYY-MM-DD',
                    status_code=400
                )
        
        if 'end_date' in data and data['end_date']:
            try:
                end_date = datetime.strptime(data['end_date'], '%Y-%m-%d').date()
            except ValueError:
                return api_response(
                    False,
                    'Formato end_date non valido. Utilizzare YYYY-MM-DD',
                    status_code=400
                )
        
        # Crea il progetto
        project = Project(
            name=data['name'],
            description=data.get('description', ''),
            client_id=contract.client_id,
            contract_id=contract_id,
            start_date=start_date,
            end_date=end_date,
            budget=contract.budget_amount,
            status=data.get('status', 'planning')
        )
        
        db.session.add(project)
        db.session.commit()
        
        # Prepara dati di risposta
        project_data = {
            'id': project.id,
            'name': project.name,
            'description': project.description,
            'client_id': project.client_id,
            'contract_id': project.contract_id,
            'start_date': project.start_date.isoformat() if project.start_date else None,
            'end_date': project.end_date.isoformat() if project.end_date else None,
            'budget': project.budget,
            'status': project.status,
            'created_at': project.created_at.isoformat(),
            'updated_at': project.updated_at.isoformat()
        }
        
        return api_response(
            data={'project': project_data},
            message='Progetto creato con successo dal contratto'
        )
        
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)


@api_contracts.route('/<int:contract_id>', methods=['DELETE'])
@csrf.exempt
@login_required
def delete_contract(contract_id):
    """Elimina un contratto"""
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, 'manage_contracts'):
            return api_response(False, 'Non hai i permessi per eliminare contratti', status_code=403)

        contract = Contract.query.get_or_404(contract_id)

        # Verifica che non ci siano progetti collegati
        if contract.projects:
            return api_response(
                False,
                f'Impossibile eliminare il contratto: ci sono {len(contract.projects)} progetti collegati',
                status_code=400
            )

        # Verifica che non ci siano timesheet entries collegate
        from models import TimesheetEntry
        linked_entries = TimesheetEntry.query.filter(TimesheetEntry.contract_id == contract.id).count()
        if linked_entries > 0:
            return api_response(
                False,
                f'Impossibile eliminare il contratto: ci sono {linked_entries} timesheet entries collegate',
                status_code=400
            )

        db.session.delete(contract)
        db.session.commit()

        return api_response(
            message=f'Contratto {contract.contract_number} eliminato con successo'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)
