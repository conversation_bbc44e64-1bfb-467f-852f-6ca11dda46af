"""
API endpoints per gestione validazioni e integrità dati.
"""
from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from utils.decorators import admin_required
from utils.api_utils import api_response
from services.validation_service import ValidationService
from extensions import db

validation_bp = Blueprint('api_validation', __name__)


@validation_bp.route('/validation/integrity-check', methods=['GET'])
@login_required
@admin_required
def check_data_integrity():
    """
    Controlla l'integrità dei dati esistenti nel database.
    Solo admin possono eseguire questo controllo.
    """
    try:
        current_app.logger.info(f"Data integrity check requested by user {current_user.id}")
        
        # Esegui controllo integrità
        report = ValidationService.check_data_integrity()
        
        # Log risultati
        summary = report.get('summary', {})
        current_app.logger.info(f"Integrity check completed: {summary.get('overall_health', 'UNKNOWN')}")
        
        return api_response(
            success=True,
            data=report,
            message="Controllo integrità completato"
        )
        
    except Exception as e:
        current_app.logger.error(f"Error during integrity check: {str(e)}")
        return api_response(
            success=False,
            message=f"Errore durante controllo integrità: {str(e)}",
            error_code="INTEGRITY_CHECK_FAILED"
        ), 500


@validation_bp.route('/validation/apply-fixes', methods=['POST'])
@login_required
@admin_required
def apply_automatic_fixes():
    """
    Applica fix automatici per problemi comuni.
    Solo admin possono applicare i fix.
    """
    try:
        current_app.logger.info(f"Automatic fixes requested by user {current_user.id}")
        
        # Applica fix automatici
        fixes_applied = ValidationService.fix_common_issues()
        
        current_app.logger.info(f"Applied {len(fixes_applied)} automatic fixes")
        
        return api_response(
            success=True,
            data={
                'fixes_applied': fixes_applied,
                'count': len(fixes_applied)
            },
            message=f"Applicati {len(fixes_applied)} fix automatici"
        )
        
    except Exception as e:
        current_app.logger.error(f"Error applying fixes: {str(e)}")
        return api_response(
            success=False,
            message=f"Errore durante applicazione fix: {str(e)}",
            error_code="FIXES_APPLICATION_FAILED"
        ), 500


@validation_bp.route('/validation/validate-model', methods=['POST'])
@login_required 
@admin_required
def validate_specific_model():
    """
    Valida un'istanza specifica di modello.
    Body: { "model_type": "PerformanceReview", "model_id": 123 }
    """
    try:
        data = request.get_json()
        model_type = data.get('model_type')
        model_id = data.get('model_id')
        
        if not model_type or not model_id:
            return api_response(
                success=False,
                message="model_type e model_id sono richiesti",
                error_code="MISSING_PARAMETERS"
            ), 400
        
        # Mappa tipi di modello
        model_classes = {
            'PerformanceReview': 'models_split.performance.PerformanceReview',
            'PerformanceGoal': 'models_split.performance.PerformanceGoal',
            'JobPosting': 'models_split.recruiting.JobPosting',
            'CompanyCertification': 'models_split.certifications.CompanyCertification'
        }
        
        if model_type not in model_classes:
            return api_response(
                success=False,
                message=f"Tipo di modello non supportato: {model_type}",
                error_code="UNSUPPORTED_MODEL_TYPE"
            ), 400
        
        # Importa dinamicamente il modello
        module_name, class_name = model_classes[model_type].rsplit('.', 1)
        module = __import__(module_name, fromlist=[class_name])
        model_class = getattr(module, class_name)
        
        # Trova l'istanza
        instance = model_class.query.get(model_id)
        if not instance:
            return api_response(
                success=False,
                message=f"{model_type} con ID {model_id} non trovato",
                error_code="MODEL_NOT_FOUND"
            ), 404
        
        # Valida l'istanza
        errors = ValidationService.validate_model_instance(instance)
        
        is_valid = errors is None
        result = {
            'model_type': model_type,
            'model_id': model_id,
            'is_valid': is_valid,
            'errors': errors or []
        }
        
        return api_response(
            success=True,
            data=result,
            message=f"Validazione {model_type} completata"
        )
        
    except Exception as e:
        current_app.logger.error(f"Error validating model: {str(e)}")
        return api_response(
            success=False,
            message=f"Errore durante validazione: {str(e)}",
            error_code="VALIDATION_FAILED"
        ), 500


@validation_bp.route('/validation/health-summary', methods=['GET'])
@login_required
@admin_required
def get_validation_health_summary():
    """
    Restituisce un riassunto rapido dello stato di salute dei dati.
    """
    try:
        # Controllo veloce su campioni di dati
        from models_split.performance import PerformanceReview, PerformanceGoal
        from models_split.recruiting import JobPosting
        from models_split.certifications import CompanyCertification
        
        # Conta record totali
        total_reviews = PerformanceReview.query.count()
        total_goals = PerformanceGoal.query.count() 
        total_jobs = JobPosting.query.count()
        total_certs = CompanyCertification.query.count()
        
        # Controlla campioni per problemi evidenti
        issues_found = 0
        
        # Sample check su reviews
        if total_reviews > 0:
            sample_reviews = PerformanceReview.query.limit(10).all()
            for review in sample_reviews:
                if review.overall_rating and (review.overall_rating < 1.0 or review.overall_rating > 5.0):
                    issues_found += 1
        
        # Sample check su goals  
        if total_goals > 0:
            sample_goals = PerformanceGoal.query.limit(10).all()
            for goal in sample_goals:
                if goal.progress_percentage and goal.progress_percentage < 0:
                    issues_found += 1
        
        # Sample check su jobs
        if total_jobs > 0:
            sample_jobs = JobPosting.query.limit(10).all()
            for job in sample_jobs:
                if (job.salary_min and job.salary_max and 
                    job.salary_min > job.salary_max):
                    issues_found += 1
        
        health_status = "GOOD" if issues_found == 0 else "ISSUES_DETECTED"
        
        summary = {
            'health_status': health_status,
            'total_records': {
                'performance_reviews': total_reviews,
                'performance_goals': total_goals,
                'job_postings': total_jobs,
                'certifications': total_certs
            },
            'issues_detected': issues_found,
            'validation_enabled': True,
            'last_check': current_app.config.get('VALIDATION_LAST_CHECK', 'Never')
        }
        
        return api_response(
            success=True,
            data=summary,
            message="Health summary generato"
        )
        
    except Exception as e:
        current_app.logger.error(f"Error generating health summary: {str(e)}")
        return api_response(
            success=False,
            message=f"Errore durante generazione summary: {str(e)}",
            error_code="HEALTH_SUMMARY_FAILED"
        ), 500