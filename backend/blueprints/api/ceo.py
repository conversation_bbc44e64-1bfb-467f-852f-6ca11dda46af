"""
API RESTful per Human CEO AI Assistant - Strategic intelligence and research
"""
from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from sqlalchemy import desc, and_, func
from datetime import datetime, timedelta
import json
import os
import uuid
from models import (
    ResearchSession, ResearchQuery, StrategicInsight, 
    AIInteraction, ScheduledTask, CompanyProfile, User,
    Project, Task, TimesheetEntry, Client, Contract
)
from utils.api_utils import (
    api_response, handle_api_error, get_pagination_params,
    format_pagination, api_permission_required
)
from utils.permissions import (
    PERMISSION_VIEW_CEO, ROL<PERSON>_ADMIN, ROLE_MANAGER
)
from extensions import db
from services.ai import (
    analyze_platform_compliance, generate_certification_insights
)
from services.data_aggregation import CEODataAggregator
from services.ceo_ai_service import process_ceo_query

# Crea il blueprint per le API CEO
api_ceo = Blueprint('api_ceo', __name__)

def load_research_topics_config():
    """
    Carica la configurazione dei topic di ricerca da file JSON.
    """
    try:
        config_path = os.path.join(
            os.path.dirname(__file__), '..', '..', 'config', 'ceo_research_topics.json'
        )
        config_path = os.path.abspath(config_path)
        
        if not os.path.exists(config_path):
            current_app.logger.warning(f"CEO research topics config not found at {config_path}")
            return get_default_research_config()
        
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        current_app.logger.error(f"Error loading research topics config: {str(e)}")
        return get_default_research_config()

def get_default_research_config():
    """
    Configurazione di default per research topics.
    """
    return {
        "research_categories": {
            "technology_company": {
                "market_analysis": {
                    "name": "Analisi Mercato Tech",
                    "description": "Trend tecnologici, competitor, opportunità",
                    "search_templates": [
                        "Trend tecnologici emergenti nel settore software 2024",
                        "Analisi competitor principali nel mercato SaaS italiano", 
                        "Opportunità di crescita settore tech PMI Italia"
                    ],
                    "estimated_duration": "5-10 minutes"
                },
                "investment_opportunities": {
                    "name": "Opportunità Investimento",
                    "search_templates": [
                        "Startup innovative da acquisire settore tech",
                        "ROI investimenti R&D settore software",
                        "Mercati emergenti per espansione SaaS"
                    ]
                }
            }
        }
    }

@api_ceo.route('/dashboard-data', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_CEO)
def get_dashboard_data():
    """
    Ottieni dati aggregati per dashboard strategico CEO.
    """
    try:
        aggregator = CEODataAggregator()
        dashboard_data = {
            'overview_metrics': get_overview_metrics(),
            'financial_summary': aggregator.get_financial_summary(),
            'operational_kpis': aggregator.get_operational_kpis(),
            'team_insights': aggregator.get_team_insights(),
            'recent_insights': get_recent_strategic_insights(),
            'active_research': get_active_research_sessions(),
            'critical_alerts': get_critical_alerts()
        }
        
        return api_response(dashboard_data)
        
    except Exception as e:
        current_app.logger.error(f"Error getting CEO dashboard data: {str(e)}")
        return handle_api_error(e)

@api_ceo.route('/research/start', methods=['POST'])
@login_required  
@api_permission_required(PERMISSION_VIEW_CEO)
def start_research_session():
    """
    Avvia una nuova sessione di ricerca multi-query.
    """
    try:
        data = request.get_json()
        
        # Validazione input
        required_fields = ['title', 'category', 'queries']
        for field in required_fields:
            if field not in data:
                return api_response(
                    {'error': f'Campo {field} richiesto'}, 
                    status_code=400
                )
        
        # Ottieni company profile per context
        company_profile = CompanyProfile.query.filter_by(is_active=True).first()
        company_context = company_profile.to_dict() if company_profile else {}
        
        # Crea sessione ricerca
        session = ResearchSession(
            user_id=current_user.id,
            title=data['title'],
            category=data['category'],
            research_config=data.get('research_config', {}),
            company_context=company_context
        )
        
        db.session.add(session)
        db.session.flush()  # Per ottenere l'ID
        
        # Crea queries individuali
        queries_created = []
        for query_data in data['queries']:
            query = ResearchQuery(
                session_id=session.id,
                query_text=query_data['text'],
                query_type=query_data.get('type', 'general'),
                status='pending'
            )
            db.session.add(query)
            queries_created.append(query)
        
        db.session.commit()
        
        # TODO: Avvia processamento asincrono delle queries
        # start_research_processing_async(session.id)
        
        return api_response({
            'session_id': session.id,
            'queries_count': len(queries_created),
            'status': 'started',
            'message': 'Ricerca avviata con successo'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error starting research session: {str(e)}")
        return handle_api_error(e)

@api_ceo.route('/research/<int:session_id>/status', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_CEO)
def get_research_status(session_id):
    """
    Ottieni status in tempo reale di una sessione di ricerca.
    """
    try:
        session = ResearchSession.query.get_or_404(session_id)
        
        # Verifica ownership o permessi admin
        if session.user_id != current_user.id and not current_user.has_role([ROLE_ADMIN]):
            return api_response(
                {'error': 'Non autorizzato ad accedere a questa ricerca'}, 
                status_code=403
            )
        
        queries = ResearchQuery.query.filter_by(session_id=session_id).all()
        
        # Calcola progress
        completed_queries = [q for q in queries if q.status == 'completed']
        failed_queries = [q for q in queries if q.status == 'failed']
        
        progress = {
            'total_queries': len(queries),
            'completed': len(completed_queries),
            'failed': len(failed_queries),
            'percentage': (len(completed_queries) / len(queries)) * 100 if queries else 0
        }
        
        return api_response({
            'session': session.to_dict(),
            'queries': [q.to_dict() for q in queries],
            'progress': progress,
            'insights_generated': len(session.insights)
        })
        
    except Exception as e:
        current_app.logger.error(f"Error getting research status: {str(e)}")
        return handle_api_error(e)

@api_ceo.route('/assistant/query', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_VIEW_CEO)
def ai_assistant_query():
    """
    Processa query per AI assistant con context aziendale.
    """
    try:
        data = request.get_json()
        
        if 'query' not in data:
            return api_response(
                {'error': 'Campo query richiesto'}, 
                status_code=400
            )
        
        query = data['query']
        category = data.get('category', 'strategic')
        conversation_id = data.get('conversation_id', str(uuid.uuid4()))
        search_mode = data.get('search_mode', 'pro')  # 'pro' or 'deep'
        
        # Ottieni context aziendale
        company_profile = CompanyProfile.query.filter_by(is_active=True).first()
        aggregator = CEODataAggregator()
        
        context_data = {
            'company_profile': company_profile.to_dict() if company_profile else {},
            'business_metrics': aggregator.get_business_context_for_ai(),
            'category': category
        }
        
        # Chiamata al service AI reale
        try:
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            response = loop.run_until_complete(
                process_ceo_query(query, context_data, search_mode)
            )
        except Exception as ai_error:
            current_app.logger.error(f"❌ CEO AI SERVICE ERROR: {str(ai_error)}")
            current_app.logger.error(f"❌ CEO insights require proper API configuration")
            return api_response(
                error=f"CEO AI service unavailable: {str(ai_error)}",
                message="CEO insights require OpenAI and Perplexity API configuration"
            )
        
        # Salva interazione
        interaction = AIInteraction(
            user_id=current_user.id,
            query=query,
            response=response['content'],
            conversation_id=conversation_id,
            category=category,
            context_data=context_data,
            model_used='gpt-4o-mini',  # TODO: da config
            confidence_score=response['confidence'],
            response_time_seconds=1.2  # TODO: misurare reale
        )
        
        db.session.add(interaction)
        db.session.commit()
        
        return api_response({
            'response': response['content'],
            'confidence': response['confidence'],
            'sources': response['sources'],
            'action_items': response.get('action_items', []),
            'conversation_id': conversation_id,
            'interaction_id': interaction.id
        })
        
    except Exception as e:
        current_app.logger.error(f"Error processing AI query: {str(e)}")
        return handle_api_error(e)

@api_ceo.route('/insights', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_CEO)
def get_strategic_insights():
    """
    Ottieni insights strategici con filtering e paginazione.
    """
    try:
        # Parametri query
        page, per_page = get_pagination_params()
        status = request.args.get('status')
        priority = request.args.get('priority')
        insight_type = request.args.get('insight_type')
        
        # Build query
        query = StrategicInsight.query
        
        if status:
            query = query.filter(StrategicInsight.status == status)
        if priority:
            query = query.filter(StrategicInsight.priority == priority)
        if insight_type:
            query = query.filter(StrategicInsight.insight_type == insight_type)
        
        # Order by priority and date
        priority_order = ['critical', 'high', 'medium', 'low']
        query = query.order_by(
            func.field(StrategicInsight.priority, *priority_order),
            desc(StrategicInsight.created_at)
        )
        
        # Paginazione
        pagination = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return api_response({
            'insights': [insight.to_dict() for insight in pagination.items],
            'pagination': format_pagination(pagination)
        })
        
    except Exception as e:
        current_app.logger.error(f"Error getting strategic insights: {str(e)}")
        return handle_api_error(e)

@api_ceo.route('/research-config', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_CEO)
def get_research_config():
    """
    Ottieni configurazione research topics per il settore aziendale.
    """
    try:
        config = load_research_topics_config()
        
        # Ottieni company profile per determinare settore
        company_profile = CompanyProfile.query.filter_by(is_active=True).first()
        
        if company_profile and company_profile.industry:
            # Map industry to config category
            industry_mapping = {
                'Software/Technology': 'technology_company',
                'Manufacturing': 'manufacturing_pmi',
                'Services': 'services_company'
            }
            
            category = industry_mapping.get(company_profile.industry, 'technology_company')
            relevant_topics = config.get('research_categories', {}).get(category, {})
        else:
            relevant_topics = config.get('research_categories', {}).get('technology_company', {})
        
        return api_response({
            'research_topics': relevant_topics,
            'company_industry': company_profile.industry if company_profile else None,
            'available_categories': list(config.get('research_categories', {}).keys())
        })
        
    except Exception as e:
        current_app.logger.error(f"Error getting research config: {str(e)}")
        return handle_api_error(e)

@api_ceo.route('/company-profile', methods=['GET', 'PUT'])
@login_required
@api_permission_required(PERMISSION_VIEW_CEO)
def company_profile():
    """
    Gestisci company profile per context AI.
    """
    try:
        if request.method == 'GET':
            profile = CompanyProfile.query.filter_by(is_active=True).first()
            if not profile:
                return api_response({'profile': None})
            
            return api_response({'profile': profile.to_dict()})
        
        elif request.method == 'PUT':
            data = request.get_json()
            
            profile = CompanyProfile.query.filter_by(is_active=True).first()
            if not profile:
                profile = CompanyProfile()
                db.session.add(profile)
            
            # Update fields
            updatable_fields = [
                'company_name', 'mission', 'vision', 'values', 'industry',
                'business_model', 'company_size', 'target_market',
                'competitive_advantages', 'key_challenges', 'strategic_objectives',
                'market_segment', 'geographic_focus', 'revenue_model',
                'current_stage', 'analysis_focus_areas', 'reporting_preferences'
            ]
            
            for field in updatable_fields:
                if field in data:
                    setattr(profile, field, data[field])
            
            profile.updated_by = current_user.id
            profile.updated_at = datetime.utcnow()
            
            db.session.commit()
            
            return api_response({
                'profile': profile.to_dict(),
                'message': 'Company profile aggiornato con successo'
            })
        
    except Exception as e:
        current_app.logger.error(f"Error managing company profile: {str(e)}")
        return handle_api_error(e)

# Helper functions

def get_overview_metrics():
    """Calcola metriche overview per dashboard."""
    try:
        # Active projects
        active_projects = Project.query.filter_by(status='active').count()
        
        # Recent AI interactions
        recent_interactions = AIInteraction.query.filter(
            AIInteraction.timestamp >= datetime.utcnow() - timedelta(days=7)
        ).count()
        
        # Pending insights
        pending_insights = StrategicInsight.query.filter_by(status='new').count()
        total_insights = StrategicInsight.query.count()
        current_app.logger.info(f"CEO Dashboard Insights: {pending_insights} pending out of {total_insights} total insights")
        
        return {
            'active_projects': active_projects,
            'recent_ai_interactions': recent_interactions,
            'pending_insights': pending_insights,
            'last_updated': datetime.utcnow().isoformat()
        }
    except Exception as e:
        current_app.logger.error(f"Error calculating overview metrics: {str(e)}")
        return {}

def get_recent_strategic_insights():
    """Ottieni insights recenti per dashboard."""
    try:
        insights = StrategicInsight.query.filter_by(status='new').order_by(
            desc(StrategicInsight.created_at)
        ).limit(5).all()
        
        return [insight.to_dict() for insight in insights]
    except Exception as e:
        current_app.logger.error(f"Error getting recent insights: {str(e)}")
        return []

def get_active_research_sessions():
    """Ottieni sessioni ricerca attive."""
    try:
        sessions = ResearchSession.query.filter_by(status='running').order_by(
            desc(ResearchSession.created_at)
        ).limit(3).all()
        
        return [session.to_dict() for session in sessions]
    except Exception as e:
        current_app.logger.error(f"Error getting active research: {str(e)}")
        return []

def get_critical_alerts():
    """Ottieni alert critici per CEO."""
    try:
        alerts = []
        
        # Check for overdue projects
        overdue_projects = Project.query.filter(
            and_(
                Project.status == 'active',
                Project.end_date < datetime.utcnow().date()
            )
        ).count()
        
        if overdue_projects > 0:
            alerts.append({
                'type': 'warning',
                'title': f'{overdue_projects} progetti in ritardo',
                'message': 'Richiede attenzione immediata del management',
                'action_url': '/app/projects'
            })
        
        return alerts
    except Exception as e:
        current_app.logger.error(f"Error getting critical alerts: {str(e)}")
        return []

@api_ceo.route('/config', methods=['GET', 'POST'])
@login_required
@api_permission_required(PERMISSION_VIEW_CEO)
def manage_ceo_configuration():
    """Get or update CEO module configuration including company profile and research topics"""
    if request.method == 'GET':
        return _get_ceo_configuration()
    else:  # POST
        return _update_ceo_configuration()
        
def _get_ceo_configuration():
    """Get current CEO configuration."""
    try:
        import json
        import os
        
        # Load tenant config
        tenant_config_path = os.path.join(current_app.root_path, 'config', 'tenant_config.json')
        with open(tenant_config_path, 'r', encoding='utf-8') as f:
            tenant_config = json.load(f)
        
        # Load CEO research config
        ceo_config_path = os.path.join(current_app.root_path, 'config', 'ceo_research_config.json')
        with open(ceo_config_path, 'r', encoding='utf-8') as f:
            ceo_research_config = json.load(f)
        
        # Check for user-specific settings in database
        user_preferences = _get_user_ceo_preferences(current_user.id)
        
        config_data = {
            'company': tenant_config.get('company', {}),
            'research_config': ceo_research_config,
            'user_preferences': user_preferences
        }
        
        return api_response(config_data)
        
    except Exception as e:
        current_app.logger.error(f"Error loading CEO configuration: {str(e)}")
        return api_response({
            'company': {
                'name': 'Your Company',
                'industry': 'Technology',
                'mission': 'Driving innovation and growth',
                'vision': 'Strategic excellence through technology'
            },
            'research_config': get_default_research_config(),
            'user_preferences': {}
        })

def _update_ceo_configuration():
    """Update CEO configuration (user preferences)."""
    try:
        data = request.get_json()
        
        if not data:
            return api_response(
                {'error': 'No data provided'}, 
                status_code=400
            )
        
        # Save user-specific preferences to database
        if 'research_preferences' in data:
            _save_user_ceo_preferences(
                current_user.id, 
                data['research_preferences']
            )
        
        # Update AI settings if provided
        if 'ai_settings' in data:
            _save_user_ai_settings(
                current_user.id,
                data['ai_settings']
            )
        
        return api_response({
            'message': 'Configuration updated successfully',
            'updated_at': datetime.utcnow().isoformat()
        })
        
    except Exception as e:
        current_app.logger.error(f"Error updating CEO configuration: {str(e)}")
        return api_response(
            {'error': 'Failed to update configuration'}, 
            status_code=500
        )

def _get_user_ceo_preferences(user_id):
    """Ottieni preferenze CEO specifiche dell'utente."""
    try:
        # Per ora uso una semplice implementazione con JSONField nel modello User
        # In futuro si può creare una tabella dedicata CEOUserPreferences
        user = User.query.get(user_id)
        if user and hasattr(user, 'ceo_preferences') and user.ceo_preferences:
            return user.ceo_preferences
        return {}
    except Exception as e:
        current_app.logger.error(f"Error getting user CEO preferences: {str(e)}")
        return {}

def _save_user_ceo_preferences(user_id, preferences):
    """Salva preferenze CEO specifiche dell'utente."""
    try:
        user = User.query.get(user_id)
        if user:
            # Se il modello User non ha ceo_preferences field, lo aggiungiamo ai settings generici
            if hasattr(user, 'ceo_preferences'):
                user.ceo_preferences = preferences
            else:
                # Fallback: salva nelle user settings generiche
                if not hasattr(user, 'settings') or not user.settings:
                    user.settings = {}
                user.settings['ceo_preferences'] = preferences
            
            user.updated_at = datetime.utcnow()
            db.session.commit()
            return True
    except Exception as e:
        current_app.logger.error(f"Error saving user CEO preferences: {str(e)}")
        db.session.rollback()
    return False

def _save_user_ai_settings(user_id, ai_settings):
    """Salva impostazioni AI specifiche dell'utente."""
    try:
        user = User.query.get(user_id)
        if user:
            if not hasattr(user, 'settings') or not user.settings:
                user.settings = {}
            user.settings['ai_settings'] = ai_settings
            user.updated_at = datetime.utcnow()
            db.session.commit()
            return True
    except Exception as e:
        current_app.logger.error(f"Error saving user AI settings: {str(e)}")
        db.session.rollback()
    return False