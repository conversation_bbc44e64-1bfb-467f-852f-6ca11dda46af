"""
API endpoints for recruiting AI features.
Provides REST API for AI-powered job posting generation, candidate evaluation,
resume enhancement, and interview question generation.
"""

import json
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app
from flask_login import current_user

from extensions import db, csrf
from services.recruiting_ai_service import recruiting_ai_service
from utils.api_utils import (
    api_response, api_login_required, api_permission_required,
    handle_api_error
)
from utils.permissions import (
    PERMISSION_VIEW_PERSONNEL_DATA, PERMISSION_EDIT_PERSONNEL_DATA
)
from models import JobPosting, Candidate, Application, Department, Project

# Create blueprint for recruiting AI
api_recruiting_ai = Blueprint('api_recruiting_ai', __name__, url_prefix='/recruiting/ai')

# CSRF exemption for API endpoints
csrf.exempt(api_recruiting_ai)

# =============================================================================
# JOB POSTING AI GENERATION
# =============================================================================

@api_recruiting_ai.route('/job-posting/generate', methods=['POST'])
@api_login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def generate_job_posting():
    """Generate job posting content using AI."""
    try:
        data = request.get_json()
        
        if not data:
            return api_response(None, 'Dati richiesta mancanti', success=False, status_code=400)
        
        # Validate required fields
        required_fields = ['role_type', 'experience_level']
        missing_fields = [field for field in required_fields if not data.get(field)]
        
        if missing_fields:
            return api_response(
                None, 
                f'Campi obbligatori mancanti: {", ".join(missing_fields)}', 
                success=False, 
                status_code=400
            )
        
        # Validate enum values
        valid_role_types = ['Technical', 'Management', 'Sales', 'Marketing', 'Operations', 'HR', 'Finance', 'Generico']
        valid_experience_levels = ['Junior', 'Mid', 'Senior', 'Executive', 'Intern']
        
        if data.get('role_type') not in valid_role_types:
            return api_response(
                None, 
                f'Tipo di ruolo non valido. Valori accettati: {", ".join(valid_role_types)}', 
                success=False, 
                status_code=400
            )
        
        if data.get('experience_level') not in valid_experience_levels:
            return api_response(
                None, 
                f'Livello esperienza non valido. Valori accettati: {", ".join(valid_experience_levels)}', 
                success=False, 
                status_code=400
            )
        
        # Validate reference job if provided
        if data.get('reference_job_id'):
            reference_job = JobPosting.query.get(data['reference_job_id'])
            if not reference_job:
                return api_response(
                    None, 
                    'Job posting di riferimento non trovato', 
                    success=False, 
                    status_code=404
                )
        
        # Validate department if provided
        if data.get('department_id'):
            department = Department.query.get(data['department_id'])
            if not department:
                return api_response(
                    None, 
                    'Dipartimento non trovato', 
                    success=False, 
                    status_code=404
                )
        
        # Generate job posting with AI
        result = recruiting_ai_service.generate_job_posting(data)
        
        if not result.get('success'):
            return api_response(
                None, 
                result.get('error', 'Errore nella generazione'), 
                success=False, 
                status_code=500
            )
        
        # Add user context
        result['generated_by'] = {
            'user_id': current_user.id,
            'user_name': current_user.full_name,
            'generated_at': datetime.utcnow().isoformat()
        }
        
        return api_response(
            result, 
            'Job posting generato con successo'
        )
        
    except Exception as e:
        return handle_api_error(e, 'Errore nella generazione job posting')

@api_recruiting_ai.route('/job-posting/templates', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_job_posting_templates():
    """Get existing job postings to use as templates."""
    try:
        # Get recent active job postings to use as templates
        templates = JobPosting.query.filter(
            JobPosting.status.in_(['active', 'closed'])
        ).order_by(JobPosting.created_at.desc()).limit(20).all()
        
        template_data = []
        for job in templates:
            template_data.append({
                'id': job.id,
                'title': job.title,
                'department': job.department.name if job.department else None,
                'employment_type': job.employment_type,
                'created_at': job.created_at.isoformat(),
                'status': job.status
            })
        
        return api_response(
            {'templates': template_data},
            'Template caricati con successo'
        )
        
    except Exception as e:
        return handle_api_error(e, 'Errore nel caricamento template')

# =============================================================================
# CANDIDATE EVALUATION AI
# =============================================================================

@api_recruiting_ai.route('/application/<int:application_id>/evaluate', methods=['POST'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def evaluate_application(application_id):
    """Evaluate application compatibility using AI."""
    try:
        data = request.get_json() or {}
        force_refresh = data.get('force_refresh', False)
        
        # Verify application exists
        application = Application.query.get(application_id)
        if not application:
            return api_response(
                None, 
                'Candidatura non trovata', 
                success=False, 
                status_code=404
            )
        
        # Generate AI evaluation
        result = recruiting_ai_service.evaluate_candidate_match(
            application_id, 
            force_refresh=force_refresh
        )
        
        if not result.get('success'):
            return api_response(
                None, 
                result.get('error', 'Errore nella valutazione'), 
                success=False, 
                status_code=500
            )
        
        # Add evaluation context
        result['evaluated_by'] = {
            'user_id': current_user.id,
            'user_name': current_user.full_name,
            'evaluated_at': datetime.utcnow().isoformat()
        }
        
        # Optionally update application score
        if result.get('evaluation', {}).get('overall_score'):
            try:
                application.overall_score = result['evaluation']['overall_score']
                
                # Add evaluation summary to notes
                evaluation_note = f"AI Evaluation ({datetime.utcnow().strftime('%Y-%m-%d %H:%M')}): "
                evaluation_note += f"Score {result['evaluation']['overall_score']}/100, "
                evaluation_note += f"Recommendation: {result['evaluation'].get('recommendation', 'N/A')}"
                
                if application.interview_notes:
                    application.interview_notes += f"\n\n{evaluation_note}"
                else:
                    application.interview_notes = evaluation_note
                
                db.session.commit()
                
            except Exception as e:
                current_app.logger.warning(f"Failed to update application score: {e}")
                # Continue without failing the API call
        
        return api_response(
            result, 
            'Valutazione completata con successo'
        )
        
    except Exception as e:
        return handle_api_error(e, 'Errore nella valutazione candidatura')

# =============================================================================
# RESUME ENHANCEMENT AI
# =============================================================================

@api_recruiting_ai.route('/candidate/<int:candidate_id>/enhance-resume', methods=['POST'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def enhance_candidate_resume(candidate_id):
    """Generate resume enhancement suggestions for candidate."""
    try:
        data = request.get_json()
        
        if not data or not data.get('enhancement_prompt'):
            return api_response(
                None, 
                'Prompt di miglioramento richiesto', 
                success=False, 
                status_code=400
            )
        
        # Verify candidate exists
        candidate = Candidate.query.get(candidate_id)
        if not candidate:
            return api_response(
                None, 
                'Candidato non trovato', 
                success=False, 
                status_code=404
            )
        
        enhancement_prompt = data.get('enhancement_prompt', '')
        
        # Generate enhancement suggestions
        result = recruiting_ai_service.enhance_resume(candidate_id, enhancement_prompt)
        
        if not result.get('success'):
            return api_response(
                None, 
                result.get('error', 'Errore nella generazione suggerimenti'), 
                success=False, 
                status_code=500
            )
        
        # Add user context
        result['generated_by'] = {
            'user_id': current_user.id,
            'user_name': current_user.full_name,
            'generated_at': datetime.utcnow().isoformat()
        }
        
        return api_response(
            result, 
            'Suggerimenti per il CV generati con successo'
        )
        
    except Exception as e:
        return handle_api_error(e, 'Errore nella generazione suggerimenti CV')

# =============================================================================
# INTERVIEW QUESTIONS AI
# =============================================================================

@api_recruiting_ai.route('/interview/questions/generate', methods=['POST'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def generate_interview_questions():
    """Generate interview questions using AI."""
    try:
        data = request.get_json()
        
        if not data:
            return api_response(None, 'Dati richiesta mancanti', success=False, status_code=400)
        
        # Validate required fields
        if not data.get('job_posting_id'):
            return api_response(
                None, 
                'ID posizione lavorativa richiesto', 
                success=False, 
                status_code=400
            )
        
        # Validate job posting exists
        job_posting = JobPosting.query.get(data['job_posting_id'])
        if not job_posting:
            return api_response(
                None, 
                'Posizione lavorativa non trovata', 
                success=False, 
                status_code=404
            )
        
        # Validate candidate if provided
        if data.get('candidate_id'):
            candidate = Candidate.query.get(data['candidate_id'])
            if not candidate:
                return api_response(
                    None, 
                    'Candidato non trovato', 
                    success=False, 
                    status_code=404
                )
        
        # Validate interview type
        valid_interview_types = ['phone_screening', 'technical', 'behavioral', 'final']
        interview_type = data.get('interview_type', 'behavioral')
        
        if interview_type not in valid_interview_types:
            return api_response(
                None, 
                f'Tipo colloquio non valido. Valori accettati: {", ".join(valid_interview_types)}', 
                success=False, 
                status_code=400
            )
        
        # Generate interview questions
        result = recruiting_ai_service.generate_interview_questions(data)
        
        if not result.get('success'):
            return api_response(
                None, 
                result.get('error', 'Errore nella generazione domande'), 
                success=False, 
                status_code=500
            )
        
        # Add user context
        result['generated_by'] = {
            'user_id': current_user.id,
            'user_name': current_user.full_name,
            'generated_at': datetime.utcnow().isoformat()
        }
        
        return api_response(
            result, 
            'Domande colloquio generate con successo'
        )
        
    except Exception as e:
        return handle_api_error(e, 'Errore nella generazione domande colloquio')

# =============================================================================
# AI ANALYTICS AND UTILITIES
# =============================================================================

@api_recruiting_ai.route('/analytics/usage', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_ai_usage_analytics():
    """Get AI usage analytics for recruiting module."""
    try:
        # This would typically pull from a dedicated analytics table
        # For now, return basic placeholder data
        analytics_data = {
            'total_generations': 0,
            'job_postings_generated': 0,
            'evaluations_performed': 0,
            'resumes_enhanced': 0,
            'questions_generated': 0,
            'average_scores': {
                'overall_satisfaction': 4.2,
                'time_saved_percentage': 65
            },
            'most_used_features': [
                {'feature': 'Job Posting Generation', 'usage_count': 0},
                {'feature': 'Candidate Evaluation', 'usage_count': 0},
                {'feature': 'Interview Questions', 'usage_count': 0},
                {'feature': 'Resume Enhancement', 'usage_count': 0}
            ]
        }
        
        return api_response(
            analytics_data,
            'Analytics AI caricate con successo'
        )
        
    except Exception as e:
        return handle_api_error(e, 'Errore nel caricamento analytics AI')

@api_recruiting_ai.route('/health', methods=['GET'])
@api_login_required
def ai_health_check():
    """Check AI service health and configuration."""
    try:
        # Basic health check
        health_data = {
            'ai_service_available': True,
            'openai_configured': recruiting_ai_service.ai_service.openai_client is not None,
            'timestamp': datetime.utcnow().isoformat(),
            'version': '1.0.0'
        }
        
        # Try a simple AI call to verify connectivity
        try:
            test_result = recruiting_ai_service.ai_service.query_openai(
                "Test connectivity",
                max_tokens=10
            )
            health_data['api_connectivity'] = test_result.get('success', False)
        except:
            health_data['api_connectivity'] = False
        
        return api_response(
            health_data,
            'Health check completato'
        )
        
    except Exception as e:
        return handle_api_error(e, 'Errore nell\'health check AI')