"""
API RESTful per la gestione dei bandi e finanziamenti.
"""
from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from sqlalchemy import desc, and_, or_, func
from datetime import datetime, date, timedelta
from models import FundingOpportunity, FundingApplication, FundingExpense, User, Project, ProjectFundingLink, TimesheetEntry
from utils.api_utils import (
    api_response, handle_api_error, get_pagination_params,
    format_pagination, api_permission_required
)
from utils.permissions import (
    PERMISSION_VIEW_FUNDING, PERMISSION_MANAGE_FUNDING
)
from extensions import db
from services.ai import search_funding_opportunities, calculate_funding_match_score, generate_detailed_funding_analysis

# Crea il blueprint
api_funding = Blueprint('api_funding', __name__, url_prefix='/api/funding')

# ============================================================================
# FUNDING OPPORTUNITIES (BANDI) ENDPOINTS
# ============================================================================

@api_funding.route('/opportunities', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_FUNDING)
def get_funding_opportunities():
    """
    Ottiene la lista delle opportunità di bando con supporto per filtri e paginazione.
    ---
    tags:
      - funding
    parameters:
      - name: page
        in: query
        description: Numero di pagina
        schema:
          type: integer
          default: 1
      - name: per_page
        in: query
        description: Elementi per pagina
        schema:
          type: integer
          default: 10
      - name: search
        in: query
        description: Ricerca nel titolo e descrizione
        schema:
          type: string
      - name: status
        in: query
        description: Filtra per stato del bando
        schema:
          type: string
          enum: [open, closed, evaluating, completed]
      - name: source_entity
        in: query
        description: Filtra per ente erogatore
        schema:
          type: string
      - name: geographic_scope
        in: query
        description: Filtra per ambito geografico
        schema:
          type: string
          enum: [locale, regionale, nazionale, europeo, internazionale]
      - name: deadline_approaching
        in: query
        description: Solo bandi con scadenza vicina (< 30 giorni)
        schema:
          type: boolean
    responses:
      200:
        description: Lista delle opportunità di bando
    """
    try:
        # Parametri di paginazione
        page, per_page = get_pagination_params()

        # Parametri di ricerca
        search = request.args.get('search', '').strip()
        status = request.args.get('status')
        source_entity = request.args.get('source_entity')
        geographic_scope = request.args.get('geographic_scope')
        deadline_approaching = request.args.get('deadline_approaching', '').lower() == 'true'

        # Query base
        query = FundingOpportunity.query.filter_by(is_active=True)

        # Applica filtri
        if search:
            query = query.filter(
                or_(
                    FundingOpportunity.title.ilike(f'%{search}%'),
                    FundingOpportunity.description.ilike(f'%{search}%'),
                    FundingOpportunity.source_entity.ilike(f'%{search}%')
                )
            )

        if status:
            query = query.filter(FundingOpportunity.status == status)

        if source_entity:
            query = query.filter(FundingOpportunity.source_entity.ilike(f'%{source_entity}%'))

        if geographic_scope:
            query = query.filter(FundingOpportunity.geographic_scope == geographic_scope)

        if deadline_approaching:
            thirty_days_from_now = date.today() + timedelta(days=30)
            query = query.filter(
                and_(
                    FundingOpportunity.application_deadline >= date.today(),
                    FundingOpportunity.application_deadline <= thirty_days_from_now
                )
            )

        # Ordina per deadline (scadenze più vicine prima)
        query = query.order_by(FundingOpportunity.application_deadline.asc())

        # Paginazione
        opportunities_paginated = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )

        # Serializza dati
        opportunities_data = []
        for opportunity in opportunities_paginated.items:
            opportunities_data.append(opportunity.to_dict())

        return api_response(
            data={
                'opportunities': opportunities_data,
                'pagination': format_pagination(opportunities_paginated)
            },
            message="Opportunità di bando recuperate con successo"
        )

    except Exception as e:
        current_app.logger.error(f"Error getting funding opportunities: {str(e)}")
        return handle_api_error(e)


@api_funding.route('/opportunities', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_MANAGE_FUNDING)
def create_funding_opportunity():
    """
    Crea una nuova opportunità di bando.
    ---
    tags:
      - funding
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - title
              - application_deadline
            properties:
              title:
                type: string
                description: Titolo del bando
              description:
                type: string
                description: Descrizione del bando
              source_entity:
                type: string
                description: Ente erogatore
              program_name:
                type: string
                description: Nome del programma
              call_identifier:
                type: string
                description: Identificativo univoco del bando
              total_budget:
                type: number
                description: Budget totale del bando
              max_grant_amount:
                type: number
                description: Massimo finanziamento per progetto
              min_grant_amount:
                type: number
                description: Minimo finanziamento
              contribution_percentage:
                type: number
                description: Percentuale di finanziamento
              application_deadline:
                type: string
                format: date
                description: Scadenza per le candidature
              geographic_scope:
                type: string
                enum: [locale, regionale, nazionale, europeo, internazionale]
              eligibility_criteria:
                type: array
                items:
                  type: string
              target_sectors:
                type: array
                items:
                  type: string
              official_url:
                type: string
                description: URL ufficiale del bando
    responses:
      201:
        description: Opportunità di bando creata con successo
      400:
        description: Dati non validi
    """
    try:
        data = request.get_json()

        # Validazione dei campi obbligatori
        required_fields = ['title', 'application_deadline']
        for field in required_fields:
            if not data.get(field):
                return api_response(
                    message=f"Campo obbligatorio mancante: {field}",
                    status_code=400
                )

        # Validazione della data
        try:
            deadline = datetime.strptime(data['application_deadline'], '%Y-%m-%d').date()
            if deadline < date.today():
                return api_response(
                    message="La scadenza non può essere nel passato",
                    status_code=400
                )
        except ValueError:
            return api_response(
                message="Formato data non valido per application_deadline (YYYY-MM-DD)",
                status_code=400
            )

        # Parse additional dates
        publication_date = None
        if data.get('opening_date'):
            try:
                publication_date = datetime.strptime(data['opening_date'], '%Y-%m-%d').date()
            except ValueError:
                pass

        # Crea la nuova opportunità
        opportunity = FundingOpportunity(
            title=data['title'],
            description=data.get('description'),
            source_entity=data.get('source_entity'),
            program_name=data.get('program_name'),
            call_identifier=data.get('call_identifier'),
            total_budget=data.get('total_budget'),
            max_grant_amount=data.get('max_grant_amount'),
            min_grant_amount=data.get('min_grant_amount'),
            contribution_percentage=data.get('funding_percentage') or data.get('contribution_percentage', 70.0),
            publication_date=publication_date,
            application_deadline=deadline,
            geographic_scope=data.get('geographic_scope'),
            status=data.get('status', 'open'),
            eligibility_criteria=data.get('eligibility_criteria', []),
            evaluation_criteria=data.get('evaluation_criteria', []),
            required_documents=data.get('required_documents', []),
            target_sectors=data.get('target_sectors', []),
            contact_info=data.get('contact_info', {}),
            official_url=data.get('website') or data.get('official_url'),
            created_by=current_user.id
        )

        # Converte le liste/dict in JSON strings
        import json
        if isinstance(opportunity.eligibility_criteria, list):
            opportunity.eligibility_criteria = json.dumps(opportunity.eligibility_criteria)
        if isinstance(opportunity.evaluation_criteria, list):
            opportunity.evaluation_criteria = json.dumps(opportunity.evaluation_criteria)
        if isinstance(opportunity.required_documents, list):
            opportunity.required_documents = json.dumps(opportunity.required_documents)
        if isinstance(opportunity.target_sectors, list):
            opportunity.target_sectors = json.dumps(opportunity.target_sectors)
        if isinstance(opportunity.contact_info, dict):
            opportunity.contact_info = json.dumps(opportunity.contact_info)

        db.session.add(opportunity)
        db.session.commit()

        return api_response(
            data={'opportunity': opportunity.to_dict()},
            message="Opportunità di bando creata con successo",
            status_code=201
        )

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error creating funding opportunity: {str(e)}")
        return handle_api_error(e)


@api_funding.route('/opportunities/<int:opportunity_id>', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_FUNDING)
def get_funding_opportunity(opportunity_id):
    """
    Ottiene i dettagli di una specifica opportunità di bando.
    """
    try:
        opportunity = FundingOpportunity.query.filter_by(
            id=opportunity_id, 
            is_active=True
        ).first()

        if not opportunity:
            return api_response(
                message="Opportunità di bando non trovata",
                status_code=404
            )

        return api_response(
            data={'opportunity': opportunity.to_dict()},
            message="Dettagli opportunità recuperati con successo"
        )

    except Exception as e:
        current_app.logger.error(f"Error getting funding opportunity {opportunity_id}: {str(e)}")
        return handle_api_error(e)


@api_funding.route('/opportunities/<int:opportunity_id>', methods=['PUT'])
@login_required
@api_permission_required(PERMISSION_MANAGE_FUNDING)
def update_funding_opportunity(opportunity_id):
    """
    Aggiorna una opportunità di bando.
    """
    try:
        opportunity = FundingOpportunity.query.filter_by(
            id=opportunity_id, 
            is_active=True
        ).first()

        if not opportunity:
            return api_response(
                message="Opportunità di bando non trovata",
                status_code=404
            )

        data = request.get_json()

        # Aggiorna i campi se presenti
        updateable_fields = [
            'title', 'description', 'source_entity', 'program_name',
            'total_budget', 'max_grant_amount', 'min_grant_amount',
            'contribution_percentage', 'geographic_scope', 'status',
            'official_url', 'internal_notes'
        ]

        for field in updateable_fields:
            if field in data:
                setattr(opportunity, field, data[field])

        # Gestione speciale per date
        if 'application_deadline' in data:
            try:
                deadline = datetime.strptime(data['application_deadline'], '%Y-%m-%d').date()
                opportunity.application_deadline = deadline
            except ValueError:
                return api_response(
                    message="Formato data non valido per application_deadline (YYYY-MM-DD)",
                    status_code=400
                )

        # Gestione liste JSON
        import json
        if 'eligibility_criteria' in data and isinstance(data['eligibility_criteria'], list):
            opportunity.eligibility_criteria = json.dumps(data['eligibility_criteria'])
        if 'target_sectors' in data and isinstance(data['target_sectors'], list):
            opportunity.target_sectors = json.dumps(data['target_sectors'])

        opportunity.updated_at = datetime.utcnow()
        db.session.commit()

        return api_response(
            data={'opportunity': opportunity.to_dict()},
            message="Opportunità di bando aggiornata con successo"
        )

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating funding opportunity {opportunity_id}: {str(e)}")
        return handle_api_error(e)


@api_funding.route('/opportunities/<int:opportunity_id>', methods=['DELETE'])
@login_required
@api_permission_required(PERMISSION_MANAGE_FUNDING)
def delete_funding_opportunity(opportunity_id):
    """
    Cancella una opportunità di bando se non è collegata a progetti.
    """
    try:
        opportunity = FundingOpportunity.query.filter_by(
            id=opportunity_id, 
            is_active=True
        ).first()

        if not opportunity:
            return api_response(
                message="Opportunità di bando non trovata",
                status_code=404
            )

        # Verifica se ci sono applicazioni collegate
        applications_count = opportunity.applications.count()
        if applications_count > 0:
            return api_response(
                message=f"Impossibile cancellare: ci sono {applications_count} candidature collegate a questo bando",
                status_code=400
            )

        # Verifica se ci sono progetti collegati tramite ProjectFundingLink
        from sqlalchemy import and_
        linked_projects = db.session.query(ProjectFundingLink).join(
            FundingApplication, ProjectFundingLink.funding_application_id == FundingApplication.id
        ).filter(
            FundingApplication.opportunity_id == opportunity_id
        ).count()

        if linked_projects > 0:
            return api_response(
                message=f"Impossibile cancellare: ci sono {linked_projects} progetti collegati a questo bando",
                status_code=400
            )

        # Soft delete: marca come non attivo invece di cancellare fisicamente
        opportunity.is_active = False
        opportunity.updated_at = datetime.utcnow()
        db.session.commit()

        return api_response(
            message="Opportunità di bando cancellata con successo"
        )

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error deleting funding opportunity {opportunity_id}: {str(e)}")
        return handle_api_error(e)


@api_funding.route('/opportunities/check-duplicate', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_VIEW_FUNDING)
def check_duplicate_opportunity():
    """
    Verifica se un bando simile esiste già nel database.
    """
    try:
        data = request.get_json()
        title = data.get('title', '').strip()

        if not title:
            return api_response(
                message="Titolo richiesto per la verifica",
                status_code=400
            )

        # Cerca bandi con titoli simili (almeno 20 caratteri in comune)
        title_substring = title[:20].lower() if len(title) >= 20 else title.lower()

        existing_opportunities = FundingOpportunity.query.filter(
            and_(
                FundingOpportunity.is_active == True,
                FundingOpportunity.title.ilike(f'%{title_substring}%')
            )
        ).all()

        # Verifica similarità più precisa
        similar_opportunities = []
        for opp in existing_opportunities:
            # Calcola similarità basata su parole comuni
            title_words = set(title.lower().split())
            opp_words = set(opp.title.lower().split())
            common_words = title_words.intersection(opp_words)

            if len(common_words) >= 2:  # Almeno 2 parole in comune
                similar_opportunities.append({
                    'id': opp.id,
                    'title': opp.title,
                    'source_entity': opp.source_entity,
                    'status': opp.status,
                    'similarity_score': len(common_words) / max(len(title_words), len(opp_words))
                })

        return api_response(
            data={
                'can_save': len(similar_opportunities) == 0,
                'similar_opportunities': similar_opportunities,
                'message': 'Nessun bando simile trovato' if len(similar_opportunities) == 0 else f'Trovati {len(similar_opportunities)} bandi simili'
            },
            message="Verifica completata"
        )

    except Exception as e:
        current_app.logger.error(f"Error checking duplicate opportunity: {str(e)}")
        return handle_api_error(e)


# ============================================================================
# FUNDING APPLICATIONS (CANDIDATURE) ENDPOINTS
# ============================================================================

@api_funding.route('/applications', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_FUNDING)
def get_funding_applications():
    """
    Ottiene la lista delle candidature ai bandi.
    """
    try:
        page, per_page = get_pagination_params()

        # Parametri di ricerca
        search = request.args.get('search', '').strip()
        status = request.args.get('status')
        opportunity_id = request.args.get('opportunity_id')

        # Query base
        query = FundingApplication.query

        # Filtri di accesso: admin e manager vedono tutto, 
        # altri utenti vedono solo le proprie candidature
        if not current_user.has_permission(PERMISSION_MANAGE_FUNDING):
            query = query.filter(
                or_(
                    FundingApplication.created_by == current_user.id,
                    FundingApplication.project_manager_id == current_user.id
                )
            )

        # Applica filtri
        if search:
            query = query.filter(
                FundingApplication.project_title.ilike(f'%{search}%')
            )

        if status:
            query = query.filter(FundingApplication.status == status)

        if opportunity_id:
            query = query.filter(FundingApplication.opportunity_id == opportunity_id)

        # Ordina per data di creazione (più recenti prima)
        query = query.order_by(desc(FundingApplication.created_at))

        # Paginazione
        applications_paginated = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )

        # Serializza dati
        applications_data = []
        for application in applications_paginated.items:
            applications_data.append(application.to_dict())

        return api_response(
            data={
                'applications': applications_data,
                'pagination': format_pagination(applications_paginated)
            },
            message="Candidature recuperate con successo"
        )

    except Exception as e:
        current_app.logger.error(f"Error getting funding applications: {str(e)}")
        return handle_api_error(e)


@api_funding.route('/applications', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_VIEW_FUNDING)  # Tutti possono candidarsi
def create_funding_application():
    """
    Crea una nuova candidatura a un bando.
    """
    try:
        data = request.get_json()

        # Validazione dei campi obbligatori
        required_fields = ['opportunity_id', 'project_title', 'requested_amount']
        for field in required_fields:
            if not data.get(field):
                return api_response(
                    message=f"Campo obbligatorio mancante: {field}",
                    status_code=400
                )

        # Verifica che l'opportunità esista ed sia aperta
        opportunity = FundingOpportunity.query.filter_by(
            id=data['opportunity_id'],
            is_active=True
        ).first()

        if not opportunity:
            return api_response(
                message="Opportunità di bando non trovata",
                status_code=404
            )

        if opportunity.status != 'open':
            return api_response(
                message="Il bando non è più aperto alle candidature",
                status_code=400
            )

        if opportunity.application_deadline < date.today():
            return api_response(
                message="La scadenza per le candidature è passata",
                status_code=400
            )

        # Verifica limiti di finanziamento
        requested_amount = float(data['requested_amount'])
        if opportunity.max_grant_amount and requested_amount > opportunity.max_grant_amount:
            return api_response(
                message=f"Importo richiesto superiore al massimo consentito (€{opportunity.max_grant_amount:,.2f})",
                status_code=400
            )

        if opportunity.min_grant_amount and requested_amount < opportunity.min_grant_amount:
            return api_response(
                message=f"Importo richiesto inferiore al minimo richiesto (€{opportunity.min_grant_amount:,.2f})",
                status_code=400
            )

        # Crea la candidatura
        application = FundingApplication(
            opportunity_id=data['opportunity_id'],
            project_title=data['project_title'],
            project_description=data.get('project_description'),
            project_duration_months=data.get('project_duration_months'),
            requested_amount=requested_amount,
            co_financing_amount=data.get('co_financing_amount', 0.0),
            project_manager_id=data.get('project_manager_id', current_user.id),
            linked_project_id=data.get('linked_project_id'),
            created_by=current_user.id
        )

        # Gestione team composition e budget breakdown
        import json
        if 'team_composition' in data and isinstance(data['team_composition'], list):
            application.team_composition = json.dumps(data['team_composition'])
        if 'budget_breakdown' in data and isinstance(data['budget_breakdown'], dict):
            application.budget_breakdown = json.dumps(data['budget_breakdown'])

        db.session.add(application)
        db.session.commit()

        return api_response(
            data={'application': application.to_dict()},
            message="Candidatura creata con successo",
            status_code=201
        )

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error creating funding application: {str(e)}")
        return handle_api_error(e)


@api_funding.route('/applications/<int:application_id>/submit', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_VIEW_FUNDING)
def submit_funding_application(application_id):
    """
    Sottomette una candidatura (cambia status da draft a submitted).
    """
    try:
        application = FundingApplication.query.get(application_id)

        if not application:
            return api_response(
                message="Candidatura non trovata",
                status_code=404
            )

        # Verifica permessi: solo chi ha creato o è PM può sottomettere
        if (application.created_by != current_user.id and 
            application.project_manager_id != current_user.id and
            not current_user.has_permission(PERMISSION_MANAGE_FUNDING)):
            return api_response(
                message="Non hai i permessi per sottomettere questa candidatura",
                status_code=403
            )

        if application.status != 'draft':
            return api_response(
                message="Solo le candidature in bozza possono essere sottomesse",
                status_code=400
            )

        # Verifica che la scadenza non sia passata
        if application.opportunity.application_deadline < date.today():
            return api_response(
                message="La scadenza per le candidature è passata",
                status_code=400
            )

        # Sottomette la candidatura
        application.status = 'submitted'
        application.submission_date = datetime.utcnow()
        application.updated_at = datetime.utcnow()

        db.session.commit()

        return api_response(
            data={'application': application.to_dict()},
            message="Candidatura sottomessa con successo"
        )

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error submitting funding application {application_id}: {str(e)}")
        return handle_api_error(e)


# ============================================================================
# DASHBOARD E STATISTICHE
# ============================================================================

@api_funding.route('/dashboard/stats', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_FUNDING)
def get_funding_dashboard_stats():
    """
    Ottiene statistiche aggregate per la dashboard bandi.
    """
    try:
        # Statistiche opportunità
        total_opportunities = FundingOpportunity.query.filter_by(is_active=True).count()
        open_opportunities = FundingOpportunity.query.filter_by(
            status='open', 
            is_active=True
        ).count()

        # Opportunità in scadenza (prossimi 30 giorni)
        thirty_days_from_now = date.today() + timedelta(days=30)
        approaching_deadline = FundingOpportunity.query.filter(
            and_(
                FundingOpportunity.application_deadline >= date.today(),
                FundingOpportunity.application_deadline <= thirty_days_from_now,
                FundingOpportunity.is_active == True
            )
        ).count()

        # Statistiche candidature
        total_applications = FundingApplication.query.count()

        # Se non admin/manager, mostra solo le proprie statistiche
        if not current_user.has_permission(PERMISSION_MANAGE_FUNDING):
            total_applications = FundingApplication.query.filter(
                or_(
                    FundingApplication.created_by == current_user.id,
                    FundingApplication.project_manager_id == current_user.id
                )
            ).count()

        # Candidature per stato
        applications_by_status = db.session.query(
            FundingApplication.status,
            func.count(FundingApplication.id).label('count')
        ).group_by(FundingApplication.status)

        if not current_user.has_permission(PERMISSION_MANAGE_FUNDING):
            applications_by_status = applications_by_status.filter(
                or_(
                    FundingApplication.created_by == current_user.id,
                    FundingApplication.project_manager_id == current_user.id
                )
            )

        status_counts = {row.status: row.count for row in applications_by_status.all()}

        # Finanziamenti ottenuti
        approved_amount = db.session.query(
            func.sum(FundingApplication.approved_amount)
        ).filter(FundingApplication.status == 'approved').scalar() or 0

        stats = {
            'total_opportunities': total_opportunities,
            'open_opportunities': open_opportunities,
            'approaching_deadline': approaching_deadline,
            'total_applications': total_applications,
            'applications_by_status': status_counts,
            'total_approved_funding': float(approved_amount)
        }

        return api_response(
            data={'stats': stats},
            message="Statistiche dashboard recuperate con successo"
        )

    except Exception as e:
        current_app.logger.error(f"Error getting funding dashboard stats: {str(e)}")
        return handle_api_error(e)


@api_funding.route('/dashboard/recent', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_FUNDING)
def get_funding_recent_activity():
    """
    Ottiene le attività recenti nell'area bandi.
    """
    try:
        limit = int(request.args.get('limit', 10))

        # Ultime opportunità create
        recent_opportunities = FundingOpportunity.query.filter_by(
            is_active=True
        ).order_by(desc(FundingOpportunity.created_at)).limit(limit).all()

        # Ultime candidature
        recent_applications_query = FundingApplication.query

        if not current_user.has_permission(PERMISSION_MANAGE_FUNDING):
            recent_applications_query = recent_applications_query.filter(
                or_(
                    FundingApplication.created_by == current_user.id,
                    FundingApplication.project_manager_id == current_user.id
                )
            )

        recent_applications = recent_applications_query.order_by(
            desc(FundingApplication.created_at)
        ).limit(limit).all()

        # Scadenze imminenti (prossimi 30 giorni)
        thirty_days_from_now = date.today() + timedelta(days=30)
        upcoming_deadlines = FundingOpportunity.query.filter(
            and_(
                FundingOpportunity.application_deadline >= date.today(),
                FundingOpportunity.application_deadline <= thirty_days_from_now,
                FundingOpportunity.is_active == True
            )
        ).order_by(FundingOpportunity.application_deadline.asc()).limit(limit).all()

        # Serializza con gestione errori
        def safe_to_dict(obj):
            try:
                current_app.logger.debug(f"Serializing {type(obj).__name__} with ID {obj.id}")

                # Caso speciale per FundingApplication con opportunity null
                if isinstance(obj, FundingApplication) and obj.opportunity_id and not obj.opportunity:
                    current_app.logger.warning(f"FundingApplication {obj.id} has opportunity_id={obj.opportunity_id} but opportunity is None")
                    # Creo un dizionario con i dati di base, senza accedere a opportunity
                    result = {
                        'id': obj.id,
                        'project_title': obj.project_title,
                        'status': obj.status,
                        'requested_amount': obj.requested_amount,
                        'submission_date': obj.submission_date.isoformat() if obj.submission_date else None,
                        'created_at': obj.created_at.isoformat(),
                        'opportunity_id': obj.opportunity_id,
                        'opportunity': None  # Esplicitamente None per evitare errori
                    }
                    return result

                # Caso normale
                result = obj.to_dict()
                return result
            except Exception as e:
                current_app.logger.error(f"Error serializing object {type(obj).__name__}: {str(e)}")
                # Fallback con informazioni minime
                if hasattr(obj, 'id'):
                    return {'id': obj.id, 'error': 'Serialization error', 'type': type(obj).__name__}
                return {'error': 'Serialization error', 'type': type(obj).__name__}

        # Serializza i dati
        recent_opp_data = [safe_to_dict(opp) for opp in recent_opportunities]
        recent_app_data = [safe_to_dict(app) for app in recent_applications]
        upcoming_data = [safe_to_dict(opp) for opp in upcoming_deadlines]

        response_data = {
            'recent_opportunities': recent_opp_data,
            'recent_applications': recent_app_data,
            'upcoming_deadlines': upcoming_data
        }

        return api_response(
            data=response_data,
            message="Attività recenti recuperate con successo"
        )

    except Exception as e:
        current_app.logger.error(f"Error getting funding recent activity: {str(e)}")
        current_app.logger.error(f"Error type: {type(e)}")
        import traceback
        current_app.logger.error(f"Full traceback: {traceback.format_exc()}")
        return handle_api_error(e)


@api_funding.route('/ai-dashboard-suggestions', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_FUNDING)
def get_ai_dashboard_suggestions():
    """
    Genera suggerimenti AI personalizzati per la dashboard funding.
    """
    try:
        from services.ai import ai_service

        # Costruisco il profilo dell'utente corrente
        user_profile = {
            'name': f"{current_user.first_name} {current_user.last_name}",
            'role': current_user.role,
            'department': current_user.department,
            'position': current_user.position
        }

        # Statistiche aziendali recenti
        total_opportunities = FundingOpportunity.query.filter_by(is_active=True).count()
        active_applications = FundingApplication.query.filter(
            FundingApplication.status.in_(['draft', 'submitted', 'under_evaluation'])
        ).count()

        # Aree di competenza dall'analisi delle candidature passate
        user_applications = FundingApplication.query.filter_by(created_by=current_user.id).all()
        keywords = []
        for app in user_applications:
            if app.keywords:
                keywords.extend(app.keywords if isinstance(app.keywords, list) else [])

        # Prompt per OpenAI
        prompt = f"""
Sei un consulente esperto in bandi di finanziamento italiani per PMI e startup.

PROFILO UTENTE:
- Nome: {user_profile['name']}
- Ruolo: {user_profile['role']}
- Dipartimento: {user_profile['department']}
- Posizione: {user_profile['position']}

SITUAZIONE AZIENDALE:
- Bandi attivi nel sistema: {total_opportunities}
- Candidature in corso: {active_applications}
- Aree competenza precedenti: {', '.join(set(keywords[:10]))}

COMPITO:
Genera 3 suggerimenti personalizzati e actionable per ottimizzare la strategia funding aziendale.
I suggerimenti devono essere:
1. Specifici per il ruolo dell'utente
2. Pratici e implementabili
3. Basati sul panorama attuale dei bandi italiani 2025

Rispondi SOLO in formato JSON:
{{
  "suggestions": [
    {{
      "title": "Titolo breve e accattivante",
      "description": "Descrizione specifica di 2-3 righe",
      "priority": "high|medium|low",
      "action": "Azione concreta da fare",
      "category": "ricerca|candidatura|strategia|networking"
    }}
  ],
  "generated_at": "{datetime.now().isoformat()}"
}}
"""

        # Chiamata AI
        ai_response = ai_service.query_openai(
            prompt,
            model="gpt-4o-mini",
            max_tokens=800,
            temperature=0.7
        )

        if ai_response and ai_response.get('success'):
            try:
                import json
                suggestions_data = json.loads(ai_response['response'])

                return api_response(
                    data=suggestions_data,
                    message="Suggerimenti AI generati con successo"
                )
            except json.JSONDecodeError:
                # Fallback con suggerimenti predefiniti
                fallback_suggestions = {
                    "suggestions": [
                        {
                            "title": "Monitora i nuovi bandi PNRR",
                            "description": "Controlla settimanalmente i bandi aperti del Piano Nazionale di Ripresa e Resilienza, spesso con scadenze ravvicinate.",
                            "priority": "high",
                            "action": "Imposta alert automatici sui portali ufficiali",
                            "category": "ricerca"
                        },
                        {
                            "title": "Prepara documentazione standard",
                            "description": "Crea template riutilizzabili per bilanci, business plan e documenti societari per velocizzare le candidature.",
                            "priority": "medium", 
                            "action": "Digitalizza e organizza tutti i documenti in cartelle tematiche",
                            "category": "candidatura"
                        },
                        {
                            "title": "Network con altri beneficiari",
                            "description": "Partecipa a eventi di networking per conoscere strategie vincenti e possibili partnership.",
                            "priority": "medium",
                            "action": "Iscriviti a gruppi LinkedIn e associazioni di categoria",
                            "category": "networking"
                        }
                    ],
                    "generated_at": datetime.now().isoformat()
                }

                return api_response(
                    data=fallback_suggestions,
                    message="Suggerimenti AI generati (fallback)"
                )
        else:
            raise Exception("AI service non disponibile")

    except Exception as e:
        current_app.logger.error(f"Error generating AI dashboard suggestions: {str(e)}")
        return handle_api_error(e)


@api_funding.route('/reporting/<int:application_id>', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_FUNDING)
def get_funding_reporting_data(application_id):
    """
    Ottiene dati completi per rendicontazione: bando + progetto + timesheet + spese.
    """
    try:
        # Verifica accesso alla candidatura
        application = FundingApplication.query.get_or_404(application_id)

        if not current_user.has_permission(PERMISSION_MANAGE_FUNDING):
            if application.created_by != current_user.id and application.project_manager_id != current_user.id:
                return api_response(
                    success=False,
                    message="Accesso non autorizzato a questa candidatura",
                    status_code=403
                )

        # Usa la funzione interna per ottenere i dati
        response_data = get_reporting_data_internal(application_id)

        return api_response(
            data=response_data,
            message="Dati rendicontazione caricati con successo"
        )

    except Exception as e:
        current_app.logger.error(f"Error getting funding reporting data: {str(e)}")
        return handle_api_error(e)


def get_reporting_data_internal(application_id):
    """
    Funzione interna per ottenere dati di rendicontazione senza response Flask.
    """
    # Ottieni candidatura
    application = FundingApplication.query.get_or_404(application_id)

    # Dati di base candidatura e bando (con gestione errori)
    try:
        funding_data = application.to_dict()
    except Exception as e:
        current_app.logger.warning(f"Error in application.to_dict(): {str(e)}")
        # Fallback con dati essenziali
        funding_data = {
            'id': application.id,
            'project_title': application.project_title,
            'status': application.status,
            'requested_amount': application.requested_amount,
            'co_financing_amount': application.co_financing_amount,
            'total_project_cost': application.total_project_cost,
            'submission_date': application.submission_date.isoformat() if application.submission_date else None,
            'linked_project_id': application.linked_project_id,
            'created_at': application.created_at.isoformat(),
            'updated_at': application.updated_at.isoformat()
        }

    # Dati progetto collegato se esiste
    project_data = None
    timesheet_data = []
    project_costs = {
        'total_hours': 0,
        'total_personnel_cost': 0,
        'personnel_breakdown': {},
        'average_hourly_rate': 0
    }

    if application.linked_project_id:
        try:
            project = application.linked_project
            if project:
                project_data = project.to_dict()

                # Query timesheet entries collegate al progetto
                timesheet_query = TimesheetEntry.query.filter_by(project_id=project.id)

                # Filtro date se specificate (dal form di candidatura al progetto corrente)
                if application.submission_date:
                    timesheet_query = timesheet_query.filter(
                        TimesheetEntry.date >= application.submission_date.date()
                    )

                timesheet_entries = timesheet_query.all()

                # Calcolo costi del personale dal timesheet
                total_hours = 0
                total_personnel_cost = 0
                personnel_breakdown = {}

                for entry in timesheet_entries:
                    user_name = f"{entry.user.first_name} {entry.user.last_name}"
                    hours = entry.hours or 0

                    # Calcolo costo orario medio (se disponibile nei dati utente)
                    hourly_rate = getattr(entry.user, 'hourly_rate', 35.0)  # Default 35€/h
                    entry_cost = hours * hourly_rate

                    total_hours += hours
                    total_personnel_cost += entry_cost

                    if user_name not in personnel_breakdown:
                        personnel_breakdown[user_name] = {
                            'hours': 0,
                            'cost': 0,
                            'user_id': entry.user.id,
                            'role': entry.user.position or 'Developer'
                        }

                    personnel_breakdown[user_name]['hours'] += hours
                    personnel_breakdown[user_name]['cost'] += entry_cost

                    timesheet_data.append({
                        'id': entry.id,
                        'date': entry.date.isoformat(),
                        'user_name': user_name,
                        'user_id': entry.user.id,
                        'hours': hours,
                        'hourly_rate': hourly_rate,
                        'cost': entry_cost,
                        'description': entry.description,
                        'task_id': entry.task_id,
                        'task_name': entry.task.name if entry.task else None
                    })

                project_costs = {
                    'total_hours': total_hours,
                    'total_personnel_cost': total_personnel_cost,
                    'personnel_breakdown': personnel_breakdown,
                    'average_hourly_rate': total_personnel_cost / total_hours if total_hours > 0 else 0
                }
            else:
                current_app.logger.warning(f"Application {application_id} has linked_project_id={application.linked_project_id} but project not found")
        except Exception as e:
            current_app.logger.warning(f"Error accessing linked project: {str(e)}")
            # Set empty project costs
            project_costs = {
                'total_hours': 0,
                'total_personnel_cost': 0,
                'personnel_breakdown': {},
                'average_hourly_rate': 0
            }

    # Spese dirette della candidatura
    expenses_query = FundingExpense.query.filter_by(application_id=application_id)
    expenses = expenses_query.all()

    expenses_data = []
    total_expenses = 0
    expenses_by_category = {}

    for expense in expenses:
        expense_dict = expense.to_dict()
        expenses_data.append(expense_dict)
        total_expenses += expense.amount

        category = expense.category or 'other'
        if category not in expenses_by_category:
            expenses_by_category[category] = 0
        expenses_by_category[category] += expense.amount

    # Riepilogo finanziario
    financial_summary = {
        'requested_funding': application.requested_amount or 0,
        'co_financing': application.co_financing_amount or 0,
        'total_project_budget': application.total_project_cost,
        'expenses_total': total_expenses,
        'personnel_costs': project_costs.get('total_personnel_cost', 0),
        'direct_expenses': total_expenses,
        'total_incurred_costs': project_costs.get('total_personnel_cost', 0) + total_expenses,
        'remaining_budget': application.total_project_cost - (project_costs.get('total_personnel_cost', 0) + total_expenses) if application.total_project_cost else 0,
        'funding_utilization_percentage': ((project_costs.get('total_personnel_cost', 0) + total_expenses) / application.total_project_cost * 100) if application.total_project_cost > 0 else 0
    }

    return {
        'application': funding_data,
        'project': project_data,
        'timesheet_entries': timesheet_data,
        'project_costs': project_costs,
        'expenses': expenses_data,
        'expenses_by_category': expenses_by_category,
        'financial_summary': financial_summary,
        'reporting_generated_at': datetime.now().isoformat()
    }


@api_funding.route('/export/<int:application_id>', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_FUNDING)
def export_funding_report(application_id):
    """
    Esporta report completo in formato XLSX multi-sheet.
    """
    try:
        import csv
        import zipfile
        from io import BytesIO, StringIO
        from flask import Response

        # Verifica accesso alla candidatura
        application = FundingApplication.query.get_or_404(application_id)

        if not current_user.has_permission(PERMISSION_MANAGE_FUNDING):
            if application.created_by != current_user.id and application.project_manager_id != current_user.id:
                return api_response(
                    success=False,
                    message="Accesso non autorizzato a questa candidatura",
                    status_code=403
                )

        # Ottieni tutti i dati di rendicontazione direttamente (senza chiamare la route)
        data = get_reporting_data_internal(application_id)
        application_data = data['application']
        financial = data['financial_summary']

        # Crea ZIP con più CSV files
        zip_buffer = BytesIO()

        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:

            # File 1: Riepilogo Bando
            csv_buffer = StringIO()
            writer = csv.writer(csv_buffer)
            writer.writerow(['Campo', 'Valore'])

            bando_rows = [
                ['Titolo Bando', application_data.get('opportunity', {}).get('title', 'N/A') if application_data.get('opportunity') else 'N/A'],
                ['Ente Finanziatore', application_data.get('opportunity', {}).get('source_entity', 'N/A') if application_data.get('opportunity') else 'N/A'],
                ['Titolo Progetto', application_data.get('project_title', 'N/A')],
                ['Importo Richiesto (€)', f"€ {application_data.get('requested_amount', 0):,.2f}"],
                ['Co-finanziamento (€)', f"€ {application_data.get('co_financing_amount', 0):,.2f}"],
                ['Budget Totale (€)', f"€ {application_data.get('total_project_cost', 0):,.2f}"],
                ['Durata Progetto (mesi)', application_data.get('project_duration_months', 'N/A')],
                ['Data Candidatura', application_data.get('submission_date', 'Non inviata')],
                ['Stato', application_data.get('status', 'N/A')],
                ['Project Manager', (application_data.get('project_manager', {}).get('first_name', '') + ' ' + application_data.get('project_manager', {}).get('last_name', '')).strip() if application_data.get('project_manager') else 'N/A']
            ]

            for row in bando_rows:
                writer.writerow(row)

            zip_file.writestr('1_Riepilogo_Bando.csv', csv_buffer.getvalue())

            # File 2: Riepilogo Finanziario
            csv_buffer = StringIO()
            writer = csv.writer(csv_buffer)
            writer.writerow(['Voce', 'Importo (€)'])

            financial_rows = [
                ['Budget Totale Progetto', f"{financial.get('total_project_budget', 0):,.2f}"],
                ['Finanziamento Richiesto', f"{financial.get('requested_funding', 0):,.2f}"],
                ['Co-finanziamento Aziendale', f"{financial.get('co_financing', 0):,.2f}"],
                ['Costi Personale (da Timesheet)', f"{financial.get('personnel_costs', 0):,.2f}"],
                ['Spese Dirette', f"{financial.get('direct_expenses', 0):,.2f}"],
                ['Totale Costi Sostenuti', f"{financial.get('total_incurred_costs', 0):,.2f}"],
                ['Budget Residuo', f"{financial.get('remaining_budget', 0):,.2f}"],
                ['Utilizzo Budget (%)', f"{financial.get('funding_utilization_percentage', 0):.1f}%"]
            ]

            for row in financial_rows:
                writer.writerow(row)

            zip_file.writestr('2_Riepilogo_Finanziario.csv', csv_buffer.getvalue())

            # File 3: Consuntivazione Ore
            if data['timesheet_entries']:
                csv_buffer = StringIO()
                writer = csv.writer(csv_buffer)
                writer.writerow(['Data', 'Risorsa', 'Ore', 'Tariffa Oraria (€)', 'Costo (€)', 'Attività', 'Descrizione'])

                for entry in data['timesheet_entries']:
                    writer.writerow([
                        entry['date'],
                        entry['user_name'],
                        entry['hours'],
                        f"{entry['hourly_rate']:.2f}",
                        f"{entry['cost']:.2f}",
                        entry['task_name'] or 'Generica',
                        entry['description'] or ''
                    ])

                zip_file.writestr('3_Consuntivazione_Ore.csv', csv_buffer.getvalue())

                # File 4: Riepilogo Risorse
                if data['project_costs']['personnel_breakdown']:
                    csv_buffer = StringIO()
                    writer = csv.writer(csv_buffer)
                    writer.writerow(['Risorsa', 'Ruolo', 'Ore Totali', 'Costo Totale (€)', 'Tariffa Media (€/h)'])

                    for person, details in data['project_costs']['personnel_breakdown'].items():
                        writer.writerow([
                            person,
                            details['role'],
                            details['hours'],
                            f"{details['cost']:.2f}",
                            f"{details['cost']/details['hours']:.2f}" if details['hours'] > 0 else "0.00"
                        ])

                    zip_file.writestr('4_Riepilogo_Risorse.csv', csv_buffer.getvalue())

            # File 5: Spese Dirette
            if data['expenses']:
                csv_buffer = StringIO()
                writer = csv.writer(csv_buffer)
                writer.writerow(['Data', 'Descrizione', 'Categoria', 'Importo (€)', 'Stato Approvazione', 'Note'])

                for expense in data['expenses']:
                    writer.writerow([
                        expense.get('expense_date', ''),
                        expense.get('description', ''),
                        expense.get('category', ''),
                        expense.get('amount', 0),
                        expense.get('approval_status', ''),
                        expense.get('notes', '')
                    ])

                zip_file.writestr('5_Spese_Dirette.csv', csv_buffer.getvalue())

        zip_buffer.seek(0)

        # Nome file con data
        project_title = application_data.get('project_title', 'Progetto').replace(' ', '_')
        filename = f"Rendicontazione_{project_title}_{datetime.now().strftime('%Y%m%d')}.zip"

        return Response(
            zip_buffer.getvalue(),
            mimetype='application/zip',
            headers={'Content-Disposition': f'attachment; filename="{filename}"'}
        )

    except Exception as e:
        current_app.logger.error(f"Error exporting funding report: {str(e)}")
        return handle_api_error(e)


# ============================================================================
# RICERCA AI CON PERPLEXITY
# ============================================================================

@api_funding.route('/ai-search', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_VIEW_FUNDING)
def ai_search_funding_opportunities():
    """
    Ricerca opportunità di bandi usando Perplexity AI Sonar Pro.
    ---
    tags:
      - funding
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              company_profile:
                type: object
                properties:
                  sector:
                    type: string
                    description: Settore aziendale
                  size:
                    type: string
                    description: Dimensione azienda (PMI, Startup, etc.)
                  location:
                    type: string
                    description: Localizzazione
                  activities:
                    type: string
                    description: Attività principali
              search_criteria:
                type: object
                properties:
                  max_amount:
                    type: string
                    description: Importo massimo
                  geographic_scope:
                    type: string
                    description: Ambito geografico
                  target_sectors:
                    type: array
                    items:
                      type: string
                    description: Settori target
                  deadline_limit:
                    type: string
                    description: Limite scadenza
              save_results:
                type: boolean
                description: Se salvare i risultati nel database
                default: false
    responses:
      200:
        description: Risultati ricerca AI
      400:
        description: Parametri richiesta non validi
      500:
        description: Errore interno server
    """
    try:
        data = request.get_json()

        if not data:
            return api_response(
                message="Dati richiesta mancanti",
                status_code=400
            )

        # Profilo aziendale (obbligatorio)
        company_profile = data.get('company_profile', {})
        if not company_profile:
            return api_response(
                message="Profilo aziendale richiesto",
                status_code=400
            )

        # Criteri di ricerca (opzionali)
        search_criteria = data.get('search_criteria', {})
        save_results = data.get('save_results', False)

        current_app.logger.info(f"AI search requested by user {current_user.id}")
        current_app.logger.info(f"Company profile: {company_profile}")
        current_app.logger.info(f"Search criteria: {search_criteria}")

        # Effettua la ricerca con Perplexity
        search_result = search_funding_opportunities(
            company_profile=company_profile,
            search_criteria=search_criteria,
            model="sonar-pro"
        )

        if not search_result.get('search_performed'):
            return api_response(
                message=f"Errore nella ricerca AI: {search_result.get('error', 'Errore sconosciuto')}",
                status_code=500
            )

        # Calcola match scores per le opportunità trovate
        opportunities = search_result.get('opportunities', [])
        for opportunity in opportunities:
            opportunity['match_score'] = calculate_funding_match_score(
                opportunity, 
                company_profile
            )

        # Ordina per match score decrescente
        opportunities.sort(key=lambda x: x.get('match_score', 0), reverse=True)

        # Opzione per salvare risultati nel database
        saved_opportunities = []
        if save_results and opportunities:
            for opp_data in opportunities[:5]:  # Salva solo i primi 5 risultati migliori
                try:
                    # Controlla se esiste già
                    existing = FundingOpportunity.query.filter_by(
                        title=opp_data.get('title', '')
                    ).first()

                    if not existing:
                        # Crea nuova opportunità
                        new_opportunity = FundingOpportunity(
                            title=opp_data.get('title', 'Bando trovato via AI'),
                            description=f"Bando trovato tramite ricerca AI.\n\n{search_result.get('content', '')}",
                            source_entity=opp_data.get('source_entity', 'Da definire'),
                            status='open',
                            geographic_scope='nazionale',
                            max_grant_amount=100000,  # Default
                            contribution_percentage=50,  # Default
                            application_deadline=date.today() + timedelta(days=60),  # Default
                            is_active=True,
                            created_by=current_user.id,
                            # Metadata AI
                            ai_generated=True,
                            ai_search_query=str(company_profile),
                            ai_match_score=opp_data.get('match_score', 0)
                        )

                        db.session.add(new_opportunity)
                        db.session.flush()  # Per ottenere l'ID
                        saved_opportunities.append(new_opportunity.to_dict())

                except Exception as save_error:
                    current_app.logger.error(f"Error saving AI opportunity: {save_error}")
                    continue

            if saved_opportunities:
                db.session.commit()
                current_app.logger.info(f"Saved {len(saved_opportunities)} AI opportunities to database")

        return api_response(
            data={
                'search_performed': True,
                'ai_content': search_result.get('content', ''),
                'citations': search_result.get('citations', []),
                'opportunities_found': len(opportunities),
                'opportunities': opportunities,
                'saved_opportunities': saved_opportunities,
                'model_used': search_result.get('model_used', 'sonar-pro'),
                'company_profile': company_profile
            },
            message=f"Ricerca AI completata. Trovate {len(opportunities)} opportunità."
        )

    except Exception as e:
        current_app.logger.error(f"Error in AI funding search: {str(e)}")
        return handle_api_error(e)


@api_funding.route('/ai-suggestions', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_VIEW_FUNDING)
def get_ai_funding_suggestions():
    """
    Ottiene suggerimenti AI per opportunità di finanziamento basati sul profilo aziendale.
    """
    try:
        data = request.get_json()
        company_profile = data.get('company_profile', {})

        if not company_profile:
            return api_response(
                message="Profilo aziendale richiesto",
                status_code=400
            )

        # Ottieni opportunità esistenti dal database
        opportunities = FundingOpportunity.query.filter_by(
            status='open',
            is_active=True
        ).all()

        # Calcola match scores per tutte le opportunità
        suggestions = []
        for opp in opportunities:
            match_score = calculate_funding_match_score(
                opp.to_dict(), 
                company_profile
            )

            if match_score > 60:  # Solo suggerimenti con alta compatibilità
                suggestion = opp.to_dict()
                suggestion['match_score'] = match_score
                suggestions.append(suggestion)

        # Ordina per match score decrescente
        suggestions.sort(key=lambda x: x.get('match_score', 0), reverse=True)

        return api_response(
            data={
                'suggestions': suggestions[:10],  # Top 10 suggerimenti
                'total_analyzed': len(opportunities),
                'high_match_count': len(suggestions)
            },
            message=f"Trovati {len(suggestions)} suggerimenti compatibili"
        )

    except Exception as e:
        current_app.logger.error(f"Error getting AI suggestions: {str(e)}")
        return handle_api_error(e)


@api_funding.route('/ai-match-score', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_VIEW_FUNDING)
def calculate_ai_match_score():
    """
    Calcola il match score AI per una specifica opportunità e profilo aziendale usando OpenAI.
    """
    try:
        data = request.get_json()
        opportunity_data = data.get('opportunity', {})
        company_profile = data.get('company_profile', {})

        if not opportunity_data or not company_profile:
            return api_response(
                message="Opportunità e profilo aziendale richiesti",
                status_code=400
            )

        # Genera analisi AI dettagliata usando OpenAI
        ai_analysis = generate_detailed_funding_analysis(
            company_profile,
            opportunity_data
        )

        # Calcola match score basato sull'analisi AI
        match_score = ai_analysis.get('match_score', 50)

        return api_response(
            data={
                'match_score': match_score,
                'insights': ai_analysis.get('insights', []),
                'recommendation': ai_analysis.get('recommendation', 'Analisi in corso...'),
                'detailed_analysis': ai_analysis.get('detailed_analysis', ''),
                'strengths': ai_analysis.get('strengths', []),
                'weaknesses': ai_analysis.get('weaknesses', []),
                'next_steps': ai_analysis.get('next_steps', []),
                'analysis_timestamp': datetime.utcnow().isoformat()
            },
            message="Analisi AI completata con successo"
        )

    except Exception as e:
        current_app.logger.error(f"Error calculating AI match score: {str(e)}")
        return handle_api_error(e)


@api_funding.route('/ai-status', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_FUNDING)
def get_ai_service_status():
    """
    Verifica lo stato del servizio AI e la configurazione Perplexity.
    """
    try:
        # Verifica configurazione Perplexity
        perplexity_configured = bool(current_app.config.get('PERPLEXITY_API_KEY'))

        # Test connessione AI (opzionale)
        ai_service_available = True
        error_message = None

        try:
            # Test rapido del servizio AI
            test_result = search_funding_opportunities(
                company_profile={'test': True},
                search_criteria={'test_mode': True},
                model="sonar-pro"
            )
            ai_service_available = test_result.get('search_performed', False)
            if not ai_service_available:
                error_message = test_result.get('error', 'Servizio AI non disponibile')
        except Exception as ai_error:
            ai_service_available = False
            error_message = str(ai_error)

        return api_response(
            data={
                'ai_service_available': ai_service_available,
                'perplexity_configured': perplexity_configured,
                'error_message': error_message,
                'features': {
                    'search': ai_service_available and perplexity_configured,
                    'suggestions': True,  # Sempre disponibile (usa logica locale)
                    'match_scoring': True  # Sempre disponibile (usa logica locale)
                },
                'status_timestamp': datetime.utcnow().isoformat()
            },
            message="Stato servizio AI verificato"
        )

    except Exception as e:
        current_app.logger.error(f"Error checking AI service status: {str(e)}")
        return handle_api_error(e)


def get_match_recommendation(match_score):
    """Genera raccomandazione basata sul match score"""
    if match_score >= 80:
        return "Altamente raccomandato - ottima compatibilità"
    elif match_score >= 60:
        return "Raccomandato - buona compatibilità"
    elif match_score >= 40:
        return "Da valutare - compatibilità media"
    else:
        return "Non raccomandato - bassa compatibilità"


# ============================================================================
# FUNDING EXPENSES (SPESE) ENDPOINTS
# ============================================================================

@api_funding.route('/expenses', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_FUNDING)
def get_funding_expenses():
    """
    Ottiene la lista delle spese di finanziamento con supporto per filtri.
    """
    try:
        page, per_page = get_pagination_params()

        # Parametri di ricerca
        application_id = request.args.get('application_id')
        category = request.args.get('category')
        status = request.args.get('status')  # approval_status: pending, approved, rejected
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')

        # Query base
        query = FundingExpense.query

        # Filtri di accesso: admin e manager vedono tutto, 
        # altri utenti vedono solo le spese delle proprie candidature
        if not current_user.has_permission(PERMISSION_MANAGE_FUNDING):
            # Trova le candidature dell'utente
            user_applications = FundingApplication.query.filter(
                or_(
                    FundingApplication.created_by == current_user.id,
                    FundingApplication.project_manager_id == current_user.id
                )
            ).with_entities(FundingApplication.id).all()

            user_application_ids = [app.id for app in user_applications]
            query = query.filter(FundingExpense.application_id.in_(user_application_ids))

        # Applica filtri
        if application_id:
            query = query.filter(FundingExpense.application_id == application_id)

        if category:
            query = query.filter(FundingExpense.category == category)

        if status:
            query = query.filter(FundingExpense.approval_status == status)

        # Filtri data
        if date_from:
            try:
                from_date = datetime.strptime(date_from, '%Y-%m-%d').date()
                query = query.filter(FundingExpense.expense_date >= from_date)
            except ValueError:
                pass

        if date_to:
            try:
                to_date = datetime.strptime(date_to, '%Y-%m-%d').date()
                query = query.filter(FundingExpense.expense_date <= to_date)
            except ValueError:
                pass

        # Ordina per data (più recenti prima)
        query = query.order_by(desc(FundingExpense.expense_date))

        # Paginazione
        expenses_paginated = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )

        # Serializza dati
        expenses_data = []
        for expense in expenses_paginated.items:
            # Ottieni informazioni sull'applicazione associata
            application = FundingApplication.query.get(expense.application_id)
            application_data = None
            if application:
                application_data = {
                    'id': application.id,
                    'project_title': application.project_title,
                    'status': application.status
                }

            # Crea dizionario con i dati della spesa
            expense_dict = {
                'id': expense.id,
                'description': expense.description,
                'amount': float(expense.amount),
                'expense_date': expense.expense_date.isoformat(),
                'category': expense.category,
                'approval_status': expense.approval_status,
                'is_eligible': expense.is_eligible,
                'notes': expense.notes,
                'application_id': expense.application_id,
                'application': application_data,
                'created_at': expense.created_at.isoformat()
            }

            expenses_data.append(expense_dict)

        return api_response(
            data={
                'expenses': expenses_data,
                'pagination': format_pagination(expenses_paginated)
            },
            message="Spese di finanziamento recuperate con successo"
        )

    except Exception as e:
        current_app.logger.error(f"Error getting funding expenses: {str(e)}")
        return handle_api_error(e)


@api_funding.route('/expenses', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_VIEW_FUNDING)
def create_funding_expense():
    """
    Crea una nuova spesa di finanziamento.
    """
    try:
        data = request.get_json()

        if not data:
            return api_response(
                success=False,
                message="Dati richiesta mancanti",
                status_code=400
            )

        # Valida campi obbligatori
        required_fields = ['application_id', 'description', 'amount', 'expense_date']
        missing_fields = [field for field in required_fields if not data.get(field)]

        if missing_fields:
            return api_response(
                success=False,
                message=f"Campi obbligatori mancanti: {', '.join(missing_fields)}",
                status_code=400
            )

        # Verifica che l'applicazione esista e l'utente abbia accesso
        application = FundingApplication.query.get(data['application_id'])
        if not application:
            return api_response(
                success=False,
                message="Candidatura non trovata",
                status_code=404
            )

        # Controllo accesso
        if not current_user.has_permission(PERMISSION_MANAGE_FUNDING):
            if application.created_by != current_user.id and application.project_manager_id != current_user.id:
                return api_response(
                    success=False,
                    message="Accesso non autorizzato a questa candidatura",
                    status_code=403
                )

        # Parsing data
        try:
            expense_date = datetime.strptime(data['expense_date'], '%Y-%m-%d').date()
        except ValueError:
            return api_response(
                success=False,
                message="Formato data non valido. Usare YYYY-MM-DD",
                status_code=400
            )

        # Crea nuova spesa
        expense = FundingExpense(
            application_id=data['application_id'],
            description=data['description'],
            amount=float(data['amount']),
            expense_date=expense_date,
            category=data.get('category', 'other'),
            is_eligible=data.get('is_eligible', True),
            notes=data.get('notes'),
            approval_status='pending'
        )

        db.session.add(expense)
        db.session.commit()

        # Prepara response con dati applicazione
        application_data = {
            'id': application.id,
            'project_title': application.project_title,
            'status': application.status
        }

        expense_data = {
            'id': expense.id,
            'description': expense.description,
            'amount': float(expense.amount),
            'expense_date': expense.expense_date.isoformat(),
            'category': expense.category,
            'approval_status': expense.approval_status,
            'is_eligible': expense.is_eligible,
            'notes': expense.notes,
            'application_id': expense.application_id,
            'application': application_data,
            'created_at': expense.created_at.isoformat()
        }

        return api_response(
            data={'expense': expense_data},
            message="Spesa creata con successo",
            status_code=201
        )

    except Exception as e:
        current_app.logger.error(f"Error creating funding expense: {str(e)}")
        return handle_api_error(e)


@api_funding.route('/expenses/<int:expense_id>', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_FUNDING)
def get_funding_expense(expense_id):
    """
    Ottiene i dettagli di una spesa specifica.
    """
    try:
        expense = FundingExpense.query.get_or_404(expense_id)

        # Verifica accesso
        application = FundingApplication.query.get(expense.application_id)
        if not current_user.has_permission(PERMISSION_MANAGE_FUNDING):
            if application.created_by != current_user.id and application.project_manager_id != current_user.id:
                return api_response(
                    success=False,
                    message="Accesso non autorizzato a questa spesa",
                    status_code=403
                )

        # Prepara dati applicazione
        application_data = None
        if application:
            application_data = {
                'id': application.id,
                'project_title': application.project_title,
                'status': application.status
            }

        expense_data = {
            'id': expense.id,
            'description': expense.description,
            'amount': float(expense.amount),
            'expense_date': expense.expense_date.isoformat(),
            'category': expense.category,
            'approval_status': expense.approval_status,
            'is_eligible': expense.is_eligible,
            'notes': expense.notes,
            'application_id': expense.application_id,
            'application': application_data,
            'created_at': expense.created_at.isoformat(),
            'updated_at': expense.updated_at.isoformat()
        }

        return api_response(
            data={'expense': expense_data},
            message="Spesa recuperata con successo"
        )

    except Exception as e:
        current_app.logger.error(f"Error getting funding expense: {str(e)}")
        return handle_api_error(e)


@api_funding.route('/expenses/<int:expense_id>', methods=['PUT'])
@login_required
@api_permission_required(PERMISSION_VIEW_FUNDING)
def update_funding_expense(expense_id):
    """
    Aggiorna una spesa esistente.
    """
    try:
        expense = FundingExpense.query.get_or_404(expense_id)

        # Verifica accesso
        application = FundingApplication.query.get(expense.application_id)
        if not current_user.has_permission(PERMISSION_MANAGE_FUNDING):
            if application.created_by != current_user.id and application.project_manager_id != current_user.id:
                return api_response(
                    success=False,
                    message="Accesso non autorizzato a questa spesa",
                    status_code=403
                )

        data = request.get_json()
        if not data:
            return api_response(
                success=False,
                message="Dati richiesta mancanti",
                status_code=400
            )

        # Aggiorna campi
        if 'description' in data:
            expense.description = data['description']
        if 'amount' in data:
            expense.amount = float(data['amount'])
        if 'expense_date' in data:
            try:
                expense.expense_date = datetime.strptime(data['expense_date'], '%Y-%m-%d').date()
            except ValueError:
                return api_response(
                    success=False,
                    message="Formato data non valido. Usare YYYY-MM-DD",
                    status_code=400
                )
        if 'category' in data:
            expense.category = data['category']
        if 'is_eligible' in data:
            expense.is_eligible = data['is_eligible']
        if 'notes' in data:
            expense.notes = data['notes']
        if 'approval_status' in data and current_user.has_permission(PERMISSION_MANAGE_FUNDING):
            expense.approval_status = data['approval_status']

        db.session.commit()

        # Prepara response
        application_data = {
            'id': application.id,
            'project_title': application.project_title,
            'status': application.status
        }

        expense_data = {
            'id': expense.id,
            'description': expense.description,
            'amount': float(expense.amount),
            'expense_date': expense.expense_date.isoformat(),
            'category': expense.category,
            'approval_status': expense.approval_status,
            'is_eligible': expense.is_eligible,
            'notes': expense.notes,
            'application_id': expense.application_id,
            'application': application_data,
            'created_at': expense.created_at.isoformat(),
            'updated_at': expense.updated_at.isoformat()
        }

        return api_response(
            data={'expense': expense_data},
            message="Spesa aggiornata con successo"
        )

    except Exception as e:
        current_app.logger.error(f"Error updating funding expense: {str(e)}")
        return handle_api_error(e)


@api_funding.route('/expenses/<int:expense_id>', methods=['DELETE'])
@login_required
@api_permission_required(PERMISSION_VIEW_FUNDING)
def delete_funding_expense(expense_id):
    """
    Elimina una spesa.
    """
    try:
        expense = FundingExpense.query.get_or_404(expense_id)

        # Verifica accesso
        application = FundingApplication.query.get(expense.application_id)
        if not current_user.has_permission(PERMISSION_MANAGE_FUNDING):
            if application.created_by != current_user.id and application.project_manager_id != current_user.id:
                return api_response(
                    success=False,
                    message="Accesso non autorizzato a questa spesa",
                    status_code=403
                )

        db.session.delete(expense)
        db.session.commit()

        return api_response(
            message="Spesa eliminata con successo"
        )

    except Exception as e:
        current_app.logger.error(f"Error deleting funding expense: {str(e)}")
        return handle_api_error(e)