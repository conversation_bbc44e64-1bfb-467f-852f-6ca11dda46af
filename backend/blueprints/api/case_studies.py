"""
API RESTful per Case Studies Business Intelligence.
Gestisce creazione, modifica, approvazione e generazione AI di case studies.
"""

from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from sqlalchemy import desc, and_, or_
from datetime import datetime

from extensions import db
from models import CaseStudy, Project, Client, User, Contract, TimesheetEntry, UserSkill
from utils.api_utils import api_response, handle_api_error
from utils.permissions import PERMISSION_VIEW_REPORTS, user_has_permission
from services.ai import generate_case_study_with_ai
from extensions import csrf
import json
import os

# Create blueprint
case_studies_api = Blueprint('case_studies_api', __name__)

@case_studies_api.route('/', methods=['GET'])
@login_required
def get_case_studies():
    """
    Recupera tutti i case studies con filtri opzionali.
    """
    try:
        if not user_has_permission(current_user.role, PERMISSION_VIEW_REPORTS):
            return api_response(
                message="Permessi insufficienti per visualizzare case studies",
                status_code=403
            )

        # Parametri di filtro
        case_type = request.args.get('case_type')  # use-case, success-story, case-study
        status = request.args.get('status')  # draft, approved, published
        sector = request.args.get('sector')
        project_id = request.args.get('project_id')
        
        # Query base
        query = CaseStudy.query
        
        # Applica filtri
        if case_type:
            query = query.filter(CaseStudy.case_type == case_type)
        if status:
            query = query.filter(CaseStudy.status == status)
        if sector:
            query = query.filter(
                or_(
                    CaseStudy.primary_sector.ilike(f'%{sector}%'),
                    CaseStudy.secondary_sectors.contains([sector])
                )
            )
        if project_id:
            query = query.filter(CaseStudy.project_id == project_id)
            
        # Ordina per data creazione (più recenti prima)
        case_studies = query.order_by(desc(CaseStudy.created_at)).all()
        
        # Serializza risultati
        result = []
        for cs in case_studies:
            case_study_data = cs.to_dict()
            result.append(case_study_data)
        
        return api_response(data={
            'case_studies': result,
            'total': len(result)
        })

    except Exception as e:
        current_app.logger.error(f"Error fetching case studies: {str(e)}")
        return handle_api_error(e)

@case_studies_api.route('/<int:case_study_id>', methods=['GET'])
@login_required
def get_case_study(case_study_id):
    """
    Recupera un case study specifico.
    """
    try:
        case_study = CaseStudy.query.get_or_404(case_study_id)
        
        if not user_has_permission(current_user.role, PERMISSION_VIEW_REPORTS):
            return api_response(
                message="Permessi insufficienti per visualizzare questo case study",
                status_code=403
            )
        
        return api_response(data=case_study.to_dict())

    except Exception as e:
        current_app.logger.error(f"Error fetching case study {case_study_id}: {str(e)}")
        return handle_api_error(e)

@case_studies_api.route('/', methods=['POST'])
@csrf.exempt
@login_required
def create_case_study():
    """
    Crea un nuovo case study.
    """
    try:
        if not user_has_permission(current_user.role, PERMISSION_VIEW_REPORTS):
            return api_response(
                message="Permessi insufficienti per creare case studies",
                status_code=403
            )

        data = request.json
        if not data:
            return api_response(
                message="Dati richiesti mancanti",
                status_code=400
            )

        # Validazione campi obbligatori
        required_fields = ['title', 'overview', 'case_type']
        for field in required_fields:
            if not data.get(field):
                return api_response(
                    message=f"Campo obbligatorio mancante: {field}",
                    status_code=400
                )

        # Crea il case study
        case_study = CaseStudy(
            title=data['title'],
            overview=data['overview'],
            content=data.get('content', ''),
            case_type=data['case_type'],
            primary_sector=data.get('primary_sector'),
            secondary_sectors=data.get('secondary_sectors', []),
            project_id=data.get('project_id'),
            client_id=data.get('client_id'),
            technologies=data.get('technologies', []),
            business_kpis=data.get('business_kpis', {}),
            implementation_duration=data.get('implementation_duration'),
            team_size=data.get('team_size'),
            target_audience=data.get('target_audience', 'internal'),
            generated_by_ai=data.get('generated_by_ai', False),
            ai_prompt_used=data.get('ai_prompt_used'),
            created_by=current_user.id
        )

        db.session.add(case_study)
        db.session.commit()

        return api_response(
            data=case_study.to_dict(),
            message="Case study creato con successo",
            status_code=201
        )

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error creating case study: {str(e)}")
        return handle_api_error(e)

@case_studies_api.route('/<int:case_study_id>', methods=['PUT'])
@csrf.exempt
@login_required
def update_case_study(case_study_id):
    """
    Aggiorna un case study esistente.
    """
    try:
        case_study = CaseStudy.query.get_or_404(case_study_id)
        
        # Verifica permessi (solo creatore o admin possono modificare)
        if case_study.created_by != current_user.id and not user_has_permission(current_user.role, 'admin_access'):
            return api_response(
                message="Non hai i permessi per modificare questo case study",
                status_code=403
            )

        data = request.json
        if not data:
            return api_response(
                message="Dati richiesti mancanti",
                status_code=400
            )

        # Aggiorna i campi
        updatable_fields = [
            'title', 'overview', 'content', 'case_type', 'primary_sector',
            'secondary_sectors', 'project_id', 'client_id', 'technologies',
            'business_kpis', 'implementation_duration', 'team_size', 'target_audience'
        ]
        
        for field in updatable_fields:
            if field in data:
                setattr(case_study, field, data[field])
        
        case_study.updated_at = datetime.utcnow()
        db.session.commit()

        return api_response(
            data=case_study.to_dict(),
            message="Case study aggiornato con successo"
        )

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating case study {case_study_id}: {str(e)}")
        return handle_api_error(e)

@case_studies_api.route('/<int:case_study_id>/approve', methods=['POST'])
@csrf.exempt
@login_required
def approve_case_study(case_study_id):
    """
    Approva un case study (solo manager/admin).
    """
    try:
        case_study = CaseStudy.query.get_or_404(case_study_id)
        
        if not user_has_permission(current_user.role, 'admin_access'):
            return api_response(
                message="Non hai i permessi per approvare case studies",
                status_code=403
            )

        case_study.status = 'approved'
        case_study.approved_by = current_user.id
        case_study.approved_at = datetime.utcnow()
        case_study.updated_at = datetime.utcnow()
        
        db.session.commit()

        return api_response(
            data=case_study.to_dict(),
            message="Case study approvato con successo"
        )

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error approving case study {case_study_id}: {str(e)}")
        return handle_api_error(e)

@case_studies_api.route('/<int:case_study_id>', methods=['DELETE'])
@csrf.exempt
@login_required
def delete_case_study(case_study_id):
    """
    Elimina un case study.
    """
    try:
        case_study = CaseStudy.query.get_or_404(case_study_id)
        
        # Verifica permessi (solo creatore o admin possono eliminare)
        if case_study.created_by != current_user.id and not user_has_permission(current_user.role, 'admin_access'):
            return api_response(
                message="Non hai i permessi per eliminare questo case study",
                status_code=403
            )

        db.session.delete(case_study)
        db.session.commit()

        return api_response(
            message="Case study eliminato con successo"
        )

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error deleting case study {case_study_id}: {str(e)}")
        return handle_api_error(e)

@case_studies_api.route('/available-projects', methods=['GET'])
@login_required
def get_available_projects():
    """
    Recupera progetti disponibili per generazione case studies.
    """
    try:
        if not user_has_permission(current_user.role, PERMISSION_VIEW_REPORTS):
            return api_response(
                message="Permessi insufficienti",
                status_code=403
            )

        # Progetti completati con successo (tutti i progetti con end_date)
        projects = Project.query.filter(
            Project.end_date.isnot(None)
        ).order_by(desc(Project.end_date)).all()

        result = []
        for project in projects:
            # Calcola metriche del progetto
            total_hours = db.session.query(
                db.func.sum(TimesheetEntry.hours)
            ).filter(TimesheetEntry.project_id == project.id).scalar() or 0
            
            project_data = {
                'id': project.id,
                'name': project.name,
                'description': project.description,
                'client_id': project.client_id,
                'client_name': project.client.name if project.client else None,
                'project_type': project.project_type,
                'budget': float(project.budget) if project.budget else 0,
                'start_date': project.start_date.isoformat() if project.start_date else None,
                'end_date': project.end_date.isoformat() if project.end_date else None,
                'total_hours': float(total_hours),
                'team_size': len(project.team_members),
                'technologies': [skill.skill.name for member in project.team_members 
                               for skill in member.detailed_skills],
                'has_case_study': len(project.case_studies) > 0
            }
            result.append(project_data)

        return api_response(data={
            'projects': result,
            'total': len(result)
        })

    except Exception as e:
        current_app.logger.error(f"Error fetching available projects: {str(e)}")
        return handle_api_error(e)

# Helper functions per AI generation
def extract_project_data_for_ai(project_id):
    """Estrae dati completi del progetto per generazione AI"""
    project = Project.query.get(project_id)
    if not project:
        return None

    # Calcola metriche
    total_hours = db.session.query(
        db.func.sum(TimesheetEntry.hours)
    ).filter(TimesheetEntry.project_id == project_id).scalar() or 0
    
    # Estrae tecnologie del team
    technologies = set()
    for member in project.team_members:
        for skill in member.detailed_skills:
            technologies.add(skill.skill.name)
    
    # Calcola timeline
    duration_days = None
    if project.start_date and project.end_date:
        duration_days = (project.end_date - project.start_date).days

    return {
        'project': {
            'id': project.id,
            'name': project.name,
            'description': project.description,
            'project_type': project.project_type,
            'budget': float(project.budget) if project.budget else 0,
            'duration_days': duration_days,
            'total_hours': float(total_hours),
            'team_size': len(project.team_members),
            'technologies': list(technologies)
        },
        'client': {
            'id': project.client.id if project.client else None,
            'name': project.client.name if project.client else None,
            'industry': project.client.industry if project.client else None
        },
        'contracts': [
            {
                'value': float(contract.value) if contract.value else 0,
                'type': contract.contract_type
            } for contract in project.contracts
        ] if hasattr(project, 'contracts') else []
    }

@case_studies_api.route('/generate-with-ai', methods=['POST'])
@csrf.exempt
@login_required
def generate_case_study_with_ai_endpoint():
    """
    Genera un case study utilizzando AI.
    Può basarsi su un progetto esistente o su prompt libero.
    """
    try:
        if not user_has_permission(current_user.role, PERMISSION_VIEW_REPORTS):
            return api_response(
                message="Permessi insufficienti per generare case studies",
                status_code=403
            )

        data = request.json
        if not data:
            return api_response(
                message="Dati richiesti mancanti",
                status_code=400
            )

        # Modalità 1: Basato su progetto esistente
        if data.get('project_id'):
            project_data = extract_project_data_for_ai(data['project_id'])
            if not project_data:
                return api_response(
                    message="Progetto non trovato",
                    status_code=404
                )
            
            # Prepara prompt per AI basato su progetto reale
            ai_prompt = f"""
            Genera un case study professionale basato su questo progetto reale:
            
            PROGETTO: {project_data['project']['name']}
            CLIENTE: {project_data['client']['name']}
            SETTORE: {project_data['client']['industry'] or 'Non specificato'}
            TIPO: {project_data['project']['project_type']}
            BUDGET: €{project_data['project']['budget']:,.0f}
            DURATA: {project_data['project']['duration_days']} giorni
            TEAM: {project_data['project']['team_size']} persone
            ORE TOTALI: {project_data['project']['total_hours']:.0f}h
            TECNOLOGIE: {', '.join(project_data['project']['technologies'])}
            
            TIPO CASE STUDY: {data.get('case_type', 'use-case')}
            TARGET: {data.get('target_audience', 'internal')}
            FOCUS: {data.get('focus_points', 'ROI e benefici')}
            
            Genera un case study che includa:
            1. OVERVIEW: Panoramica generale del progetto e obiettivi
            2. SFIDA: Problemi/esigenze del cliente
            3. SOLUZIONE: Approccio tecnologico e metodologico
            4. IMPLEMENTAZIONE: Timeline e processo
            5. RISULTATI: KPI business misurabili
            6. TECNOLOGIE: Stack tecnologico utilizzato
            7. ESTENDIBILITÀ: Altri settori applicabili
            
            Stile: Professionale, orientato ai risultati, con metriche concrete.
            """

        # Modalità 2: Prompt libero
        else:
            if not data.get('custom_prompt'):
                return api_response(
                    message="Prompt personalizzato obbligatorio se non si specifica un progetto",
                    status_code=400
                )
            
            ai_prompt = f"""
            Genera un case study professionale basato su queste specifiche:
            
            TIPO: {data.get('case_type', 'use-case')}
            SETTORE: {data.get('sector', 'Generale')}
            TARGET: {data.get('target_audience', 'internal')}
            
            RICHIESTA:
            {data['custom_prompt']}
            
            Genera un case study strutturato con:
            1. OVERVIEW
            2. SFIDA
            3. SOLUZIONE
            4. IMPLEMENTAZIONE
            5. RISULTATI
            6. TECNOLOGIE
            7. ESTENDIBILITÀ
            
            Includi KPI business concreti e metriche misurabili.
            """
            
            project_data = None

        # Carica configurazione tenant per info azienda
        try:
            tenant_config_path = os.path.join(current_app.root_path, 'config', 'tenant_config.json')
            with open(tenant_config_path, 'r', encoding='utf-8') as f:
                tenant_config = json.load(f)
        except Exception as e:
            current_app.logger.warning(f"Could not load tenant config: {e}")
            tenant_config = {}
        
        company_context = {
            'name': tenant_config.get('company', {}).get('name', 'La nostra azienda'),
            'description': tenant_config.get('company', {}).get('description', ''),
            'expertise': tenant_config.get('company', {}).get('expertise', []),
            'team_size': tenant_config.get('company', {}).get('team_size', ''),
            'founded': tenant_config.get('company', {}).get('founded', '')
        }

        # Genera contenuto con AI
        current_app.logger.info(f"Generating case study with AI for user {current_user.id}")
        
        ai_result = generate_case_study_with_ai(
            prompt=ai_prompt,
            case_type=data.get('case_type', 'use-case'),
            target_audience=data.get('target_audience', 'internal'),
            project_context=project_data,
            company_context=company_context
        )

        # Prepara risposta con contenuto generato
        response_data = {
            'generated_content': ai_result,
            'source_project': project_data['project'] if project_data else None,
            'ai_prompt_used': ai_prompt,
            'generation_timestamp': datetime.utcnow().isoformat()
        }

        return api_response(
            data=response_data,
            message="Case study generato con successo"
        )

    except Exception as e:
        current_app.logger.error(f"Error generating case study with AI: {str(e)}")
        return handle_api_error(e)

@case_studies_api.route('/sectors', methods=['GET'])
@login_required
def get_available_sectors():
    """
    Recupera settori disponibili dai progetti e clienti esistenti.
    """
    try:
        # Settori dai clienti
        client_industries = db.session.query(Client.industry).filter(
            Client.industry.isnot(None)
        ).distinct().all()
        
        # Settori dai case studies esistenti
        case_study_sectors = db.session.query(CaseStudy.primary_sector).filter(
            CaseStudy.primary_sector.isnot(None)
        ).distinct().all()
        
        # Combina e rimuovi duplicati
        sectors = set()
        for (industry,) in client_industries:
            if industry:
                sectors.add(industry)
        
        for (sector,) in case_study_sectors:
            if sector:
                sectors.add(sector)
        
        # Aggiungi settori standard se non presenti
        standard_sectors = [
            'FinTech', 'Healthcare', 'E-commerce', 'Education', 'Manufacturing',
            'Retail', 'Consulting', 'Real Estate', 'Technology', 'Automotive'
        ]
        sectors.update(standard_sectors)
        
        return api_response(data={
            'sectors': sorted(list(sectors))
        })

    except Exception as e:
        current_app.logger.error(f"Error fetching sectors: {str(e)}")
        return handle_api_error(e)

@case_studies_api.route('/technologies', methods=['GET'])
@login_required
def get_available_technologies():
    """
    Recupera tecnologie disponibili dai progetti e case studies esistenti.
    """
    try:
        # Tecnologie dalle skills degli utenti
        user_skills = db.session.query(UserSkill.skill_id).distinct().all()
        skill_names = []
        
        for (skill_id,) in user_skills:
            skill = db.session.query(UserSkill).filter_by(skill_id=skill_id).first()
            if skill and skill.skill:
                skill_names.append(skill.skill.name)
        
        # Tecnologie dai case studies esistenti
        case_studies = CaseStudy.query.filter(
            CaseStudy.technologies.isnot(None)
        ).all()
        
        tech_set = set(skill_names)
        for cs in case_studies:
            if cs.technologies:
                tech_set.update(cs.technologies)
        
        # Aggiungi tecnologie standard
        standard_techs = [
            'React', 'Vue.js', 'Angular', 'Node.js', 'Python', 'Django', 'Flask',
            'PostgreSQL', 'MongoDB', 'AWS', 'Azure', 'Docker', 'Kubernetes',
            'TypeScript', 'JavaScript', 'PHP', 'Laravel', 'Next.js', 'Nuxt.js'
        ]
        tech_set.update(standard_techs)
        
        return api_response(data={
            'technologies': sorted(list(tech_set))
        })

    except Exception as e:
        current_app.logger.error(f"Error fetching technologies: {str(e)}")
        return handle_api_error(e)