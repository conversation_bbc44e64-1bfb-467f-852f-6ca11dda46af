"""
API RESTful per la gestione dei task.
"""
from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from sqlalchemy import desc
from datetime import datetime, date, timedelta
from models import Task, Project, User
from utils.api_utils import (
    api_response, handle_api_error, get_pagination_params,
    format_pagination, api_permission_required
)
from utils.permissions import (
    PERMISSION_VIEW_ALL_PROJECTS, PERMISSION_MANAGE_PROJECT_TASKS,
    user_has_permission
)
from extensions import db, csrf
from blueprints.api.notifications import create_notification_for_user

# Crea il blueprint per le API dei task
api_tasks = Blueprint('api_tasks', __name__, url_prefix='/tasks')

@api_tasks.route('/', methods=['GET'])
@login_required
def get_tasks():
    """
    Ottiene la lista dei task con supporto per filtri e paginazione.
    ---
    tags:
      - tasks
    parameters:
      - $ref: '#/components/parameters/pageParam'
      - $ref: '#/components/parameters/perPageParam'
      - name: project_id
        in: query
        description: Filtra per ID progetto
        schema:
          type: integer
      - name: status
        in: query
        description: Filtra per stato del task
        schema:
          type: string
          enum: [todo, in-progress, review, done]
      - name: priority
        in: query
        description: Filtra per priorità del task
        schema:
          type: string
          enum: [low, medium, high, urgent]
      - name: assignee_id
        in: query
        description: Filtra per ID dell'assegnatario
        schema:
          type: integer
      - name: search
        in: query
        description: Cerca nei nomi e nelle descrizioni dei task
        schema:
          type: string
    responses:
      200:
        description: Lista di task
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    tasks:
                      type: array
                      items:
                        $ref: '#/components/schemas/Task'
                pagination:
                  $ref: '#/components/schemas/Pagination'
      401:
        $ref: '#/components/responses/Unauthorized'
    """
    try:
        # Ottieni parametri di paginazione
        page, per_page = get_pagination_params()

        # Inizia la query
        query = Task.query

        # Applica filtri
        project_id = request.args.get('project_id', type=int)
        if project_id:
            query = query.filter(Task.project_id == project_id)

        status = request.args.get('status')
        if status:
            query = query.filter(Task.status == status)

        priority = request.args.get('priority')
        if priority:
            query = query.filter(Task.priority == priority)

        assignee_id = request.args.get('assignee_id', type=int)
        if assignee_id:
            query = query.filter(Task.assignee_id == assignee_id)

        search = request.args.get('search')
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                (Task.name.ilike(search_term)) |
                (Task.description.ilike(search_term))
            )

        # Filtra per permessi
        if not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
            # Se l'utente non ha il permesso di vedere tutti i progetti,
            # mostra solo i task assegnati a lui o dei progetti a cui è assegnato
            query = query.filter(
                (Task.assignee_id == current_user.id) |
                (Task.project_id.in_(
                    db.session.query(Project.id).join(
                        Project.team_members
                    ).filter(User.id == current_user.id)
                ))
            )

        # Applica ordinamento
        query = query.order_by(desc(Task.updated_at))

        # Esegui query con paginazione
        pagination = query.paginate(page=page, per_page=per_page)

        # Prepara i dati dei task
        tasks_data = []
        for task in pagination.items:
            tasks_data.append({
                'id': task.id,
                'name': task.name,
                'description': task.description,
                'project_id': task.project_id,
                'assignee_id': task.assignee_id,
                'status': task.status,
                'priority': task.priority,
                'start_date': task.start_date.isoformat() if task.start_date else None,  # Aggiunto
                'due_date': task.due_date.isoformat() if task.due_date else None,
                'estimated_hours': task.estimated_hours,  # Aggiunto
                'created_at': task.created_at.isoformat(),
                'updated_at': task.updated_at.isoformat()
            })

        # Restituisci risposta
        return api_response(
            data={'tasks': tasks_data},
            pagination=format_pagination(pagination)
        )
    except Exception as e:
        current_app.logger.error(f"Error in get_tasks: {str(e)}")
        return handle_api_error(e)

@api_tasks.route('/<int:task_id>', methods=['GET'])
@login_required
def get_task(task_id):
    """
    Ottiene i dettagli di un task specifico.
    ---
    tags:
      - tasks
    parameters:
      - name: task_id
        in: path
        required: true
        description: ID del task
        schema:
          type: integer
    responses:
      200:
        description: Dettagli del task
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    task:
                      $ref: '#/components/schemas/Task'
      404:
        $ref: '#/components/responses/NotFound'
      401:
        $ref: '#/components/responses/Unauthorized'
    """
    try:
        task = Task.query.get_or_404(task_id)

        # Verifica permessi
        if not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
            # Verifica se l'utente è assegnato al task o al progetto
            is_assignee = task.assignee_id == current_user.id
            is_in_project = current_user in task.project.team_members if task.project else False

            if not (is_assignee or is_in_project):
                return api_response(
                    message="Non hai i permessi per visualizzare questo task",
                    status_code=403
                )

        # Prepara i dati del task
        task_data = {
            'id': task.id,
            'name': task.name,
            'description': task.description,
            'project_id': task.project_id,
            'assignee_id': task.assignee_id,
            'status': task.status,
            'priority': task.priority,
            'start_date': task.start_date.isoformat() if task.start_date else None,  # Aggiunto
            'due_date': task.due_date.isoformat() if task.due_date else None,
            'estimated_hours': task.estimated_hours,  # Aggiunto
            'created_at': task.created_at.isoformat(),
            'updated_at': task.updated_at.isoformat()
        }

        return api_response(data={'task': task_data})
    except Exception as e:
        current_app.logger.error(f"Error in get_task: {str(e)}")
        return handle_api_error(e)

@api_tasks.route('/', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_MANAGE_PROJECT_TASKS)
@csrf.exempt  # Aggiunto per evitare errori CSRF nelle richieste API
def create_task():
    """
    Crea un nuovo task.
    ---
    tags:
      - tasks
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - name
              - project_id
            properties:
              name:
                type: string
                description: Nome del task
              description:
                type: string
                description: Descrizione del task
              project_id:
                type: integer
                description: ID del progetto associato
              assignee_id:
                type: integer
                description: ID dell'utente assegnato al task
              status:
                type: string
                enum: [todo, in-progress, review, done]
                default: todo
                description: Stato del task
              priority:
                type: string
                enum: [low, medium, high, urgent]
                default: medium
                description: Priorità del task
              start_date:
                type: string
                format: date
                description: Data di inizio pianificata (YYYY-MM-DD)
              due_date:
                type: string
                format: date
                description: Data di scadenza (YYYY-MM-DD)
              estimated_hours:
                type: number
                format: float
                description: Ore stimate per completare il task
    responses:
      201:
        description: Task creato con successo
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    task:
                      $ref: '#/components/schemas/Task'
                    message:
                      type: string
                      example: Task creato con successo
      400:
        description: Dati non validi
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: false
                message:
                  type: string
                  example: Nome del task e ID progetto obbligatori
      401:
        $ref: '#/components/responses/Unauthorized'
      403:
        $ref: '#/components/responses/Forbidden'
    """
    try:
        # Ottieni i dati dalla richiesta
        data = request.json
        current_app.logger.info(f"Create task data: {data}")

        # Validazione dei dati
        if not data or not data.get('name') or not data.get('project_id'):
            return api_response(
                message="Nome del task e ID progetto obbligatori",
                status_code=400
            )

        # Verifica che il progetto esista
        project = Project.query.get(data.get('project_id'))
        if not project:
            return api_response(
                message="Progetto non trovato",
                status_code=404
            )

        # Crea il nuovo task
        new_task = Task(
            name=data.get('name'),
            description=data.get('description', ''),
            project_id=data.get('project_id'),
            assignee_id=data.get('assignee_id'),
            status=data.get('status', 'todo'),
            priority=data.get('priority', 'medium'),
            start_date=data.get('start_date'),
            due_date=data.get('due_date'),
            estimated_hours=data.get('estimated_hours')
        )

        # Aggiungi il task al database
        db.session.add(new_task)
        db.session.commit()

        # Notifica all'utente assegnato se presente
        if new_task.assignee_id and new_task.assignee_id != current_user.id:
            create_notification_for_user(
                user_id=new_task.assignee_id,
                title="Nuovo task assegnato",
                message=f"Ti è stato assegnato il task '{new_task.name}' nel progetto {project.name}",
                notification_type="info",
                link=f"/projects/{project.id}/tasks/{new_task.id}"
            )

        # Prepara i dati del task per la risposta
        task_data = {
            'id': new_task.id,
            'name': new_task.name,
            'description': new_task.description,
            'project_id': new_task.project_id,
            'assignee_id': new_task.assignee_id,
            'status': new_task.status,
            'priority': new_task.priority,
            'start_date': new_task.start_date.isoformat() if new_task.start_date else None,
            'due_date': new_task.due_date.isoformat() if new_task.due_date else None,
            'estimated_hours': new_task.estimated_hours,
            'created_at': new_task.created_at.isoformat(),
            'updated_at': new_task.updated_at.isoformat()
        }

        return api_response(
            data={'task': task_data},
            message="Task creato con successo",
            status_code=201
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error in create_task: {str(e)}")
        return handle_api_error(e)

@api_tasks.route('/<int:task_id>', methods=['PUT'])
@login_required
@api_permission_required(PERMISSION_MANAGE_PROJECT_TASKS)
@csrf.exempt  # Aggiunto per evitare errori CSRF nelle richieste API
def update_task(task_id):
    """
    Aggiorna un task esistente.
    ---
    tags:
      - tasks
    parameters:
      - name: task_id
        in: path
        required: true
        description: ID del task
        schema:
          type: integer
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/TaskInput'
    responses:
      200:
        description: Task aggiornato con successo
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    task:
                      $ref: '#/components/schemas/Task'
                message:
                  type: string
                  example: Task aggiornato con successo
      404:
        $ref: '#/components/responses/NotFound'
      401:
        $ref: '#/components/responses/Unauthorized'
      403:
        $ref: '#/components/responses/Forbidden'
    """
    try:
        # Ottieni il task dal database
        task = Task.query.get_or_404(task_id)

        # Ottieni i dati dalla richiesta
        data = request.json
        current_app.logger.info(f"Update task data: {data}")
        
        if not data:
            return api_response(
                message="Nessun dato fornito per l'aggiornamento",
                status_code=400
            )

        # Memorizza il vecchio assignee per le notifiche
        old_assignee_id = task.assignee_id
        
        # Aggiorna i campi del task
        if 'name' in data:
            task.name = data['name']
        if 'description' in data:
            task.description = data['description']
        if 'project_id' in data:
            # Verifica che il progetto esista
            project = Project.query.get(data['project_id'])
            if not project:
                return api_response(
                    message="Progetto non trovato",
                    status_code=404
                )
            task.project_id = data['project_id']
        if 'assignee_id' in data:
            # Verifica che l'utente esista se specificato
            if data['assignee_id']:
                user = User.query.get(data['assignee_id'])
                if not user:
                    return api_response(
                        message="Utente non trovato",
                        status_code=404
                    )
            task.assignee_id = data['assignee_id']
        if 'status' in data:
            task.status = data['status']
        if 'priority' in data:
            task.priority = data['priority']
        if 'start_date' in data:
            task.start_date = data['start_date']
        if 'due_date' in data:
            task.due_date = data['due_date']
        if 'estimated_hours' in data:
            task.estimated_hours = data['estimated_hours']

        # Commit delle modifiche
        db.session.commit()

        # Gestisci notifiche per cambio assegnazione
        if 'assignee_id' in data and task.assignee_id != old_assignee_id:
            # Notifica al nuovo assignee (se diverso da chi ha fatto il cambiamento)
            if task.assignee_id and task.assignee_id != current_user.id:
                create_notification_for_user(
                    user_id=task.assignee_id,
                    title="Task assegnato",
                    message=f"Ti è stato assegnato il task '{task.name}' nel progetto {task.project.name}",
                    notification_type="info",
                    link=f"/projects/{task.project_id}/tasks/{task.id}"
                )
            
            # Notifica al vecchio assignee della rimozione (se diverso da chi ha fatto il cambiamento)
            if old_assignee_id and old_assignee_id != current_user.id and old_assignee_id != task.assignee_id:
                create_notification_for_user(
                    user_id=old_assignee_id,
                    title="Task rimosso",
                    message=f"Il task '{task.name}' ti è stato rimosso",
                    notification_type="warning",
                    link=f"/projects/{task.project_id}"
                )

        # Prepara i dati del task per la risposta
        task_data = {
            'id': task.id,
            'name': task.name,
            'description': task.description,
            'project_id': task.project_id,
            'assignee_id': task.assignee_id,
            'status': task.status,
            'priority': task.priority,
            'start_date': task.start_date.isoformat() if task.start_date else None,
            'due_date': task.due_date.isoformat() if task.due_date else None,
            'estimated_hours': task.estimated_hours,
            'created_at': task.created_at.isoformat(),
            'updated_at': task.updated_at.isoformat()
        }

        return api_response(
            data={'task': task_data},
            message="Task aggiornato con successo"
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error in update_task: {str(e)}")
        return handle_api_error(e)

@api_tasks.route('/status', methods=['PATCH'])
@login_required
@api_permission_required(PERMISSION_MANAGE_PROJECT_TASKS)
def bulk_update_task_status():
    """
    Aggiorna lo stato di più task contemporaneamente.
    ---
    tags:
      - tasks
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - tasks
            properties:
              tasks:
                type: array
                items:
                  type: object
                  required:
                    - id
                    - status
                  properties:
                    id:
                      type: integer
                      description: ID del task
                    status:
                      type: string
                      enum: [todo, in-progress, review, done]
                      description: Nuovo stato del task
    responses:
      200:
        description: Stato dei task aggiornato con successo
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    results:
                      type: array
                      items:
                        type: object
                        properties:
                          task_id:
                            type: integer
                            example: 1
                          success:
                            type: boolean
                            example: true
                          message:
                            type: string
                            example: Stato aggiornato con successo
                    message:
                      type: string
                      example: Stato dei task aggiornato con successo
      400:
        description: Dati non validi
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: false
                message:
                  type: string
                  example: Dati non validi
      401:
        $ref: '#/components/responses/Unauthorized'
      403:
        $ref: '#/components/responses/Forbidden'
    """
    try:
        # Ottieni i dati dalla richiesta
        data = request.json

        # Validazione dei dati
        if not data or 'tasks' not in data or not isinstance(data['tasks'], list):
            return api_response(
                message="Dati non validi: è richiesta una lista di task",
                status_code=400
            )

        # Risultati delle operazioni
        results = []

        # Aggiorna lo stato dei task
        for task_data in data['tasks']:
            # Validazione dei dati del task
            if 'id' not in task_data or 'status' not in task_data:
                results.append({
                    'task_id': task_data.get('id', 'unknown'),
                    'success': False,
                    'message': "ID task e stato obbligatori"
                })
                continue

            # Verifica che lo stato sia valido
            if task_data['status'] not in ['todo', 'in-progress', 'review', 'done']:
                results.append({
                    'task_id': task_data['id'],
                    'success': False,
                    'message': f"Stato '{task_data['status']}' non valido"
                })
                continue

            try:
                # Ottieni il task dal database
                task = Task.query.get(task_data['id'])
                if not task:
                    results.append({
                        'task_id': task_data['id'],
                        'success': False,
                        'message': "Task non trovato"
                    })
                    continue

                # Aggiorna lo stato del task
                old_status = task.status
                task.status = task_data['status']

                results.append({
                    'task_id': task.id,
                    'success': True,
                    'old_status': old_status,
                    'new_status': task.status,
                    'message': f"Stato aggiornato da '{old_status}' a '{task.status}'"
                })
            except Exception as e:
                results.append({
                    'task_id': task_data['id'],
                    'success': False,
                    'message': f"Errore durante l'aggiornamento: {str(e)}"
                })

        # Commit delle modifiche
        db.session.commit()

        # Conta i successi e i fallimenti
        success_count = sum(1 for result in results if result['success'])
        failure_count = len(results) - success_count

        return api_response(
            data={'results': results},
            message=f"Aggiornamento completato: {success_count} task aggiornati, {failure_count} falliti"
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error in bulk_update_task_status: {str(e)}")
        return handle_api_error(e)

@api_tasks.route('/<int:task_id>', methods=['DELETE'])
@login_required
@api_permission_required(PERMISSION_MANAGE_PROJECT_TASKS)
def delete_task(task_id):
    """
    Elimina un task esistente.
    ---
    tags:
      - tasks
    parameters:
      - name: task_id
        in: path
        required: true
        description: ID del task da eliminare
        schema:
          type: integer
    responses:
      200:
        description: Task eliminato con successo
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                message:
                  type: string
                  example: Task eliminato con successo
      404:
        $ref: '#/components/responses/NotFound'
      401:
        $ref: '#/components/responses/Unauthorized'
      403:
        $ref: '#/components/responses/Forbidden'
    """
    try:
        # Ottieni il task dal database
        task = Task.query.get_or_404(task_id)

        # Salva il nome del task per il messaggio di risposta
        task_name = task.name

        # Elimina il task
        db.session.delete(task)
        db.session.commit()

        return api_response(
            message=f"Task '{task_name}' eliminato con successo"
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error in delete_task: {str(e)}")
        return handle_api_error(e)


@api_tasks.route('/<int:project_id>/generate-ai', methods=['POST'])
@csrf.exempt
@login_required
def generate_tasks_with_ai(project_id):
    """
    Genera task automaticamente usando AI per un progetto specifico.
    Analizza il progetto e crea un set completo di task per tutte le fasi di implementazione.
    """
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, PERMISSION_MANAGE_PROJECT_TASKS):
            return api_response(False, 'Non hai i permessi per generare task', status_code=403)

        # Verifica che il progetto esista
        project = Project.query.get_or_404(project_id)
        
        # Verifica permessi sul progetto specifico
        if not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
            if current_user not in project.team_members:
                return api_response(False, 'Non hai i permessi per questo progetto', status_code=403)

        # Dati della richiesta
        data = request.get_json() or {}
        
        # Raccogli dati del progetto per il contesto AI
        project_context = {
            "name": project.name,
            "description": project.description,
            "project_type": project.project_type,
            "budget": project.budget,
            "start_date": project.start_date.isoformat() if project.start_date else None,
            "end_date": project.end_date.isoformat() if project.end_date else None,
            "client": {
                "name": project.client.name,
                "industry": project.client.industry
            } if project.client else None,
            "team_size": len(project.team_members) if project.team_members else 1
        }

        # Calcola durata progetto in giorni lavorativi
        project_duration_days = None
        if project.start_date and project.end_date:
            total_days = (project.end_date - project.start_date).days
            project_duration_days = max(1, int(total_days * 0.714))  # Circa 5/7 per giorni lavorativi

        # Prompt AI strutturato per la generazione task
        ai_prompt = f"""
        Sei un Project Manager esperto che deve creare una WBS (Work Breakdown Structure) completa per il seguente progetto software/consulenza.

        DATI PROGETTO:
        - Nome: {project_context['name']}
        - Descrizione: {project_context['description']}
        - Tipo: {project_context['project_type']}
        - Budget: €{project_context['budget']} 
        - Durata stimata: {project_duration_days or 'Non specificata'} giorni lavorativi
        - Dimensione team: {project_context['team_size']} persone
        - Cliente: {project_context['client']['name'] if project_context['client'] else 'Interno'} ({project_context['client']['industry'] if project_context['client'] else 'Non specificato'})

        ISTRUZIONI:
        Crea una lista completa di task che copra TUTTE le fasi del progetto, incluse:
        1. **Analisi e Pianificazione** (10-15% del tempo)
        2. **Design e Architettura** (15-20% del tempo)  
        3. **Sviluppo/Implementazione** (50-60% del tempo)
        4. **Testing e QA** (10-15% del tempo)
        5. **Deployment e Go-Live** (3-5% del tempo)
        6. **Supporto Post-Produzione** (2-5% del tempo)

        Per ogni task fornire:
        - Nome chiaro e specifico (max 80 caratteri)
        - Descrizione dettagliata del deliverable
        - Priorità (urgent/high/medium/low)
        - Ore stimate realistiche
        - Dipendenze logiche tra task

        REGOLE:
        - Minimo 8-12 task, massimo 25 task
        - Distribuzione realistica delle ore basata sul budget
        - Task specifici e actionable, non generici
        - Includere sempre: kick-off, milestone di revisione, testing, deploy, handover
        - Considerare il tipo di progetto e settore cliente
        - Se budget alto (>€50k) = più task dettagliati
        - Se durata lunga (>60gg) = suddivisione in fasi più granulari

        FORMATO RICHIESTO - Restituisci SOLO questo JSON valido:
        {{
            "project_analysis": {{
                "complexity_level": "low|medium|high|enterprise",
                "estimated_total_hours": numero_ore_totali,
                "methodology_recommendation": "agile|waterfall|hybrid"
            }},
            "tasks": [
                {{
                    "name": "Nome task conciso e chiaro",
                    "description": "Descrizione dettagliata del deliverable, accettazione criteri e output attesi. Essere specifici su cosa deve essere consegnato.",
                    "priority": "urgent|high|medium|low",
                    "estimated_hours": numero_decimale,
                    "phase": "analysis|design|development|testing|deployment|support",
                    "dependencies": ["task_1", "task_2"],
                    "order_index": numero_sequenza
                }}
            ]
        }}
        """

        # Chiamata a OpenAI GPT-4
        from services.ai import get_openai_client
        import json
        
        try:
            client = get_openai_client()
            response = client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": "Sei un Project Manager esperto con 15+ anni di esperienza in progetti software e consulenza. Rispondi SEMPRE con JSON valido senza testo aggiuntivo."},
                    {"role": "user", "content": ai_prompt}
                ],
                temperature=0.1,  # Bassa temperatura per output più consistente
                response_format={"type": "json_object"},
            )
            
            ai_response = response.choices[0].message.content
            ai_data = json.loads(ai_response)
            
            # Validazione e pulizia dati AI
            if "tasks" not in ai_data or not isinstance(ai_data["tasks"], list):
                raise ValueError("Risposta AI non valida: manca array tasks")
                
            if len(ai_data["tasks"]) < 5:
                raise ValueError("Risposta AI insufficiente: troppo pochi task generati")
            
        except Exception as e:
            print(f"AI Generation Error: {str(e)}")
            # Fallback con template di task base
            ai_data = {
                "project_analysis": {
                    "complexity_level": "medium",
                    "estimated_total_hours": project_context.get('budget', 10000) / 50,  # €50/ora media
                    "methodology_recommendation": "agile"
                },
                "tasks": [
                    {
                        "name": "Kick-off e Analisi Requisiti",
                        "description": "Incontro iniziale con stakeholder, raccolta requisiti funzionali e non funzionali, definizione scope e deliverable",
                        "priority": "high",
                        "estimated_hours": 16,
                        "phase": "analysis",
                        "dependencies": [],
                        "order_index": 1
                    },
                    {
                        "name": "Design Architetturale",
                        "description": "Progettazione architettura tecnica, definizione componenti, API design, database schema",
                        "priority": "high", 
                        "estimated_hours": 24,
                        "phase": "design",
                        "dependencies": ["Kick-off e Analisi Requisiti"],
                        "order_index": 2
                    },
                    {
                        "name": "Sviluppo Core Features",
                        "description": "Implementazione delle funzionalità principali identificate nei requisiti",
                        "priority": "high",
                        "estimated_hours": 80,
                        "phase": "development", 
                        "dependencies": ["Design Architetturale"],
                        "order_index": 3
                    },
                    {
                        "name": "Testing e Quality Assurance",
                        "description": "Test unitari, test di integrazione, test end-to-end, correzione bug",
                        "priority": "medium",
                        "estimated_hours": 32,
                        "phase": "testing",
                        "dependencies": ["Sviluppo Core Features"],
                        "order_index": 4
                    },
                    {
                        "name": "Deployment e Go-Live",
                        "description": "Rilascio in produzione, configurazione ambienti, monitoraggio post-deploy",
                        "priority": "high",
                        "estimated_hours": 16,
                        "phase": "deployment",
                        "dependencies": ["Testing e Quality Assurance"],
                        "order_index": 5
                    },
                    {
                        "name": "Handover e Supporto",
                        "description": "Formazione utenti, documentazione, supporto post go-live per 2 settimane",
                        "priority": "medium",
                        "estimated_hours": 12,
                        "phase": "support",
                        "dependencies": ["Deployment e Go-Live"],
                        "order_index": 6
                    }
                ]
            }

        # Calcola date per i task basandosi sulla durata del progetto
        
        # Data inizio: oggi o data inizio progetto
        start_date = project.start_date if project.start_date else date.today()
        
        # Ordina i task per order_index
        tasks_sorted = sorted(ai_data["tasks"], key=lambda x: x.get("order_index", 0))
        
        generated_tasks = []
        current_date = start_date
        
        for i, task_data in enumerate(tasks_sorted):
            # Calcola durata task in giorni (stima: 8 ore = 1 giorno, minimo 1 giorno)
            estimated_hours = float(task_data.get("estimated_hours", 8))
            task_duration_days = max(1, int(estimated_hours / 8))
            
            # Data fine task (esclude weekend se necessario)
            task_end_date = current_date + timedelta(days=task_duration_days - 1)  # -1 perché il giorno di inizio conta
            
            # Prepara dati task formattati - SEMPRE valorizzare start_date e due_date per Gantt
            task_info = {
                "name": task_data.get("name", f"Task {i+1}")[:127],  # Limite database
                "description": task_data.get("description", ""),
                "priority": task_data.get("priority", "medium"),
                "estimated_hours": estimated_hours,
                "start_date": current_date.isoformat(),
                "due_date": task_end_date.isoformat(),
                "phase": task_data.get("phase", "development"),
                "dependencies": task_data.get("dependencies", []),
                "order_index": task_data.get("order_index", i + 1)
            }
            
            generated_tasks.append(task_info)
            
            # Prossimo task inizia il giorno dopo la fine del precedente (con buffer di 1 giorno)
            current_date = task_end_date + timedelta(days=2)

        return api_response(
            data={
                "project_analysis": ai_data.get("project_analysis", {}),
                "generated_tasks": generated_tasks,
                "summary": {
                    "total_tasks": len(generated_tasks),
                    "total_estimated_hours": sum(task["estimated_hours"] for task in generated_tasks),
                    "estimated_duration_days": (current_date - start_date).days,
                    "phases_covered": list(set(task["phase"] for task in generated_tasks))
                }
            },
            message="Task generati con AI con successo"
        )

    except Exception as e:
        current_app.logger.error(f"Error in generate_tasks_with_ai: {str(e)}")
        return handle_api_error(e)


@api_tasks.route('/<int:project_id>/apply-ai-tasks', methods=['POST'])
@csrf.exempt
@login_required
def apply_generated_tasks(project_id):
    """
    Applica i task generati dall'AI al progetto, creandoli effettivamente nel database.
    """
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, PERMISSION_MANAGE_PROJECT_TASKS):
            return api_response(False, 'Non hai i permessi per creare task', status_code=403)

        # Verifica che il progetto esista
        project = Project.query.get_or_404(project_id)
        
        # Verifica permessi sul progetto specifico
        if not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
            if current_user not in project.team_members:
                return api_response(False, 'Non hai i permessi per questo progetto', status_code=403)

        # Dati della richiesta
        data = request.get_json()
        if not data or 'tasks' not in data:
            return api_response(False, 'Dati task mancanti', status_code=400)

        generated_tasks = data['tasks']
        if not isinstance(generated_tasks, list) or len(generated_tasks) == 0:
            return api_response(False, 'Lista task non valida', status_code=400)

        # Crea i task nel database
        created_tasks = []
        
        for i, task_data in enumerate(generated_tasks):
            try:
                # Parsing date - con fallback per garantire sempre date valide per Gantt
                start_date = None
                due_date = None
                
                if task_data.get('start_date'):
                    try:
                        start_date = datetime.strptime(task_data['start_date'], '%Y-%m-%d').date()
                    except ValueError:
                        print(f"Invalid start_date format: {task_data['start_date']}")
                        
                if task_data.get('due_date'):
                    try:
                        due_date = datetime.strptime(task_data['due_date'], '%Y-%m-%d').date()
                    except ValueError:
                        print(f"Invalid due_date format: {task_data['due_date']}")

                # Fallback: se non ci sono date valide, genera sequenzialmente
                if not start_date or not due_date:
                    today = date.today()
                    start_date = today + timedelta(days=i * 2)  # Spaziatura di 2 giorni
                    estimated_hours = float(task_data.get('estimated_hours', 8)) if task_data.get('estimated_hours') else 8
                    duration_days = max(1, int(estimated_hours / 8))
                    due_date = start_date + timedelta(days=duration_days)

                # Crea il task - SEMPRE con date valide per Gantt
                task = Task(
                    name=task_data.get('name', 'Task generato')[:127],
                    description=task_data.get('description', ''),
                    project_id=project_id,
                    status='todo',
                    priority=task_data.get('priority', 'medium'),
                    start_date=start_date,  # Sempre valorizzato
                    due_date=due_date,      # Sempre valorizzato
                    estimated_hours=float(task_data.get('estimated_hours', 0)) if task_data.get('estimated_hours') else None
                )
                
                db.session.add(task)
                db.session.flush()  # Per ottenere l'ID
                
                created_tasks.append({
                    'id': task.id,
                    'name': task.name,
                    'description': task.description,
                    'priority': task.priority,
                    'estimated_hours': task.estimated_hours,
                    'start_date': task.start_date.isoformat() if task.start_date else None,
                    'due_date': task.due_date.isoformat() if task.due_date else None,
                    'phase': task_data.get('phase', 'development')
                })
                
            except Exception as task_error:
                print(f"Error creating task: {str(task_error)}")
                continue

        # Commit delle modifiche
        db.session.commit()
        
        # Notifica i membri del team
        if project.team_members:
            for member in project.team_members:
                if member.id != current_user.id:  # Non notificare chi ha creato
                    try:
                        create_notification_for_user(
                            user_id=member.id,
                            title="Nuovi task generati con AI",
                            message=f"Sono stati creati {len(created_tasks)} nuovi task per il progetto '{project.name}' tramite AI",
                            link=f"/app/projects/{project_id}#tasks",
                            type="info"
                        )
                    except Exception as notif_error:
                        print(f"Error creating notification: {str(notif_error)}")

        return api_response(
            data={
                'created_tasks': created_tasks,
                'summary': {
                    'total_created': len(created_tasks),
                    'total_estimated_hours': sum(task.get('estimated_hours', 0) or 0 for task in created_tasks),
                    'project_id': project_id,
                    'project_name': project.name
                }
            },
            message=f"{len(created_tasks)} task creati con successo per il progetto '{project.name}'"
        )

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error in apply_generated_tasks: {str(e)}")
        return handle_api_error(e)