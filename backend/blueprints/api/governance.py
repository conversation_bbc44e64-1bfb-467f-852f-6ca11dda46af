"""
API Blueprint per Governance, Compliance e Audit
Fornisce endpoints per audit trail, compliance dashboard e reporting
"""
from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user
from sqlalchemy import desc, func, and_, or_
from datetime import datetime, timedelta, date
from extensions import db
from models import (
    ComplianceAuditLog, ComplianceEvent, CompliancePolicy, ComplianceReport, Risk,
    User, AdminLog
)
from utils.decorators import admin_required
from utils.permissions import user_has_permission, PERMISSION_VIEW_COMPLIANCE
from utils.api_utils import (
    api_response, handle_api_error, get_pagination_params,
    format_pagination, api_permission_required
)

# Blueprint per Governance
api_governance = Blueprint('api_governance', __name__)

@api_governance.route('/dashboard', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMPLIANCE)
def get_compliance_dashboard():
    """
    Dashboard compliance con metriche aggregate
    Fornisce overview delle attività audit e compliance
    """
    try:
        # Parametri query
        days = request.args.get('days', 30, type=int)
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # Metriche base audit logs
        total_activities = ComplianceAuditLog.query.filter(
            ComplianceAuditLog.timestamp >= start_date
        ).count()
        
        # Breakdown per action type
        action_breakdown = db.session.query(
            ComplianceAuditLog.action_type,
            func.count(ComplianceAuditLog.id).label('count')
        ).filter(
            ComplianceAuditLog.timestamp >= start_date
        ).group_by(ComplianceAuditLog.action_type).all()
        
        # Breakdown per risk level
        risk_breakdown = db.session.query(
            ComplianceAuditLog.risk_level,
            func.count(ComplianceAuditLog.id).label('count')
        ).filter(
            ComplianceAuditLog.timestamp >= start_date
        ).group_by(ComplianceAuditLog.risk_level).all()
        
        # Top users per attività
        top_users = db.session.query(
            ComplianceAuditLog.user_id,
            User.username,
            func.count(ComplianceAuditLog.id).label('activity_count')
        ).join(User, ComplianceAuditLog.user_id == User.id)\
         .filter(ComplianceAuditLog.timestamp >= start_date)\
         .group_by(ComplianceAuditLog.user_id, User.username)\
         .order_by(desc('activity_count'))\
         .limit(10).all()
        
        # Eventi compliance
        compliance_events = ComplianceEvent.query.filter(
            ComplianceEvent.created_at >= start_date
        ).order_by(desc(ComplianceEvent.created_at)).limit(10).all()
        
        # Statistiche eventi per severity
        event_severity = db.session.query(
            ComplianceEvent.severity,
            func.count(ComplianceEvent.id).label('count')
        ).filter(
            ComplianceEvent.created_at >= start_date
        ).group_by(ComplianceEvent.severity).all()
        
        # Attività per giorno (ultimi 30 giorni)
        daily_activity = db.session.query(
            func.date(ComplianceAuditLog.timestamp).label('date'),
            func.count(ComplianceAuditLog.id).label('count')
        ).filter(
            ComplianceAuditLog.timestamp >= start_date
        ).group_by(func.date(ComplianceAuditLog.timestamp))\
         .order_by('date').all()
        
        # Resource access patterns
        resource_access = db.session.query(
            ComplianceAuditLog.resource_type,
            func.count(ComplianceAuditLog.id).label('access_count')
        ).filter(
            ComplianceAuditLog.timestamp >= start_date,
            ComplianceAuditLog.resource_type.isnot(None)
        ).group_by(ComplianceAuditLog.resource_type)\
         .order_by(desc('access_count')).all()
        
        # High risk activities
        high_risk_activities = ComplianceAuditLog.query.filter(
            ComplianceAuditLog.timestamp >= start_date,
            ComplianceAuditLog.risk_level.in_(['high', 'critical'])
        ).order_by(desc(ComplianceAuditLog.timestamp)).limit(20).all()
        
        # Policy compliance status
        active_policies = CompliancePolicy.query.filter_by(is_active=True).count()
        policies_need_review = CompliancePolicy.query.filter(
            CompliancePolicy.is_active == True,
            or_(
                CompliancePolicy.next_review_date < datetime.utcnow(),
                CompliancePolicy.next_review_date.is_(None)
            )
        ).count()
        
        dashboard_data = {
            'period': {
                'start_date': start_date.isoformat(),
                'end_date': datetime.utcnow().isoformat(),
                'days': days
            },
            'metrics': {
                'total_activities': total_activities,
                'total_events': len(compliance_events),
                'active_policies': active_policies,
                'policies_need_review': policies_need_review,
                'high_risk_activities': len(high_risk_activities)
            },
            'breakdowns': {
                'actions': [{'type': item.action_type, 'count': item.count} for item in action_breakdown],
                'risk_levels': [{'level': item.risk_level, 'count': item.count} for item in risk_breakdown],
                'event_severity': [{'severity': item.severity, 'count': item.count} for item in event_severity],
                'resource_access': [{'resource': item.resource_type, 'count': item.access_count} for item in resource_access]
            },
            'trends': {
                'daily_activity': [
                    {'date': item.date.isoformat(), 'count': item.count} 
                    for item in daily_activity
                ]
            },
            'top_users': [
                {
                    'user_id': item.user_id,
                    'username': item.username,
                    'activity_count': item.activity_count
                }
                for item in top_users
            ],
            'recent_events': [
                {
                    'id': event.id,
                    'type': event.event_type,
                    'severity': event.severity,
                    'title': event.title,
                    'created_at': event.created_at.isoformat(),
                    'user_id': event.user_id,
                    'status': event.status
                }
                for event in compliance_events
            ],
            'high_risk_activities': [
                {
                    'id': activity.id,
                    'action_type': activity.action_type,
                    'resource_type': activity.resource_type,
                    'user_id': activity.user_id,
                    'timestamp': activity.timestamp.isoformat(),
                    'risk_level': activity.risk_level,
                    'ip_address': activity.ip_address
                }
                for activity in high_risk_activities
            ]
        }
        
        return api_response(data=dashboard_data)
        
    except Exception as e:
        return handle_api_error(e)

@api_governance.route('/audit-trail', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMPLIANCE)
def get_audit_trail():
    """
    Recupera audit trail con filtri e paginazione
    """
    try:
        # Parametri paginazione
        page, per_page = get_pagination_params()
        
        # Filtri
        user_id = request.args.get('user_id', type=int)
        action_type = request.args.get('action_type')
        resource_type = request.args.get('resource_type')
        risk_level = request.args.get('risk_level')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        ip_address = request.args.get('ip_address')
        
        # Query base
        query = ComplianceAuditLog.query
        
        # Applica filtri
        if user_id:
            query = query.filter(ComplianceAuditLog.user_id == user_id)
        
        if action_type:
            query = query.filter(ComplianceAuditLog.action_type == action_type)
        
        if resource_type:
            query = query.filter(ComplianceAuditLog.resource_type == resource_type)
        
        if risk_level:
            query = query.filter(ComplianceAuditLog.risk_level == risk_level)
        
        if ip_address:
            query = query.filter(ComplianceAuditLog.ip_address == ip_address)
        
        if start_date:
            start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            query = query.filter(ComplianceAuditLog.timestamp >= start_dt)
        
        if end_date:
            end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            query = query.filter(ComplianceAuditLog.timestamp <= end_dt)
        
        # Ordina per timestamp desc
        query = query.order_by(desc(ComplianceAuditLog.timestamp))
        
        # Paginazione
        paginated = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )
        
        # Join con User per username
        audit_logs = []
        for log in paginated.items:
            log_data = {
                'id': log.id,
                'user_id': log.user_id,
                'username': log.user.username if log.user else 'System',
                'session_id': log.session_id,
                'action_type': log.action_type,
                'resource_type': log.resource_type,
                'resource_id': log.resource_id,
                'endpoint': log.endpoint,
                'method': log.method,
                'ip_address': log.ip_address,
                'user_agent': log.user_agent,
                'response_status': log.response_status,
                'processing_time_ms': log.processing_time_ms,
                'risk_level': log.risk_level,
                'data_classification': log.data_classification,
                'is_sensitive': log.is_sensitive,
                'timestamp': log.timestamp.isoformat(),
                'compliance_context': log.compliance_context
            }
            audit_logs.append(log_data)
        
        return api_response(
            data={
                'audit_logs': audit_logs,
                'pagination': format_pagination(paginated)
            }
        )
        
    except Exception as e:
        return handle_api_error(e)

@api_governance.route('/events', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMPLIANCE)
def get_compliance_events():
    """
    Recupera eventi compliance con filtri
    """
    try:
        # Parametri paginazione
        page, per_page = get_pagination_params()
        
        # Filtri
        event_type = request.args.get('event_type')
        severity = request.args.get('severity')
        status = request.args.get('status', 'open')
        framework = request.args.get('framework')
        
        # Query base
        query = ComplianceEvent.query
        
        # Applica filtri
        if event_type:
            query = query.filter(ComplianceEvent.event_type == event_type)
        
        if severity:
            query = query.filter(ComplianceEvent.severity == severity)
        
        if status and status != 'all':
            query = query.filter(ComplianceEvent.status == status)
        
        if framework:
            query = query.filter(ComplianceEvent.compliance_framework.contains(framework))
        
        # Ordina per data creazione desc
        query = query.order_by(desc(ComplianceEvent.created_at))
        
        # Paginazione
        paginated = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )
        
        events = []
        for event in paginated.items:
            event_data = {
                'id': event.id,
                'event_type': event.event_type,
                'event_category': event.event_category,
                'severity': event.severity,
                'title': event.title,
                'description': event.description,
                'user_id': event.user_id,
                'username': event.user.username if event.user else None,
                'compliance_framework': event.compliance_framework,
                'regulatory_impact': event.regulatory_impact,
                'risk_score': event.risk_score,
                'status': event.status,
                'created_at': event.created_at.isoformat(),
                'resolved_at': event.resolved_at.isoformat() if event.resolved_at else None,
                'age_hours': event.age_hours,
                'source_ip': event.source_ip,
                'metadata': event.event_metadata
            }
            events.append(event_data)
        
        return api_response(
            data={
                'events': events,
                'pagination': format_pagination(paginated)
            }
        )
        
    except Exception as e:
        return handle_api_error(e)

@api_governance.route('/events/<int:event_id>/resolve', methods=['PUT'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMPLIANCE)
def resolve_compliance_event(event_id):
    """
    Risolve un evento compliance
    """
    try:
        data = request.get_json()
        resolution_notes = data.get('resolution_notes', '')
        
        event = ComplianceEvent.query.get_or_404(event_id)
        
        event.status = 'resolved'
        event.resolved_at = datetime.utcnow()
        event.resolved_by = current_user.id
        event.resolution_notes = resolution_notes
        
        db.session.commit()
        
        return api_response(
            message="Evento compliance risolto",
            data={'event_id': event_id}
        )
        
    except Exception as e:
        return handle_api_error(e)

@api_governance.route('/policies', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMPLIANCE)
def get_compliance_policies():
    """
    Recupera policy compliance con filtri e paginazione
    """
    try:
        # Parametri paginazione
        page, per_page = get_pagination_params()
        
        # Filtri
        policy_type = request.args.get('policy_type')
        framework = request.args.get('framework')
        status = request.args.get('status')
        expiry_filter = request.args.get('expiry_filter')
        
        # Query base
        query = CompliancePolicy.query.filter_by(is_active=True)
        
        # Applica filtri
        if policy_type:
            query = query.filter(CompliancePolicy.policy_type == policy_type)
        
        if framework:
            query = query.filter(CompliancePolicy.framework == framework)
        
        if status:
            if status == 'active':
                query = query.filter(
                    or_(
                        CompliancePolicy.expiry_date.is_(None),
                        CompliancePolicy.expiry_date > datetime.utcnow()
                    )
                )
            elif status == 'expired':
                query = query.filter(
                    CompliancePolicy.expiry_date <= datetime.utcnow()
                )
        
        if expiry_filter:
            now = datetime.utcnow()
            if expiry_filter == 'next_30':
                query = query.filter(
                    and_(
                        CompliancePolicy.expiry_date.isnot(None),
                        CompliancePolicy.expiry_date <= now + timedelta(days=30),
                        CompliancePolicy.expiry_date > now
                    )
                )
            elif expiry_filter == 'next_90':
                query = query.filter(
                    and_(
                        CompliancePolicy.expiry_date.isnot(None),
                        CompliancePolicy.expiry_date <= now + timedelta(days=90),
                        CompliancePolicy.expiry_date > now
                    )
                )
            elif expiry_filter == 'expired':
                query = query.filter(
                    CompliancePolicy.expiry_date <= now
                )
        
        # Ordina e pagina
        query = query.order_by(CompliancePolicy.name)
        paginated = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )
        
        policies_data = []
        for policy in paginated.items:
            policy_data = {
                'id': policy.id,
                'name': policy.name,
                'description': policy.description,
                'policy_type': policy.policy_type,
                'framework': policy.framework,
                'version': policy.version,
                'effective_date': policy.effective_date.isoformat(),
                'expiry_date': policy.expiry_date.isoformat() if policy.expiry_date else None,
                'next_review_date': policy.next_review_date.isoformat() if policy.next_review_date else None,
                'needs_review': policy.needs_review,
                'owner_role': policy.owner_role,
                'status': 'active' if (not policy.expiry_date or policy.expiry_date > datetime.utcnow()) else 'expired'
            }
            policies_data.append(policy_data)
        
        return api_response(data={
            'policies': policies_data,
            'pagination': format_pagination(paginated)
        })
        
    except Exception as e:
        return handle_api_error(e)

@api_governance.route('/policies', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMPLIANCE)
def create_compliance_policy():
    """
    Crea una nuova policy compliance
    """
    try:
        data = request.get_json()
        
        # Validazione dati richiesti
        required_fields = ['name', 'description', 'policy_type', 'version', 'effective_date']
        for field in required_fields:
            if field not in data or not data[field]:
                return api_response(
                    message=f"Campo richiesto mancante: {field}",
                    status_code=400
                )
        
        # Crea nuova policy
        policy = CompliancePolicy(
            name=data['name'],
            description=data['description'],
            policy_type=data['policy_type'],
            framework=data.get('framework'),
            version=data['version'],
            content=data.get('content', ''),
            compliance_requirements=data.get('compliance_requirements'),
            violation_penalties=data.get('violation_penalties'),
            effective_date=datetime.fromisoformat(data['effective_date'].replace('Z', '+00:00')).date(),
            expiry_date=datetime.fromisoformat(data['expiry_date'].replace('Z', '+00:00')).date() if data.get('expiry_date') else None,
            next_review_date=datetime.fromisoformat(data['next_review_date'].replace('Z', '+00:00')).date() if data.get('next_review_date') else None,
            status=data.get('status', 'draft'),
            owner_role=data.get('owner'),
            approver_role=data.get('approver'),
            created_by=current_user.id,
            is_active=True
        )
        
        db.session.add(policy)
        db.session.commit()
        
        return api_response(
            message="Policy creata con successo",
            data={'policy_id': policy.id},
            status_code=201
        )
        
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

@api_governance.route('/policies/<int:policy_id>', methods=['PUT'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMPLIANCE)
def update_compliance_policy(policy_id):
    """
    Aggiorna una policy esistente
    """
    try:
        policy = CompliancePolicy.query.get_or_404(policy_id)
        data = request.get_json()
        
        # Aggiorna campi forniti
        updatable_fields = [
            'name', 'description', 'policy_type', 'framework', 'version',
            'content', 'compliance_requirements', 'violation_penalties', 'status'
        ]
        
        for field in updatable_fields:
            if field in data:
                setattr(policy, field, data[field])
        
        # Map frontend field names to backend field names
        if 'owner' in data:
            policy.owner_role = data['owner']
        if 'approver' in data:
            policy.approver_role = data['approver']
        
        # Gestisci date
        if 'effective_date' in data and data['effective_date']:
            policy.effective_date = datetime.fromisoformat(data['effective_date'].replace('Z', '+00:00')).date()
        
        if 'expiry_date' in data:
            if data['expiry_date']:
                policy.expiry_date = datetime.fromisoformat(data['expiry_date'].replace('Z', '+00:00')).date()
            else:
                policy.expiry_date = None
                
        if 'next_review_date' in data:
            if data['next_review_date']:
                policy.next_review_date = datetime.fromisoformat(data['next_review_date'].replace('Z', '+00:00')).date()
            else:
                policy.next_review_date = None
        
        # Aggiorna timestamp
        policy.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return api_response(
            message="Policy aggiornata con successo",
            data={'policy_id': policy_id}
        )
        
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

@api_governance.route('/reports/generate', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMPLIANCE)
def generate_compliance_report():
    """
    Genera report compliance per periodo specificato
    """
    try:
        data = request.get_json()
        
        report_type = data.get('report_type', 'audit_trail')
        period_start = datetime.fromisoformat(data['period_start'].replace('Z', '+00:00'))
        period_end = datetime.fromisoformat(data['period_end'].replace('Z', '+00:00'))
        framework = data.get('framework', 'GDPR')
        
        # Genera dati del report
        summary_data = {
            'total_activities': ComplianceAuditLog.query.filter(
                and_(
                    ComplianceAuditLog.timestamp >= period_start,
                    ComplianceAuditLog.timestamp <= period_end
                )
            ).count(),
            'high_risk_activities': ComplianceAuditLog.query.filter(
                and_(
                    ComplianceAuditLog.timestamp >= period_start,
                    ComplianceAuditLog.timestamp <= period_end,
                    ComplianceAuditLog.risk_level.in_(['high', 'critical'])
                )
            ).count(),
            'compliance_events': ComplianceEvent.query.filter(
                and_(
                    ComplianceEvent.created_at >= period_start,
                    ComplianceEvent.created_at <= period_end
                )
            ).count()
        }
        
        # Crea record report
        report = ComplianceReport(
            name=f"Compliance Report {framework} - {period_start.strftime('%Y-%m-%d')} to {period_end.strftime('%Y-%m-%d')}",
            report_type=report_type,
            period_start=period_start,
            period_end=period_end,
            framework=framework,
            summary_data=summary_data,
            generated_by=current_user.id,
            status='generated'
        )
        
        db.session.add(report)
        db.session.commit()
        
        return api_response(
            message="Report compliance generato",
            data={'report_id': report.id}
        )
        
    except Exception as e:
        return handle_api_error(e)

@api_governance.route('/user-activity/<int:user_id>', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMPLIANCE)
def get_user_activity_summary(user_id):
    """
    Riepilogo attività per utente specifico
    """
    try:
        days = request.args.get('days', 30, type=int)
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # Verifica che l'utente esista
        user = User.query.get_or_404(user_id)
        
        # Statistiche attività
        total_activities = ComplianceAuditLog.query.filter(
            ComplianceAuditLog.user_id == user_id,
            ComplianceAuditLog.timestamp >= start_date
        ).count()
        
        # Breakdown per action type
        actions = db.session.query(
            ComplianceAuditLog.action_type,
            func.count(ComplianceAuditLog.id).label('count')
        ).filter(
            ComplianceAuditLog.user_id == user_id,
            ComplianceAuditLog.timestamp >= start_date
        ).group_by(ComplianceAuditLog.action_type).all()
        
        # Attività recenti
        recent_activities = ComplianceAuditLog.query.filter(
            ComplianceAuditLog.user_id == user_id
        ).order_by(desc(ComplianceAuditLog.timestamp)).limit(50).all()
        
        user_summary = {
            'user_id': user_id,
            'username': user.username,
            'period_days': days,
            'total_activities': total_activities,
            'action_breakdown': [
                {'action': action.action_type, 'count': action.count}
                for action in actions
            ],
            'recent_activities': [
                {
                    'id': activity.id,
                    'action_type': activity.action_type,
                    'resource_type': activity.resource_type,
                    'timestamp': activity.timestamp.isoformat(),
                    'risk_level': activity.risk_level,
                    'ip_address': activity.ip_address
                }
                for activity in recent_activities
            ]
        }
        
        return api_response(data=user_summary)
        
    except Exception as e:
        return handle_api_error(e)

@api_governance.route('/risks', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMPLIANCE)
def get_risks():
    """
    Recupera lista rischi con filtri e paginazione
    """
    try:
        # Parametri paginazione
        page, per_page = get_pagination_params()
        
        # Filtri
        category = request.args.get('category')
        status = request.args.get('status')
        risk_level = request.args.get('risk_level')
        owner_id = request.args.get('owner_id', type=int)
        
        # Query base
        query = Risk.query
        
        # Applica filtri
        if category:
            query = query.filter(Risk.category == category)
        
        if status:
            query = query.filter(Risk.status == status)
        
        if risk_level:
            query = query.filter(Risk.risk_level == risk_level)
        
        if owner_id:
            query = query.filter(Risk.owner_id == owner_id)
        
        # Ordina per risk score desc, poi per data creazione
        query = query.order_by(desc(Risk.risk_score), desc(Risk.created_at))
        
        # Paginazione
        paginated = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )
        
        risks = []
        for risk in paginated.items:
            risk_data = {
                'id': risk.id,
                'title': risk.title,
                'description': risk.description,
                'category': risk.category,
                'risk_type': risk.risk_type,
                'probability': risk.probability,
                'impact': risk.impact,
                'risk_level': risk.risk_level,
                'risk_score': risk.risk_score,
                'status': risk.status,
                'owner_id': risk.owner_id,
                'owner_name': risk.owner.username if risk.owner else None,
                'responsible_department': risk.responsible_department,
                'mitigation_strategy': risk.mitigation_strategy,
                'mitigation_deadline': risk.mitigation_deadline.isoformat() if risk.mitigation_deadline else None,
                'compliance_framework': risk.compliance_framework,
                'identified_date': risk.identified_date.isoformat() if risk.identified_date else None,
                'last_review_date': risk.last_review_date.isoformat() if risk.last_review_date else None,
                'next_review_date': risk.next_review_date.isoformat() if risk.next_review_date else None,
                'is_overdue': risk.is_overdue,
                'requires_immediate_attention': risk.requires_immediate_attention,
                'created_at': risk.created_at.isoformat(),
                'updated_at': risk.updated_at.isoformat()
            }
            risks.append(risk_data)
        
        return api_response(
            data={
                'risks': risks,
                'pagination': format_pagination(paginated)
            }
        )
        
    except Exception as e:
        return handle_api_error(e)

@api_governance.route('/risks/<int:risk_id>', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMPLIANCE)
def get_risk_detail(risk_id):
    """
    Recupera dettaglio di un rischio specifico
    """
    try:
        risk = Risk.query.get_or_404(risk_id)
        
        risk_data = {
            'id': risk.id,
            'title': risk.title,
            'description': risk.description,
            'category': risk.category,
            'risk_type': risk.risk_type,
            'probability': risk.probability,
            'impact': risk.impact,
            'risk_level': risk.risk_level,
            'risk_score': risk.risk_score,
            'status': risk.status,
            'owner_id': risk.owner_id,
            'owner_name': risk.owner.username if risk.owner else None,
            'responsible_department': risk.responsible_department,
            'mitigation_strategy': risk.mitigation_strategy,
            'mitigation_actions': risk.mitigation_actions,
            'mitigation_deadline': risk.mitigation_deadline.isoformat() if risk.mitigation_deadline else None,
            'mitigation_cost': risk.mitigation_cost,
            'regulatory_requirements': risk.regulatory_requirements,
            'compliance_framework': risk.compliance_framework,
            'identified_date': risk.identified_date.isoformat() if risk.identified_date else None,
            'last_review_date': risk.last_review_date.isoformat() if risk.last_review_date else None,
            'next_review_date': risk.next_review_date.isoformat() if risk.next_review_date else None,
            'resolved_date': risk.resolved_date.isoformat() if risk.resolved_date else None,
            'tags': risk.tags,
            'external_references': risk.external_references,
            'is_overdue': risk.is_overdue,
            'requires_immediate_attention': risk.requires_immediate_attention,
            'created_at': risk.created_at.isoformat(),
            'updated_at': risk.updated_at.isoformat()
        }
        
        return api_response(data=risk_data)
        
    except Exception as e:
        return handle_api_error(e)

@api_governance.route('/risks', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMPLIANCE)
def create_risk():
    """
    Crea un nuovo rischio
    """
    try:
        data = request.get_json()
        
        # Validazione dati richiesti
        required_fields = ['title', 'description', 'category', 'probability', 'impact']
        for field in required_fields:
            if field not in data:
                return api_response(
                    message=f"Campo richiesto mancante: {field}",
                    status_code=400
                )
        
        # Crea nuovo rischio
        risk = Risk(
            title=data['title'],
            description=data['description'],
            category=data['category'],
            risk_type=data.get('risk_type'),
            probability=data['probability'],
            impact=data['impact'],
            status=data.get('status', 'identified'),
            owner_id=data.get('owner_id', current_user.id),
            responsible_department=data.get('responsible_department'),
            mitigation_strategy=data.get('mitigation_strategy'),
            mitigation_actions=data.get('mitigation_actions'),
            mitigation_deadline=datetime.fromisoformat(data['mitigation_deadline'].replace('Z', '+00:00')) if data.get('mitigation_deadline') else None,
            mitigation_cost=data.get('mitigation_cost'),
            regulatory_requirements=data.get('regulatory_requirements'),
            compliance_framework=data.get('compliance_framework'),
            tags=data.get('tags'),
            external_references=data.get('external_references')
        )
        
        # Calcola automaticamente risk score e level
        risk.update_risk_score()
        
        db.session.add(risk)
        db.session.commit()
        
        return api_response(
            message="Rischio creato con successo",
            data={'risk_id': risk.id},
            status_code=201
        )
        
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

@api_governance.route('/risks/<int:risk_id>', methods=['PUT'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMPLIANCE)
def update_risk(risk_id):
    """
    Aggiorna un rischio esistente
    """
    try:
        risk = Risk.query.get_or_404(risk_id)
        data = request.get_json()
        
        # Aggiorna campi forniti
        updatable_fields = [
            'title', 'description', 'category', 'risk_type', 'probability', 'impact',
            'status', 'owner_id', 'responsible_department', 'mitigation_strategy',
            'mitigation_actions', 'mitigation_cost', 'regulatory_requirements',
            'compliance_framework', 'tags', 'external_references'
        ]
        
        for field in updatable_fields:
            if field in data:
                setattr(risk, field, data[field])
        
        # Gestisci date
        if 'mitigation_deadline' in data and data['mitigation_deadline']:
            risk.mitigation_deadline = datetime.fromisoformat(data['mitigation_deadline'].replace('Z', '+00:00'))
        
        if 'next_review_date' in data and data['next_review_date']:
            risk.next_review_date = datetime.fromisoformat(data['next_review_date'].replace('Z', '+00:00'))
        
        # Aggiorna last_review_date se necessario
        if 'probability' in data or 'impact' in data:
            risk.last_review_date = datetime.utcnow()
        
        # Ricalcola risk score e level
        risk.update_risk_score()
        
        db.session.commit()
        
        return api_response(
            message="Rischio aggiornato con successo",
            data={'risk_id': risk_id}
        )
        
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

@api_governance.route('/risks/<int:risk_id>', methods=['DELETE'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMPLIANCE)
def delete_risk(risk_id):
    """
    Elimina un rischio
    """
    try:
        risk = Risk.query.get_or_404(risk_id)
        
        db.session.delete(risk)
        db.session.commit()
        
        return api_response(
            message="Rischio eliminato con successo",
            data={'risk_id': risk_id}
        )
        
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

@api_governance.route('/risks/stats', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMPLIANCE)
def get_risk_statistics():
    """
    Statistiche aggregate sui rischi
    """
    try:
        # Statistiche per categoria
        category_stats = db.session.query(
            Risk.category,
            func.count(Risk.id).label('count'),
            func.avg(Risk.risk_score).label('avg_score')
        ).group_by(Risk.category).all()
        
        # Statistiche per livello di rischio
        level_stats = db.session.query(
            Risk.risk_level,
            func.count(Risk.id).label('count')
        ).group_by(Risk.risk_level).all()
        
        # Statistiche per status
        status_stats = db.session.query(
            Risk.status,
            func.count(Risk.id).label('count')
        ).group_by(Risk.status).all()
        
        # Rischi che richiedono attenzione immediata
        critical_risks = Risk.query.filter(
            Risk.risk_level.in_(['high', 'critical']),
            Risk.status.in_(['identified', 'under_review'])
        ).count()
        
        # Rischi scaduti per review
        overdue_reviews = Risk.query.filter(
            Risk.next_review_date < datetime.utcnow()
        ).count()
        
        stats = {
            'total_risks': Risk.query.count(),
            'critical_risks': critical_risks,
            'overdue_reviews': overdue_reviews,
            'by_category': [
                {
                    'category': stat.category,
                    'count': stat.count,
                    'avg_score': round(float(stat.avg_score), 2) if stat.avg_score else 0
                }
                for stat in category_stats
            ],
            'by_level': [
                {'level': stat.risk_level, 'count': stat.count}
                for stat in level_stats
            ],
            'by_status': [
                {'status': stat.status, 'count': stat.count}
                for stat in status_stats
            ]
        }
        
        return api_response(data=stats)
        
    except Exception as e:
        return handle_api_error(e)