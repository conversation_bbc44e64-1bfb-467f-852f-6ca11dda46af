"""
Business Intelligence API con metriche reali dal database.
Calcola KPI basati su dati effettivi: progetti, fatturato, team, performance.
"""

from flask import Blueprint, request, jsonify
from flask_login import current_user
from sqlalchemy import func, and_, or_, extract
from datetime import datetime, timedelta
from decimal import Decimal

from extensions import db
from models import (
    User, Project, Task, TimesheetEntry, Client, Contact, 
    Proposal, Contract, Invoice, InvoiceLine, UserSkill, Department, CoreCompetency, Skill,
    TechnicalOffer, MarketProspect, BIReport
)
from utils.api_utils import api_response, api_login_required, handle_api_error, api_permission_required
from utils.permissions import PERMISSION_VIEW_REPORTS, user_has_permission

# Create blueprint
bi_api = Blueprint('bi_api', __name__)

def safe_query(query_func, default_value=0):
    """Esegue query in modo sicuro con fallback."""
    try:
        result = query_func()
        return result if result is not None else default_value
    except Exception as e:
        db.session.rollback()
        return default_value

@bi_api.route('/dashboard/overview', methods=['GET'])
@api_login_required
def get_dashboard_overview():
    """
    Dashboard principale con metriche reali calcolate dal database.
    """
    try:
        if not user_has_permission(current_user.role, PERMISSION_VIEW_REPORTS):
            return api_response(
                message="Permessi insufficienti per visualizzare dashboard",
                status_code=403
            )

        # Periodo di analisi (default: anno corrente)
        period = request.args.get('period', 'year')
        now = datetime.utcnow()
        
        if period == 'month':
            start_date = now.replace(day=1)
        elif period == 'quarter':
            quarter_start = ((now.month - 1) // 3) * 3 + 1
            start_date = now.replace(month=quarter_start, day=1)
        else:  # year
            start_date = now.replace(month=1, day=1)

        # 1. METRICHE FINANCIAL REALI
        financial_metrics = get_financial_metrics(start_date, now)
        
        # 2. METRICHE OPERATIVO REALI  
        operational_metrics = get_operational_metrics(start_date, now)
        
        # 3. METRICHE COMMERCIALE REALI
        commercial_metrics = get_commercial_metrics(start_date, now)
        
        # 4. METRICHE TEAM REALI
        team_metrics = get_team_metrics()
        
        # 5. TREND REALI
        trend_data = get_trend_data(start_date, now)

        dashboard_data = {
            'period': {
                'type': period,
                'start_date': start_date.isoformat(),
                'end_date': now.isoformat()
            },
            'financial': financial_metrics,
            'operational': operational_metrics, 
            'commercial': commercial_metrics,
            'team': team_metrics,
            'trends': trend_data,
            'last_updated': now.isoformat()
        }

        return api_response(data=dashboard_data)

    except Exception as e:
        return handle_api_error(e)

def get_financial_metrics(start_date, end_date):
    """Calcola metriche finanziarie reali."""
    
    # Fatturato da contratti completati
    total_revenue = safe_query(lambda: db.session.query(func.sum(Contract.budget_amount)).filter(
        Contract.status == 'completed',
        Contract.updated_at >= start_date,
        Contract.updated_at <= end_date
    ).scalar())

    # Fatturato da fatture emesse
    invoiced_amount = safe_query(lambda: db.session.query(func.sum(Invoice.total_amount)).filter(
        Invoice.created_at >= start_date,
        Invoice.created_at <= end_date
    ).scalar())

    # Pipeline value (proposte + contratti attivi)
    pipeline_proposals = safe_query(lambda: db.session.query(func.sum(Proposal.value)).filter(
        Proposal.status.in_(['sent', 'negotiating'])
    ).scalar())
    
    pipeline_contracts = safe_query(lambda: db.session.query(func.sum(Contract.budget_amount)).filter(
        Contract.status == 'active'
    ).scalar())
    
    total_pipeline = (pipeline_proposals or 0) + (pipeline_contracts or 0)

    # Tasso di conversione proposte
    total_proposals = safe_query(lambda: Proposal.query.filter(
        Proposal.created_at >= start_date
    ).count())
    
    won_proposals = safe_query(lambda: Proposal.query.filter(
        Proposal.status == 'accepted',
        Proposal.created_at >= start_date
    ).count())
    
    conversion_rate = (won_proposals / total_proposals * 100) if total_proposals > 0 else 0

    # Valore medio contratto
    total_contracts = safe_query(lambda: Contract.query.filter(
        Contract.created_at >= start_date
    ).count())
    
    avg_contract_value = (total_revenue / total_contracts) if total_contracts > 0 else 0

    return {
        'total_revenue': round(float(total_revenue or 0), 2),
        'invoiced_amount': round(float(invoiced_amount or 0), 2),
        'pipeline_value': round(float(total_pipeline), 2),
        'conversion_rate': round(conversion_rate, 2),
        'avg_contract_value': round(float(avg_contract_value), 2),
        'total_contracts': total_contracts
    }

def get_operational_metrics(start_date, end_date):
    """Calcola metriche operative reali."""
    
    # Progetti per status
    projects_active = safe_query(lambda: Project.query.filter_by(status='active').count())
    projects_completed = safe_query(lambda: Project.query.filter(
        Project.status == 'completed',
        Project.updated_at >= start_date
    ).count())
    projects_overdue = safe_query(lambda: Project.query.filter(
        Project.status == 'active',
        Project.end_date < datetime.utcnow().date()
    ).count())

    # Ore fatturabili vs totali
    total_hours = safe_query(lambda: db.session.query(func.sum(TimesheetEntry.hours)).filter(
        TimesheetEntry.date >= start_date.date(),
        TimesheetEntry.date <= end_date.date()
    ).scalar())
    
    billable_hours = safe_query(lambda: db.session.query(func.sum(TimesheetEntry.hours)).filter(
        TimesheetEntry.date >= start_date.date(),
        TimesheetEntry.date <= end_date.date(),
        TimesheetEntry.billable == True
    ).scalar())
    
    billable_rate = (billable_hours / total_hours * 100) if total_hours > 0 else 0

    # Produttività media (ore per progetto completato)
    productivity = (total_hours / projects_completed) if projects_completed > 0 else 0

    # Task completate
    completed_tasks = safe_query(lambda: Task.query.filter(
        Task.status == 'completed',
        Task.updated_at >= start_date
    ).count())

    return {
        'projects_active': projects_active,
        'projects_completed': projects_completed,
        'projects_overdue': projects_overdue,
        'total_hours': float(total_hours or 0),
        'billable_hours': float(billable_hours or 0),
        'billable_rate': round(billable_rate, 1),
        'productivity_hours_per_project': round(productivity, 1),
        'completed_tasks': completed_tasks
    }

def get_commercial_metrics(start_date, end_date):
    """Calcola metriche commerciali reali."""
    
    # Nuovi clienti nel periodo
    new_clients = safe_query(lambda: Client.query.filter(
        Client.created_at >= start_date
    ).count())

    # Clienti attivi (con contratti/proposte attive)
    active_clients = safe_query(lambda: db.session.query(Client.id).join(Contract).filter(
        Contract.status.in_(['active', 'completed'])
    ).distinct().count())

    # Top clienti per valore
    top_clients = []
    try:
        top_clients_raw = db.session.query(
            Client.name,
            func.sum(Contract.budget_amount).label('total_value'),
            func.count(Contract.id).label('contracts_count')
        ).join(Contract).group_by(Client.id, Client.name).order_by(
            func.sum(Contract.budget_amount).desc()
        ).limit(5).all()
        
        top_clients = [
            {
                'name': client.name,
                'total_value': float(client.total_value or 0),
                'contracts_count': client.contracts_count
            }
            for client in top_clients_raw
        ]
    except:
        db.session.rollback()

    # Win rate
    total_proposals = safe_query(lambda: Proposal.query.count())
    won_proposals = safe_query(lambda: Proposal.query.filter_by(status='accepted').count())
    win_rate = (won_proposals / total_proposals * 100) if total_proposals > 0 else 0

    return {
        'new_clients': new_clients,
        'active_clients': active_clients,
        'top_clients': top_clients,
        'win_rate': round(win_rate, 1),
        'total_proposals': total_proposals,
        'won_proposals': won_proposals
    }

def get_team_metrics():
    """Calcola metriche del team reali."""
    
    # Dipendenti attivi
    active_employees = safe_query(lambda: User.query.filter_by(is_active=True).count())
    
    # Competenze più utilizzate
    top_skills = []
    try:
        top_skills_raw = db.session.query(
            Skill.name,
            func.count(UserSkill.id).label('team_count'),
            func.avg(UserSkill.proficiency_level).label('avg_proficiency')
        ).join(UserSkill, UserSkill.skill_id == Skill.id).group_by(Skill.name).order_by(
            func.count(UserSkill.id).desc()
        ).limit(10).all()
        
        top_skills = [
            {
                'skill': skill.name,
                'team_count': skill.team_count,
                'avg_proficiency': round(float(skill.avg_proficiency or 0), 1)
            }
            for skill in top_skills_raw
        ]
    except:
        db.session.rollback()

    # Dipartimenti
    departments = []
    try:
        departments_raw = db.session.query(
            Department.name,
            func.count(User.id).label('employee_count')
        ).outerjoin(User).group_by(Department.id, Department.name).all()
        
        departments = [
            {
                'name': dept.name,
                'employee_count': dept.employee_count
            }
            for dept in departments_raw
        ]
    except:
        db.session.rollback()

    # Utilizzo team (ore timesheet)
    this_month = datetime.utcnow().replace(day=1)
    team_utilization = safe_query(lambda: db.session.query(func.avg(TimesheetEntry.hours)).filter(
        TimesheetEntry.date >= this_month.date()
    ).scalar())

    return {
        'active_employees': active_employees,
        'top_skills': top_skills,
        'departments': departments,
        'avg_monthly_hours': round(float(team_utilization or 0), 1)
    }

def get_trend_data(start_date, end_date):
    """Calcola trend temporali reali."""
    
    trends = {
        'monthly_revenue': [],
        'monthly_projects': [],
        'monthly_hours': []
    }
    
    try:
        # Trend ultimi 6 mesi
        for i in range(6):
            month_start = (datetime.utcnow() - timedelta(days=30*i)).replace(day=1)
            month_end = month_start.replace(month=month_start.month+1) if month_start.month < 12 else month_start.replace(year=month_start.year+1, month=1)
            
            # Revenue mensile
            monthly_revenue = safe_query(lambda: db.session.query(func.sum(Contract.budget_amount)).filter(
                Contract.status == 'completed',
                Contract.updated_at >= month_start,
                Contract.updated_at < month_end
            ).scalar())
            
            # Progetti completati
            monthly_projects = safe_query(lambda: Project.query.filter(
                Project.status == 'completed',
                Project.updated_at >= month_start,
                Project.updated_at < month_end
            ).count())
            
            # Ore lavorate
            monthly_hours = safe_query(lambda: db.session.query(func.sum(TimesheetEntry.hours)).filter(
                TimesheetEntry.date >= month_start.date(),
                TimesheetEntry.date < month_end.date()
            ).scalar())
            
            trends['monthly_revenue'].append({
                'month': month_start.strftime('%Y-%m'),
                'value': float(monthly_revenue or 0)
            })
            
            trends['monthly_projects'].append({
                'month': month_start.strftime('%Y-%m'),
                'value': monthly_projects
            })
            
            trends['monthly_hours'].append({
                'month': month_start.strftime('%Y-%m'),
                'value': float(monthly_hours or 0)
            })
    
    except:
        db.session.rollback()
    
    # Inverti per ordine cronologico
    for key in trends:
        trends[key] = trends[key][::-1]
    
    return trends

# ============================================================================
# CORE COMPETENCIES API
# ============================================================================

@bi_api.route('/core-competencies', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_REPORTS)
def get_core_competencies():
    """Ottiene le competenze core aziendali"""
    try:
        competencies = CoreCompetency.query.filter_by(is_active=True).all()
        
        competencies_data = []
        for comp in competencies:
            # Calcola copertura team
            team_coverage = 0
            if comp.skill_ids:
                covered_users = db.session.query(UserSkill.user_id).filter(
                    UserSkill.skill_id.in_(comp.skill_ids),
                    UserSkill.proficiency_level >= comp.avg_proficiency_required
                ).distinct().count()
                team_coverage = covered_users
            
            # Ottieni skills correlate
            skills = []
            if comp.skill_ids:
                skills = Skill.query.filter(Skill.id.in_(comp.skill_ids)).all()
                skills = [{'id': s.id, 'name': s.name, 'category': s.category} for s in skills]
            
            competencies_data.append({
                'id': comp.id,
                'name': comp.name,
                'description': comp.description,
                'category': comp.category,
                'market_positioning': comp.market_positioning,
                'skills': skills,
                'min_team_size': comp.min_team_size,
                'avg_proficiency_required': comp.avg_proficiency_required,
                'team_coverage': team_coverage,
                'business_value': comp.business_value,
                'target_markets': comp.target_markets or [],
                'competitive_advantage': comp.competitive_advantage,
                'created_at': comp.created_at.isoformat()
            })
        
        return api_response(
            data={'competencies': competencies_data},
            message=f"Recuperate {len(competencies_data)} competenze core"
        )
        
    except Exception as e:
        return handle_api_error(e)

@bi_api.route('/core-competencies', methods=['POST'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_REPORTS)
def create_core_competency():
    """Crea una nuova competenza core"""
    try:
        data = request.get_json()
        
        competency = CoreCompetency(
            name=data['name'],
            description=data.get('description'),
            category=data.get('category', 'technical'),
            market_positioning=data.get('market_positioning'),
            skill_ids=data.get('skill_ids', []),
            min_team_size=data.get('min_team_size', 3),
            avg_proficiency_required=data.get('avg_proficiency_required', 3.5),
            business_value=data.get('business_value'),
            target_markets=data.get('target_markets', []),
            competitive_advantage=data.get('competitive_advantage')
        )
        
        db.session.add(competency)
        db.session.commit()
        
        return api_response(
            data={'id': competency.id},
            message=f"Competenza core '{competency.name}' creata con successo"
        )
        
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

@bi_api.route('/core-competencies/generate-ai', methods=['POST'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_REPORTS)
def generate_core_competency_ai():
    """Genera una competenza core usando AI"""
    try:
        from services.ai import AIService
        
        data = request.get_json()
        
        # Prepara prompt per AI
        prompt = f"""
        Genera una competenza core aziendale dettagliata basata sui seguenti input:
        
        Area di Competenza: {data.get('competency_area', '')}
        Contesto Business: {data.get('business_context', '')}
        Mercato Target: {data.get('target_market', '')}
        Requisiti Specifici: {data.get('specific_requirements', '')}
        
        Fornisci una risposta in formato JSON con i seguenti campi:
        - name: Nome della competenza (max 100 caratteri)
        - description: Descrizione dettagliata (200-300 parole)
        - category: Una tra "technical", "business", "soft_skills"
        - min_team_size: Numero minimo di persone necessarie (1-10)
        - avg_proficiency_required: Livello di competenza richiesto (1.0-5.0)
        - business_value: Valore business specifico (100-200 parole)
        - market_positioning: Posizionamento sul mercato (50-100 parole)
        - target_markets: Array di mercati target (max 5)
        - competitive_advantage: Vantaggio competitivo (100-150 parole)
        
        Assicurati che la risposta sia in italiano e professionale.
        """
        
        ai_service = AIService()
        ai_response = ai_service.generate_structured_content(prompt)
        
        # Parse AI response
        if isinstance(ai_response, str):
            import json
            try:
                ai_data = json.loads(ai_response)
            except json.JSONDecodeError:
                # Fallback if AI doesn't return valid JSON
                ai_data = {
                    'name': data.get('competency_area', 'Nuova Competenza'),
                    'description': f"Competenza in {data.get('competency_area', '')} per il settore {data.get('target_market', '')}. {ai_response[:200]}",
                    'category': 'technical',
                    'min_team_size': 3,
                    'avg_proficiency_required': 3.5,
                    'business_value': 'Valore business generato dall\'AI per migliorare competitività e efficienza operativa.',
                    'market_positioning': 'Posizionamento strategico nel mercato di riferimento.',
                    'target_markets': [data.get('target_market', 'General')],
                    'competitive_advantage': 'Vantaggio competitivo derivante da questa competenza specifica.'
                }
        else:
            ai_data = ai_response
        
        return api_response(
            data=ai_data,
            message='Competenza core generata con successo tramite AI'
        )
        
    except Exception as e:
        return handle_api_error(e)

# ============================================================================
# MARKET INTELLIGENCE API
# ============================================================================

@bi_api.route('/market-prospects', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_REPORTS)
def get_market_prospects():
    """Ottiene i prospect di mercato"""
    try:
        # Filtri
        sector = request.args.get('sector')
        min_fit_score = request.args.get('min_fit_score', type=float)
        lead_status = request.args.get('lead_status')
        
        query = MarketProspect.query
        
        if sector:
            query = query.filter(MarketProspect.sector == sector)
        if min_fit_score:
            query = query.filter(MarketProspect.fit_score >= min_fit_score)
        if lead_status:
            query = query.filter(MarketProspect.lead_status == lead_status)
            
        prospects = query.order_by(MarketProspect.fit_score.desc()).all()
        
        prospects_data = []
        for prospect in prospects:
            # Ottieni competenze matching
            matching_comps = []
            if prospect.matching_competencies:
                comps = CoreCompetency.query.filter(
                    CoreCompetency.id.in_(prospect.matching_competencies)
                ).all()
                matching_comps = [{'id': c.id, 'name': c.name} for c in comps]
            
            prospects_data.append({
                'id': prospect.id,
                'company_name': prospect.company_name,
                'sector': prospect.sector,
                'size_category': prospect.size_category,
                'location': prospect.location,
                'website': prospect.website,
                'contact_info': prospect.contact_info or {},
                'technology_needs': prospect.technology_needs or [],
                'matching_competencies': matching_comps,
                'fit_score': prospect.fit_score,
                'budget_range': prospect.budget_range,
                'lead_status': prospect.lead_status,
                'source': prospect.source,
                'notes': prospect.notes,
                'assigned_to': prospect.assigned_to,
                'created_at': prospect.created_at.isoformat()
            })
        
        return api_response(
            data={'prospects': prospects_data},
            message=f"Recuperati {len(prospects_data)} prospect"
        )
        
    except Exception as e:
        return handle_api_error(e)

@bi_api.route('/market-prospects/analyze', methods=['POST'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_REPORTS)
def analyze_market_prospects():
    """Analizza prospect con AI per matching competenze"""
    try:
        data = request.get_json()
        company_data = data.get('company_data', {})
        
        # Simula analisi AI per ora (da implementare con API esterna)
        # In futuro: integrare con Sonar-Pro o API simili
        
        # Calcola fit score basato su technology_needs vs competenze
        competencies = CoreCompetency.query.filter_by(is_active=True).all()
        best_matches = []
        
        tech_needs = company_data.get('technology_needs', [])
        for comp in competencies:
            # Logica semplificata di matching
            match_score = 0
            for need in tech_needs:
                if any(need.lower() in market.lower() for market in (comp.target_markets or [])):
                    match_score += 2
                if need.lower() in comp.business_value.lower():
                    match_score += 1
            
            if match_score > 0:
                best_matches.append({
                    'competency_id': comp.id,
                    'competency_name': comp.name,
                    'match_score': match_score
                })
        
        # Ordina per score
        best_matches.sort(key=lambda x: x['match_score'], reverse=True)
        
        # Calcola fit score generale
        fit_score = min(10.0, sum(m['match_score'] for m in best_matches[:3]) * 1.5)
        
        return api_response(
            data={
                'fit_score': round(fit_score, 1),
                'matching_competencies': [m['competency_id'] for m in best_matches[:3]],
                'analysis': {
                    'top_matches': best_matches[:5],
                    'recommended_approach': 'Focus on competenze con score più alto',
                    'estimated_budget_range': {
                        'min': 15000 if fit_score > 7 else 8000,
                        'max': 50000 if fit_score > 8 else 25000
                    }
                }
            },
            message="Analisi prospect completata"
        )
        
    except Exception as e:
        return handle_api_error(e)

@bi_api.route('/market-prospects/search', methods=['POST'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_REPORTS)
def search_prospects():
    """Ricerca prospect utilizzando Sonar-Pro e Perplexity AI"""
    try:
        from services.ai import AIService
        
        data = request.get_json()
        keywords = data.get('keywords', '')
        sector = data.get('sector', '')
        location = data.get('location', '')
        size_category = data.get('size_category', '')
        technology_keywords = data.get('technology_keywords', '')
        max_results = int(data.get('max_results', 20))
        
        if not keywords:
            return jsonify({
                'success': False,
                'message': 'Keywords are required for search'
            }), 400
        
        # Prepara prompt per ricerca AI
        search_prompt = f"""
        Agisci come un esperto di market intelligence e ricerca prospect B2B.
        
        Criteri di ricerca:
        - Parole chiave: {keywords}
        - Settore: {sector or 'Qualsiasi'}
        - Località: {location or 'Globale'}
        - Dimensione: {size_category or 'Qualsiasi'}
        - Tecnologie: {technology_keywords or 'Non specificate'}
        
        Genera una lista di {max_results} aziende prospect realistiche che matchano questi criteri.
        
        Per ogni azienda, fornisci:
        1. Nome azienda (realistico ma fittizio per privacy)
        2. Breve descrizione (2-3 righe)
        3. Settore specifico
        4. Dimensione (startup/small/medium/large/enterprise)
        5. Località (città, paese)
        6. Sito web (formato realistico)
        7. Tecnologie utilizzate/ricercate
        8. Budget stimato range (min-max in EUR)
        9. Probabilità di interesse (1-10)
        
        Rispondi SOLO in formato JSON valido con array "results".
        """
        
        # Chiama AI per generare prospect
        try:
            ai_response = AIService.research_with_perplexity(
                question=search_prompt,
                context="",
                model="sonar-pro"
            )
            
            # Parse risposta AI
            import json
            import re
            
            # Estrai JSON dalla risposta
            json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
            if json_match:
                try:
                    ai_data = json.loads(json_match.group())
                    results = ai_data.get('results', [])
                    
                    # Standardizza i risultati
                    standardized_results = []
                    for i, result in enumerate(results[:max_results]):
                        standardized_result = {
                            'id': f'search_{i}',
                            'company_name': result.get('company_name', result.get('nome_azienda', f'Company {i+1}')),
                            'description': result.get('description', result.get('descrizione', '')),
                            'sector': result.get('sector', result.get('settore', sector or 'Technology')),
                            'size_category': result.get('size_category', result.get('dimensione', size_category or 'medium')),
                            'location': result.get('location', result.get('località', location or 'Milano, Italia')),
                            'website': result.get('website', result.get('sito_web', f'https://www.company{i+1}.com')),
                            'technology_needs': result.get('technology_needs', result.get('tecnologie', technology_keywords.split(',') if technology_keywords else [])),
                            'estimated_budget_min': result.get('budget_min', result.get('budget_minimo', 10000)),
                            'estimated_budget_max': result.get('budget_max', result.get('budget_massimo', 50000)),
                            'interest_probability': result.get('interest_probability', result.get('probabilità_interesse', 7))
                        }
                        standardized_results.append(standardized_result)
                    
                    return jsonify({
                        'success': True,
                        'data': {
                            'results': standardized_results,
                            'search_criteria': {
                                'keywords': keywords,
                                'sector': sector,
                                'location': location,
                                'size_category': size_category,
                                'technology_keywords': technology_keywords
                            }
                        }
                    })
                    
                except json.JSONDecodeError:
                    pass
        except Exception as ai_error:
            print(f"AI Error: {ai_error}")
        
        # Fallback: genera risultati mock
        mock_results = []
        sectors_list = ['Technology', 'Finance', 'Healthcare', 'Retail', 'Manufacturing', 'Education']
        sizes_list = ['startup', 'small', 'medium', 'large', 'enterprise']
        
        for i in range(min(max_results, 10)):
            mock_results.append({
                'id': f'mock_{i}',
                'company_name': f'{keywords.title()} Solutions {i+1}',
                'description': f'Azienda innovativa nel settore {sector or "technology"} specializzata in {keywords}.',
                'sector': sector or sectors_list[i % len(sectors_list)],
                'size_category': size_category or sizes_list[i % len(sizes_list)],
                'location': location or 'Milano, Italia',
                'website': f'https://www.{keywords.replace(" ", "").lower()}-solutions{i+1}.com',
                'technology_needs': technology_keywords.split(',') if technology_keywords else ['React', 'Python', 'AWS'],
                'estimated_budget_min': 15000 + (i * 5000),
                'estimated_budget_max': 50000 + (i * 10000),
                'interest_probability': 6 + (i % 4)
            })
        
        return jsonify({
            'success': True,
            'data': {
                'results': mock_results,
                'search_criteria': {
                    'keywords': keywords,
                    'sector': sector,
                    'location': location,
                    'size_category': size_category,
                    'technology_keywords': technology_keywords
                },
                'note': 'Results generated using AI simulation (Sonar-Pro integration placeholder)'
            }
        })
        
    except Exception as e:
        return handle_api_error(e)

# ============================================================================
# TECHNICAL OFFERS API
# ============================================================================

@bi_api.route('/technical-offers', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_REPORTS)
def get_technical_offers():
    """Ottiene le offerte tecniche"""
    try:
        offers = TechnicalOffer.query.order_by(TechnicalOffer.created_at.desc()).all()
        
        offers_data = []
        for offer in offers:
            offers_data.append({
                'id': offer.id,
                'title': offer.title,
                'description': offer.description,
                'core_competency': {
                    'id': offer.core_competency.id,
                    'name': offer.core_competency.name
                } if offer.core_competency else None,
                'target_sector': offer.target_sector,
                'technology_stack': offer.technology_stack or [],
                'team_composition': offer.team_composition or {},
                'estimated_duration_days': offer.estimated_duration_days,
                'estimated_cost_range': offer.estimated_cost_range,
                'deliverables': offer.deliverables or [],
                'success_metrics': offer.success_metrics or [],
                'risk_factors': offer.risk_factors or [],
                'generated_by_ai': offer.generated_by_ai,
                'status': offer.status,
                'created_at': offer.created_at.isoformat()
            })
        
        return api_response(
            data={'offers': offers_data},
            message=f"Recuperate {len(offers_data)} offerte tecniche"
        )
        
    except Exception as e:
        return handle_api_error(e)

@bi_api.route('/technical-offers/generate-ai', methods=['POST'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_REPORTS)
def generate_technical_offer_ai():
    """Genera offerta tecnica con AI"""
    try:
        data = request.get_json()
        competency_id = data.get('competency_id')
        target_sector = data.get('target_sector')
        custom_requirements = data.get('custom_requirements', '')
        
        competency = CoreCompetency.query.get_or_404(competency_id)
        
        # Genera offerta AI utilizzando servizio AI reale
        try:
            from services.ai import generate_technical_offer_ai
            ai_generated_offer = generate_technical_offer_ai(
                competency_name=competency.name,
                target_sector=target_sector,
                competency_description=competency.description
            )
        except Exception as e:
            current_app.logger.error(f"Errore generazione AI offerta tecnica: {str(e)}")
            return api_response(
                message=f"Errore nella generazione AI dell'offerta tecnica: {str(e)}",
                status_code=500
            )
        
        # Crea l'offerta
        offer = TechnicalOffer(
            title=ai_generated_offer['title'],
            description=ai_generated_offer['description'],
            core_competency_id=competency_id,
            target_sector=target_sector,
            technology_stack=ai_generated_offer['technology_stack'],
            team_composition=ai_generated_offer['team_composition'],
            estimated_duration_days=ai_generated_offer['estimated_duration_days'],
            estimated_cost_min=ai_generated_offer['estimated_cost_min'],
            estimated_cost_max=ai_generated_offer['estimated_cost_max'],
            deliverables=ai_generated_offer['deliverables'],
            success_metrics=ai_generated_offer['success_metrics'],
            risk_factors=ai_generated_offer['risk_factors'],
            generated_by_ai=True,
            ai_prompt_used=f"Competency: {competency.name}, Sector: {target_sector}, Requirements: {custom_requirements}",
            created_by=current_user.id
        )
        
        db.session.add(offer)
        db.session.commit()
        
        return api_response(
            data={
                'offer_id': offer.id,
                'generated_offer': ai_generated_offer
            },
            message="Offerta tecnica generata con successo"
        )
        
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

# ============================================================================
# ADVANCED REPORTS API
# ============================================================================

@bi_api.route('/reports', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_REPORTS)
def get_bi_reports():
    """Ottiene i report BI configurati"""
    try:
        reports = BIReport.query.filter_by(status='active').order_by(BIReport.created_at.desc()).all()
        
        reports_data = []
        for report in reports:
            reports_data.append({
                'id': report.id,
                'name': report.name,
                'description': report.description,
                'report_type': report.report_type,
                'data_sources': report.data_sources or [],
                'filters': report.filters or {},
                'chart_config': report.chart_config or {},
                'export_formats': report.export_formats or [],
                'is_scheduled': report.is_scheduled,
                'last_generated': report.last_generated.isoformat() if report.last_generated else None,
                'next_generation': report.next_generation.isoformat() if report.next_generation else None,
                'is_overdue': report.is_overdue,
                'created_at': report.created_at.isoformat()
            })
        
        return api_response(
            data={'reports': reports_data},
            message=f"Recuperati {len(reports_data)} report"
        )
        
    except Exception as e:
        return handle_api_error(e)

@bi_api.route('/reports/<int:report_id>/generate', methods=['POST'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_REPORTS)
def generate_bi_report(report_id):
    """Genera un report specifico"""
    try:
        report = BIReport.query.get_or_404(report_id)
        
        # Simula generazione report (da implementare logica reale)
        if report.report_type == 'skills_analysis':
            # Genera report competenze
            report_data = generate_skills_analysis_report(report.filters or {})
        elif report.report_type == 'market_analysis':
            # Genera report market intelligence
            report_data = generate_market_analysis_report(report.filters or {})
        else:
            report_data = {'message': 'Tipo report non supportato'}
        
        # Aggiorna timestamp generazione
        report.last_generated = datetime.utcnow()
        db.session.commit()
        
        return api_response(
            data={
                'report_id': report_id,
                'generated_at': report.last_generated.isoformat(),
                'data': report_data
            },
            message=f"Report '{report.name}' generato con successo"
        )
        
    except Exception as e:
        return handle_api_error(e)

def generate_skills_analysis_report(filters):
    """Genera report analisi competenze"""
    try:
        # Analisi competenze team
        skills_data = db.session.query(
            Skill.name,
            Skill.category,
            func.count(UserSkill.id).label('team_count'),
            func.avg(UserSkill.proficiency_level).label('avg_level'),
            func.max(UserSkill.proficiency_level).label('max_level')
        ).join(UserSkill).group_by(Skill.id, Skill.name, Skill.category).all()
        
        skills_analysis = []
        for skill in skills_data:
            skills_analysis.append({
                'skill': skill.name,
                'category': skill.category,
                'team_count': skill.team_count,
                'avg_proficiency': round(float(skill.avg_level), 1),
                'max_proficiency': skill.max_level,
                'coverage_percentage': round((skill.team_count / 10) * 100, 1)  # Assumendo team di 10
            })
        
        # Gap analysis con core competencies
        competencies = CoreCompetency.query.filter_by(is_active=True).all()
        gap_analysis = []
        
        for comp in competencies:
            if comp.skill_ids:
                covered_users = db.session.query(UserSkill.user_id).filter(
                    UserSkill.skill_id.in_(comp.skill_ids),
                    UserSkill.proficiency_level >= comp.avg_proficiency_required
                ).distinct().count()
                
                gap_analysis.append({
                    'competency': comp.name,
                    'required_team_size': comp.min_team_size,
                    'current_coverage': covered_users,
                    'gap': max(0, comp.min_team_size - covered_users),
                    'status': 'sufficient' if covered_users >= comp.min_team_size else 'gap'
                })
        
        return {
            'skills_analysis': skills_analysis,
            'gap_analysis': gap_analysis,
            'summary': {
                'total_skills': len(skills_analysis),
                'avg_team_proficiency': round(sum(s['avg_proficiency'] for s in skills_analysis) / len(skills_analysis), 1) if skills_analysis else 0,
                'competencies_with_gaps': len([g for g in gap_analysis if g['status'] == 'gap'])
            }
        }
        
    except Exception as e:
        return {'error': str(e)}

def generate_market_analysis_report(filters):
    """Genera report market intelligence"""
    try:
        # Analisi prospect per settore
        sector_analysis = db.session.query(
            MarketProspect.sector,
            func.count(MarketProspect.id).label('prospect_count'),
            func.avg(MarketProspect.fit_score).label('avg_fit_score'),
            func.sum(MarketProspect.estimated_budget_max).label('total_potential')
        ).group_by(MarketProspect.sector).all()
        
        sectors_data = []
        for sector in sector_analysis:
            sectors_data.append({
                'sector': sector.sector,
                'prospect_count': sector.prospect_count,
                'avg_fit_score': round(float(sector.avg_fit_score or 0), 1),
                'total_potential': float(sector.total_potential or 0)
            })
        
        # Top prospect
        top_prospects = MarketProspect.query.filter(
            MarketProspect.fit_score >= filters.get('fit_score_min', 7.0)
        ).order_by(MarketProspect.fit_score.desc()).limit(10).all()
        
        prospects_data = []
        for prospect in top_prospects:
            prospects_data.append({
                'company': prospect.company_name,
                'sector': prospect.sector,
                'fit_score': prospect.fit_score,
                'budget_max': prospect.estimated_budget_max,
                'lead_status': prospect.lead_status
            })
        
        return {
            'sector_analysis': sectors_data,
            'top_prospects': prospects_data,
            'summary': {
                'total_prospects': MarketProspect.query.count(),
                'qualified_prospects': MarketProspect.query.filter(MarketProspect.fit_score >= 7.0).count(),
                'total_market_potential': sum(s['total_potential'] for s in sectors_data)
            }
        }
        
    except Exception as e:
        return {'error': str(e)}

# ==================== ENDPOINTS SCHEDULER ====================

@bi_api.route('/scheduler/status', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_REPORTS)
def get_scheduler_status():
    """Ottiene lo status dello scheduler."""
    try:
        from services.scheduler import report_scheduler
        
        status_data = {
            'is_enabled': report_scheduler.is_enabled(),
            'mode': 'on-demand',
            'scheduled_reports_count': BIReport.query.filter_by(is_scheduled=True, status='active').count(),
            'description': 'Scheduler configurato per generazione manuale',
            'last_activity': datetime.utcnow().isoformat()
        }
        
        return api_response(data=status_data)
        
    except Exception as e:
        return handle_api_error(e)

@bi_api.route('/scheduler/run-scheduled', methods=['POST'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_REPORTS)
def run_scheduled_reports():
    """Esegue manualmente tutti i report schedulati che sono in scadenza."""
    try:
        from services.scheduler import report_scheduler
        
        if not report_scheduler.is_enabled():
            return api_response(
                message="Scheduler disabilitato. Abilitare ENABLE_REPORT_SCHEDULER=true in .env",
                status_code=400
            )
        
        # Esegui check manuale
        generated_count = report_scheduler.check_and_generate_reports()
        
        return api_response(
            message=f"Controllo completato. {generated_count} report generati.",
            data={'generated_reports': generated_count}
        )
        
    except Exception as e:
        return handle_api_error(e)

@bi_api.route('/reports/<int:report_id>/schedule', methods=['POST'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_REPORTS)
def update_report_schedule(report_id):
    """Aggiorna la configurazione di schedulazione di un report."""
    try:
        data = request.get_json()
        
        report = BIReport.query.get_or_404(report_id)
        
        # Aggiorna configurazione schedulazione
        report.is_scheduled = data.get('is_scheduled', False)
        report.schedule_frequency = data.get('schedule_frequency')
        report.schedule_recipients = data.get('schedule_recipients', '')
        
        if report.is_scheduled and not report.next_generation:
            # Calcola prossima generazione
            from services.scheduler import report_scheduler
            report.next_generation = report_scheduler.calculate_next_generation(
                report.schedule_frequency, 
                datetime.utcnow()
            )
        
        db.session.commit()
        
        return api_response(
            message="Configurazione schedulazione aggiornata",
            data={
                'id': report.id,
                'is_scheduled': report.is_scheduled,
                'schedule_frequency': report.schedule_frequency,
                'next_generation': report.next_generation.isoformat() if report.next_generation else None
            }
        )
        
    except Exception as e:
        return handle_api_error(e)