"""
API endpoints for OAuth authentication flow.
Handles OAuth login redirects and callbacks for Google and Microsoft.
"""

from datetime import datetime
from flask import Blueprint, request, redirect, url_for, session, current_app
from flask_login import login_user, current_user, logout_user

from extensions import db
from models import User
from utils.api_utils import api_response, handle_api_error
from services.oauth_service import OAuthService

# Create blueprint
api_oauth = Blueprint('api_oauth', __name__)

@api_oauth.route('/oauth/login/<provider>', methods=['GET'])
def oauth_login(provider):
    """
    Initiate OAuth login flow for specified provider.
    
    Args:
        provider: OAuth provider ('google' or 'microsoft')
        
    Redirects user to OAuth provider authorization URL.
    """
    try:
        # Validate provider
        if provider not in ['google', 'microsoft']:
            current_app.logger.warning(f"Invalid OAuth provider requested: {provider}")
            return redirect('/?error=provider_not_supported')
            
        # Check if provider is enabled in configuration
        if provider == 'google' and not current_app.config.get('GOOGLE_OAUTH_ENABLED'):
            current_app.logger.warning("Google OAuth login attempted but not enabled")
            return redirect('/?error=google_oauth_disabled')
            
        if provider == 'microsoft' and not current_app.config.get('MICROSOFT_OAUTH_ENABLED'):
            current_app.logger.warning("Microsoft OAuth login attempted but not enabled")
            return redirect('/?error=microsoft_oauth_disabled')
            
        # Generate OAuth authorization URL
        auth_url = OAuthService.get_auth_url(provider)
        
        current_app.logger.info(f"OAuth login initiated for provider: {provider}")
        
        # Redirect to OAuth provider
        return redirect(auth_url)
        
    except ValueError as e:
        current_app.logger.error(f"OAuth login error for {provider}: {str(e)}")
        return redirect('/?error=oauth_error')
    except Exception as e:
        current_app.logger.error(f"Unexpected OAuth login error for {provider}: {str(e)}")
        return redirect('/?error=system_error')

@api_oauth.route('/oauth/callback', methods=['GET'])
def oauth_callback():
    """
    Handle OAuth callback from providers.
    Processes authorization code and links to existing user.
    """
    try:
        # Get authorization code and state from query parameters
        code = request.args.get('code')
        state = request.args.get('state')
        error = request.args.get('error')
        
        # Check for OAuth provider errors
        if error:
            current_app.logger.warning(f"OAuth provider returned error: {error}")
            
            # Map common OAuth errors to user-friendly messages
            error_messages = {
                'access_denied': 'oauth_access_denied',
                'invalid_request': 'oauth_invalid_request',
                'unauthorized_client': 'oauth_unauthorized',
                'unsupported_response_type': 'oauth_unsupported',
                'invalid_scope': 'oauth_invalid_scope',
                'server_error': 'oauth_server_error',
                'temporarily_unavailable': 'oauth_temporarily_unavailable'
            }
            error_key = error_messages.get(error, 'oauth_error')
            return redirect(f'/?error={error_key}')
            
        # Validate required parameters
        if not code:
            current_app.logger.warning("OAuth callback missing authorization code")
            return redirect('/?error=oauth_no_code')
            
        if not state:
            current_app.logger.warning("OAuth callback missing state parameter")
            return redirect('/?error=oauth_no_state')
            
        # Process OAuth callback and get user
        user = OAuthService.handle_callback(code, state)
        
        # Check if user is active
        if not user.is_active:
            current_app.logger.warning(f"OAuth login attempt for inactive user: {user.username}")
            return redirect('/?error=user_inactive')
            
        # Clear OAuth session data first
        session.pop('oauth_state', None)
        session.pop('oauth_provider', None)
        
        # Update last login timestamp
        user.last_login = datetime.utcnow()
        db.session.commit()
        
        # Log user in (this must be after session cleanup)
        login_user(user, remember=True)
        
        current_app.logger.info(f"OAuth login successful for user: {user.username}")
        
        # Redirect to SPA root with success parameter
        return redirect('/?oauth_success=true')
        
    except ValueError as e:
        # Business logic errors (user not found, invalid state, etc.)
        current_app.logger.warning(f"OAuth callback validation error: {str(e)}")
        
        # Map specific errors to user-friendly messages
        error_message = 'oauth_error'
        if 'Nessun utente trovato' in str(e):
            error_message = 'user_not_found'
        elif 'Invalid state parameter' in str(e):
            error_message = 'oauth_csrf_error'
        elif 'Provider' in str(e) and 'non supportato' in str(e):
            error_message = 'provider_not_supported'
        elif 'Impossibile ottenere token' in str(e):
            error_message = 'oauth_token_error'
        elif 'Impossibile ottenere informazioni utente' in str(e):
            error_message = 'oauth_userinfo_error'
            
        return redirect(f'/?error={error_message}')
        
    except Exception as e:
        # Unexpected system errors
        current_app.logger.error(f"Unexpected OAuth callback error: {str(e)}")
        return redirect('/?error=system_error')

@api_oauth.route('/oauth/providers', methods=['GET'])
def get_oauth_providers():
    """
    Get list of enabled OAuth providers for frontend.
    
    Returns:
        JSON response with enabled providers and their configuration
    """
    try:
        providers = {}
        
        # Check Google OAuth
        if current_app.config.get('GOOGLE_OAUTH_ENABLED', False):
            providers['google'] = {
                'name': 'Google',
                'display_name': 'Accedi con Google',
                'enabled': True,
                'login_url': url_for('api_oauth.oauth_login', provider='google')
            }
            
        # Check Microsoft OAuth
        if current_app.config.get('MICROSOFT_OAUTH_ENABLED', False):
            providers['microsoft'] = {
                'name': 'Microsoft',
                'display_name': 'Accedi con Microsoft',
                'enabled': True,
                'login_url': url_for('api_oauth.oauth_login', provider='microsoft')
            }
            
        return api_response({
            'providers': providers,
            'oauth_enabled': len(providers) > 0,
            'require_existing_user': current_app.config.get('OAUTH_REQUIRE_EXISTING_USER', True)
        })
        
    except Exception as e:
        return handle_api_error(e, 'Errore nel recupero provider OAuth')

@api_oauth.route('/oauth/accounts', methods=['GET'])
def get_user_oauth_accounts():
    """
    Get OAuth accounts linked to current user.
    Requires authentication.
    
    Returns:
        JSON response with user's linked OAuth accounts
    """
    try:
        # Check if user is authenticated
        if not current_user.is_authenticated:
            return api_response({'error': 'Autenticazione richiesta'}, 401)
            
        # Get user's OAuth accounts
        oauth_accounts = OAuthService.get_user_oauth_accounts(current_user.id)
        
        return api_response({
            'oauth_accounts': [account.to_dict() for account in oauth_accounts],
            'total': len(oauth_accounts)
        })
        
    except Exception as e:
        return handle_api_error(e, 'Errore nel recupero account OAuth')

@api_oauth.route('/oauth/accounts/<int:account_id>', methods=['DELETE'])
def unlink_oauth_account(account_id):
    """
    Unlink an OAuth account from current user.
    Requires authentication and account ownership.
    
    Args:
        account_id: ID of OAuth account to unlink
        
    Returns:
        JSON response confirming unlinking
    """
    try:
        # Check if user is authenticated
        if not current_user.is_authenticated:
            return api_response({'error': 'Autenticazione richiesta'}, 401)
            
        # Unlink OAuth account (with authorization check)
        success = OAuthService.unlink_oauth_account(account_id, current_user.id)
        
        if success:
            current_app.logger.info(f"OAuth account {account_id} unlinked by user {current_user.username}")
            return api_response({
                'message': 'Account OAuth scollegato con successo'
            })
        else:
            return api_response({'error': 'Errore nello scollegamento account'}, 500)
            
    except ValueError as e:
        # Business logic errors (account not found, not authorized, etc.)
        current_app.logger.warning(f"OAuth unlink error for user {current_user.username}: {str(e)}")
        
        if 'Account OAuth non trovato' in str(e):
            return api_response({'error': 'Account OAuth non trovato'}, 404)
        elif 'Non autorizzato' in str(e):
            return api_response({'error': 'Non autorizzato a scollegare questo account'}, 403)
        else:
            return api_response({'error': str(e)}, 400)
            
    except Exception as e:
        return handle_api_error(e, 'Errore nello scollegamento account OAuth')

@api_oauth.route('/oauth/status', methods=['GET'])
def get_oauth_status():
    """
    Get OAuth configuration status for system monitoring.
    
    Returns:
        JSON response with OAuth system status
    """
    try:
        status = {
            'oauth_enabled': False,
            'providers': {},
            'configuration': {
                'require_existing_user': current_app.config.get('OAUTH_REQUIRE_EXISTING_USER', True),
                'redirect_uri': current_app.config.get('OAUTH_REDIRECT_URI')
            }
        }
        
        # Check Google configuration
        google_enabled = current_app.config.get('GOOGLE_OAUTH_ENABLED', False)
        status['providers']['google'] = {
            'enabled': google_enabled,
            'configured': bool(current_app.config.get('GOOGLE_CLIENT_ID'))
        }
        
        # Check Microsoft configuration
        microsoft_enabled = current_app.config.get('MICROSOFT_OAUTH_ENABLED', False)
        status['providers']['microsoft'] = {
            'enabled': microsoft_enabled,
            'configured': bool(current_app.config.get('MICROSOFT_CLIENT_ID'))
        }
        
        # Overall OAuth status
        status['oauth_enabled'] = google_enabled or microsoft_enabled
        
        return api_response(status)
        
    except Exception as e:
        return handle_api_error(e, 'Errore nel controllo stato OAuth')