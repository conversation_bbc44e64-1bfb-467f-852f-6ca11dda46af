"""
API RESTful per il sistema help e documentazione.
"""

from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from sqlalchemy import or_, desc
from models import HelpCategory, HelpContent, HelpConversation, HelpFeedback, HelpAnalytics
from utils.api_utils import (
    api_response, handle_api_error, get_pagination_params,
    format_pagination
)
from extensions import db

# Crea il blueprint per le API help
api_help = Blueprint('api_help', __name__, url_prefix='/help')

@api_help.route('/categories', methods=['GET'])
@login_required
def get_help_categories():
    """
    Ottiene la lista delle categorie help.
    """
    try:
        # Query base
        query = HelpCategory.query
        
        # Filtro per stato attivo
        include_inactive = request.args.get('include_inactive', 'false').lower() == 'true'
        if not include_inactive:
            query = query.filter(HelpCategory.is_active == True)
        
        # Ordina per sort_order e nome
        query = query.order_by(HelpCategory.sort_order, HelpCategory.name)
        
        categories = query.all()
        
        return api_response(data={
            'categories': [cat.to_dict() for cat in categories]
        })
        
    except Exception as e:
        current_app.logger.error(f"Error fetching help categories: {str(e)}")
        return handle_api_error(e)


@api_help.route('/categories/<int:category_id>', methods=['GET'])
@login_required
def get_help_category(category_id):
    """Ottiene una singola categoria help."""
    try:
        category = HelpCategory.query.get_or_404(category_id)
        return api_response(data=category.to_dict())
        
    except Exception as e:
        current_app.logger.error(f"Error fetching category {category_id}: {str(e)}")
        return handle_api_error(e)


@api_help.route('/content', methods=['GET'])
@login_required
def get_help_content():
    """
    Ottiene la lista dei contenuti help con supporto per filtri e paginazione.
    """
    try:
        # Parametri di paginazione
        page, per_page = get_pagination_params()
        
        # Parametri di ricerca
        search = request.args.get('search', '').strip()
        category_id = request.args.get('category_id', type=int)
        content_type = request.args.get('content_type')
        difficulty = request.args.get('difficulty')
        featured = request.args.get('featured')
        
        # Query base
        query = HelpContent.query.filter(HelpContent.is_published == True)
        
        # Applica filtri di ricerca
        if search:
            search_filter = or_(
                HelpContent.title.ilike(f'%{search}%'),
                HelpContent.content.ilike(f'%{search}%'),
                HelpContent.excerpt.ilike(f'%{search}%')
            )
            query = query.filter(search_filter)
        
        # Filtro per categoria
        if category_id:
            query = query.filter(HelpContent.category_id == category_id)
        
        # Filtro per tipo di contenuto
        if content_type:
            query = query.filter(HelpContent.content_type == content_type)
        
        # Filtro per difficoltà
        if difficulty:
            query = query.filter(HelpContent.difficulty_level == difficulty)
        
        # Filtro per contenuti in evidenza
        if featured and featured.lower() == 'true':
            query = query.filter(HelpContent.featured == True)
        
        # Ordina per featured, view_count, data di creazione
        query = query.order_by(
            desc(HelpContent.featured),
            desc(HelpContent.view_count),
            desc(HelpContent.created_at)
        )
        
        # Paginazione
        content_paginated = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        return api_response(data={
            'content': [item.to_dict() for item in content_paginated.items],
            'pagination': format_pagination(content_paginated)
        })
        
    except Exception as e:
        current_app.logger.error(f"Error fetching help content: {str(e)}")
        return handle_api_error(e)


@api_help.route('/content/<int:content_id>', methods=['GET'])
@login_required
def get_help_content_item(content_id):
    """Ottiene un singolo contenuto help."""
    try:
        content = HelpContent.query.get_or_404(content_id)
        
        # Incrementa il contatore di visualizzazioni
        content.view_count += 1
        db.session.commit()
        
        return api_response(data=content.to_dict())
        
    except Exception as e:
        current_app.logger.error(f"Error fetching content {content_id}: {str(e)}")
        return handle_api_error(e)


@api_help.route('/content/<int:content_id>/vote', methods=['POST'])
@login_required
def vote_help_content(content_id):
    """Vota l'utilità di un contenuto help."""
    try:
        content = HelpContent.query.get_or_404(content_id)
        data = request.get_json()
        
        if not data or 'helpful' not in data:
            return api_response(
                success=False,
                message="Campo 'helpful' richiesto",
                status_code=400
            )
        
        is_helpful = data['helpful']
        
        # Verifica se l'utente ha già votato
        existing_feedback = HelpFeedback.query.filter_by(
            content_id=content_id,
            user_id=current_user.id,
            feedback_type='helpful' if is_helpful else 'not_helpful'
        ).first()
        
        if existing_feedback:
            return api_response(
                success=False,
                message="Hai già votato questo contenuto",
                status_code=409
            )
        
        # Aggiungi il voto
        if is_helpful:
            content.helpful_votes += 1
        else:
            content.not_helpful_votes += 1
        
        # Crea record feedback
        feedback = HelpFeedback(
            content_id=content_id,
            user_id=current_user.id,
            feedback_type='helpful' if is_helpful else 'not_helpful'
        )
        db.session.add(feedback)
        db.session.commit()
        
        return api_response(
            data={'helpful_votes': content.helpful_votes, 'not_helpful_votes': content.not_helpful_votes},
            message="Voto registrato con successo"
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error voting content {content_id}: {str(e)}")
        return handle_api_error(e)


@api_help.route('/feedback', methods=['POST'])
@login_required
def submit_help_feedback():
    """Invia feedback sul sistema help."""
    try:
        data = request.get_json()
        
        if not data or not data.get('feedback_type'):
            return api_response(
                success=False,
                message="Campo 'feedback_type' richiesto",
                status_code=400
            )
        
        feedback = HelpFeedback(
            content_id=data.get('content_id'),
            user_id=current_user.id,
            feedback_type=data['feedback_type'],
            feedback_text=data.get('feedback_text'),
            rating=data.get('rating')
        )
        
        db.session.add(feedback)
        db.session.commit()
        
        return api_response(
            data=feedback.to_dict(),
            message="Feedback inviato con successo",
            status_code=201
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error submitting feedback: {str(e)}")
        return handle_api_error(e)


@api_help.route('/search', methods=['GET'])
@login_required
def search_help():
    """Ricerca avanzata nei contenuti help."""
    try:
        search_query = request.args.get('q', '').strip()
        
        if not search_query:
            return api_response(
                success=False,
                message="Parametro di ricerca 'q' richiesto",
                status_code=400
            )
        
        # Cerca in categorie e contenuti
        categories = HelpCategory.query.filter(
            HelpCategory.is_active == True,
            or_(
                HelpCategory.name.ilike(f'%{search_query}%'),
                HelpCategory.description.ilike(f'%{search_query}%')
            )
        ).all()
        
        content = HelpContent.query.filter(
            HelpContent.is_published == True,
            or_(
                HelpContent.title.ilike(f'%{search_query}%'),
                HelpContent.content.ilike(f'%{search_query}%'),
                HelpContent.excerpt.ilike(f'%{search_query}%')
            )
        ).limit(20).all()
        
        return api_response(data={
            'query': search_query,
            'categories': [cat.to_dict() for cat in categories],
            'content': [item.to_dict() for item in content]
        })
        
    except Exception as e:
        current_app.logger.error(f"Error searching help: {str(e)}")
        return handle_api_error(e)


@api_help.route('/chat/start', methods=['POST'])
@login_required
def start_chat_session():
    """Avvia nuova sessione chat con assistente AI."""
    try:
        data = request.get_json()
        
        current_module = data.get('current_module')
        title = data.get('title', 'Nuova conversazione help')
        
        # Genera session ID unico
        import uuid
        session_id = str(uuid.uuid4())
        
        # Crea record conversazione
        conversation = HelpConversation(
            user_id=current_user.id,
            session_id=session_id,
            title=title,
            messages='[]',  # Array vuoto iniziale
            status='active'
        )
        
        db.session.add(conversation)
        db.session.commit()
        
        # Messaggio di benvenuto
        welcome_message = {
            'id': str(uuid.uuid4()),
            'type': 'ai',
            'content': f"Ciao! Sono l'assistente AI di DatPortal. Come posso aiutarti{' con ' + current_module if current_module else ''}?",
            'timestamp': conversation.created_at.isoformat(),
            'metadata': {
                'welcome': True,
                'module': current_module
            }
        }
        
        # Aggiorna conversation con messaggio di benvenuto
        import json
        messages = [welcome_message]
        conversation.messages = json.dumps(messages)
        db.session.commit()
        
        return api_response(
            data={
                'session_id': session_id,
                'title': title,
                'messages': messages,
                'status': 'active',
                'created_at': conversation.created_at.isoformat()
            },
            message="Sessione chat avviata con successo",
            status_code=201
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error starting chat session: {str(e)}")
        return handle_api_error(e)


@api_help.route('/chat/<session_id>/message', methods=['POST'])
@login_required
def send_chat_message(session_id):
    """Invia messaggio nella sessione chat."""
    try:
        data = request.get_json()
        
        if not data or not data.get('message'):
            return api_response(
                success=False,
                message="Campo 'message' richiesto",
                status_code=400
            )
        
        # Trova conversazione
        conversation = HelpConversation.query.filter_by(
            session_id=session_id,
            user_id=current_user.id
        ).first()
        
        if not conversation:
            return api_response(
                success=False,
                message="Sessione chat non trovata",
                status_code=404
            )
        
        # Carica messaggi esistenti
        import json
        import uuid
        from datetime import datetime
        
        existing_messages = json.loads(conversation.messages) if conversation.messages else []
        
        # Aggiungi messaggio utente
        user_message = {
            'id': str(uuid.uuid4()),
            'type': 'user',
            'content': data['message'],
            'timestamp': datetime.utcnow().isoformat()
        }
        existing_messages.append(user_message)
        
        # Processa con AI service
        import asyncio
        from services.help_ai_service import HelpAIService
        
        ai_service = HelpAIService()
        
        # Chiamata async wrapped in sync
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            ai_response = loop.run_until_complete(
                ai_service.process_help_query(
                    user_message=data['message'],
                    user_id=current_user.id,
                    session_id=session_id,
                    current_module=data.get('current_module')
                )
            )
        finally:
            loop.close()
        
        # Aggiungi risposta AI
        ai_message = {
            'id': str(uuid.uuid4()),
            'type': 'ai',
            'content': ai_response.get('content', 'Mi dispiace, non riesco a elaborare la richiesta al momento.'),
            'timestamp': datetime.utcnow().isoformat(),
            'metadata': {
                'confidence': ai_response.get('confidence', 0.5),
                'category': ai_response.get('category'),
                'support_type': ai_response.get('support_type'),
                'suggested_actions': ai_response.get('suggested_actions', []),
                'escalation_recommended': ai_response.get('escalation_recommended', False)
            }
        }
        existing_messages.append(ai_message)
        
        # Salva messaggi aggiornati
        conversation.messages = json.dumps(existing_messages)
        conversation.updated_at = datetime.utcnow()
        db.session.commit()
        
        return api_response(
            data={
                'message': ai_message,
                'session_id': session_id,
                'total_messages': len(existing_messages)
            },
            message="Messaggio inviato con successo"
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error sending chat message: {str(e)}")
        return handle_api_error(e)


@api_help.route('/chat/<session_id>', methods=['GET'])
@login_required
def get_chat_conversation(session_id):
    """Ottiene conversazione chat esistente."""
    try:
        conversation = HelpConversation.query.filter_by(
            session_id=session_id,
            user_id=current_user.id
        ).first()
        
        if not conversation:
            return api_response(
                success=False,
                message="Conversazione non trovata",
                status_code=404
            )
        
        return api_response(data=conversation.to_dict())
        
    except Exception as e:
        current_app.logger.error(f"Error fetching conversation {session_id}: {str(e)}")
        return handle_api_error(e)


@api_help.route('/chat/conversations', methods=['GET'])
@login_required
def get_user_conversations():
    """Ottiene lista conversazioni utente."""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 10, type=int), 50)
        
        conversations = HelpConversation.query.filter_by(
            user_id=current_user.id
        ).order_by(
            desc(HelpConversation.updated_at)
        ).paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        return api_response(data={
            'conversations': [conv.to_dict() for conv in conversations.items],
            'pagination': {
                'page': conversations.page,
                'per_page': conversations.per_page,
                'total': conversations.total,
                'pages': conversations.pages
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"Error fetching user conversations: {str(e)}")
        return handle_api_error(e)