"""
API endpoints for recruiting management.
Provides REST API for job postings, candidates, applications, and interviews.
Riutilizza il sistema CV esistente da personnel per analisi competenze.
"""

import os
import json
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, current_app, make_response
from flask_login import current_user
from sqlalchemy import desc, and_, or_, func
from sqlalchemy.orm import joinedload
from werkzeug.utils import secure_filename

from extensions import db
from models import (
    JobPosting, Candidate, Application, InterviewSession, 
    RecruitingWorkflow, CandidateSkill, User, Project, 
    Department, Proposal
)
from utils.api_utils import (
    api_response, get_pagination_params, api_permission_required,
    handle_api_error, api_login_required, format_pagination, require_feature
)
from utils.permissions import (
    PERMISSION_VIEW_PERSONNEL_DATA, PERMISSION_EDIT_PERSONNEL_DATA
)
from utils.cv_parser import extract_text_from_cv, is_valid_cv_file, get_file_size_mb
from services.ai import extract_skills_from_cv
from services.email_service import recruiting_email_service
from services.calendar_service import recruiting_calendar_service
from middleware.audit_logger import audit_export_operation, audit_file_download

# Create blueprint
api_recruiting = Blueprint('api_recruiting', __name__, url_prefix='/recruiting')

# CSRF exemption for API endpoints
from extensions import csrf
csrf.exempt(api_recruiting)

# =============================================================================
# PUBLIC ENDPOINTS (NO AUTH REQUIRED)
# =============================================================================

@api_recruiting.route('/public/job-postings', methods=['GET'])
def get_public_job_postings():
    """Ottiene posizioni pubbliche aperte per candidature esterne."""
    try:
        # Solo posizioni attive e pubbliche
        query = JobPosting.query.filter(
            JobPosting.status == 'active',
            JobPosting.is_public == True
        ).options(
            joinedload(JobPosting.department),
            joinedload(JobPosting.project)
        ).order_by(desc(JobPosting.created_at))
        
        job_postings = query.all()
        
        # Serializzazione per pubblico (rimuovi info sensibili)
        public_jobs = []
        for job in job_postings:
            public_job = {
                'id': job.id,
                'title': job.title,
                'description': job.description,
                'requirements': job.requirements,
                'responsibilities': job.responsibilities,
                'location': job.location,
                'remote_allowed': job.remote_allowed,
                'employment_type': job.employment_type,
                'salary_min': float(job.salary_min) if job.salary_min else None,
                'salary_max': float(job.salary_max) if job.salary_max else None,
                'salary_currency': job.salary_currency,
                'department': {
                    'id': job.department.id,
                    'name': job.department.name
                } if job.department else None,
                'published_at': job.published_at.isoformat() if job.published_at else None,
                'created_at': job.created_at.isoformat()
            }
            public_jobs.append(public_job)
        
        return api_response({
            'job_postings': public_jobs,
            'total': len(public_jobs)
        }, 'Posizioni pubbliche caricate con successo')
        
    except Exception as e:
        return handle_api_error(e, 'Errore nel caricamento posizioni pubbliche')

@api_recruiting.route('/public/applications', methods=['POST'])
def submit_public_application():
    """Gestisce candidature pubbliche da sito web."""
    try:
        data = request.get_json()
        
        if not data:
            return api_response(None, 'Dati richiesta mancanti', success=False, status_code=400)
        
        # Validazione campi obbligatori
        required_fields = ['job_posting_id', 'first_name', 'last_name', 'email']
        missing_fields = [field for field in required_fields if not data.get(field)]
        
        if missing_fields:
            return api_response(
                None, 
                f'Campi obbligatori mancanti: {", ".join(missing_fields)}', 
                success=False, 
                status_code=400
            )
        
        # Verifica che la posizione esista ed sia pubblica
        job_posting = JobPosting.query.filter(
            JobPosting.id == data['job_posting_id'],
            JobPosting.status == 'active',
            JobPosting.is_public == True
        ).first()
        
        if not job_posting:
            return api_response(
                None, 
                'Posizione non trovata o non più disponibile', 
                success=False, 
                status_code=404
            )
        
        # Crea o trova candidato esistente
        candidate = Candidate.query.filter_by(email=data['email']).first()
        
        if not candidate:
            # Crea nuovo candidato
            candidate = Candidate(
                first_name=data['first_name'],
                last_name=data['last_name'],
                email=data['email'],
                phone=data.get('phone'),
                source='website',
                status='new',
                created_at=datetime.utcnow()
            )
            db.session.add(candidate)
            db.session.flush()  # Per ottenere l'ID
        
        # Verifica candidatura duplicata
        existing_application = Application.query.filter_by(
            candidate_id=candidate.id,
            job_posting_id=job_posting.id
        ).first()
        
        if existing_application:
            return api_response(
                None, 
                'Hai già inviato una candidatura per questa posizione', 
                success=False, 
                status_code=409
            )
        
        # Crea candidatura
        application = Application(
            job_posting_id=job_posting.id,
            candidate_id=candidate.id,
            applied_at=datetime.utcnow(),
            cover_letter=data.get('cover_letter', ''),
            current_step='application_received',
            status='pending',
            created_at=datetime.utcnow()
        )
        
        db.session.add(application)
        db.session.commit()
        
        # Invia email di conferma al candidato
        try:
            recruiting_email_service.send_application_confirmation(application)
            current_app.logger.info(f"Email conferma inviata al candidato {candidate.email}")
        except Exception as e:
            current_app.logger.error(f"Errore invio email conferma candidato {candidate.email}: {str(e)}")
        
        # Notifica HR team
        try:
            recruiting_email_service.send_new_application_notification(application)
            current_app.logger.info(f"Notifica HR inviata per candidatura {application.id}")
        except Exception as e:
            current_app.logger.error(f"Errore invio notifica HR per candidatura {application.id}: {str(e)}")
        
        return api_response({
            'application_id': application.id,
            'candidate_id': candidate.id,
            'message': 'Candidatura inviata con successo'
        }, 'Candidatura ricevuta! Ti contatteremo presto.')
        
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e, 'Errore nell\'invio della candidatura')

# =============================================================================
# AUTHENTICATED ENDPOINTS
# =============================================================================

@api_recruiting.route('/overview', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
@require_feature('recruiting_module')
def get_recruiting_overview():
    """Ottiene overview dashboard recruiting con metriche principali."""
    try:
        # Data range per metriche mensili
        current_month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        week_from_now = datetime.now() + timedelta(days=7)
        
        # Calcola metriche principali con nomi che matchano il frontend
        stats = {
            'open_positions': JobPosting.query.filter_by(status='active').count(),
            'active_candidates': Candidate.query.filter_by(status='active').count(),
            'interviews_this_week': InterviewSession.query.filter(
                InterviewSession.scheduled_date >= datetime.now(),
                InterviewSession.scheduled_date <= week_from_now,
                InterviewSession.status == 'scheduled'
            ).count(),
            'offers_sent': Application.query.filter_by(current_step='offer').count()
        }
        
        # Pipeline stats per fase
        pipeline_stats = {
            'new': Application.query.filter_by(current_step='application_received').count(),
            'screening': Application.query.filter_by(current_step='screening').count(),
            'interviewing': Application.query.filter(
                Application.current_step.in_(['interview_1', 'interview_2'])
            ).count(),
            'offer': Application.query.filter_by(current_step='offer').count()
        }
        
        # Posizioni più attive (con più candidature)
        active_positions = db.session.query(
            JobPosting,
            func.count(Application.id).label('applications_count')
        ).outerjoin(Application).filter(
            JobPosting.status == 'active'
        ).group_by(JobPosting.id).order_by(
            desc('applications_count')
        ).limit(5).all()
        
        active_positions_data = []
        for posting, app_count in active_positions:
            active_positions_data.append({
                'id': posting.id,
                'title': posting.title,
                'location': posting.location,
                'department': {
                    'id': posting.department.id,
                    'name': posting.department.name
                } if posting.department else None,
                'applications_count': app_count,
                'created_at': posting.created_at.isoformat()
            })
        
        # Prossimi colloqui
        upcoming_interviews = InterviewSession.query.filter(
            InterviewSession.scheduled_date >= datetime.now(),
            InterviewSession.status == 'scheduled'
        ).order_by(InterviewSession.scheduled_date).limit(5).all()
        
        upcoming_interviews_data = []
        for interview in upcoming_interviews:
            upcoming_interviews_data.append({
                'id': interview.id,
                'candidate_name': interview.application.candidate.full_name,
                'position_title': interview.application.job_posting.title,
                'interview_type': interview.interview_type,
                'scheduled_date': interview.scheduled_date.isoformat(),
                'interviewer': {
                    'id': interview.interviewer.id,
                    'name': f"{interview.interviewer.first_name} {interview.interviewer.last_name}"
                }
            })
        
        overview_data = {
            'stats': stats,
            'pipeline_stats': pipeline_stats,
            'active_positions': active_positions_data,
            'upcoming_interviews': upcoming_interviews_data
        }
        
        return api_response(data=overview_data)
        
    except Exception as e:
        current_app.logger.error(f"Error in get_recruiting_overview: {str(e)}")
        return handle_api_error(e)

@api_recruiting.route('/job-postings', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_job_postings():
    """
    Lista job postings con filtri e paginazione.
    
    Query Parameters:
    - page: Numero pagina (default: 1)
    - per_page: Elementi per pagina (default: 20)
    - search: Ricerca in titolo e location
    - status: Filtra per stato (draft, active, paused, closed)
    - department_id: Filtra per dipartimento
    - project_id: Filtra per progetto
    - is_public: Filtra per posizioni pubbliche
    """
    try:
        page, per_page = get_pagination_params()
        
        # Query base con joins ottimizzati
        query = JobPosting.query.options(
            joinedload(JobPosting.creator),
            joinedload(JobPosting.department),
            joinedload(JobPosting.project),
            joinedload(JobPosting.proposal)
        )
        
        # Filtri
        search = request.args.get('search', '').strip()
        if search:
            search_filter = or_(
                JobPosting.title.ilike(f'%{search}%'),
                JobPosting.location.ilike(f'%{search}%')
            )
            query = query.filter(search_filter)
            
        status = request.args.get('status')
        if status:
            query = query.filter(JobPosting.status == status)
            
        department_id = request.args.get('department_id', type=int)
        if department_id:
            query = query.filter(JobPosting.department_id == department_id)
            
        project_id = request.args.get('project_id', type=int)
        if project_id:
            query = query.filter(JobPosting.project_id == project_id)
            
        is_public = request.args.get('is_public')
        if is_public is not None:
            is_public_bool = is_public.lower() in ['true', '1', 'yes']
            query = query.filter(JobPosting.is_public == is_public_bool)
        
        # Ordinamento
        sort_by = request.args.get('sort_by', 'created_at')
        sort_order = request.args.get('sort_order', 'desc')
        
        if hasattr(JobPosting, sort_by):
            sort_column = getattr(JobPosting, sort_by)
            if sort_order == 'desc':
                query = query.order_by(desc(sort_column))
            else:
                query = query.order_by(sort_column)
        else:
            query = query.order_by(desc(JobPosting.created_at))
        
        # Paginazione
        postings_paginated = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        # Serializzazione dati
        postings_data = []
        for posting in postings_paginated.items:
            # Conta applicazioni per questa posizione
            applications_count = Application.query.filter_by(job_posting_id=posting.id).count()
            
            posting_data = {
                'id': posting.id,
                'title': posting.title,
                'description': posting.description,
                'requirements': posting.requirements,
                'responsibilities': posting.responsibilities,
                'location': posting.location,
                'remote_allowed': posting.remote_allowed,
                'employment_type': posting.employment_type,
                'salary_min': float(posting.salary_min) if posting.salary_min else None,
                'salary_max': float(posting.salary_max) if posting.salary_max else None,
                'salary_currency': posting.salary_currency,
                'department': {
                    'id': posting.department.id,
                    'name': posting.department.name
                } if posting.department else None,
                'project': {
                    'id': posting.project.id,
                    'name': posting.project.name
                } if posting.project else None,
                'proposal': {
                    'id': posting.proposal.id,
                    'title': posting.proposal.title
                } if posting.proposal else None,
                'status': posting.status,
                'is_public': posting.is_public,
                'applications_count': applications_count,
                'created_at': posting.created_at.isoformat(),
                'updated_at': posting.updated_at.isoformat(),
                'published_at': posting.published_at.isoformat() if posting.published_at else None,
                'closed_at': posting.closed_at.isoformat() if posting.closed_at else None,
                'created_by': {
                    'id': posting.creator.id,
                    'name': f"{posting.creator.first_name} {posting.creator.last_name}"
                }
            }
            postings_data.append(posting_data)
        
        return api_response(
            data={
                'job_postings': postings_data,
                'pagination': format_pagination(postings_paginated)
            }
        )
        
    except Exception as e:
        current_app.logger.error(f"Error in get_job_postings: {str(e)}")
        return handle_api_error(e)

@api_recruiting.route('/job-postings', methods=['POST'])
@api_login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def create_job_posting():
    """Crea nuova job posting."""
    try:
        data = request.get_json()
        
        # Validazione dati richiesti
        if not data.get('title'):
            return api_response(
                success=False,
                message='Il titolo è obbligatorio'
            )
        
        # Crea job posting
        job_posting = JobPosting(
            title=data['title'],
            description=data.get('description'),
            requirements=data.get('requirements'),
            responsibilities=data.get('responsibilities'),
            location=data.get('location'),
            remote_allowed=data.get('remote_allowed', False),
            employment_type=data.get('employment_type', 'full_time'),
            salary_min=data.get('salary_min'),
            salary_max=data.get('salary_max'),
            salary_currency=data.get('salary_currency', 'EUR'),
            department_id=data.get('department_id'),
            project_id=data.get('project_id'),
            proposal_id=data.get('proposal_id'),
            status='draft',
            is_public=data.get('is_public', False),
            created_by=current_user.id
        )
        
        # Se status è active, imposta published_at
        if data.get('status') == 'active':
            job_posting.status = 'active'
            job_posting.published_at = datetime.utcnow()
        
        db.session.add(job_posting)
        db.session.commit()
        
        return api_response(
            data={'id': job_posting.id},
            message='Posizione creata con successo'
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error creating job posting: {str(e)}")
        return handle_api_error(e)

@api_recruiting.route('/job-postings/<int:posting_id>', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_job_posting(posting_id):
    """Ottiene dettagli di una singola job posting."""
    try:
        posting = JobPosting.query.options(
            joinedload(JobPosting.creator),
            joinedload(JobPosting.department),
            joinedload(JobPosting.project),
            joinedload(JobPosting.proposal),
            joinedload(JobPosting.applications).joinedload(Application.candidate)
        ).get_or_404(posting_id)
        
        # Conta candidature per status
        applications_by_status = db.session.query(
            Application.status,
            func.count(Application.id)
        ).filter_by(job_posting_id=posting_id).group_by(Application.status).all()
        
        applications_stats = {status: count for status, count in applications_by_status}
        
        posting_data = {
            'id': posting.id,
            'title': posting.title,
            'description': posting.description,
            'requirements': posting.requirements,
            'responsibilities': posting.responsibilities,
            'location': posting.location,
            'remote_allowed': posting.remote_allowed,
            'employment_type': posting.employment_type,
            'salary_min': float(posting.salary_min) if posting.salary_min else None,
            'salary_max': float(posting.salary_max) if posting.salary_max else None,
            'salary_currency': posting.salary_currency,
            'department': {
                'id': posting.department.id,
                'name': posting.department.name
            } if posting.department else None,
            'project': {
                'id': posting.project.id,
                'name': posting.project.name
            } if posting.project else None,
            'proposal': {
                'id': posting.proposal.id,
                'title': posting.proposal.title
            } if posting.proposal else None,
            'status': posting.status,
            'is_public': posting.is_public,
            'applications_count': Application.query.filter_by(job_posting_id=posting.id).count(),
            'applications_stats': applications_stats,
            'created_at': posting.created_at.isoformat(),
            'updated_at': posting.updated_at.isoformat(),
            'published_at': posting.published_at.isoformat() if posting.published_at else None,
            'closed_at': posting.closed_at.isoformat() if posting.closed_at else None,
            'created_by': {
                'id': posting.creator.id,
                'name': f"{posting.creator.first_name} {posting.creator.last_name}"
            }
        }
        
        return api_response(data=posting_data)
        
    except Exception as e:
        current_app.logger.error(f"Error getting job posting {posting_id}: {str(e)}")
        return handle_api_error(e)

@api_recruiting.route('/job-postings/<int:posting_id>', methods=['PUT'])
@api_login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def update_job_posting(posting_id):
    """Aggiorna job posting esistente."""
    try:
        posting = JobPosting.query.get_or_404(posting_id)
        data = request.get_json()
        
        # Aggiorna campi
        if 'title' in data:
            posting.title = data['title']
        if 'description' in data:
            posting.description = data['description']
        if 'requirements' in data:
            posting.requirements = data['requirements']
        if 'responsibilities' in data:
            posting.responsibilities = data['responsibilities']
        if 'location' in data:
            posting.location = data['location']
        if 'remote_allowed' in data:
            posting.remote_allowed = data['remote_allowed']
        if 'employment_type' in data:
            posting.employment_type = data['employment_type']
        if 'salary_min' in data:
            posting.salary_min = data['salary_min']
        if 'salary_max' in data:
            posting.salary_max = data['salary_max']
        if 'salary_currency' in data:
            posting.salary_currency = data['salary_currency']
        if 'department_id' in data:
            posting.department_id = data['department_id']
        if 'project_id' in data:
            posting.project_id = data['project_id']
        if 'proposal_id' in data:
            posting.proposal_id = data['proposal_id']
        if 'is_public' in data:
            posting.is_public = data['is_public']
        
        # Gestione cambio status
        if 'status' in data:
            old_status = posting.status
            new_status = data['status']
            
            if old_status != 'active' and new_status == 'active':
                posting.published_at = datetime.utcnow()
            elif old_status != 'closed' and new_status == 'closed':
                posting.closed_at = datetime.utcnow()
                
            posting.status = new_status
        
        posting.updated_at = datetime.utcnow()
        db.session.commit()
        
        return api_response(message='Posizione aggiornata con successo')
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating job posting {posting_id}: {str(e)}")
        return handle_api_error(e)

@api_recruiting.route('/job-postings/<int:posting_id>', methods=['DELETE'])
@api_login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def delete_job_posting(posting_id):
    """Elimina job posting (solo se non ha candidature)."""
    try:
        posting = JobPosting.query.get_or_404(posting_id)
        
        # Verifica se ha candidature
        applications_count = Application.query.filter_by(job_posting_id=posting_id).count()
        if applications_count > 0:
            return api_response(
                success=False,
                message=f'Impossibile eliminare posizione con {applications_count} candidature'
            )
        
        db.session.delete(posting)
        db.session.commit()
        
        return api_response(message='Posizione eliminata con successo')
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error deleting job posting {posting_id}: {str(e)}")
        return handle_api_error(e)

@api_recruiting.route('/candidates', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_candidates():
    """
    Lista candidati con filtri e paginazione.
    
    Query Parameters:
    - page: Numero pagina
    - per_page: Elementi per pagina  
    - search: Ricerca in nome, email
    - status: Filtra per stato candidato
    - source: Filtra per fonte candidatura
    - skills: Filtra per competenze
    """
    try:
        page, per_page = get_pagination_params()
        
        # Query base
        query = Candidate.query.options(
            joinedload(Candidate.applications),
            joinedload(Candidate.skills),
            joinedload(Candidate.hired_as_user)
        )
        
        # Filtri
        search = request.args.get('search', '').strip()
        if search:
            search_filter = or_(
                Candidate.first_name.ilike(f'%{search}%'),
                Candidate.last_name.ilike(f'%{search}%'),
                Candidate.email.ilike(f'%{search}%')
            )
            query = query.filter(search_filter)
            
        status = request.args.get('status')
        if status:
            query = query.filter(Candidate.status == status)
            
        source = request.args.get('source')
        if source:
            query = query.filter(Candidate.source == source)
            
        # Filtra per competenze
        skills = request.args.get('skills')
        if skills:
            skill_names = [s.strip() for s in skills.split(',')]
            query = query.join(CandidateSkill).filter(
                CandidateSkill.skill_name.in_(skill_names)
            ).distinct()
        
        # Ordinamento
        query = query.order_by(desc(Candidate.created_at))
        
        # Paginazione
        candidates_paginated = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        # Serializzazione
        candidates_data = []
        for candidate in candidates_paginated.items:
            applications_count = Application.query.filter_by(candidate_id=candidate.id).count()
            
            # Parse CV analysis se presente
            cv_analysis = {}
            if candidate.cv_analysis_data:
                try:
                    cv_analysis = json.loads(candidate.cv_analysis_data)
                except:
                    cv_analysis = {}
            
            candidate_data = {
                'id': candidate.id,
                'first_name': candidate.first_name,
                'last_name': candidate.last_name,
                'full_name': candidate.full_name,
                'email': candidate.email,
                'phone': candidate.phone,
                'location': candidate.location,
                'linkedin_url': candidate.linkedin_url,
                'source': candidate.source,
                'status': candidate.status,
                'notes': candidate.notes,
                'tags': json.loads(candidate.tags) if candidate.tags else [],
                'cv_path': candidate.current_cv_path,
                'cv_last_updated': candidate.cv_last_updated.isoformat() if candidate.cv_last_updated else None,
                'cv_analysis': cv_analysis,
                'applications_count': applications_count,
                'skills_count': CandidateSkill.query.filter_by(candidate_id=candidate.id).count(),
                'hired_as_user': {
                    'id': candidate.hired_as_user.id,
                    'name': f"{candidate.hired_as_user.first_name} {candidate.hired_as_user.last_name}"
                } if candidate.hired_as_user else None,
                'hired_date': candidate.hired_date.isoformat() if candidate.hired_date else None,
                'created_at': candidate.created_at.isoformat(),
                'updated_at': candidate.updated_at.isoformat()
            }
            candidates_data.append(candidate_data)
        
        return api_response(
            data={
                'candidates': candidates_data,
                'pagination': format_pagination(candidates_paginated)
            }
        )
        
    except Exception as e:
        current_app.logger.error(f"Error in get_candidates: {str(e)}")
        return handle_api_error(e)

@api_recruiting.route('/candidates', methods=['POST'])
@api_login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def create_candidate():
    """Crea nuovo candidato."""
    try:
        data = request.get_json()
        
        # Validazione
        if not data.get('first_name') or not data.get('last_name') or not data.get('email'):
            return api_response(
                success=False,
                message='Nome, cognome ed email sono obbligatori'
            )
        
        # Verifica email unica
        existing = Candidate.query.filter_by(email=data['email']).first()
        if existing:
            return api_response(
                success=False,
                message='Email già esistente'
            )
        
        candidate = Candidate(
            first_name=data['first_name'],
            last_name=data['last_name'],
            email=data['email'],
            phone=data.get('phone'),
            location=data.get('location'),
            linkedin_url=data.get('linkedin_url'),
            source=data.get('source', 'website'),
            status='new',
            notes=data.get('notes'),
            tags=json.dumps(data.get('tags', []))
        )
        
        db.session.add(candidate)
        db.session.commit()
        
        return api_response(
            data={'id': candidate.id},
            message='Candidato creato con successo'
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error creating candidate: {str(e)}")
        return handle_api_error(e)

@api_recruiting.route('/candidates/<int:candidate_id>', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_candidate(candidate_id):
    """Ottiene dettagli candidato specifico."""
    try:
        candidate = Candidate.query.options(
            joinedload(Candidate.applications),
            joinedload(Candidate.skills)
        ).get_or_404(candidate_id)
        
        # Conta candidature per stato
        applications_count = Application.query.filter_by(candidate_id=candidate_id).count()
        
        candidate_data = {
            'id': candidate.id,
            'first_name': candidate.first_name,
            'last_name': candidate.last_name,
            'full_name': f"{candidate.first_name} {candidate.last_name}",
            'email': candidate.email,
            'phone': candidate.phone,
            'location': candidate.location,
            'linkedin_url': candidate.linkedin_url,
            'source': candidate.source,
            'status': candidate.status,
            'notes': candidate.notes,
            'tags': json.loads(candidate.tags) if candidate.tags else [],
            'cv_path': candidate.current_cv_path,
            'created_at': candidate.created_at.isoformat() if candidate.created_at else None,
            'updated_at': candidate.updated_at.isoformat() if candidate.updated_at else None,
            'applications_count': applications_count,
            'skills': [{'id': skill.id, 'name': skill.skill_name} for skill in candidate.skills] if candidate.skills else []
        }
        
        return api_response(data=candidate_data)
        
    except Exception as e:
        current_app.logger.error(f"Error getting candidate {candidate_id}: {str(e)}")
        return handle_api_error(e)

@api_recruiting.route('/candidates/<int:candidate_id>', methods=['PUT'])
@api_login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def update_candidate(candidate_id):
    """Aggiorna candidato esistente."""
    try:
        candidate = Candidate.query.get_or_404(candidate_id)
        data = request.get_json()
        
        # Validazione
        if 'email' in data:
            # Verifica email unica (escluso candidato corrente)
            existing = Candidate.query.filter(
                Candidate.email == data['email'],
                Candidate.id != candidate_id
            ).first()
            if existing:
                return api_response(
                    success=False,
                    message='Email già esistente per altro candidato'
                )
        
        # Aggiorna campi forniti
        if 'first_name' in data:
            candidate.first_name = data['first_name']
        if 'last_name' in data:
            candidate.last_name = data['last_name']
        if 'email' in data:
            candidate.email = data['email']
        if 'phone' in data:
            candidate.phone = data['phone']
        if 'location' in data:
            candidate.location = data['location']
        if 'linkedin_url' in data:
            candidate.linkedin_url = data['linkedin_url']
        if 'source' in data:
            candidate.source = data['source']
        if 'status' in data:
            candidate.status = data['status']
        if 'notes' in data:
            candidate.notes = data['notes']
        if 'tags' in data:
            candidate.tags = json.dumps(data['tags'])
        
        candidate.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return api_response(
            data={'id': candidate.id},
            message='Candidato aggiornato con successo'
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating candidate {candidate_id}: {str(e)}")
        return handle_api_error(e)

@api_recruiting.route('/candidates/<int:candidate_id>', methods=['DELETE'])
@api_login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def delete_candidate(candidate_id):
    """Elimina candidato con validazione business logic."""
    try:
        candidate = Candidate.query.get_or_404(candidate_id)
        
        # Validazione business: verifica candidature attive
        active_applications = Application.query.filter(
            Application.candidate_id == candidate_id,
            Application.status.in_(['pending', 'in_progress'])
        ).count()
        
        if active_applications > 0:
            return api_response(
                success=False,
                message=f'Impossibile eliminare candidato con {active_applications} candidature attive'
            )
        
        # Verifica interview future
        future_interviews = InterviewSession.query.join(Application).filter(
            Application.candidate_id == candidate_id,
            InterviewSession.scheduled_date > datetime.utcnow(),
            InterviewSession.status != 'cancelled'
        ).count()
        
        if future_interviews > 0:
            return api_response(
                success=False,
                message=f'Impossibile eliminare candidato con {future_interviews} colloqui programmati'
            )
        
        # Se tutto ok, elimina il candidato (le candidature completate rimangono per storico)
        db.session.delete(candidate)
        db.session.commit()
        
        return api_response(
            message=f'Candidato "{candidate.first_name} {candidate.last_name}" eliminato con successo'
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error deleting candidate {candidate_id}: {str(e)}")
        return handle_api_error(e)

# ENDPOINT CV - RIUTILIZZA SISTEMA ESISTENTE
@api_recruiting.route('/candidates/<int:candidate_id>/cv', methods=['POST'])
@api_login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def upload_candidate_cv(candidate_id):
    """Upload e analisi CV candidato - RIUSA TUTTO DA PERSONNEL!"""
    try:
        candidate = Candidate.query.get_or_404(candidate_id)
        
        if 'cv' not in request.files:
            return api_response(
                success=False,
                message='Nessun file CV fornito'
            )
        
        file = request.files['cv']
        
        # RIUSA validazione da personnel
        if not is_valid_cv_file(file.filename):
            return api_response(
                success=False,
                message='Formato file non supportato. Usa PDF, DOCX o TXT'
            )
        
        # RIUSA check dimensione
        if get_file_size_mb(file) > 5:
            return api_response(
                success=False,
                message='Il file è troppo grande. Max 5MB'
            )
        
        # Salva file
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        safe_filename = f"cv_candidate_{candidate_id}_{timestamp}.{file.filename.rsplit('.', 1)[1].lower()}"
        
        # Directory per CV recruiting
        cv_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'recruiting_cvs')
        os.makedirs(cv_dir, exist_ok=True)
        
        cv_path = os.path.join(cv_dir, safe_filename)
        file.save(cv_path)
        
        # RIUSA estrazione testo
        cv_text = extract_text_from_cv(cv_path)
        
        if not cv_text:
            os.remove(cv_path)
            return api_response(
                success=False,
                message='Impossibile estrarre testo dal CV'
            )
        
        # RIUSA analisi AI (stessa funzione di personnel!)
        analysis_result = extract_skills_from_cv(cv_text)
        
        # Salva nel modello (stesso formato di UserProfile)
        candidate.current_cv_path = f"recruiting_cvs/{safe_filename}"
        candidate.cv_last_updated = datetime.utcnow()
        candidate.cv_analysis_data = json.dumps(analysis_result)
        
        # Crea/aggiorna skills del candidato
        # Prima cancella skills esistenti da CV
        CandidateSkill.query.filter_by(
            candidate_id=candidate_id,
            extracted_from_cv=True
        ).delete()
        
        # Crea nuove skills dal CV
        for skill_data in analysis_result.get('skills', []):
            level_map = {'beginner': 1, 'intermediate': 3, 'advanced': 4, 'expert': 5}
            skill = CandidateSkill(
                candidate_id=candidate_id,
                skill_name=skill_data['name'],
                skill_category=skill_data.get('category', 'other'),
                skill_level=level_map.get(skill_data.get('level', 'intermediate'), 3),
                years_experience=skill_data.get('years', 0),
                extracted_from_cv=True,
                confidence_score=0.9  # Alta fiducia per analisi AI
            )
            db.session.add(skill)
        
        db.session.commit()
        
        return api_response(
            data={
                'cv_path': candidate.current_cv_path,
                'analysis': analysis_result,
                'skills_extracted': len(analysis_result.get('skills', []))
            },
            message='CV caricato e analizzato con successo'
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error uploading CV for candidate {candidate_id}: {str(e)}")
        return handle_api_error(e)

# Endpoint per lista dipartimenti (helper per form)
@api_recruiting.route('/departments', methods=['GET'])
@api_login_required
def get_departments():
    """Lista dipartimenti per dropdown."""
    try:
        departments = Department.query.filter_by(is_active=True).all()
        departments_data = [
            {
                'id': dept.id,
                'name': dept.name,
                'description': dept.description
            }
            for dept in departments
        ]
        
        return api_response(data=departments_data)
        
    except Exception as e:
        return handle_api_error(e)

# Endpoint per lista progetti (helper per form)
@api_recruiting.route('/projects', methods=['GET'])
@api_login_required
def get_projects():
    """Lista progetti attivi per dropdown."""
    try:
        projects = Project.query.filter_by(status='active').all()
        projects_data = [
            {
                'id': project.id,
                'name': project.name,
                'description': project.description
            }
            for project in projects
        ]
        
        return api_response(data=projects_data)
        
    except Exception as e:
        return handle_api_error(e)

# =============================================================================
# APPLICATION MANAGEMENT ENDPOINTS 
# =============================================================================

@api_recruiting.route('/applications', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_applications():
    """
    Lista applications con filtri e paginazione.
    
    Query Parameters:
    - page: Numero pagina (default: 1)
    - per_page: Elementi per pagina (default: 20)
    - search: Ricerca in candidate name, job title
    - status: Filtra per stato application
    - current_step: Filtra per step corrente
    - job_posting_id: Filtra per posizione
    - candidate_id: Filtra per candidato
    """
    try:
        page, per_page = get_pagination_params()
        
        # Query base con joins ottimizzati
        query = Application.query.options(
            joinedload(Application.candidate),
            joinedload(Application.job_posting),
            joinedload(Application.job_posting, JobPosting.department)
        )
        
        # Filtri
        search = request.args.get('search', '').strip()
        if search:
            search_filter = or_(
                Candidate.first_name.ilike(f'%{search}%'),
                Candidate.last_name.ilike(f'%{search}%'),
                JobPosting.title.ilike(f'%{search}%')
            )
            query = query.join(Candidate).join(JobPosting).filter(search_filter)
        
        status = request.args.get('status')
        if status:
            query = query.filter(Application.status == status)
            
        current_step = request.args.get('current_step')
        if current_step:
            query = query.filter(Application.current_step == current_step)
            
        job_posting_id = request.args.get('job_posting_id', type=int)
        if job_posting_id:
            query = query.filter(Application.job_posting_id == job_posting_id)
            
        candidate_id = request.args.get('candidate_id', type=int)
        if candidate_id:
            query = query.filter(Application.candidate_id == candidate_id)
        
        # Ordinamento per data applicazione
        query = query.order_by(desc(Application.applied_at))
        
        # Paginazione
        applications_paginated = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        # Serializzazione dati
        applications_data = []
        for app in applications_paginated.items:
            app_data = {
                'id': app.id,
                'candidate': {
                    'id': app.candidate.id,
                    'full_name': app.candidate.full_name,
                    'email': app.candidate.email,
                    'phone': app.candidate.phone,
                    'location': app.candidate.location,
                    'status': app.candidate.status
                },
                'job_posting': {
                    'id': app.job_posting.id,
                    'title': app.job_posting.title,
                    'location': app.job_posting.location,
                    'employment_type': app.job_posting.employment_type,
                    'department': {
                        'id': app.job_posting.department.id,
                        'name': app.job_posting.department.name
                    } if app.job_posting.department else None
                },
                'applied_at': app.applied_at.isoformat(),
                'current_step': app.current_step,
                'status': app.status,
                'overall_score': app.overall_score,
                'cover_letter': app.cover_letter,
                'interview_notes': app.interview_notes,
                'created_at': app.created_at.isoformat() if app.created_at else None,
                'updated_at': app.updated_at.isoformat() if app.updated_at else None
            }
            applications_data.append(app_data)
        
        # Formatta paginazione
        pagination_data = format_pagination(applications_paginated)
        
        return api_response(
            data={
                'applications': applications_data,
                'pagination': pagination_data
            }
        )
        
    except Exception as e:
        current_app.logger.error(f"Error in get_applications: {str(e)}")
        return handle_api_error(e)

@api_recruiting.route('/applications', methods=['POST'])
@api_login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def create_application():
    """Crea nuova candidatura collegando candidato e posizione."""
    try:
        data = request.get_json()
        
        # Validazione
        required_fields = ['job_posting_id', 'candidate_id']
        for field in required_fields:
            if field not in data:
                return api_response(
                    success=False,
                    message=f'Campo {field} obbligatorio'
                )
        
        # Verifica che job posting e candidate esistano
        job_posting = JobPosting.query.get(data['job_posting_id'])
        if not job_posting:
            return api_response(
                success=False,
                message='Posizione lavorativa non trovata'
            )
            
        candidate = Candidate.query.get(data['candidate_id'])
        if not candidate:
            return api_response(
                success=False,
                message='Candidato non trovato'
            )
        
        # Verifica candidatura duplicata
        existing = Application.query.filter_by(
            job_posting_id=data['job_posting_id'],
            candidate_id=data['candidate_id']
        ).first()
        
        if existing:
            return api_response(
                success=False,
                message='Il candidato ha già applicato per questa posizione'
            )
        
        # Crea application
        application = Application(
            job_posting_id=data['job_posting_id'],
            candidate_id=data['candidate_id'],
            applied_at=datetime.utcnow(),
            cover_letter=data.get('cover_letter', ''),
            current_step='application_received',
            status='pending'
        )
        
        db.session.add(application)
        db.session.commit()
        
        return api_response(
            data={'id': application.id},
            message='Candidatura creata con successo'
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error creating application: {str(e)}")
        return handle_api_error(e)

@api_recruiting.route('/applications/<int:application_id>', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_application(application_id):
    """Ottiene dettagli completi di una candidatura."""
    try:
        application = Application.query.options(
            joinedload(Application.candidate),
            joinedload(Application.job_posting),
            joinedload(Application.job_posting, JobPosting.department),
            joinedload(Application.interviews),
            joinedload(Application.workflow_steps)
        ).get_or_404(application_id)
        
        # Serializza application completa
        app_data = {
            'id': application.id,
            'candidate': {
                'id': application.candidate.id,
                'full_name': application.candidate.full_name,
                'email': application.candidate.email,
                'phone': application.candidate.phone,
                'location': application.candidate.location,
                'linkedin_url': application.candidate.linkedin_url,
                'source': application.candidate.source,
                'status': application.candidate.status,
                'notes': application.candidate.notes,
                'cv_path': application.candidate.current_cv_path
            },
            'job_posting': {
                'id': application.job_posting.id,
                'title': application.job_posting.title,
                'description': application.job_posting.description,
                'requirements': application.job_posting.requirements,
                'location': application.job_posting.location,
                'employment_type': application.job_posting.employment_type,
                'salary_min': float(application.job_posting.salary_min) if application.job_posting.salary_min else None,
                'salary_max': float(application.job_posting.salary_max) if application.job_posting.salary_max else None,
                'department': {
                    'id': application.job_posting.department.id,
                    'name': application.job_posting.department.name
                } if application.job_posting.department else None
            },
            'applied_at': application.applied_at.isoformat(),
            'current_step': application.current_step,
            'status': application.status,
            'overall_score': application.overall_score,
            'cover_letter': application.cover_letter,
            'interview_notes': application.interview_notes,
            'created_at': application.created_at.isoformat() if application.created_at else None,
            'updated_at': application.updated_at.isoformat() if application.updated_at else None,
            
            # Interview sessions
            'interviews': [
                {
                    'id': interview.id,
                    'interview_type': interview.interview_type,
                    'scheduled_date': interview.scheduled_date.isoformat(),
                    'duration_minutes': interview.duration_minutes,
                    'location': interview.location,
                    'status': interview.status,
                    'score': interview.score,
                    'notes': interview.notes,
                    'feedback': interview.feedback,
                    'recommendation': interview.recommendation,
                    'interviewer': {
                        'id': interview.interviewer.id,
                        'name': f"{interview.interviewer.first_name} {interview.interviewer.last_name}"
                    } if interview.interviewer else None
                }
                for interview in application.interviews
            ],
            
            # Workflow steps
            'workflow_steps': [
                {
                    'id': step.id,
                    'step_name': step.step_name,
                    'step_order': step.step_order,
                    'status': step.status,
                    'started_at': step.started_at.isoformat() if step.started_at else None,
                    'completed_at': step.completed_at.isoformat() if step.completed_at else None,
                    'due_date': step.due_date.isoformat() if step.due_date else None,
                    'result': step.result,
                    'notes': step.notes,
                    'assigned_to': {
                        'id': step.assigned_user.id,
                        'name': f"{step.assigned_user.first_name} {step.assigned_user.last_name}"
                    } if step.assigned_user else None
                }
                for step in sorted(application.workflow_steps, key=lambda x: x.step_order)
            ]
        }
        
        return api_response(data=app_data)
        
    except Exception as e:
        current_app.logger.error(f"Error getting application {application_id}: {str(e)}")
        return handle_api_error(e)

@api_recruiting.route('/applications/<int:application_id>', methods=['PUT'])
@api_login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def update_application(application_id):
    """Aggiorna candidatura (step, status, score, note)."""
    try:
        application = Application.query.get_or_404(application_id)
        data = request.get_json()
        
        # Aggiorna campi
        if 'current_step' in data:
            application.current_step = data['current_step']
        
        if 'status' in data:
            application.status = data['status']
            
        if 'overall_score' in data:
            application.overall_score = data['overall_score']
            
        if 'interview_notes' in data:
            application.interview_notes = data['interview_notes']
            
        if 'cover_letter' in data:
            application.cover_letter = data['cover_letter']
        
        application.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return api_response(
            data={'id': application.id},
            message='Candidatura aggiornata con successo'
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating application {application_id}: {str(e)}")
        return handle_api_error(e)

@api_recruiting.route('/applications/<int:application_id>', methods=['DELETE'])
@api_login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def delete_application(application_id):
    """Elimina candidatura (con controlli)."""
    try:
        application = Application.query.get_or_404(application_id)
        
        # Controllo business logic - non eliminare se ci sono colloqui completati
        completed_interviews = InterviewSession.query.filter_by(
            application_id=application_id,
            status='completed'
        ).count()
        
        if completed_interviews > 0:
            return api_response(
                success=False,
                message=f'Impossibile eliminare candidatura con {completed_interviews} colloqui completati'
            )
        
        # Elimina workflow steps e interview sessions associate
        RecruitingWorkflow.query.filter_by(application_id=application_id).delete()
        InterviewSession.query.filter_by(application_id=application_id).delete()
        
        db.session.delete(application)
        db.session.commit()
        
        return api_response(message='Candidatura eliminata con successo')
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error deleting application {application_id}: {str(e)}")
        return handle_api_error(e)

@api_recruiting.route('/job-postings/<int:job_posting_id>/applications', methods=['GET'])
@api_login_required  
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_job_posting_applications(job_posting_id):
    """Ottiene tutte le candidature per una specifica posizione."""
    try:
        job_posting = JobPosting.query.get_or_404(job_posting_id)
        
        applications = Application.query.options(
            joinedload(Application.candidate)
        ).filter_by(job_posting_id=job_posting_id).order_by(
            desc(Application.applied_at)
        ).all()
        
        applications_data = []
        for app in applications:
            app_data = {
                'id': app.id,
                'candidate': {
                    'id': app.candidate.id,
                    'full_name': app.candidate.full_name,
                    'email': app.candidate.email,
                    'phone': app.candidate.phone,
                    'location': app.candidate.location,
                    'status': app.candidate.status,
                    'source': app.candidate.source
                },
                'applied_at': app.applied_at.isoformat(),
                'current_step': app.current_step,
                'status': app.status,
                'overall_score': app.overall_score
            }
            applications_data.append(app_data)
        
        return api_response(
            data={
                'job_posting': {
                    'id': job_posting.id,
                    'title': job_posting.title,
                    'location': job_posting.location
                },
                'applications': applications_data,
                'total_applications': len(applications_data)
            }
        )
        
    except Exception as e:
        current_app.logger.error(f"Error getting applications for job posting {job_posting_id}: {str(e)}")
        return handle_api_error(e)

@api_recruiting.route('/candidates/<int:candidate_id>/applications', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_candidate_applications(candidate_id):
    """Ottiene tutte le candidature per un candidato specifico."""
    try:
        candidate = Candidate.query.get_or_404(candidate_id)
        
        applications = Application.query.options(
            joinedload(Application.job_posting),
            joinedload(Application.job_posting, JobPosting.department)
        ).filter_by(candidate_id=candidate_id).order_by(
            desc(Application.applied_at)
        ).all()
        
        applications_data = []
        for app in applications:
            app_data = {
                'id': app.id,
                'job_posting': {
                    'id': app.job_posting.id,
                    'title': app.job_posting.title,
                    'location': app.job_posting.location,
                    'employment_type': app.job_posting.employment_type,
                    'department': {
                        'id': app.job_posting.department.id,
                        'name': app.job_posting.department.name
                    } if app.job_posting.department else None
                },
                'applied_at': app.applied_at.isoformat(),
                'current_step': app.current_step,
                'status': app.status,
                'overall_score': app.overall_score
            }
            applications_data.append(app_data)
        
        return api_response(
            data={
                'candidate': {
                    'id': candidate.id,
                    'full_name': candidate.full_name,
                    'email': candidate.email
                },
                'applications': applications_data,
                'total_applications': len(applications_data)
            }
        )
        
    except Exception as e:
        current_app.logger.error(f"Error getting applications for candidate {candidate_id}: {str(e)}")
        return handle_api_error(e)

# =============================================================================
# INTERVIEW MANAGEMENT ENDPOINTS  
# =============================================================================

@api_recruiting.route('/interviews', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_interviews():
    """
    Lista colloqui con filtri e paginazione.
    
    Query Parameters:
    - page: Numero pagina
    - per_page: Elementi per pagina
    - candidate_id: Filtra per candidato
    - application_id: Filtra per candidatura  
    - status: Filtra per stato (scheduled, completed, cancelled)
    - date_from: Data inizio periodo
    - date_to: Data fine periodo
    - interviewer_id: Filtra per intervistatore
    """
    try:
        page, per_page = get_pagination_params()
        
        # Filtri
        candidate_id = request.args.get('candidate_id', type=int)
        application_id = request.args.get('application_id', type=int)
        status = request.args.get('status')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        interviewer_id = request.args.get('interviewer_id', type=int)
        
        # Query base
        query = InterviewSession.query.options(
            joinedload(InterviewSession.application),
            joinedload(InterviewSession.application, Application.candidate),
            joinedload(InterviewSession.application, Application.job_posting),
            joinedload(InterviewSession.interviewer)
        )
        
        # Applica filtri
        if candidate_id:
            # Filtra per candidate attraverso application
            query = query.join(Application).filter(Application.candidate_id == candidate_id)
            
        if application_id:
            query = query.filter(InterviewSession.application_id == application_id)
            
        if status:
            query = query.filter(InterviewSession.status == status)
            
        if interviewer_id:
            query = query.filter(InterviewSession.interviewer_id == interviewer_id)
            
        if date_from:
            try:
                date_from_obj = datetime.fromisoformat(date_from)
                query = query.filter(InterviewSession.scheduled_date >= date_from_obj)
            except ValueError:
                pass
                
        if date_to:
            try:
                date_to_obj = datetime.fromisoformat(date_to)
                query = query.filter(InterviewSession.scheduled_date <= date_to_obj)
            except ValueError:
                pass
        
        # Ordinamento default: per data programmazione
        query = query.order_by(desc(InterviewSession.scheduled_date))
        
        # Paginazione
        interviews_paginated = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        # Serializzazione
        interviews_data = []
        for interview in interviews_paginated.items:
            interview_data = {
                'id': interview.id,
                'scheduled_date': interview.scheduled_date.isoformat() if interview.scheduled_date else None,
                'duration_minutes': interview.duration_minutes,
                'interview_type': interview.interview_type,
                'status': interview.status,
                'location': interview.location,
                'notes': interview.notes,
                'feedback': interview.feedback,
                'score': interview.score,
                'created_at': interview.created_at.isoformat() if interview.created_at else None,
                'updated_at': interview.updated_at.isoformat() if interview.updated_at else None,
                'candidate': {
                    'id': interview.application.candidate.id,
                    'full_name': interview.application.candidate.full_name,
                    'email': interview.application.candidate.email
                } if interview.application and interview.application.candidate else None,
                'application': {
                    'id': interview.application.id,
                    'job_posting': {
                        'id': interview.application.job_posting.id,
                        'title': interview.application.job_posting.title
                    }
                } if interview.application and interview.application.job_posting else None,
                'interviewer': {
                    'id': interview.interviewer.id,
                    'full_name': interview.interviewer.full_name,
                    'email': interview.interviewer.email
                } if interview.interviewer else None
            }
            interviews_data.append(interview_data)
        
        return api_response(
            data={
                'interviews': interviews_data,
                'pagination': format_pagination(interviews_paginated)
            }
        )
        
    except Exception as e:
        current_app.logger.error(f"Error in get_interviews: {str(e)}")
        return handle_api_error(e)

@api_recruiting.route('/interviews', methods=['POST'])
@api_login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def create_interview():
    """Crea nuovo colloquio."""
    try:
        data = request.get_json()
        
        # Validazione
        required_fields = ['candidate_id', 'application_id', 'scheduled_date', 'interview_type']
        for field in required_fields:
            if not data.get(field):
                return api_response(
                    success=False,
                    message=f'Il campo {field} è obbligatorio'
                )
        
        # Verifica che candidato e application esistano
        candidate = Candidate.query.get(data['candidate_id'])
        if not candidate:
            return api_response(
                success=False,
                message='Candidato non trovato'
            )
            
        application = Application.query.get(data['application_id'])
        if not application:
            return api_response(
                success=False,
                message='Candidatura non trovata'
            )
            
        # Verifica che candidato e application siano collegati
        if application.candidate_id != data['candidate_id']:
            return api_response(
                success=False,
                message='Candidato e candidatura non corrispondono'
            )
        
        # Parse data
        try:
            scheduled_date = datetime.fromisoformat(data['scheduled_date'])
        except ValueError:
            return api_response(
                success=False,
                message='Formato data non valido'
            )
        
        interview = InterviewSession(
            application_id=data['application_id'],
            interviewer_id=data.get('interviewer_id', current_user.id),
            scheduled_date=scheduled_date,
            duration_minutes=data.get('duration_minutes', 60),
            interview_type=data['interview_type'],
            status='scheduled',
            location=data.get('location'),
            notes=data.get('notes')
        )
        
        db.session.add(interview)
        db.session.commit()
        
        return api_response(
            data={'id': interview.id},
            message='Colloquio programmato con successo'
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error creating interview: {str(e)}")
        return handle_api_error(e)

@api_recruiting.route('/interviews/<int:interview_id>', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_interview(interview_id):
    """Ottiene dettagli colloquio specifico."""
    try:
        interview = InterviewSession.query.options(
            joinedload(InterviewSession.application),
            joinedload(InterviewSession.application, Application.candidate),
            joinedload(InterviewSession.application, Application.job_posting),
            joinedload(InterviewSession.interviewer)
        ).get_or_404(interview_id)
        
        interview_data = {
            'id': interview.id,
            'scheduled_date': interview.scheduled_date.isoformat() if interview.scheduled_date else None,
            'duration_minutes': interview.duration_minutes,
            'interview_type': interview.interview_type,
            'status': interview.status,
            'location': interview.location,
            'notes': interview.notes,
            'feedback': interview.feedback,
            'score': interview.score,
            'created_at': interview.created_at.isoformat() if interview.created_at else None,
            'updated_at': interview.updated_at.isoformat() if interview.updated_at else None,
            'candidate': {
                'id': interview.application.candidate.id,
                'full_name': interview.application.candidate.full_name,
                'email': interview.application.candidate.email,
                'phone': interview.application.candidate.phone
            } if interview.application and interview.application.candidate else None,
            'application': {
                'id': interview.application.id,
                'status': interview.application.status,
                'current_step': interview.application.current_step,
                'job_posting': {
                    'id': interview.application.job_posting.id,
                    'title': interview.application.job_posting.title,
                    'location': interview.application.job_posting.location
                }
            } if interview.application and interview.application.job_posting else None,
            'interviewer': {
                'id': interview.interviewer.id,
                'full_name': interview.interviewer.full_name,
                'email': interview.interviewer.email
            } if interview.interviewer else None
        }
        
        return api_response(data=interview_data)
        
    except Exception as e:
        current_app.logger.error(f"Error getting interview {interview_id}: {str(e)}")
        return handle_api_error(e)

@api_recruiting.route('/interviews/<int:interview_id>', methods=['PUT'])
@api_login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def update_interview(interview_id):
    """Aggiorna colloquio esistente."""
    try:
        interview = InterviewSession.query.get_or_404(interview_id)
        data = request.get_json()
        
        # Aggiorna campi forniti
        if 'scheduled_date' in data:
            try:
                interview.scheduled_date = datetime.fromisoformat(data['scheduled_date'])
            except ValueError:
                return api_response(
                    success=False,
                    message='Formato data non valido'
                )
        
        if 'duration_minutes' in data:
            interview.duration_minutes = data['duration_minutes']
            
        if 'interview_type' in data:
            interview.interview_type = data['interview_type']
            
        if 'status' in data:
            interview.status = data['status']
            
        if 'location' in data:
            interview.location = data['location']
            
        if 'notes' in data:
            interview.notes = data['notes']
            
        if 'feedback' in data:
            interview.feedback = data['feedback']
            
        if 'score' in data:
            interview.score = data['score']
            
        if 'interviewer_id' in data:
            interview.interviewer_id = data['interviewer_id']
        
        interview.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return api_response(
            data={'id': interview.id},
            message='Colloquio aggiornato con successo'
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating interview {interview_id}: {str(e)}")
        return handle_api_error(e)

@api_recruiting.route('/interviews/<int:interview_id>', methods=['DELETE'])
@api_login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def delete_interview(interview_id):
    """Elimina colloquio."""
    try:
        interview = InterviewSession.query.get_or_404(interview_id)
        
        # Validazione business: non eliminare colloqui completati
        if interview.status == 'completed':
            return api_response(
                success=False,
                message='Impossibile eliminare colloquio già completato'
            )
        
        db.session.delete(interview)
        db.session.commit()
        
        return api_response(
            message='Colloquio eliminato con successo'
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error deleting interview {interview_id}: {str(e)}")
        return handle_api_error(e)

# Helper endpoints per dropdown data
@api_recruiting.route('/interviewers', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_interviewers():
    """Lista utenti che possono condurre colloqui (Manager e HR)."""
    try:
        # Utenti con permessi di modifica personnel (Manager e HR)
        interviewers = User.query.filter(
            User.is_active == True,
            User.role.in_(['admin', 'manager', 'hr'])
        ).order_by(User.first_name, User.last_name).all()
        
        interviewers_data = []
        for user in interviewers:
            interviewers_data.append({
                'id': user.id,
                'full_name': user.full_name,
                'email': user.email,
                'role': user.role
            })
        
        return api_response(data=interviewers_data)
        
    except Exception as e:
        current_app.logger.error(f"Error fetching interviewers: {str(e)}")
        return handle_api_error(e)

# ===============================================
# EMAIL ENDPOINTS
# ===============================================

@api_recruiting.route('/interviews/<int:interview_id>/send-email', methods=['POST'])
@api_login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def send_interview_email(interview_id):
    """Invia email per interview (conferma, reminder, etc.)."""
    try:
        interview = InterviewSession.query.options(
            joinedload(InterviewSession.application).joinedload(Application.candidate),
            joinedload(InterviewSession.application).joinedload(Application.job_posting),
            joinedload(InterviewSession.interviewer)
        ).get_or_404(interview_id)
        
        data = request.get_json()
        email_type = data.get('type', 'confirmation')  # confirmation, reminder
        
        # Prepara dati interview per email
        interview_data = {
            'id': interview.id,
            'scheduled_date': interview.scheduled_date.isoformat(),
            'duration_minutes': interview.duration_minutes,
            'interview_type': interview.interview_type,
            'location': interview.location,
            'notes': interview.notes,
            'candidate': {
                'id': interview.application.candidate.id,
                'full_name': interview.application.candidate.full_name,
                'email': interview.application.candidate.email
            },
            'job_posting': {
                'id': interview.job_posting.id,
                'title': interview.job_posting.title
            },
            'interviewer': {
                'id': interview.interviewer.id,
                'full_name': interview.interviewer.full_name,
                'email': interview.interviewer.email
            } if interview.interviewer else None
        }
        
        # Invia email in base al tipo
        success = False
        if email_type == 'confirmation':
            success = recruiting_email_service.send_interview_confirmation(interview_data)
        elif email_type == 'reminder':
            success = recruiting_email_service.send_interview_reminder(interview_data)
        
        if success:
            return api_response(
                message=f'Email {email_type} inviata con successo a {interview.application.candidate.email}'
            )
        else:
            return api_response(
                success=False,
                message='Errore nell\'invio dell\'email'
            )
        
    except Exception as e:
        current_app.logger.error(f"Error sending interview email {interview_id}: {str(e)}")
        return handle_api_error(e)

@api_recruiting.route('/applications/<int:application_id>/send-status-email', methods=['POST'])
@api_login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def send_application_status_email(application_id):
    """Invia email per cambio status candidatura."""
    try:
        application = Application.query.options(
            joinedload(Application.candidate),
            joinedload(Application.job_posting)
        ).get_or_404(application_id)
        
        data = request.get_json()
        old_status = data.get('old_status', application.status)
        new_status = data.get('new_status', application.status)
        
        # Prepara dati application per email
        application_data = {
            'id': application.id,
            'status': application.status,
            'current_step': application.current_step,
            'candidate': {
                'id': application.candidate.id,
                'full_name': application.candidate.full_name,
                'email': application.candidate.email
            },
            'job_posting': {
                'id': application.job_posting.id,
                'title': application.job_posting.title
            }
        }
        
        success = recruiting_email_service.send_application_status_update(
            application_data, old_status, new_status
        )
        
        if success:
            return api_response(
                message=f'Email status aggiornato inviata a {application.candidate.email}'
            )
        else:
            return api_response(
                success=False,
                message='Errore nell\'invio dell\'email'
            )
        
    except Exception as e:
        current_app.logger.error(f"Error sending status email {application_id}: {str(e)}")
        return handle_api_error(e)

@api_recruiting.route('/candidates/<int:candidate_id>/send-welcome-email', methods=['POST'])
@api_login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def send_candidate_welcome_email(candidate_id):
    """Invia email di benvenuto a candidato."""
    try:
        candidate = Candidate.query.get_or_404(candidate_id)
        
        candidate_data = {
            'id': candidate.id,
            'full_name': candidate.full_name,
            'email': candidate.email
        }
        
        success = recruiting_email_service.send_welcome_candidate(candidate_data)
        
        if success:
            return api_response(
                message=f'Email di benvenuto inviata a {candidate.email}'
            )
        else:
            return api_response(
                success=False,
                message='Errore nell\'invio dell\'email'
            )
        
    except Exception as e:
        current_app.logger.error(f"Error sending welcome email {candidate_id}: {str(e)}")
        return handle_api_error(e)

# ===============================================
# CALENDAR ENDPOINTS
# ===============================================

@api_recruiting.route('/interviews/<int:interview_id>/calendar', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def download_interview_calendar(interview_id):
    """Download file .ics per singolo interview."""
    try:
        interview = InterviewSession.query.options(
            joinedload(InterviewSession.application).joinedload(Application.candidate),
            joinedload(InterviewSession.application).joinedload(Application.job_posting),
            joinedload(InterviewSession.interviewer)
        ).get_or_404(interview_id)
        
        # Prepara dati interview per calendario
        interview_data = {
            'id': interview.id,
            'scheduled_date': interview.scheduled_date.isoformat(),
            'duration_minutes': interview.duration_minutes,
            'interview_type': interview.interview_type,
            'location': interview.location,
            'notes': interview.notes,
            'status': interview.status,
            'candidate': {
                'id': interview.application.candidate.id,
                'full_name': interview.application.candidate.full_name,
                'email': interview.application.candidate.email
            },
            'job_posting': {
                'id': interview.job_posting.id,
                'title': interview.job_posting.title,
                'department': {
                    'name': interview.job_posting.department.name
                } if interview.job_posting.department else None
            },
            'interviewer': {
                'id': interview.interviewer.id,
                'full_name': interview.interviewer.full_name,
                'email': interview.interviewer.email
            } if interview.interviewer else None
        }
        
        # Genera file .ics
        ics_content = recruiting_calendar_service.generate_interview_ics(interview_data)
        
        # Crea response con file download
        response = make_response(ics_content)
        response.headers['Content-Type'] = 'text/calendar; charset=utf-8'
        response.headers['Content-Disposition'] = f'attachment; filename="colloquio_{interview.application.candidate.full_name.replace(" ", "_")}_{interview_id}.ics"'
        
        return response
        
    except Exception as e:
        current_app.logger.error(f"Error generating calendar for interview {interview_id}: {str(e)}")
        return handle_api_error(e)

@api_recruiting.route('/interviews/calendar', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def download_interviews_batch_calendar():
    """Download file .ics per multipli interviews con filtri."""
    try:
        # Parametri filtri
        status = request.args.get('status')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        interviewer_id = request.args.get('interviewer_id')
        
        # Base query
        query = InterviewSession.query.options(
            joinedload(InterviewSession.application).joinedload(Application.candidate),
            joinedload(InterviewSession.application).joinedload(Application.job_posting),
            joinedload(InterviewSession.interviewer)
        )
        
        # Applica filtri
        if status:
            query = query.filter(InterviewSession.status == status)
        
        if date_from:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(InterviewSession.scheduled_date >= date_from_obj)
        
        if date_to:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
            query = query.filter(InterviewSession.scheduled_date <= date_to_obj)
        
        if interviewer_id:
            query = query.filter(InterviewSession.interviewer_id == interviewer_id)
        
        interviews = query.order_by(InterviewSession.scheduled_date).all()
        
        if not interviews:
            return api_response(
                success=False,
                message='Nessun colloquio trovato con i filtri specificati'
            )
        
        # Prepara dati interviews
        interviews_data = []
        for interview in interviews:
            interview_data = {
                'id': interview.id,
                'scheduled_date': interview.scheduled_date.isoformat(),
                'duration_minutes': interview.duration_minutes,
                'interview_type': interview.interview_type,
                'location': interview.location,
                'notes': interview.notes,
                'status': interview.status,
                'candidate': {
                    'id': interview.candidate.id,
                    'full_name': interview.application.candidate.full_name,
                    'email': interview.application.candidate.email
                },
                'job_posting': {
                    'id': interview.job_posting.id,
                    'title': interview.job_posting.title,
                    'department': {
                        'name': interview.job_posting.department.name
                    } if interview.job_posting.department else None
                },
                'interviewer': {
                    'id': interview.interviewer.id,
                    'full_name': interview.interviewer.full_name,
                    'email': interview.interviewer.email
                } if interview.interviewer else None
            }
            interviews_data.append(interview_data)
        
        # Genera file .ics batch
        title = f"Colloqui_{len(interviews)}_interviews"
        if date_from and date_to:
            title += f"_{date_from}_to_{date_to}"
        
        ics_content = recruiting_calendar_service.generate_interviews_batch_ics(
            interviews_data, title
        )
        
        # Crea response con file download
        response = make_response(ics_content)
        response.headers['Content-Type'] = 'text/calendar; charset=utf-8'
        response.headers['Content-Disposition'] = f'attachment; filename="{title}.ics"'
        
        return response
        
    except Exception as e:
        current_app.logger.error(f"Error generating batch calendar: {str(e)}")
        return handle_api_error(e)

@api_recruiting.route('/calendar/month/<int:year>/<int:month>', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def download_monthly_calendar(year, month):
    """Download calendario mensile completo."""
    try:
        # Validation mese/anno
        if not (1 <= month <= 12):
            return api_response(
                success=False,
                message='Mese non valido (1-12)'
            )
        
        # Range date del mese
        start_date = datetime(year, month, 1)
        if month == 12:
            end_date = datetime(year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = datetime(year, month + 1, 1) - timedelta(days=1)
        
        # Query interviews del mese
        interviews = InterviewSession.query.options(
            joinedload(InterviewSession.application).joinedload(Application.candidate),
            joinedload(InterviewSession.application).joinedload(Application.job_posting),
            joinedload(InterviewSession.interviewer)
        ).filter(
            InterviewSession.scheduled_date >= start_date,
            InterviewSession.scheduled_date <= end_date
        ).order_by(InterviewSession.scheduled_date).all()
        
        if not interviews:
            return api_response(
                success=False,
                message=f'Nessun colloquio trovato per {start_date.strftime("%B %Y")}'
            )
        
        # Prepara dati interviews
        interviews_data = []
        for interview in interviews:
            interview_data = {
                'id': interview.id,
                'scheduled_date': interview.scheduled_date.isoformat(),
                'duration_minutes': interview.duration_minutes,
                'interview_type': interview.interview_type,
                'location': interview.location,
                'notes': interview.notes,
                'status': interview.status,
                'candidate': {
                    'id': interview.candidate.id,
                    'full_name': interview.application.candidate.full_name,
                    'email': interview.application.candidate.email
                },
                'job_posting': {
                    'id': interview.job_posting.id,
                    'title': interview.job_posting.title,
                    'department': {
                        'name': interview.job_posting.department.name
                    } if interview.job_posting.department else None
                },
                'interviewer': {
                    'id': interview.interviewer.id,
                    'full_name': interview.interviewer.full_name,
                    'email': interview.interviewer.email
                } if interview.interviewer else None
            }
            interviews_data.append(interview_data)
        
        # Genera calendario mensile
        ics_content = recruiting_calendar_service.generate_monthly_calendar_ics(
            interviews_data, year, month
        )
        
        month_name = start_date.strftime('%B_%Y')
        filename = f"calendario_colloqui_{month_name}.ics"
        
        # Crea response con file download
        response = make_response(ics_content)
        response.headers['Content-Type'] = 'text/calendar; charset=utf-8'
        response.headers['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        return response
        
    except Exception as e:
        current_app.logger.error(f"Error generating monthly calendar {year}/{month}: {str(e)}")
        return handle_api_error(e)


# ===============================================
# CV DOWNLOAD ENDPOINTS
# ===============================================

@api_recruiting.route('/candidates/<int:candidate_id>/cv/download', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
@audit_file_download('candidate_cv', 'cv_download')
def download_candidate_cv(candidate_id):
    """Download CV del candidato con validazione sicurezza path."""
    from flask import current_app, send_file
    from utils.security_utils import get_cv_path_validator, SecurityError, FilenameSecurityUtils
    
    try:
        candidate = Candidate.query.get_or_404(candidate_id)
        
        if not candidate.cv_path:
            return api_response(
                success=False,
                message='CV non disponibile per questo candidato',
                status_code=404
            )
        
        # Validazione sicurezza path con PathValidator
        try:
            path_validator = get_cv_path_validator()
            
            # Se path è relativo, costruisci path completo
            if not os.path.isabs(candidate.cv_path):
                # Usa prima directory CV configurata
                cv_directories = current_app.config.get('CV_UPLOAD_DIRECTORIES', [])
                if cv_directories:
                    base_dir = cv_directories[0]
                else:
                    base_dir = os.path.join(current_app.root_path, 'uploads', 'cvs')
                
                full_cv_path = os.path.join(base_dir, candidate.cv_path)
            else:
                full_cv_path = candidate.cv_path
            
            # Validazione path sicuro
            validated_path = path_validator.validate_file_path(full_cv_path, check_exists=True)
            
        except SecurityError as se:
            current_app.logger.warning(f"Security error accessing CV for candidate {candidate_id}: {str(se)}")
            return api_response(
                success=False,
                message='Accesso al file non permesso per motivi di sicurezza',
                status_code=403
            )
        
        # Validazione tipo file
        from utils.security_utils import FileSecurityValidator
        if not FileSecurityValidator.validate_file_type(validated_path, 'cv'):
            current_app.logger.warning(f"Invalid file type for CV candidate {candidate_id}: {validated_path}")
            return api_response(
                success=False,
                message='Tipo di file CV non valido',
                status_code=400
            )
        
        # Genera filename sicuro per download
        file_extension = os.path.splitext(validated_path)[1]
        base_name = f"CV_{candidate.full_name}"
        download_filename = FilenameSecurityUtils.sanitize_filename(f"{base_name}{file_extension}")
        
        # Determina MIME type
        mime_types = {
            '.pdf': 'application/pdf',
            '.doc': 'application/msword',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.txt': 'text/plain',
            '.rtf': 'application/rtf'
        }
        mime_type = mime_types.get(file_extension.lower(), 'application/octet-stream')
        
        # Audit logging gestito automaticamente dal decorator @audit_file_download
        
        return send_file(
            validated_path,
            as_attachment=True,
            download_name=download_filename,
            mimetype=mime_type
        )
        
    except Exception as e:
        current_app.logger.error(f"Error downloading CV for candidate {candidate_id}: {str(e)}")
        return handle_api_error(e, "Errore durante il download del CV")


@api_recruiting.route('/export/', methods=['GET'])
@api_login_required  
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
@audit_export_operation('recruiting', 'data_export')
def export_recruiting_data():
    """Esporta dati recruiting in formato Excel o PDF con audit logging."""
    try:
        from utils.export_utils import export_recruiting_data, generate_filename, ExportError
        from flask import send_file
        import tempfile
        import os
        
        # Parametri di filtro
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        job_posting_id = request.args.get('job_posting_id')
        status = request.args.get('status')
        export_format = request.args.get('format', 'excel').lower()
        
        # Query candidati
        candidates_query = Candidate.query.join(Application).join(JobPosting, isouter=True)
        
        # Applica filtri
        if start_date:
            try:
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
                candidates_query = candidates_query.filter(Application.applied_date >= start_date_obj)
            except ValueError:
                return api_response(False, 'Formato data inizio non valido (YYYY-MM-DD)', status_code=400)
        
        if end_date:
            try:
                end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
                candidates_query = candidates_query.filter(Application.applied_date <= end_date_obj)
            except ValueError:
                return api_response(False, 'Formato data fine non valido (YYYY-MM-DD)', status_code=400)
        
        if job_posting_id:
            candidates_query = candidates_query.filter(Application.job_posting_id == job_posting_id)
        
        if status:
            candidates_query = candidates_query.filter(Application.status == status)
        
        # Esegui query candidati
        candidates_data = candidates_query.add_columns(
            JobPosting.title.label('job_title'),
            Application.applied_date,
            Application.status.label('application_status')
        ).all()
        
        if not candidates_data:
            return api_response(False, 'Nessun dato trovato per i filtri specificati', status_code=404)
        
        # Prepara dati candidati per export
        candidates_export = []
        for candidate_data in candidates_data:
            candidate = candidate_data[0]
            job_title = candidate_data[1] or 'Nessuna posizione'
            applied_date = candidate_data[2]
            app_status = candidate_data[3]
            
            candidates_export.append({
                'nome_completo': candidate.full_name,
                'email': candidate.email,
                'telefono': candidate.phone or '',
                'posizione_applied': job_title,
                'data_candidatura': applied_date,
                'stato_candidatura': app_status,
                'esperienza_anni': candidate.years_of_experience or 0,
                'cv_disponibile': 'Sì' if candidate.cv_path else 'No',
                'data_creazione': candidate.created_at
            })
        
        # Query interview data
        interviews_query = InterviewSession.query.join(Application).join(Candidate)
        
        # Applica stessi filtri per interviews
        if start_date:
            interviews_query = interviews_query.filter(Application.applied_date >= start_date_obj)
        if end_date:
            interviews_query = interviews_query.filter(Application.applied_date <= end_date_obj)
        if job_posting_id:
            interviews_query = interviews_query.filter(Application.job_posting_id == job_posting_id)
        if status:
            interviews_query = interviews_query.filter(Application.status == status)
            
        interviews = interviews_query.add_columns(
            Candidate.full_name.label('candidate_name')
        ).all()
        
        # Prepara dati interview per export
        interviews_export = []
        for interview_data in interviews:
            interview = interview_data[0]
            candidate_name = interview_data[1]
            
            interviews_export.append({
                'candidato': candidate_name,
                'data_colloquio': interview.scheduled_date,
                'durata_minuti': interview.duration_minutes,
                'tipo_colloquio': interview.interview_type,
                'stato': interview.status,
                'location': interview.location or '',
                'note': interview.notes or '',
                'creato_il': interview.created_at
            })
        
        # Statistiche di riepilogo
        total_candidates = len(candidates_export)
        total_interviews = len(interviews_export)
        unique_positions = len(set(c['posizione_applied'] for c in candidates_export))
        
        summary = {
            'periodo': f"{start_date or 'Inizio'} - {end_date or 'Fine'}",
            'totale_candidati': total_candidates,
            'totale_colloqui': total_interviews,
            'posizioni_attive': unique_positions,
            'percentuale_colloqui': round((total_interviews / total_candidates * 100), 2) if total_candidates > 0 else 0
        }
        
        # Genera export
        try:
            export_data = export_recruiting_data(candidates_export, interviews_export, summary, export_format)
            
            # Genera nome file
            filename = generate_filename("recruiting_export", 
                                       'xlsx' if export_format == 'excel' else export_format)
            
            # Crea file temporaneo
            temp_path = os.path.join(tempfile.gettempdir(), filename)
            with open(temp_path, 'wb') as f:
                f.write(export_data.read())
            
            # Restituisci file
            return send_file(
                temp_path,
                as_attachment=True,
                download_name=filename,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' if export_format == 'excel' else 'application/pdf'
            )
            
        except ExportError as e:
            current_app.logger.error(f"Errore export recruiting: {str(e)}")
            return api_response(False, f'Errore durante export: {str(e)}', status_code=500)
        
    except Exception as e:
        current_app.logger.error(f"Errore endpoint export recruiting: {str(e)}")
        return handle_api_error(e, "Errore durante l'export dei dati recruiting")