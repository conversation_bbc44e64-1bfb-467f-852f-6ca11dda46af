"""
API RESTful per la gestione dei clienti.
"""
from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from sqlalchemy import desc
from models import Client, Contact
from utils.api_utils import (
    api_response, handle_api_error, get_pagination_params,
    format_pagination, api_permission_required
)
from utils.permissions import (
    PERMISSION_VIEW_ALL_PROJECTS, PERMISSION_EDIT_PROJECT
)
from extensions import db

# Crea il blueprint per le API dei clienti
api_clients = Blueprint('api_clients', __name__, url_prefix='/clients')

@api_clients.route('/', methods=['GET'])
@login_required
def get_clients():
    """
    Ottiene la lista dei clienti con supporto per filtri e paginazione.
    ---
    tags:
      - clients
    parameters:
      - $ref: '#/components/parameters/pageParam'
      - $ref: '#/components/parameters/perPageParam'
      - name: search
        in: query
        description: Cerca nei nomi dei clienti
        schema:
          type: string
    responses:
      200:
        description: Lista di clienti
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    clients:
                      type: array
                      items:
                        type: object
                        properties:
                          id:
                            type: integer
                          name:
                            type: string
                          industry:
                            type: string
                          description:
                            type: string
                          website:
                            type: string
                          address:
                            type: string
                          created_at:
                            type: string
                            format: date-time
                    pagination:
                      $ref: '#/components/schemas/Pagination'
      500:
        description: Errore interno del server
    """
    try:
        # Parametri di paginazione
        page, per_page = get_pagination_params()
        
        # Parametri di ricerca
        search = request.args.get('search', '').strip()
        
        # Query base
        query = Client.query
        
        # Applica filtri di ricerca
        if search:
            query = query.filter(
                Client.name.ilike(f'%{search}%')
            )
        
        # Ordina per nome
        query = query.order_by(Client.name)
        
        # Paginazione
        clients_paginated = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )
        
        # Prepara i dati dei clienti
        clients_data = []
        for client in clients_paginated.items:
            client_data = {
                'id': client.id,
                'name': client.name,
                'industry': client.industry,
                'description': client.description,
                'website': client.website,
                'address': client.address,
                'status': client.status,
                'email': client.email,
                'phone': client.phone,
                'contacts_count': client.contacts.count(),
                'created_at': client.created_at.isoformat() if client.created_at else None
            }
            clients_data.append(client_data)
        
        return api_response(
            data={
                'clients': clients_data,
                'pagination': format_pagination(clients_paginated)
            }
        )
        
    except Exception as e:
        current_app.logger.error(f"Error in get_clients: {str(e)}")
        return handle_api_error(e)


@api_clients.route('/', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_EDIT_PROJECT)
def create_client():
    """
    Crea un nuovo cliente.
    ---
    tags:
      - clients
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - name
            properties:
              name:
                type: string
                description: Nome del cliente
              industry:
                type: string
                description: Settore di attività
              description:
                type: string
                description: Descrizione del cliente
              website:
                type: string
                description: Sito web
              address:
                type: string
                description: Indirizzo
    responses:
      201:
        description: Cliente creato con successo
      400:
        description: Dati non validi
      500:
        description: Errore interno del server
    """
    try:
        data = request.get_json()
        
        # Validazione dati richiesti
        if not data.get('name'):
            return api_response(
                success=False,
                message="Il nome del cliente è obbligatorio",
                status_code=400
            )
        
        # Crea il nuovo cliente
        new_client = Client(
            name=data['name'],
            industry=data.get('industry'),
            description=data.get('description'),
            website=data.get('website'),
            address=data.get('address'),
            status=data.get('status', 'lead'),
            email=data.get('email'),
            phone=data.get('phone')
        )
        
        # Aggiungi il cliente al database
        db.session.add(new_client)
        db.session.commit()
        
        # Prepara i dati del cliente per la risposta
        client_data = {
            'id': new_client.id,
            'name': new_client.name,
            'industry': new_client.industry,
            'description': new_client.description,
            'website': new_client.website,
            'address': new_client.address,
            'status': new_client.status,
            'email': new_client.email,
            'phone': new_client.phone,
            'contacts_count': 0,
            'created_at': new_client.created_at.isoformat()
        }
        
        return api_response(
            data={'client': client_data},
            message="Cliente creato con successo",
            status_code=201
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error in create_client: {str(e)}")
        return handle_api_error(e)


@api_clients.route('/<int:client_id>', methods=['GET'])
@login_required
def get_client(client_id):
    """
    Ottiene i dettagli di un cliente specifico.
    ---
    tags:
      - clients
    parameters:
      - name: client_id
        in: path
        required: true
        description: ID del cliente
        schema:
          type: integer
    responses:
      200:
        description: Dettagli del cliente
      404:
        description: Cliente non trovato
      500:
        description: Errore interno del server
    """
    try:
        client = Client.query.get_or_404(client_id)
        
        # Prepara i dati del cliente
        client_data = {
            'id': client.id,
            'name': client.name,
            'industry': client.industry,
            'description': client.description,
            'website': client.website,
            'address': client.address,
            'status': client.status,
            'email': client.email,
            'phone': client.phone,
            'contacts_count': client.contacts.count(),
            'created_at': client.created_at.isoformat() if client.created_at else None,
            'updated_at': client.updated_at.isoformat() if client.updated_at else None
        }
        
        return api_response(
            data={'client': client_data}
        )
        
    except Exception as e:
        current_app.logger.error(f"Error in get_client: {str(e)}")
        return handle_api_error(e)


@api_clients.route('/<int:client_id>', methods=['PUT'])
@login_required
@api_permission_required(PERMISSION_EDIT_PROJECT)
def update_client(client_id):
    """
    Aggiorna un cliente esistente.
    ---
    tags:
      - clients
    parameters:
      - name: client_id
        in: path
        required: true
        description: ID del cliente da aggiornare
        schema:
          type: integer
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              name:
                type: string
                description: Nome del cliente
              industry:
                type: string
                description: Settore di attività
              description:
                type: string
                description: Descrizione del cliente
              website:
                type: string
                description: Sito web
              address:
                type: string
                description: Indirizzo
    responses:
      200:
        description: Cliente aggiornato con successo
      404:
        description: Cliente non trovato
      500:
        description: Errore interno del server
    """
    try:
        client = Client.query.get_or_404(client_id)
        data = request.get_json()
        
        # Aggiorna i campi del cliente
        if 'name' in data:
            client.name = data['name']
        if 'industry' in data:
            client.industry = data['industry']
        if 'description' in data:
            client.description = data['description']
        if 'website' in data:
            client.website = data['website']
        if 'address' in data:
            client.address = data['address']
        if 'status' in data:
            client.status = data['status']
        if 'email' in data:
            client.email = data['email']
        if 'phone' in data:
            client.phone = data['phone']
        
        # Salva le modifiche
        db.session.commit()
        
        # Prepara i dati del cliente aggiornato
        client_data = {
            'id': client.id,
            'name': client.name,
            'industry': client.industry,
            'description': client.description,
            'website': client.website,
            'address': client.address,
            'status': client.status,
            'email': client.email,
            'phone': client.phone,
            'contacts_count': client.contacts.count(),
            'created_at': client.created_at.isoformat() if client.created_at else None,
            'updated_at': client.updated_at.isoformat() if client.updated_at else None
        }
        
        return api_response(
            data={'client': client_data},
            message="Cliente aggiornato con successo"
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error in update_client: {str(e)}")
        return handle_api_error(e)


@api_clients.route('/<int:client_id>', methods=['DELETE'])
@login_required
@api_permission_required(PERMISSION_EDIT_PROJECT)
def delete_client(client_id):
    """
    Elimina un cliente esistente.
    ---
    tags:
      - clients
    parameters:
      - name: client_id
        in: path
        required: true
        description: ID del cliente da eliminare
        schema:
          type: integer
    responses:
      200:
        description: Cliente eliminato con successo
      404:
        description: Cliente non trovato
      500:
        description: Errore interno del server
    """
    try:
        client = Client.query.get_or_404(client_id)
        
        # Elimina il cliente
        db.session.delete(client)
        db.session.commit()
        
        return api_response(
            message=f"Cliente '{client.name}' eliminato con successo"
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error in delete_client: {str(e)}")
        return handle_api_error(e)
