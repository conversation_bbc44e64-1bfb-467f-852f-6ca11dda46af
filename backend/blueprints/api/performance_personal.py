# API endpoints for personal performance management
from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from sqlalchemy import and_, or_, func, extract, desc
from datetime import datetime, date
import json

from models import db
from models_split.performance import PerformanceGoal, PerformanceReview, PerformanceTemplate
from models_split.user import User
from utils.decorators import permission_required
from utils.api_utils import api_response

performance_personal_bp = Blueprint('performance_personal', __name__, url_prefix='/api/performance')

@performance_personal_bp.route('/employees/<int:employee_id>/goals/<int:year>', methods=['GET'])
@login_required
@permission_required('view_personnel_data')
def get_employee_goals_by_year(employee_id, year):
    """Ottieni obiettivi per dipendente e anno specifico"""
    try:
        # Verifica che l'utente possa vedere i dati di questo dipendente
        employee = User.query.get_or_404(employee_id)
        
        # L'utente può vedere solo i propri dati o essere admin/manager
        if (current_user.id != employee_id and 
            current_user.role not in ['admin', 'manager']):
            return api_response(success=False, message="Non autorizzato", status_code=403)

        # Query per obiettivi dell'anno (sia year che target_year)
        goals = PerformanceGoal.query.filter(
            PerformanceGoal.employee_id == employee_id,
            or_(
                PerformanceGoal.year == year,
                PerformanceGoal.target_year == year
            ),
            PerformanceGoal.is_template == False
        ).order_by(PerformanceGoal.created_at.desc()).all()

        # Ottieni template disponibili se l'utente è admin/manager
        templates = []
        if current_user.role in ['admin', 'manager']:
            templates = PerformanceGoal.query.filter_by(is_template=True).all()

        # Prepara dati per risposta
        goals_data = []
        for goal in goals:
            goal_data = {
                'id': goal.id,
                'title': goal.title,
                'description': goal.description,
                'category': goal.category,
                'status': goal.status,
                'progress_percentage': goal.progress_percentage or 0,
                'achievement_rating': goal.achievement_rating,
                'year': goal.year,
                'target_year': goal.target_year,
                'start_date': goal.start_date.isoformat() if goal.start_date else None,
                'target_date': goal.target_date.isoformat() if goal.target_date else None,
                'completion_date': goal.completion_date.isoformat() if goal.completion_date else None,
                'priority': goal.priority,
                'weight': goal.weight,
                'success_criteria': goal.success_criteria,
                'measurable_outcomes': goal.measurable_outcomes,
                'employee_self_assessment': goal.employee_self_assessment,
                'manager_assessment': goal.manager_assessment,
                'completion_notes': goal.completion_notes,
                'is_template': goal.is_template,
                'template_id': goal.template_id,
                'visibility': goal.visibility,
                'created_at': goal.created_at.isoformat() if goal.created_at else None,
                'updated_at': goal.updated_at.isoformat() if goal.updated_at else None,
                # Template info se applicabile
                'template_source': {
                    'id': goal.template.id,
                    'title': goal.template.title
                } if goal.template else None,
                # Info su chi ha assegnato
                'assigned_by': {
                    'id': goal.assigned_by.id,
                    'full_name': goal.assigned_by.full_name
                } if goal.assigned_by else None,
                # Review collegata
                'linked_review': {
                    'id': goal.review.id,
                    'review_year': goal.review.review_year,
                    'status': goal.review.status
                } if goal.review else None
            }
            goals_data.append(goal_data)

        # Prepara template data
        templates_data = []
        for template in templates:
            template_data = {
                'id': template.id,
                'title': template.title,
                'description': template.description,
                'category': template.category,
                'priority': template.priority,
                'weight': template.weight,
                'success_criteria': template.success_criteria,
                'measurable_outcomes': template.measurable_outcomes,
                'visibility': template.visibility
            }
            templates_data.append(template_data)

        # Statistiche riassuntive
        total_goals = len(goals)
        completed_goals = len([g for g in goals if g.status == 'completed'])
        in_progress_goals = len([g for g in goals if g.status == 'active'])
        avg_progress = sum([g.progress_percentage or 0 for g in goals]) / total_goals if total_goals > 0 else 0
        avg_rating = sum([g.achievement_rating for g in goals if g.achievement_rating]) / len([g for g in goals if g.achievement_rating]) if any(g.achievement_rating for g in goals) else None

        summary = {
            'total_goals': total_goals,
            'completed_goals': completed_goals,
            'in_progress_goals': in_progress_goals,
            'completion_rate': round((completed_goals / total_goals) * 100, 1) if total_goals > 0 else 0,
            'avg_progress': round(avg_progress, 1),
            'avg_achievement_rating': round(avg_rating, 1) if avg_rating else None
        }

        return api_response({
            'goals': goals_data,
            'templates': templates_data,
            'employee': {
                'id': employee.id,
                'full_name': employee.full_name,
                'email': employee.email,
                'department': employee.department
            },
            'year': year,
            'summary': summary
        })

    except Exception as e:
        current_app.logger.error(f"Error fetching employee goals: {str(e)}")
        return api_response(success=False, message="Errore nel caricamento obiettivi", status_code=500)


@performance_personal_bp.route('/employees/<int:employee_id>/summary/<int:year>', methods=['GET'])
@login_required
@permission_required('view_personnel_data')
def get_employee_performance_summary(employee_id, year):
    """Ottieni riassunto performance per dipendente e anno"""
    try:
        # Verifica autorizzazioni
        employee = User.query.get_or_404(employee_id)
        
        if (current_user.id != employee_id and 
            current_user.role not in ['admin', 'manager']):
            return api_response(success=False, message="Non autorizzato", status_code=403)

        # Obiettivi dell'anno
        goals = PerformanceGoal.query.filter(
            PerformanceGoal.employee_id == employee_id,
            or_(
                PerformanceGoal.year == year,
                PerformanceGoal.target_year == year
            ),
            PerformanceGoal.is_template == False
        ).all()

        # Review dell'anno
        reviews = PerformanceReview.query.filter(
            PerformanceReview.employee_id == employee_id,
            PerformanceReview.review_year == year
        ).all()

        # Calcoli obiettivi
        goals_stats = {
            'total': len(goals),
            'completed': len([g for g in goals if g.status == 'completed']),
            'in_progress': len([g for g in goals if g.status == 'active']),
            'cancelled': len([g for g in goals if g.status == 'cancelled']),
            'avg_progress': round(sum([g.progress_percentage or 0 for g in goals]) / len(goals), 1) if goals else 0,
            'avg_rating': None
        }
        
        # Calcola rating medio solo per obiettivi con rating
        rated_goals = [g for g in goals if g.achievement_rating]
        if rated_goals:
            goals_stats['avg_rating'] = round(sum([g.achievement_rating for g in rated_goals]) / len(rated_goals), 1)

        # Stats per categoria
        categories_stats = {}
        for goal in goals:
            cat = goal.category or 'other'
            if cat not in categories_stats:
                categories_stats[cat] = {'total': 0, 'completed': 0, 'avg_rating': None}
            
            categories_stats[cat]['total'] += 1
            if goal.status == 'completed':
                categories_stats[cat]['completed'] += 1

        # Calcola rating medio per categoria
        for cat in categories_stats:
            cat_goals = [g for g in goals if (g.category or 'other') == cat and g.achievement_rating]
            if cat_goals:
                categories_stats[cat]['avg_rating'] = round(sum([g.achievement_rating for g in cat_goals]) / len(cat_goals), 1)

        # Stats review
        reviews_stats = {
            'total': len(reviews),
            'completed': len([r for r in reviews if r.status == 'completed']),
            'in_progress': len([r for r in reviews if r.status == 'in_progress']),
            'pending': len([r for r in reviews if r.status in ['draft', 'pending']]),
            'avg_overall_rating': None
        }

        completed_reviews = [r for r in reviews if r.status == 'completed' and r.overall_rating]
        if completed_reviews:
            reviews_stats['avg_overall_rating'] = round(sum([r.overall_rating for r in completed_reviews]) / len(completed_reviews), 1)

        # Obiettivi recenti
        recent_goals = sorted(goals, key=lambda x: x.updated_at or x.created_at, reverse=True)[:5]
        recent_goals_data = []
        for goal in recent_goals:
            recent_goals_data.append({
                'id': goal.id,
                'title': goal.title,
                'status': goal.status,
                'progress_percentage': goal.progress_percentage or 0,
                'achievement_rating': goal.achievement_rating,
                'updated_at': goal.updated_at.isoformat() if goal.updated_at else goal.created_at.isoformat()
            })

        return api_response({
            'employee': {
                'id': employee.id,
                'full_name': employee.full_name,
                'email': employee.email,
                'department': employee.department
            },
            'year': year,
            'goals_stats': goals_stats,
            'categories_stats': categories_stats,
            'reviews_stats': reviews_stats,
            'recent_goals': recent_goals_data
        })

    except Exception as e:
        current_app.logger.error(f"Error fetching employee summary: {str(e)}")
        return api_response(success=False, message="Errore nel caricamento riassunto", status_code=500)


@performance_personal_bp.route('/employees/<int:employee_id>/goals/assign', methods=['POST'])
@login_required
@permission_required('manage_personnel')
def assign_template_goals_to_employee(employee_id):
    """Assegna obiettivi da template a dipendente"""
    try:
        data = request.get_json()
        template_ids = data.get('template_ids', [])
        year = data.get('year', datetime.now().year)
        
        if not template_ids:
            return api_response(success=False, message="Nessun template selezionato", status_code=400)

        employee = User.query.get_or_404(employee_id)
        
        # Verifica che i template esistano
        templates = PerformanceGoal.query.filter(
            PerformanceGoal.id.in_(template_ids),
            PerformanceGoal.is_template == True
        ).all()
        
        if len(templates) != len(template_ids):
            return api_response(success=False, message="Alcuni template non sono validi", status_code=400)

        created_goals = []
        for template in templates:
            # Verifica se l'obiettivo non è già stato assegnato
            existing = PerformanceGoal.query.filter(
                PerformanceGoal.employee_id == employee_id,
                PerformanceGoal.template_id == template.id,
                or_(
                    PerformanceGoal.year == year,
                    PerformanceGoal.target_year == year
                )
            ).first()
            
            if existing:
                continue  # Skip se già assegnato
            
            # Crea nuovo obiettivo da template
            new_goal = PerformanceGoal.create_from_template(
                template_id=template.id,
                employee_id=employee_id,
                assigned_by_id=current_user.id,
                year=year
            )
            
            db.session.add(new_goal)
            created_goals.append(new_goal)

        db.session.commit()

        # Prepara risposta
        goals_data = []
        for goal in created_goals:
            goals_data.append({
                'id': goal.id,
                'title': goal.title,
                'description': goal.description,
                'category': goal.category,
                'year': goal.year,
                'target_year': goal.target_year,
                'template_source': {
                    'id': goal.template.id,
                    'title': goal.template.title
                }
            })

        return api_response({
            'assigned_goals': goals_data,
            'count': len(created_goals),
            'employee': {
                'id': employee.id,
                'full_name': employee.full_name
            },
            'year': year
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error assigning template goals: {str(e)}")
        return api_response(success=False, message="Errore nell'assegnazione obiettivi", status_code=500)


@performance_personal_bp.route('/templates/goals', methods=['GET'])
@login_required
@permission_required('view_personnel_data')
def get_goal_templates():
    """Ottieni tutti i template di obiettivi disponibili"""
    try:
        # Query parametri
        category = request.args.get('category')
        visibility = request.args.get('visibility')
        search = request.args.get('search')
        
        query = PerformanceGoal.query.filter_by(is_template=True)
        
        # Filtri
        if category:
            query = query.filter(PerformanceGoal.category == category)
        
        if visibility:
            query = query.filter(PerformanceGoal.visibility == visibility)
        
        if search:
            query = query.filter(
                or_(
                    PerformanceGoal.title.ilike(f'%{search}%'),
                    PerformanceGoal.description.ilike(f'%{search}%')
                )
            )
        
        templates = query.order_by(PerformanceGoal.category, PerformanceGoal.title).all()

        # Prepara dati
        templates_data = []
        for template in templates:
            # Conta quante volte è stato utilizzato
            usage_count = PerformanceGoal.query.filter_by(template_id=template.id).count()
            
            template_data = {
                'id': template.id,
                'title': template.title,
                'description': template.description,
                'category': template.category,
                'priority': template.priority,
                'weight': template.weight,
                'success_criteria': template.success_criteria,
                'measurable_outcomes': template.measurable_outcomes,
                'visibility': template.visibility,
                'usage_count': usage_count,
                'created_at': template.created_at.isoformat() if template.created_at else None,
                'created_by': {
                    'id': template.created_by_user.id,
                    'full_name': template.created_by_user.full_name
                } if template.created_by_user else None
            }
            templates_data.append(template_data)

        # Ottieni categorie disponibili
        categories = db.session.query(PerformanceGoal.category).filter(
            PerformanceGoal.is_template == True,
            PerformanceGoal.category.isnot(None)
        ).distinct().all()
        categories_list = [cat[0] for cat in categories if cat[0]]

        return api_response({
            'templates': templates_data,
            'total': len(templates_data),
            'categories': categories_list
        })

    except Exception as e:
        current_app.logger.error(f"Error fetching goal templates: {str(e)}")
        return api_response(success=False, message="Errore nel caricamento template", status_code=500)


@performance_personal_bp.route('/employees/<int:employee_id>/goals', methods=['POST'])
@login_required
@permission_required('manage_personnel')
def create_employee_goal(employee_id):
    """Crea nuovo obiettivo per dipendente"""
    try:
        data = request.get_json()
        
        employee = User.query.get_or_404(employee_id)
        
        # Verifica dati richiesti
        required_fields = ['title', 'year']
        for field in required_fields:
            if not data.get(field):
                return api_response(success=False, message=f"Campo '{field}' richiesto", status_code=400)

        new_goal = PerformanceGoal(
            employee_id=employee_id,
            title=data['title'],
            description=data.get('description', ''),
            category=data.get('category'),
            year=data['year'],
            target_year=data.get('target_year', data['year']),
            start_date=datetime.strptime(data['start_date'], '%Y-%m-%d').date() if data.get('start_date') else None,
            target_date=datetime.strptime(data['target_date'], '%Y-%m-%d').date() if data.get('target_date') else None,
            priority=data.get('priority', 'medium'),
            weight=data.get('weight'),
            success_criteria=data.get('success_criteria', ''),
            measurable_outcomes=data.get('measurable_outcomes', ''),
            visibility=data.get('visibility', 'private'),
            set_by_id=current_user.id,
            created_by=current_user.id,
            is_template=False
        )

        db.session.add(new_goal)
        db.session.commit()

        return api_response({
            'goal': {
                'id': new_goal.id,
                'title': new_goal.title,
                'description': new_goal.description,
                'category': new_goal.category,
                'year': new_goal.year,
                'status': new_goal.status,
                'created_at': new_goal.created_at.isoformat()
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error creating employee goal: {str(e)}")
        return api_response(success=False, message="Errore nella creazione obiettivo", status_code=500)


@performance_personal_bp.route('/goals/<int:goal_id>', methods=['PUT'])
@login_required
def update_goal(goal_id):
    """Aggiorna obiettivo esistente"""
    try:
        goal = PerformanceGoal.query.get_or_404(goal_id)
        data = request.get_json()
        
        # Verifica autorizzazioni
        can_edit = (
            current_user.id == goal.employee_id or  # Proprietario
            current_user.role in ['admin', 'manager'] or  # Admin/Manager
            current_user.id == goal.set_by_id  # Chi ha creato l'obiettivo
        )
        
        if not can_edit:
            return api_response(success=False, message="Non autorizzato", status_code=403)

        # Aggiorna campi
        updateable_fields = [
            'title', 'description', 'category', 'priority', 'weight',
            'success_criteria', 'measurable_outcomes', 'progress_percentage',
            'employee_self_assessment', 'manager_assessment', 'completion_notes'
        ]
        
        for field in updateable_fields:
            if field in data:
                setattr(goal, field, data[field])

        # Gestione date
        if 'start_date' in data and data['start_date']:
            goal.start_date = datetime.strptime(data['start_date'], '%Y-%m-%d').date()
        if 'target_date' in data and data['target_date']:
            goal.target_date = datetime.strptime(data['target_date'], '%Y-%m-%d').date()
        if 'completion_date' in data and data['completion_date']:
            goal.completion_date = datetime.strptime(data['completion_date'], '%Y-%m-%d').date()

        # Aggiorna status se completato
        if 'status' in data:
            goal.status = data['status']
            if data['status'] == 'completed' and not goal.completion_date:
                goal.completion_date = date.today()

        # Achievement rating solo per admin/manager o proprietario
        if 'achievement_rating' in data and (current_user.role in ['admin', 'manager'] or current_user.id == goal.employee_id):
            goal.achievement_rating = data['achievement_rating']

        goal.updated_at = datetime.utcnow()
        db.session.commit()

        return api_response({
            'goal': {
                'id': goal.id,
                'title': goal.title,
                'status': goal.status,
                'progress_percentage': goal.progress_percentage,
                'achievement_rating': goal.achievement_rating,
                'updated_at': goal.updated_at.isoformat()
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating goal: {str(e)}")
        return api_response(success=False, message="Errore nell'aggiornamento obiettivo", status_code=500)