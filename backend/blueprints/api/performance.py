"""
API endpoints for performance management.
Provides REST API for performance reviews, goals, KPIs, and feedback.
"""

from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from sqlalchemy import desc, and_, or_, func
from sqlalchemy.orm import joinedload
from datetime import datetime, date

from extensions import db
from models import User, Department
from utils.api_utils import (
    api_response, handle_api_error, get_pagination_params,
    format_pagination, api_permission_required
)
from utils.permissions import (
    PERMISSION_VIEW_PERSONNEL_DATA, PERMISSION_EDIT_PERSONNEL_DATA, PERMISSION_MANAGE_USERS
)
from models import (
    PerformanceReview, PerformanceFeedback, PerformanceGoal, 
    PerformanceKPI, PerformanceReward, PerformanceTemplate,
    PerformanceReviewParticipant
)
# Models imported successfully

# Create blueprint
api_performance = Blueprint('api_performance', __name__)

@api_performance.route('/test', methods=['GET'])
def test_endpoint():
    return {"success": True, "message": "Performance module working!"}

@api_performance.route('/reviews', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_performance_reviews():
    """
    Get list of performance reviews with filtering and pagination.
    
    Query Parameters:
    - page: Page number (default: 1)
    - per_page: Items per page (default: 20)
    - year: Filter by review year
    - status: Filter by review status
    - employee_id: Filter by employee (admin/hr only)
    """
    try:
        # Get pagination parameters
        page, per_page = get_pagination_params()
        
        # Build base query
        query = PerformanceReview.query.options(
            joinedload(PerformanceReview.employee),
            joinedload(PerformanceReview.reviewer)
        )
        
        # Apply filters based on user role
        if current_user.role not in ['admin', 'hr']:
            # Regular users can only see their own reviews
            query = query.filter(PerformanceReview.employee_id == current_user.id)
        else:
            # Admin/HR can filter by employee_id if provided
            employee_id = request.args.get('employee_id', type=int)
            if employee_id:
                query = query.filter(PerformanceReview.employee_id == employee_id)
        
        # Year filter
        year = request.args.get('year', type=int)
        if year:
            query = query.filter(PerformanceReview.review_year == year)
        
        # Status filter
        status = request.args.get('status')
        if status:
            query = query.filter(PerformanceReview.status == status)
        
        # Order by most recent
        query = query.order_by(desc(PerformanceReview.created_at))
        
        # Execute pagination
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        
        # Serialize reviews
        reviews_data = []
        for review in pagination.items:
            reviews_data.append({
                'id': review.id,
                'employee_id': review.employee_id,
                'employee': {
                    'id': review.employee.id,
                    'first_name': review.employee.first_name,
                    'last_name': review.employee.last_name,
                    'email': review.employee.email,
                    'position': review.employee.position
                },
                'employee_name': f"{review.employee.first_name} {review.employee.last_name}",
                'reviewer_id': review.reviewer_id,
                'reviewer': {
                    'id': review.reviewer.id,
                    'first_name': review.reviewer.first_name,
                    'last_name': review.reviewer.last_name,
                    'email': review.reviewer.email
                },
                'reviewer_name': f"{review.reviewer.first_name} {review.reviewer.last_name}",
                'review_year': review.review_year,
                'review_period_start': review.review_period_start.isoformat() if review.review_period_start else None,
                'review_period_end': review.review_period_end.isoformat() if review.review_period_end else None,
                'status': review.status,
                'due_date': review.due_date.isoformat() if review.due_date else None,
                'overall_rating': review.overall_rating,
                'created_at': review.created_at.isoformat(),
                'updated_at': review.updated_at.isoformat()
            })
        
        return api_response(
            data={
                'reviews': reviews_data,
                'pagination': format_pagination(pagination)
            },
            message=f"Retrieved {len(reviews_data)} performance reviews"
        )
        
    except Exception as e:
        current_app.logger.error(f"Error retrieving performance reviews: {str(e)}")
        return handle_api_error(e)

@api_performance.route('/reviews/<int:review_id>', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_performance_review(review_id):
    """Get detailed information for a specific performance review."""
    try:
        review = PerformanceReview.query.options(
            joinedload(PerformanceReview.employee),
            joinedload(PerformanceReview.reviewer),
            joinedload(PerformanceReview.goals),
            joinedload(PerformanceReview.feedback_received)
        ).get_or_404(review_id)
        
        # Check permission
        if current_user.role not in ['admin', 'hr'] and review.employee_id != current_user.id:
            return api_response(data={}, message="Access denied", status_code=403)
        
        # Get goals and KPIs
        goals = []
        for goal in review.goals:
            kpis = []
            for kpi in goal.kpis:
                kpis.append({
                    'id': kpi.id,
                    'name': kpi.name,
                    'target_value': kpi.target_value,
                    'actual_value': kpi.current_value,
                    'unit': kpi.unit_of_measure
                })
            
            goals.append({
                'id': goal.id,
                'title': goal.title,
                'description': goal.description,
                'category': goal.category,
                'priority': goal.priority,
                'status': goal.status,
                'progress': goal.progress,
                'kpis': kpis
            })
        
        # Get feedback
        feedback = []
        for fb in review.feedback_received:
            feedback.append({
                'id': fb.id,
                'feedback_type': fb.feedback_type,
                'from_user_id': fb.from_user_id,
                'content': fb.content,
                'rating': fb.rating,
                'created_at': fb.created_at.isoformat()
            })
        
        review_data = {
            'id': review.id,
            'employee': {
                'id': review.employee.id,
                'first_name': review.employee.first_name,
                'last_name': review.employee.last_name,
                'email': review.employee.email,
                'position': review.employee.position
            },
            'reviewer': {
                'id': review.reviewer.id,
                'first_name': review.reviewer.first_name,
                'last_name': review.reviewer.last_name,
                'email': review.reviewer.email
            },
            'review_year': review.review_year,
            'review_period_start': review.review_period_start.isoformat() if review.review_period_start else None,
            'review_period_end': review.review_period_end.isoformat() if review.review_period_end else None,
            'status': review.status,
            'due_date': review.due_date.isoformat() if review.due_date else None,
            'overall_rating': review.overall_rating,
            'technical_skills_rating': review.technical_skills_rating,
            'soft_skills_rating': review.soft_skills_rating,
            'goals_achievement_rating': review.goals_achievement_rating,
            'leadership_rating': review.leadership_rating,
            'teamwork_rating': review.teamwork_rating,
            'communication_rating': review.communication_rating,
            'initiative_rating': review.initiative_rating,
            'comments': review.comments,
            'goals': goals,
            'feedback': feedback,
            'created_at': review.created_at.isoformat(),
            'updated_at': review.updated_at.isoformat()
        }
        
        return api_response(
            data={'review': review_data},
            message="Performance review retrieved successfully"
        )
        
    except Exception as e:
        current_app.logger.error(f"Error retrieving performance review: {str(e)}")
        return handle_api_error(e)

@api_performance.route('/reviews', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def create_performance_review():
    """Create a new performance review."""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['employee_id', 'reviewer_id', 'review_year']
        for field in required_fields:
            if field not in data:
                return api_response(
                    data={}, 
                    message=f"Missing required field: {field}", 
                    status_code=400
                )
        
        # Create new review
        review = PerformanceReview(
            employee_id=data['employee_id'],
            reviewer_id=data['reviewer_id'],
            review_year=data['review_year'],
            review_period_start=datetime.fromisoformat(data['review_period_start']) if data.get('review_period_start') else None,
            review_period_end=datetime.fromisoformat(data['review_period_end']) if data.get('review_period_end') else None,
            due_date=datetime.fromisoformat(data['due_date']) if data.get('due_date') else None,
            status=data.get('status', 'draft'),
            template_id=data.get('template_id')
        )
        
        db.session.add(review)
        db.session.commit()
        
        return api_response(
            data={'review_id': review.id},
            message="Performance review created successfully",
            status_code=201
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error creating performance review: {str(e)}")
        return handle_api_error(e)

@api_performance.route('/reviews/<int:review_id>', methods=['PUT'])
@login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def update_performance_review(review_id):
    """Update an existing performance review."""
    try:
        review = PerformanceReview.query.get_or_404(review_id)
        data = request.get_json()
        
        # Update fields
        if 'status' in data:
            review.status = data['status']
        if 'overall_rating' in data:
            review.overall_rating = data['overall_rating']
        if 'technical_skills_rating' in data:
            review.technical_skills_rating = data['technical_skills_rating']
        if 'soft_skills_rating' in data:
            review.soft_skills_rating = data['soft_skills_rating']
        if 'goals_achievement_rating' in data:
            review.goals_achievement_rating = data['goals_achievement_rating']
        if 'leadership_rating' in data:
            review.leadership_rating = data['leadership_rating']
        if 'teamwork_rating' in data:
            review.teamwork_rating = data['teamwork_rating']
        if 'communication_rating' in data:
            review.communication_rating = data['communication_rating']
        if 'initiative_rating' in data:
            review.initiative_rating = data['initiative_rating']
        if 'comments' in data:
            review.comments = data['comments']
        
        review.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return api_response(
            data={'review_id': review.id},
            message="Performance review updated successfully"
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating performance review: {str(e)}")
        return handle_api_error(e)

@api_performance.route('/goals', methods=['GET'])
@login_required
def get_performance_goals():
    """Get list of performance goals."""
    try:
        page, per_page = get_pagination_params()
        
        query = PerformanceGoal.query.options(
            joinedload(PerformanceGoal.review).joinedload(PerformanceReview.employee)
        )
        
        # Always join with PerformanceReview for filtering
        query = query.join(PerformanceReview)
        
        # Filter by user role
        if current_user.role not in ['admin', 'hr']:
            query = query.filter(PerformanceReview.employee_id == current_user.id)
        
        # Filters
        review_id = request.args.get('review_id', type=int)
        if review_id:
            query = query.filter(PerformanceGoal.review_id == review_id)
        
        # Handle employee_id filter (for admin/hr users)
        employee_id_str = request.args.get('employee_id', '')
        employee_id = None
        if employee_id_str and employee_id_str.isdigit():
            employee_id = int(employee_id_str)
        
        if employee_id and current_user.role in ['admin', 'hr']:
            query = query.filter(PerformanceReview.employee_id == employee_id)
        
        year_str = request.args.get('year', '')
        year = None
        if year_str and year_str.isdigit():
            year = int(year_str)
            
            
        if year:
            query = query.filter(PerformanceReview.review_year == year)
        
        query = query.order_by(desc(PerformanceGoal.created_at))
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        
        goals_data = []
        for goal in pagination.items:
            goals_data.append({
                'id': goal.id,
                'review_id': goal.review_id,
                'title': goal.title,
                'description': goal.description,
                'category': goal.category,
                'priority': goal.priority,
                'status': goal.status,
                'progress': goal.progress,
                'target_date': goal.target_date.isoformat() if goal.target_date else None,
                'created_at': goal.created_at.isoformat()
            })
        
        return api_response(
            data={
                'goals': goals_data,
                'pagination': format_pagination(pagination)
            },
            message=f"Retrieved {len(goals_data)} performance goals"
        )
        
    except Exception as e:
        current_app.logger.error(f"Error retrieving performance goals: {str(e)}")
        return handle_api_error(e)

@api_performance.route('/goals', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def create_performance_goal():
    """Create a new performance goal."""
    try:
        data = request.get_json()
        
        required_fields = ['title', 'category']
        for field in required_fields:
            if field not in data:
                return api_response(
                    data={}, 
                    message=f"Missing required field: {field}", 
                    status_code=400
                )
        
        goal = PerformanceGoal(
            review_id=data.get('review_id'),  # Can be None for standalone goals
            employee_id=data.get('employee_id', current_user.id),  # Default to current user
            title=data['title'],
            description=data.get('description'),
            category=data['category'],
            priority=data.get('priority', 'medium'),
            status=data.get('status', 'active'),
            target_date=datetime.fromisoformat(data['target_date']).date() if data.get('target_date') else None,
            year=data.get('year', datetime.now().year)
        )
        
        db.session.add(goal)
        db.session.commit()
        
        return api_response(
            data={'goal_id': goal.id},
            message="Performance goal created successfully",
            status_code=201
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error creating performance goal: {str(e)}")
        return handle_api_error(e)

@api_performance.route('/goals/<int:goal_id>', methods=['PUT'])
@login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def update_performance_goal(goal_id):
    """Update an existing performance goal."""
    try:
        goal = PerformanceGoal.query.get_or_404(goal_id)
        data = request.get_json()
        
        # Check permissions
        if current_user.role not in ['admin', 'hr']:
            if goal.employee_id != current_user.id:
                return api_response(
                    data={}, 
                    message="Access denied", 
                    status_code=403
                )
        
        # Update fields
        if 'title' in data:
            goal.title = data['title']
        if 'description' in data:
            goal.description = data['description']
        if 'category' in data:
            goal.category = data['category']
        if 'priority' in data:
            goal.priority = data['priority']
        if 'status' in data:
            goal.status = data['status']
        if 'progress' in data:
            goal.progress = data['progress']
        if 'target_date' in data:
            goal.target_date = datetime.fromisoformat(data['target_date']).date() if data['target_date'] else None
        if 'success_criteria' in data:
            goal.success_criteria = data['success_criteria']
        if 'measurable_outcomes' in data:
            goal.measurable_outcomes = data['measurable_outcomes']
        if 'manager_assessment' in data:
            goal.manager_assessment = data['manager_assessment']
        if 'employee_self_assessment' in data:
            goal.employee_self_assessment = data['employee_self_assessment']
        if 'achievement_rating' in data:
            goal.achievement_rating = data['achievement_rating']
        if 'notes' in data:
            goal.notes = data['notes']
        if 'completion_notes' in data:
            goal.completion_notes = data['completion_notes']
        
        # Auto-set completion date if status changed to completed
        if data.get('status') == 'completed' and not goal.completion_date:
            goal.completion_date = date.today()
        
        goal.updated_at = datetime.utcnow()
        db.session.commit()
        
        return api_response(
            data={'goal_id': goal.id},
            message="Performance goal updated successfully"
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating performance goal: {str(e)}")
        return handle_api_error(e)

@api_performance.route('/goals/<int:goal_id>', methods=['DELETE'])
@login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def delete_performance_goal(goal_id):
    """Delete a performance goal."""
    try:
        goal = PerformanceGoal.query.get_or_404(goal_id)
        
        # Check permissions
        if current_user.role not in ['admin', 'hr']:
            if goal.employee_id != current_user.id:
                return api_response(
                    data={}, 
                    message="Access denied", 
                    status_code=403
                )
        
        # Delete associated KPIs first (cascade)
        PerformanceKPI.query.filter_by(goal_id=goal_id).delete()
        
        # Delete the goal
        db.session.delete(goal)
        db.session.commit()
        
        return api_response(
            data={},
            message="Performance goal deleted successfully"
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error deleting performance goal: {str(e)}")
        return handle_api_error(e)

@api_performance.route('/feedback', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_performance_feedback():
    """Get list of performance feedback (both review-linked and standalone)."""
    try:
        page, per_page = get_pagination_params()
        
        query = PerformanceFeedback.query.options(
            joinedload(PerformanceFeedback.from_user),
            joinedload(PerformanceFeedback.to_user)
        )
        
        # Filter by user role
        if current_user.role not in ['admin', 'hr']:
            # Users can see feedback they gave or received
            query = query.filter(
                or_(
                    PerformanceFeedback.from_user_id == current_user.id,
                    PerformanceFeedback.to_user_id == current_user.id
                )
            )
        
        # Filters
        review_id = request.args.get('review_id', type=int)
        if review_id:
            query = query.filter(PerformanceFeedback.review_id == review_id)
        
        feedback_type = request.args.get('feedback_type')
        if feedback_type:
            query = query.filter(PerformanceFeedback.feedback_type == feedback_type)
        
        # Filter standalone vs review-linked
        standalone = request.args.get('standalone', type=bool)
        if standalone is True:
            query = query.filter(PerformanceFeedback.review_id.is_(None))
        elif standalone is False:
            query = query.filter(PerformanceFeedback.review_id.isnot(None))
        
        # Filter by status
        status = request.args.get('status')
        if status:
            query = query.filter(PerformanceFeedback.status == status)
        
        # Filter by priority (for standalone feedback)
        priority = request.args.get('priority')
        if priority:
            query = query.filter(PerformanceFeedback.priority == priority)
        
        query = query.order_by(desc(PerformanceFeedback.created_at))
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        
        feedback_data = []
        for feedback in pagination.items:
            feedback_data.append(feedback.to_dict())
        
        return api_response(
            data={
                'feedback': feedback_data,
                'pagination': format_pagination(pagination)
            },
            message=f"Retrieved {len(feedback_data)} feedback entries"
        )
        
    except Exception as e:
        current_app.logger.error(f"Error retrieving performance feedback: {str(e)}")
        return handle_api_error(e)

@api_performance.route('/feedback', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def create_performance_feedback():
    """Create new performance feedback (review-linked or standalone)."""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['to_user_id', 'feedback_type', 'content']
        for field in required_fields:
            if field not in data:
                return api_response(
                    data={}, 
                    message=f"Missing required field: {field}", 
                    status_code=400
                )
        
        # Validate feedback type
        valid_types = [
            # Review feedback
            'peer', 'upward', 'downward', 'self', '360',
            # Standalone feedback
            'followup', 'chiarimento', 'criticita', 'riconoscimento', 'suggestion', 'coaching'
        ]
        
        if data['feedback_type'] not in valid_types:
            return api_response(
                data={}, 
                message=f"Invalid feedback_type. Must be one of: {', '.join(valid_types)}", 
                status_code=400
            )
        
        # Create feedback
        feedback = PerformanceFeedback(
            review_id=data.get('review_id'),  # NULL for standalone
            from_user_id=current_user.id,  # Current user is always the sender
            to_user_id=data['to_user_id'],
            feedback_type=data['feedback_type'],
            title=data.get('title'),
            content=data['content'],
            strengths=data.get('strengths'),
            improvements=data.get('improvements'),
            rating=data.get('rating'),
            priority=data.get('priority', 'normal'),
            requires_response=data.get('requires_response', False),
            status='submitted'  # Auto-submit when created
        )
        
        # Set submitted_date when status is submitted
        feedback.submitted_date = datetime.utcnow()
        
        db.session.add(feedback)
        db.session.commit()
        
        return api_response(
            data={'feedback_id': feedback.id},
            message="Performance feedback created successfully",
            status_code=201
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error creating performance feedback: {str(e)}")
        return handle_api_error(e)

@api_performance.route('/feedback/<int:feedback_id>', methods=['PUT'])
@login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def update_performance_feedback(feedback_id):
    """Update performance feedback status or acknowledgment."""
    try:
        feedback = PerformanceFeedback.query.get_or_404(feedback_id)
        data = request.get_json()
        
        # Check permissions - users can only update feedback they are involved in
        if current_user.role not in ['admin', 'hr']:
            if current_user.id not in [feedback.from_user_id, feedback.to_user_id]:
                return api_response(
                    data={}, 
                    message="Permission denied", 
                    status_code=403
                )
        
        # Update status
        if 'status' in data:
            feedback.status = data['status']
            
            # Set appropriate dates based on status
            if data['status'] == 'acknowledged' and not feedback.acknowledged_date:
                feedback.acknowledged_date = datetime.utcnow()
            elif data['status'] == 'resolved' and not feedback.resolved_date:
                feedback.resolved_date = datetime.utcnow()
        
        feedback.updated_at = datetime.utcnow()
        db.session.commit()
        
        return api_response(
            data={'feedback_id': feedback.id},
            message="Performance feedback updated successfully"
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating performance feedback: {str(e)}")
        return handle_api_error(e)

@api_performance.route('/templates', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_performance_templates():
    """Get list of performance review templates."""
    try:
        templates = PerformanceTemplate.query.filter(
            PerformanceTemplate.is_active == True
        ).order_by(PerformanceTemplate.name).all()
        
        templates_data = []
        for template in templates:
            templates_data.append({
                'id': template.id,
                'name': template.name,
                'description': template.description,
                'template_type': template.template_type,
                'is_default': template.is_default,
                'is_active': template.is_active,
                'fields_config': template.fields_config,
                'created_at': template.created_at.isoformat()
            })
        
        return api_response(
            data={'templates': templates_data},
            message=f"Retrieved {len(templates_data)} performance templates"
        )
        
    except Exception as e:
        current_app.logger.error(f"Error retrieving performance templates: {str(e)}")
        return handle_api_error(e)

@api_performance.route('/templates', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def create_performance_template():
    """Create a new performance template."""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['name', 'template_type']
        for field in required_fields:
            if field not in data:
                return api_response(
                    data={}, 
                    message=f"Missing required field: {field}", 
                    status_code=400
                )
        
        # If setting as default, unset existing default
        if data.get('is_default', False):
            PerformanceTemplate.query.filter_by(is_default=True).update({'is_default': False})
        
        # Create template
        template = PerformanceTemplate(
            name=data['name'],
            description=data.get('description'),
            template_type=data['template_type'],
            job_level=data.get('job_level'),
            department=data.get('department'),
            evaluation_criteria=data.get('evaluation_criteria'),
            rating_scale=data.get('rating_scale'),
            fields_config=data.get('fields_config'),
            target_role=data.get('target_role'),
            is_default=data.get('is_default', False),
            is_active=data.get('is_active', True),
            created_by=current_user.id
        )
        
        db.session.add(template)
        db.session.commit()
        
        return api_response(
            data={'template_id': template.id},
            message="Performance template created successfully",
            status_code=201
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error creating performance template: {str(e)}")
        return handle_api_error(e)

@api_performance.route('/templates/<int:template_id>', methods=['PUT'])
@login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def update_performance_template(template_id):
    """Update an existing performance template."""
    try:
        template = PerformanceTemplate.query.get_or_404(template_id)
        data = request.get_json()
        
        # If setting as default, unset existing default
        if data.get('is_default', False) and not template.is_default:
            PerformanceTemplate.query.filter_by(is_default=True).update({'is_default': False})
        
        # Update fields
        if 'name' in data:
            template.name = data['name']
        if 'description' in data:
            template.description = data['description']
        if 'template_type' in data:
            template.template_type = data['template_type']
        if 'job_level' in data:
            template.job_level = data['job_level']
        if 'department' in data:
            template.department = data['department']
        if 'evaluation_criteria' in data:
            template.evaluation_criteria = data['evaluation_criteria']
        if 'rating_scale' in data:
            template.rating_scale = data['rating_scale']
        if 'fields_config' in data:
            template.fields_config = data['fields_config']
        if 'target_role' in data:
            template.target_role = data['target_role']
        if 'is_default' in data:
            template.is_default = data['is_default']
        if 'is_active' in data:
            template.is_active = data['is_active']
        
        template.updated_at = datetime.utcnow()
        db.session.commit()
        
        return api_response(
            data={'template_id': template.id},
            message="Performance template updated successfully"
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating performance template: {str(e)}")
        return handle_api_error(e)

@api_performance.route('/templates/<int:template_id>', methods=['DELETE'])
@login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def delete_performance_template(template_id):
    """Delete a performance template (soft delete by setting is_active=False)."""
    try:
        template = PerformanceTemplate.query.get_or_404(template_id)
        
        # Check if template is being used by any reviews
        reviews_using_template = PerformanceReview.query.filter_by(template_id=template_id).count()
        
        if reviews_using_template > 0:
            # Soft delete - set as inactive instead of deleting
            template.is_active = False
            template.updated_at = datetime.utcnow()
            db.session.commit()
            
            return api_response(
                data={},
                message=f"Template deactivated (used by {reviews_using_template} reviews)"
            )
        else:
            # Hard delete if not used
            db.session.delete(template)
            db.session.commit()
            
            return api_response(
                data={},
                message="Performance template deleted successfully"
            )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error deleting performance template: {str(e)}")
        return handle_api_error(e)

@api_performance.route('/dashboard', methods=['GET'])
@login_required
def get_performance_dashboard():
    """
    Ottiene i dati della dashboard performance.
    """
    try:
        # Parametri di query
        year = request.args.get('year', datetime.now().year, type=int)
        employee_id = request.args.get('employee_id', type=int)
        
        # Query base
        reviews_query = PerformanceReview.query
        goals_query = PerformanceGoal.query
        
        # Get query parameters  
        current_year = datetime.now().year
        year_str = request.args.get('year', str(current_year))
        year = current_year
        if year_str and year_str.isdigit():
            year = int(year_str)
            
        employee_id_str = request.args.get('employee_id', '')
        employee_id = None
        if employee_id_str and employee_id_str.isdigit():
            employee_id = int(employee_id_str)
            
        user_role = getattr(current_user, 'role', 'anonymous') if current_user.is_authenticated else 'anonymous'
        
        # Base queries
        
        if current_user.is_authenticated and current_user.role in ['admin', 'hr']:
            # Admin/HR see all data
            reviews_query = PerformanceReview.query
            goals_query = PerformanceGoal.query.join(PerformanceReview)
            
            # Filter by specific employee if provided
            if employee_id:
                reviews_query = reviews_query.filter(PerformanceReview.employee_id == employee_id)
                goals_query = goals_query.filter(PerformanceReview.employee_id == employee_id)
        else:
            # Regular users see only their own data, anonymous users see nothing
            if current_user.is_authenticated:
                reviews_query = PerformanceReview.query.filter(
                    PerformanceReview.employee_id == current_user.id
                )
                goals_query = PerformanceGoal.query.join(PerformanceReview).filter(
                    PerformanceReview.employee_id == current_user.id
                )
            else:
                reviews_query = PerformanceReview.query.filter(False)  # No results
                goals_query = PerformanceGoal.query.filter(False)  # No results
        
        # Filter by year
        if year:
            reviews_query = reviews_query.filter(PerformanceReview.review_year == year)
            goals_query = goals_query.filter(PerformanceReview.review_year == year)
        
        # Reviews stats
        total_reviews = reviews_query.count()
        
        pending_reviews = reviews_query.filter(
            PerformanceReview.status == 'pending'
        ).count()
        completed_reviews = reviews_query.filter(
            PerformanceReview.status == 'completed'
        ).count()
        
        # Goals stats
        total_goals = goals_query.count()
        completed_goals = goals_query.filter(
            PerformanceGoal.status == 'completed'
        ).count()
        in_progress_goals = goals_query.filter(
            PerformanceGoal.status == 'in_progress'
        ).count()
        
        # Average ratings (for current year)
        avg_rating_query = reviews_query.filter(
            PerformanceReview.review_year == current_year,
            PerformanceReview.overall_rating.isnot(None)
        )
        avg_rating = db.session.query(
            func.avg(PerformanceReview.overall_rating)
        ).filter(
            PerformanceReview.id.in_([r.id for r in avg_rating_query.all()])
        ).scalar()
        
        dashboard_data = {
            'reviews': {
                'total': total_reviews,
                'pending': pending_reviews,
                'completed': completed_reviews,
                'completion_rate': round((completed_reviews / total_reviews * 100), 1) if total_reviews > 0 else 0
            },
            'goals': {
                'total': total_goals,
                'completed': completed_goals,
                'in_progress': in_progress_goals,
                'completion_rate': round((completed_goals / total_goals * 100), 1) if total_goals > 0 else 0
            },
            'average_rating': round(float(avg_rating), 1) if avg_rating else None,
            'current_year': current_year
        }
        
        return api_response(
            data=dashboard_data,
            message="Performance dashboard data retrieved successfully"
        )
        
    except Exception as e:
        current_app.logger.error(f"Error retrieving performance dashboard data: {str(e)}")
        return handle_api_error(e)

@api_performance.route('/analytics', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_performance_analytics():
    """Get advanced performance analytics with goal-review correlation."""
    try:
        # Get parameters
        employee_id = request.args.get('employee_id', type=int)
        year = request.args.get('year', type=int, default=datetime.now().year)
        period = request.args.get('period', type=int, default=12)  # months
        
        # Base queries with permissions
        if current_user.role in ['admin', 'hr']:
            # Admin/HR can see all data or filter by employee_id
            reviews_query = PerformanceReview.query
            goals_query = PerformanceGoal.query
            if employee_id:
                reviews_query = reviews_query.filter(PerformanceReview.employee_id == employee_id)
                goals_query = goals_query.filter(PerformanceGoal.employee_id == employee_id)
        else:
            # Regular users see only their own data
            reviews_query = PerformanceReview.query.filter(
                PerformanceReview.employee_id == current_user.id
            )
            goals_query = PerformanceGoal.query.filter(
                PerformanceGoal.employee_id == current_user.id
            )
        
        # Time range filter
        start_date = datetime(year, 1, 1)
        end_date = datetime(year, 12, 31)
        
        reviews_query = reviews_query.filter(
            PerformanceReview.review_year == year
        )
        goals_query = goals_query.filter(
            PerformanceGoal.year == year
        ).filter(
            PerformanceGoal.year.isnot(None)
        )
        
        # Performance Trends (monthly ratings)
        performance_trends = []
        for month in range(1, 13):
            # Calculate start of current month
            start_of_month = datetime(year, month, 1)
            
            # Calculate start of next month (handle December -> January)
            if month == 12:
                start_of_next_month = datetime(year + 1, 1, 1)
            else:
                start_of_next_month = datetime(year, month + 1, 1)
            
            month_reviews = reviews_query.filter(
                PerformanceReview.created_at >= start_of_month,
                PerformanceReview.created_at < start_of_next_month,
                PerformanceReview.overall_rating.isnot(None)
            ).all()
            
            avg_rating = sum(r.overall_rating for r in month_reviews) / len(month_reviews) if month_reviews else 0
            performance_trends.append({
                'month': month,
                'month_name': datetime(year, month, 1).strftime('%b'),
                'average_rating': round(avg_rating, 2),
                'reviews_count': len(month_reviews)
            })
        
        # Goal Completion Analysis
        all_goals = goals_query.all()
        goals_by_category = {}
        for goal in all_goals:
            category = goal.category or 'other'
            if category not in goals_by_category:
                goals_by_category[category] = {
                    'total': 0,
                    'completed': 0,
                    'in_progress': 0,
                    'active': 0
                }
            goals_by_category[category]['total'] += 1
            if goal.status == 'completed':
                goals_by_category[category]['completed'] += 1
            elif goal.status == 'in_progress':
                goals_by_category[category]['in_progress'] += 1
            elif goal.status == 'active':
                goals_by_category[category]['active'] += 1
        
        # Calculate completion rates by category
        goal_completion_by_category = []
        for category, stats in goals_by_category.items():
            completion_rate = (stats['completed'] / stats['total'] * 100) if stats['total'] > 0 else 0
            goal_completion_by_category.append({
                'category': category,
                'total': stats['total'],
                'completed': stats['completed'],
                'in_progress': stats['in_progress'],
                'active': stats['active'],
                'completion_rate': round(completion_rate, 1)
            })
        
        # Review-Goal Correlation
        reviews_with_goals = []
        all_reviews = reviews_query.filter(PerformanceReview.overall_rating.isnot(None)).all()
        
        for review in all_reviews:
            # Find goals for this employee in the review period
            employee_goals = goals_query.filter(
                PerformanceGoal.employee_id == review.employee_id,
                PerformanceGoal.year == review.review_year,
                PerformanceGoal.year.isnot(None)
            ).all()
            
            total_goals = len(employee_goals)
            completed_goals = sum(1 for g in employee_goals if g.status == 'completed')
            goal_completion_rate = (completed_goals / total_goals * 100) if total_goals > 0 else 0
            
            reviews_with_goals.append({
                'review_id': review.id,
                'overall_rating': review.overall_rating,
                'goal_completion_rate': goal_completion_rate,
                'total_goals': total_goals,
                'completed_goals': completed_goals
            })
        
        # Calculate correlation coefficient (simplified)
        correlation = 0
        if len(reviews_with_goals) > 1:
            try:
                ratings = [r['overall_rating'] for r in reviews_with_goals if r['overall_rating'] is not None]
                goal_rates = [r['goal_completion_rate'] for r in reviews_with_goals if r['goal_completion_rate'] is not None]
                
                # Ensure we have enough valid data points
                if len(ratings) >= 2 and len(goal_rates) >= 2 and len(ratings) == len(goal_rates):
                    # Simple correlation calculation with safety checks
                    n = len(ratings)
                    sum_ratings = sum(ratings)
                    sum_goals = sum(goal_rates)
                    sum_rating_goal = sum(r * g for r, g in zip(ratings, goal_rates))
                    sum_ratings_sq = sum(r * r for r in ratings)
                    sum_goals_sq = sum(g * g for g in goal_rates)
                    
                    numerator = n * sum_rating_goal - sum_ratings * sum_goals
                    variance_ratings = n * sum_ratings_sq - sum_ratings * sum_ratings
                    variance_goals = n * sum_goals_sq - sum_goals * sum_goals
                    
                    # Check for valid variance (avoid division by zero)
                    if variance_ratings > 0 and variance_goals > 0:
                        denominator = (variance_ratings * variance_goals) ** 0.5
                        correlation = numerator / denominator
                    else:
                        correlation = 0
                else:
                    correlation = 0
            except (ValueError, ZeroDivisionError, TypeError) as e:
                current_app.logger.warning(f"Error calculating correlation: {str(e)}")
                correlation = 0
        
        # Team Performance Distribution (admin/hr only)
        team_performance = []
        if current_user.role in ['admin', 'hr'] and not employee_id:
            # Get performance distribution across employees
            employee_reviews = db.session.query(
                PerformanceReview.employee_id,
                func.avg(PerformanceReview.overall_rating).label('avg_rating'),
                func.count(PerformanceReview.id).label('review_count')
            ).filter(
                PerformanceReview.review_year == year,
                PerformanceReview.overall_rating.isnot(None)
            ).group_by(PerformanceReview.employee_id).all()
            
            rating_distribution = {'1-2': 0, '2-3': 0, '3-4': 0, '4-5': 0}
            for emp_review in employee_reviews:
                avg_rating = float(emp_review.avg_rating)
                if avg_rating < 2:
                    rating_distribution['1-2'] += 1
                elif avg_rating < 3:
                    rating_distribution['2-3'] += 1
                elif avg_rating < 4:
                    rating_distribution['3-4'] += 1
                else:
                    rating_distribution['4-5'] += 1
            
            team_performance = [
                {'range': range_name, 'count': count}
                for range_name, count in rating_distribution.items()
            ]
        
        # Analytics summary
        analytics_data = {
            'performance_trends': performance_trends,
            'goal_completion_by_category': goal_completion_by_category,
            'review_goal_correlation': {
                'coefficient': round(correlation, 3),
                'strength': 'strong' if abs(correlation) > 0.7 else 'moderate' if abs(correlation) > 0.3 else 'weak',
                'data_points': len(reviews_with_goals)
            },
            'team_performance_distribution': team_performance,
            'summary_stats': {
                'total_reviews': len(all_reviews),
                'total_goals': len(all_goals),
                'avg_goals_per_review': round(len(all_goals) / len(all_reviews), 1) if all_reviews else 0,
                'overall_goal_completion_rate': round(
                    sum(1 for g in all_goals if g.status == 'completed') / len(all_goals) * 100, 1
                ) if all_goals else 0
            },
            'period': {
                'year': year,
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat()
            }
        }
        
        return api_response(
            data=analytics_data,
            message="Performance analytics retrieved successfully"
        )
        
    except Exception as e:
        current_app.logger.error(f"Error retrieving performance analytics: {str(e)}")
        return handle_api_error(e)

@api_performance.route('/reviews/<int:review_id>/goals', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_review_linked_goals(review_id):
    """Get goals linked to a specific review."""
    try:
        review = PerformanceReview.query.get_or_404(review_id)
        
        # Check permission
        if current_user.role not in ['admin', 'hr'] and review.employee_id != current_user.id:
            return api_response(data={}, message="Access denied", status_code=403)
        
        # Get linked goals (both directly linked and by employee/year)
        linked_goals = PerformanceGoal.query.filter(
            or_(
                PerformanceGoal.review_id == review_id,
                and_(
                    PerformanceGoal.employee_id == review.employee_id,
                    PerformanceGoal.year == review.review_year,
                    PerformanceGoal.review_id.is_(None)  # Available for linking
                )
            )
        ).all()
        
        goals_data = []
        for goal in linked_goals:
            goals_data.append({
                'id': goal.id,
                'title': goal.title,
                'description': goal.description,
                'category': goal.category,
                'priority': goal.priority,
                'status': goal.status,
                'progress': goal.progress,
                'target_date': goal.target_date.isoformat() if goal.target_date else None,
                'is_linked': goal.review_id == review_id,
                'created_at': goal.created_at.isoformat()
            })
        
        return api_response(
            data={'goals': goals_data},
            message=f"Retrieved {len(goals_data)} goals for review"
        )
        
    except Exception as e:
        current_app.logger.error(f"Error retrieving review goals: {str(e)}")
        return handle_api_error(e)

@api_performance.route('/reviews/<int:review_id>/link-goals', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def link_goals_to_review(review_id):
    """Link multiple goals to a review."""
    try:
        review = PerformanceReview.query.get_or_404(review_id)
        data = request.get_json()
        
        # Check permission
        if current_user.role not in ['admin', 'hr'] and review.employee_id != current_user.id:
            return api_response(data={}, message="Access denied", status_code=403)
        
        goal_ids = data.get('goal_ids', [])
        if not goal_ids:
            return api_response(data={}, message="No goals specified", status_code=400)
        
        # Validate and link goals
        linked_count = 0
        for goal_id in goal_ids:
            goal = PerformanceGoal.query.get(goal_id)
            if goal and goal.employee_id == review.employee_id:
                goal.review_id = review_id
                goal.updated_at = datetime.utcnow()
                linked_count += 1
        
        db.session.commit()
        
        return api_response(
            data={'linked_count': linked_count},
            message=f"Linked {linked_count} goals to review successfully"
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error linking goals to review: {str(e)}")
        return handle_api_error(e)

@api_performance.route('/goals/<int:goal_id>/review-link', methods=['PUT'])
@login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def update_goal_review_link(goal_id):
    """Update goal's review link."""
    try:
        goal = PerformanceGoal.query.get_or_404(goal_id)
        data = request.get_json()
        
        # Check permission
        if current_user.role not in ['admin', 'hr'] and goal.employee_id != current_user.id:
            return api_response(data={}, message="Access denied", status_code=403)
        
        review_id = data.get('review_id')
        
        if review_id:
            # Validate review exists and belongs to same employee
            review = PerformanceReview.query.get(review_id)
            if not review:
                return api_response(data={}, message="Review not found", status_code=404)
            if review.employee_id != goal.employee_id:
                return api_response(data={}, message="Review and goal must belong to same employee", status_code=400)
        
        goal.review_id = review_id
        goal.updated_at = datetime.utcnow()
        db.session.commit()
        
        action = "linked to review" if review_id else "unlinked from review"
        return api_response(
            data={'goal_id': goal.id, 'review_id': review_id},
            message=f"Goal {action} successfully"
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating goal review link: {str(e)}")
        return handle_api_error(e)

@api_performance.route('/reviews/<int:review_id>/evaluate-goals', methods=['PUT'])
@login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def evaluate_review_goals(review_id):
    """
    Batch evaluation of goals linked to a performance review.
    
    Request Body:
    {
        "evaluations": [
            {
                "goal_id": 1,
                "achievement_rating": 4.5,
                "manager_assessment": "Excellent achievement..."
            }
        ]
    }
    """
    try:
        data = request.get_json()
        if not data or 'evaluations' not in data:
            return api_response(False, message="Missing evaluations data", status_code=400)
        
        evaluations = data.get('evaluations', [])
        if not evaluations:
            return api_response(False, message="No evaluations provided", status_code=400)
        
        # Get the review
        review = PerformanceReview.query.get_or_404(review_id)
        
        # Check permissions - user must be reviewer or have admin/hr role
        if (review.reviewer_id != current_user.id and 
            current_user.role not in ['admin', 'hr']):
            return api_response(False, message="Unauthorized to evaluate this review", status_code=403)
        
        updated_goals = []
        errors = []
        
        for evaluation in evaluations:
            goal_id = evaluation.get('goal_id')
            achievement_rating = evaluation.get('achievement_rating')
            manager_assessment = evaluation.get('manager_assessment', '')
            
            if not goal_id:
                errors.append("Missing goal_id in evaluation")
                continue
                
            # Get the goal
            goal = PerformanceGoal.query.get(goal_id)
            if not goal:
                errors.append(f"Goal {goal_id} not found")
                continue
                
            # Verify goal is linked to this review
            if goal.review_id != review_id:
                errors.append(f"Goal {goal_id} is not linked to review {review_id}")
                continue
                
            # Validate achievement_rating
            if achievement_rating is not None:
                if not isinstance(achievement_rating, (int, float)) or achievement_rating < 1 or achievement_rating > 5:
                    errors.append(f"Invalid achievement_rating for goal {goal_id}. Must be between 1 and 5")
                    continue
            
            # Update the goal
            if achievement_rating is not None:
                goal.achievement_rating = achievement_rating
            if manager_assessment:
                goal.manager_assessment = manager_assessment
            
            goal.updated_at = datetime.utcnow()
            updated_goals.append({
                'goal_id': goal.id,
                'title': goal.title,
                'achievement_rating': goal.achievement_rating,
                'manager_assessment': goal.manager_assessment
            })
        
        if errors:
            db.session.rollback()
            return api_response(False, 
                              message="Validation errors occurred", 
                              data={'errors': errors}, 
                              status_code=400)
        
        # Update review goals achievement rating (average of all goals)
        linked_goals = PerformanceGoal.query.filter_by(review_id=review_id).all()
        evaluated_goals = [g for g in linked_goals if g.achievement_rating is not None]
        
        if evaluated_goals:
            avg_rating = sum(g.achievement_rating for g in evaluated_goals) / len(evaluated_goals)
            review.goals_achievement_rating = round(avg_rating, 2)
            review.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return api_response(True,
                          data={
                              'updated_goals': updated_goals,
                              'total_updated': len(updated_goals),
                              'review_goals_rating': review.goals_achievement_rating,
                              'evaluated_count': len(evaluated_goals),
                              'total_goals': len(linked_goals)
                          },
                          message=f"Successfully evaluated {len(updated_goals)} goals")
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error evaluating review goals: {str(e)}")
        return handle_api_error(e)

# Registra il blueprint
def register_performance_routes(app):
    """Registra le route performance nell'app Flask"""
    app.register_blueprint(api_performance, url_prefix='/api/performance') 