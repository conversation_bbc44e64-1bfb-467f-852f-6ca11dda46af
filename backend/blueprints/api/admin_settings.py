"""
API endpoints for admin settings management.
Provides REST API for company invoicing settings and integrations.
"""

from flask import Blueprint, request, jsonify
from flask_login import current_user
from datetime import datetime
import json

from extensions import db
from models import IntegrationSettings, CompanyInvoicingSettings
from utils.api_utils import (
    api_response, api_login_required, api_permission_required, handle_api_error
)
from utils.permissions import PERMISSION_ADMIN
from utils.encryption import encrypt_data, decrypt_data
from services.fattureincloud_service import FattureInCloudService

# Create blueprint
api_admin_settings = Blueprint('api_admin_settings', __name__)

# ============================================================================
# COMPANY INVOICING SETTINGS
# ============================================================================

@api_admin_settings.route('/invoicing', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_ADMIN)
def get_invoicing_settings():
    """
    Get company invoicing settings
    """
    try:
        settings = CompanyInvoicingSettings.query.filter_by(is_active=True).first()
        
        if not settings:
            # Create default settings if they don't exist
            settings = CompanyInvoicingSettings(
                company_name="",
                invoice_prefix="PRE",
                current_year=datetime.now().year,
                last_number=0
            )
            db.session.add(settings)
            db.session.commit()
        
        data = {
            'id': settings.id,
            'company_name': settings.company_name,
            'vat_number': settings.vat_number,
            'fiscal_code': settings.fiscal_code,
            'address': settings.address,
            'phone': settings.phone,
            'email': settings.email,
            'pec': settings.pec,
            'default_vat_rate': float(settings.default_vat_rate),
            'default_retention_rate': float(settings.default_retention_rate),
            'default_payment_terms': settings.default_payment_terms,
            'invoice_prefix': settings.invoice_prefix,
            'current_year': settings.current_year,
            'last_number': settings.last_number,
            'tax_regime': settings.tax_regime,
            'is_active': settings.is_active,
            'updated_at': settings.updated_at.isoformat() if settings.updated_at else None
        }
        
        return api_response(
            data=data,
            message="Company invoicing settings retrieved successfully"
        )
        
    except Exception as e:
        return handle_api_error(e)

@api_admin_settings.route('/invoicing', methods=['PUT'])
@api_login_required
@api_permission_required(PERMISSION_ADMIN)
def update_invoicing_settings():
    """
    Update company invoicing settings
    """
    try:
        data = request.get_json()
        
        settings = CompanyInvoicingSettings.query.filter_by(is_active=True).first()
        if not settings:
            settings = CompanyInvoicingSettings()
            db.session.add(settings)
        
        # Update fields
        if 'company_name' in data:
            settings.company_name = data['company_name']
        if 'vat_number' in data:
            settings.vat_number = data['vat_number']
        if 'fiscal_code' in data:
            settings.fiscal_code = data['fiscal_code']
        if 'address' in data:
            settings.address = data['address']
        if 'phone' in data:
            settings.phone = data['phone']
        if 'email' in data:
            settings.email = data['email']
        if 'pec' in data:
            settings.pec = data['pec']
        if 'default_vat_rate' in data:
            settings.default_vat_rate = float(data['default_vat_rate'])
        if 'default_retention_rate' in data:
            settings.default_retention_rate = float(data['default_retention_rate'])
        if 'default_payment_terms' in data:
            settings.default_payment_terms = int(data['default_payment_terms'])
        if 'invoice_prefix' in data:
            settings.invoice_prefix = data['invoice_prefix']
        if 'tax_regime' in data:
            settings.tax_regime = data['tax_regime']
        
        settings.updated_at = datetime.utcnow()
        db.session.commit()
        
        return api_response(
            data={'id': settings.id, 'updated_at': settings.updated_at.isoformat()},
            message="Company invoicing settings updated successfully"
        )
        
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

# ============================================================================
# INTEGRATION SETTINGS - FATTURE IN CLOUD
# ============================================================================

@api_admin_settings.route('/integration/fattureincloud', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_ADMIN)
def get_fattureincloud_settings():
    """
    Get FattureInCloud integration settings
    """
    try:
        integration = IntegrationSettings.query.filter_by(
            provider='fattureincloud'
        ).first()
        
        if not integration:
            data = {
                'configured': False,
                'is_active': False,
                'last_sync_date': None,
                'last_error': None
            }
        else:
            # Check if API key is configured (but don't return it)
            has_api_key = bool(integration.api_key_encrypted)
            
            data = {
                'id': integration.id,
                'configured': has_api_key and bool(integration.company_id),
                'has_api_key': has_api_key,
                'company_id': integration.company_id,
                'is_active': integration.is_active,
                'last_sync_date': integration.last_sync_date.isoformat() if integration.last_sync_date else None,
                'last_error': integration.last_error,
                'settings': integration.settings_json or {},
                'updated_at': integration.updated_at.isoformat() if integration.updated_at else None
            }
        
        return api_response(
            data=data,
            message="FattureInCloud integration settings retrieved successfully"
        )
        
    except Exception as e:
        return handle_api_error(e)

@api_admin_settings.route('/integration/fattureincloud', methods=['PUT'])
@api_login_required
@api_permission_required(PERMISSION_ADMIN)
def update_fattureincloud_settings():
    """
    Update FattureInCloud integration settings
    """
    try:
        data = request.get_json()
        
        integration = IntegrationSettings.query.filter_by(
            provider='fattureincloud'
        ).first()
        
        if not integration:
            integration = IntegrationSettings(
                provider='fattureincloud',
                created_by=current_user.id
            )
            db.session.add(integration)
        
        # Update fields
        if 'api_key' in data and data['api_key']:
            integration.api_key_encrypted = encrypt_data(data['api_key'])
        
        if 'company_id' in data:
            integration.company_id = data['company_id']
        
        if 'is_active' in data:
            integration.is_active = bool(data['is_active'])
        
        if 'settings' in data:
            integration.settings_json = data['settings']
        
        integration.updated_at = datetime.utcnow()
        integration.updated_by = current_user.id
        
        db.session.commit()
        
        return api_response(
            data={'id': integration.id, 'updated_at': integration.updated_at.isoformat()},
            message="FattureInCloud integration settings updated successfully"
        )
        
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

@api_admin_settings.route('/integration/fattureincloud/test', methods=['POST'])
@api_login_required
@api_permission_required(PERMISSION_ADMIN)
def test_fattureincloud_connection():
    """
    Test FattureInCloud API connection
    """
    try:
        data = request.get_json()
        api_key = data.get('api_key')
        company_id = data.get('company_id')
        
        if not api_key or not company_id:
            return api_response(
                message="API key and Company ID are required for testing",
                status_code=400
            )
        
        # Test connection using the service
        test_result = FattureInCloudService.test_connection_with_credentials(api_key, company_id)
        
        if test_result['success']:
            return api_response(
                data=test_result,
                message="Connection test successful"
            )
        else:
            return api_response(
                data=test_result,
                message="Connection test failed",
                status_code=400
            )
        
    except Exception as e:
        return handle_api_error(e)

@api_admin_settings.route('/integration/providers', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_ADMIN)
def get_available_providers():
    """
    Get list of available integration providers
    """
    try:
        providers = [
            {
                'id': 'fattureincloud',
                'name': 'FattureInCloud',
                'description': 'Integrazione con FattureInCloud per la gestione delle fatture',
                'status': 'available',
                'features': ['invoicing', 'clients', 'products']
            }
        ]
        
        return api_response(
            data={'providers': providers},
            message="Available integration providers retrieved successfully"
        )
        
    except Exception as e:
        return handle_api_error(e)

@api_admin_settings.route('/health', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_ADMIN)
def settings_health_check():
    """
    Health check for admin settings API
    """
    try:
        # Check database connectivity
        db.session.execute('SELECT 1')
        
        # Check if required tables exist
        invoicing_count = CompanyInvoicingSettings.query.count()
        integration_count = IntegrationSettings.query.count()
        
        return api_response(
            data={
                'database': 'connected',
                'invoicing_settings_count': invoicing_count,
                'integration_settings_count': integration_count,
                'timestamp': datetime.utcnow().isoformat()
            },
            message="Admin settings API is healthy"
        )
        
    except Exception as e:
        return handle_api_error(e) 