"""
API Blueprint per la gestione delle pre-fatture.
Sistema di pre-fatturazione italiana con calcoli fiscali.
"""

from flask import Blueprint, request, jsonify
from flask_login import current_user, login_required
from datetime import datetime, date
from decimal import Decimal

from models import PreInvoice, Client
from services.pre_invoicing_service import PreInvoicingService, BillingDashboardService
from utils.api_utils import api_response, handle_api_error
from utils.permissions import user_has_permission
from extensions import db, csrf

api_pre_invoices = Blueprint('api_pre_invoices', __name__)

# Inizializza servizi
pre_invoicing_service = PreInvoicingService()
billing_dashboard_service = BillingDashboardService()


@api_pre_invoices.route('/', methods=['GET'])
@login_required
def get_pre_invoices():
    """Recupera lista pre-fatture con filtri"""
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, 'view_all_invoices'):
            return api_response(False, 'Non hai i permessi per visualizzare le pre-fatture', status_code=403)
        
        # Parametri filtro
        client_id = request.args.get('client_id', type=int)
        status = request.args.get('status')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        search = request.args.get('search')
        
        # Paginazione
        page = request.args.get('page', type=int, default=1)
        per_page = request.args.get('per_page', type=int, default=50)
        
        # Query base
        query = PreInvoice.query
        
        # Applica filtri
        if client_id:
            query = query.filter(PreInvoice.client_id == client_id)
            
        if status:
            query = query.filter(PreInvoice.status == status)
            
        if start_date:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            query = query.filter(PreInvoice.generated_date >= start_date_obj)
            
        if end_date:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(PreInvoice.generated_date <= end_date_obj)
            
        if search:
            search_pattern = f"%{search}%"
            query = query.filter(PreInvoice.pre_invoice_number.ilike(search_pattern))
        
        # Ordina per data generazione (più recenti prima)
        query = query.order_by(PreInvoice.generated_date.desc())
        
        # Applica paginazione
        paginated = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        # Prepara dati
        pre_invoices_data = []
        for pre_invoice in paginated.items:
            pre_invoices_data.append({
                'id': pre_invoice.id,
                'pre_invoice_number': pre_invoice.pre_invoice_number,
                'client': {
                    'id': pre_invoice.client.id,
                    'name': pre_invoice.client.name
                },
                'billing_period_start': pre_invoice.billing_period_start.isoformat(),
                'billing_period_end': pre_invoice.billing_period_end.isoformat(),
                'generated_date': pre_invoice.generated_date.isoformat(),
                'status': pre_invoice.status,
                'display_status': pre_invoice.display_status,
                'total_amount': float(pre_invoice.total_amount),
                'external_status': pre_invoice.external_status,
                'lines_count': len(pre_invoice.lines),
                'created_at': pre_invoice.created_at.isoformat()
            })
        
        return api_response(
            data={
                'pre_invoices': pre_invoices_data,
                'pagination': {
                    'page': paginated.page,
                    'pages': paginated.pages,
                    'per_page': paginated.per_page,
                    'total': paginated.total,
                    'has_next': paginated.has_next,
                    'has_prev': paginated.has_prev
                }
            },
            message=f"Recuperate {len(pre_invoices_data)} pre-fatture"
        )
        
    except Exception as e:
        return handle_api_error(e)


@api_pre_invoices.route('/generate', methods=['POST'])
@csrf.exempt
@login_required
def generate_pre_invoice():
    """Genera pre-fattura da timesheet entries"""
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, 'manage_invoices'):
            return api_response(False, 'Non hai i permessi per generare pre-fatture', status_code=403)
        
        data = request.get_json()
        
        # Validazione campi richiesti
        required_fields = ['client_id', 'billing_period_start', 'billing_period_end']
        for field in required_fields:
            if field not in data:
                return api_response(
                    False,
                    f'Campo {field} richiesto',
                    status_code=400
                )
        
        # Parsing date
        try:
            period_start = datetime.strptime(data['billing_period_start'], '%Y-%m-%d').date()
            period_end = datetime.strptime(data['billing_period_end'], '%Y-%m-%d').date()
        except ValueError:
            return api_response(
                False,
                'Formato date non valido. Utilizzare YYYY-MM-DD',
                status_code=400
            )
        
        # Validazione logica date
        if period_start > period_end:
            return api_response(
                False,
                'La data di inizio periodo non può essere successiva alla data di fine',
                status_code=400
            )
        
        # Parametri opzionali
        project_ids = data.get('project_ids')  # Lista di project IDs da includere
        vat_rate = Decimal(str(data['vat_rate'])) if data.get('vat_rate') else None
        retention_rate = Decimal(str(data['retention_rate'])) if data.get('retention_rate') else None
        notes = data.get('notes')
        
        # Genera pre-fattura
        pre_invoice = pre_invoicing_service.generate_pre_invoice(
            client_id=data['client_id'],
            period_start=period_start,
            period_end=period_end,
            created_by=current_user.id,
            project_ids=project_ids,
            vat_rate=vat_rate,
            retention_rate=retention_rate,
            notes=notes
        )
        
        # Ottieni dettagli completi per la risposta
        pre_invoice_details = pre_invoicing_service.get_pre_invoice_with_details(pre_invoice.id)
        
        return api_response(
            data=pre_invoice_details,
            message=f'Pre-fattura {pre_invoice.pre_invoice_number} generata con successo'
        )
        
    except ValueError as e:
        return api_response(False, str(e), status_code=400)
    except Exception as e:
        return handle_api_error(e)


@api_pre_invoices.route('/preview', methods=['POST'])
@csrf.exempt
@login_required
def preview_pre_invoice():
    """Anteprima pre-fattura senza salvarla nel database"""
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, 'view_all_invoices'):
            return api_response(False, 'Non hai i permessi per vedere anteprime', status_code=403)
        
        data = request.get_json()
        
        # Validazione campi richiesti
        required_fields = ['client_id', 'billing_period_start', 'billing_period_end']
        for field in required_fields:
            if field not in data:
                return api_response(
                    False,
                    f'Campo {field} richiesto',
                    status_code=400
                )
        
        # Parsing date
        try:
            period_start = datetime.strptime(data['billing_period_start'], '%Y-%m-%d').date()
            period_end = datetime.strptime(data['billing_period_end'], '%Y-%m-%d').date()
        except ValueError:
            return api_response(
                False,
                'Formato date non valido. Utilizzare YYYY-MM-DD',
                status_code=400
            )
        
        # Parametri opzionali
        project_ids = data.get('project_ids')
        
        # Calcola riassunto fatturazione
        billing_summary = pre_invoicing_service.calculate_billing_summary(
            client_id=data['client_id'],
            period_start=period_start,
            period_end=period_end,
            project_ids=project_ids
        )
        
        # Aggiungi calcoli fiscali
        subtotal = Decimal(str(billing_summary['total_amount']))
        vat_rate = Decimal(str(data.get('vat_rate', 22.0)))
        retention_rate = Decimal(str(data.get('retention_rate', 20.0)))
        
        vat_amount = subtotal * (vat_rate / 100)
        retention_amount = subtotal * (retention_rate / 100)
        total_amount = subtotal + vat_amount - retention_amount
        
        billing_summary.update({
            'fiscal_calculations': {
                'subtotal': float(subtotal),
                'vat_rate': float(vat_rate),
                'vat_amount': float(vat_amount),
                'retention_rate': float(retention_rate),
                'retention_amount': float(retention_amount),
                'total_amount': float(total_amount)
            }
        })
        
        return api_response(
            data=billing_summary,
            message="Anteprima pre-fattura generata con successo"
        )
        
    except ValueError as e:
        return api_response(False, str(e), status_code=400)
    except Exception as e:
        return handle_api_error(e)


@api_pre_invoices.route('/<int:pre_invoice_id>', methods=['GET'])
@login_required
def get_pre_invoice(pre_invoice_id):
    """Recupera dettaglio singola pre-fattura"""
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, 'view_all_invoices'):
            return api_response(False, 'Non hai i permessi per visualizzare le pre-fatture', status_code=403)
        
        pre_invoice_details = pre_invoicing_service.get_pre_invoice_with_details(pre_invoice_id)
        
        if not pre_invoice_details:
            return api_response(
                False,
                f'Pre-fattura con ID {pre_invoice_id} non trovata',
                status_code=404
            )
        
        return api_response(
            data=pre_invoice_details,
            message="Dettaglio pre-fattura recuperato con successo"
        )
        
    except Exception as e:
        return handle_api_error(e)


@api_pre_invoices.route('/<int:pre_invoice_id>/status', methods=['PUT'])
@csrf.exempt
@login_required
def update_pre_invoice_status(pre_invoice_id):
    """Aggiorna lo stato di una pre-fattura"""
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, 'manage_invoices'):
            return api_response(False, 'Non hai i permessi per modificare pre-fatture', status_code=403)
        
        data = request.get_json()
        
        if 'status' not in data:
            return api_response(
                False,
                'Campo status richiesto',
                status_code=400
            )
        
        pre_invoice = pre_invoicing_service.update_pre_invoice_status(
            pre_invoice_id, data['status']
        )
        
        return api_response(
            data={
                'id': pre_invoice.id,
                'status': pre_invoice.status,
                'display_status': pre_invoice.display_status,
                'updated_at': pre_invoice.updated_at.isoformat()
            },
            message=f'Stato pre-fattura aggiornato a: {pre_invoice.display_status}'
        )
        
    except ValueError as e:
        return api_response(False, str(e), status_code=400)
    except Exception as e:
        return handle_api_error(e)


@api_pre_invoices.route('/<int:pre_invoice_id>', methods=['DELETE'])
@csrf.exempt
@login_required
def delete_pre_invoice(pre_invoice_id):
    """Elimina una pre-fattura"""
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, 'manage_invoices'):
            return api_response(False, 'Non hai i permessi per eliminare pre-fatture', status_code=403)
        
        # Ottieni dettagli prima dell'eliminazione
        pre_invoice = PreInvoice.query.get(pre_invoice_id)
        if not pre_invoice:
            return api_response(
                False,
                f'Pre-fattura con ID {pre_invoice_id} non trovata',
                status_code=404
            )
        
        pre_invoice_number = pre_invoice.pre_invoice_number
        
        # Elimina pre-fattura
        pre_invoicing_service.delete_pre_invoice(pre_invoice_id)
        
        return api_response(
            message=f'Pre-fattura {pre_invoice_number} eliminata con successo'
        )
        
    except ValueError as e:
        return api_response(False, str(e), status_code=400)
    except Exception as e:
        return handle_api_error(e)


# Dashboard Endpoints

@api_pre_invoices.route('/dashboard/stats', methods=['GET'])
@login_required
def get_billing_dashboard_stats():
    """Statistiche fatturazione per dashboard"""
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, 'view_all_invoices'):
            return api_response(False, 'Non hai i permessi per visualizzare statistiche fatturazione', status_code=403)
        
        stats = billing_dashboard_service.get_billing_dashboard_stats()
        
        return api_response(
            data=stats,
            message="Statistiche dashboard recuperate con successo"
        )
        
    except Exception as e:
        return handle_api_error(e)


@api_pre_invoices.route('/clients/<int:client_id>/billing-summary', methods=['GET'])
@login_required
def get_client_billing_summary(client_id):
    """Riassunto fatturazione per singolo cliente"""
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, 'view_all_invoices'):
            return api_response(False, 'Non hai i permessi per visualizzare dati fatturazione', status_code=403)
        
        summary = billing_dashboard_service.get_client_billing_summary(client_id)
        
        return api_response(
            data=summary,
            message=f"Riassunto fatturazione cliente recuperato con successo"
        )
        
    except ValueError as e:
        return api_response(False, str(e), status_code=404)
    except Exception as e:
        return handle_api_error(e)


@api_pre_invoices.route('/clients/<int:client_id>/billable-hours', methods=['GET'])
@login_required
def get_client_billable_hours(client_id):
    """Ore fatturabili per un cliente in un periodo"""
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, 'view_all_invoices'):
            return api_response(False, 'Non hai i permessi per visualizzare ore fatturabili', status_code=403)
        
        # Parametri periodo
        period_start = request.args.get('period_start')
        period_end = request.args.get('period_end')
        project_ids = request.args.getlist('project_ids', type=int)
        
        if not period_start or not period_end:
            return api_response(
                False,
                'Parametri period_start e period_end richiesti',
                status_code=400
            )
        
        try:
            start_date = datetime.strptime(period_start, '%Y-%m-%d').date()
            end_date = datetime.strptime(period_end, '%Y-%m-%d').date()
        except ValueError:
            return api_response(
                False,
                'Formato date non valido. Utilizzare YYYY-MM-DD',
                status_code=400
            )
        
        # Calcola riassunto
        billing_summary = pre_invoicing_service.calculate_billing_summary(
            client_id=client_id,
            period_start=start_date,
            period_end=end_date,
            project_ids=project_ids if project_ids else None
        )
        
        return api_response(
            data=billing_summary,
            message="Ore fatturabili recuperate con successo"
        )
        
    except ValueError as e:
        return api_response(False, str(e), status_code=400)
    except Exception as e:
        return handle_api_error(e)