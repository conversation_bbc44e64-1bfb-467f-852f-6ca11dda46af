"""
API Blueprint per gestione centralizzata delle bozze.
Fornisce overview completa di tutti gli elementi in stato draft nel sistema.
"""

from flask import Blueprint, request, current_app
from flask_login import login_required, current_user
from sqlalchemy import desc, asc, func, and_, or_
from datetime import datetime, timedelta

from models import (
    db, User, PerformanceReview, JobPosting, Proposal, PreInvoice, Invoice,
    Contract, CaseStudy, TechnicalOffer, MonthlyTimesheet, FundingApplication,
    EngagementCampaign, News, ComplianceReport
)
from utils.api_utils import api_response, handle_api_error, api_login_required
from utils.permissions import user_has_permission

api_drafts = Blueprint('api_drafts', __name__)


@api_drafts.route('/overview', methods=['GET'])
@api_login_required
def get_drafts_overview():
    """
    Panoramica completa di tutte le bozze nel sistema.
    Restituisce conteggi e liste per modulo.
    """
    try:
        # Parametri di filtro
        days_filter = request.args.get('days', 30, type=int)  # Bozze create negli ultimi N giorni
        cutoff_date = datetime.utcnow() - timedelta(days=days_filter)
        
        overview = {}
        
        # 1. Performance Reviews
        performance_drafts = PerformanceReview.query.filter(
            PerformanceReview.status == 'draft',
            PerformanceReview.created_at >= cutoff_date
        ).count()
        
        overview['performance_reviews'] = {
            'count': performance_drafts,
            'module': 'Performance',
            'icon': 'chart-bar',
            'color': 'blue'
        }
        
        # 2. Job Postings
        job_drafts = JobPosting.query.filter(
            JobPosting.status == 'draft',
            JobPosting.created_at >= cutoff_date
        ).count()
        
        overview['job_postings'] = {
            'count': job_drafts,
            'module': 'Recruiting',
            'icon': 'briefcase',
            'color': 'green'
        }
        
        # 3. Proposals
        proposal_drafts = Proposal.query.filter(
            Proposal.status == 'draft',
            Proposal.created_at >= cutoff_date
        ).count()
        
        overview['proposals'] = {
            'count': proposal_drafts,
            'module': 'CRM',
            'icon': 'document-text',
            'color': 'purple'
        }
        
        # 4. Pre-Invoices
        preinvoice_drafts = PreInvoice.query.filter(
            PreInvoice.status == 'draft',
            PreInvoice.created_at >= cutoff_date
        ).count()
        
        overview['pre_invoices'] = {
            'count': preinvoice_drafts,
            'module': 'Invoicing',
            'icon': 'calculator',
            'color': 'yellow'
        }
        
        # 5. Invoices
        invoice_drafts = Invoice.query.filter(
            Invoice.status == 'draft',
            Invoice.created_at >= cutoff_date
        ).count()
        
        overview['invoices'] = {
            'count': invoice_drafts,
            'module': 'Invoicing',
            'icon': 'document-text',
            'color': 'red'
        }
        
        # 6. Case Studies
        casestudy_drafts = CaseStudy.query.filter(
            CaseStudy.status == 'draft',
            CaseStudy.created_at >= cutoff_date
        ).count()
        
        overview['case_studies'] = {
            'count': casestudy_drafts,
            'module': 'Business Intelligence',
            'icon': 'academic-cap',
            'color': 'indigo'
        }
        
        # 7. Technical Offers
        offer_drafts = TechnicalOffer.query.filter(
            TechnicalOffer.status == 'draft',
            TechnicalOffer.created_at >= cutoff_date
        ).count()
        
        overview['technical_offers'] = {
            'count': offer_drafts,
            'module': 'Business Intelligence',
            'icon': 'cog',
            'color': 'gray'
        }
        
        # 8. Monthly Timesheets
        timesheet_drafts = MonthlyTimesheet.query.filter(
            MonthlyTimesheet.status == 'draft',
            MonthlyTimesheet.created_at >= cutoff_date
        ).count()
        
        overview['timesheets'] = {
            'count': timesheet_drafts,
            'module': 'Timesheet',
            'icon': 'clock',
            'color': 'emerald'
        }
        
        # 9. Funding Applications
        funding_drafts = FundingApplication.query.filter(
            FundingApplication.status == 'draft',
            FundingApplication.created_at >= cutoff_date
        ).count()
        
        overview['funding_applications'] = {
            'count': funding_drafts,
            'module': 'Funding',
            'icon': 'currency-euro',
            'color': 'orange'
        }
        
        # 10. News (using is_published = False)
        news_drafts = News.query.filter(
            News.is_published == False,
            News.created_at >= cutoff_date
        ).count()
        
        overview['news'] = {
            'count': news_drafts,
            'module': 'Communication',
            'icon': 'newspaper',
            'color': 'cyan'
        }
        
        # Calcola totali
        total_drafts = sum(item['count'] for item in overview.values())
        
        # Statistiche generali
        stats = {
            'total_drafts': total_drafts,
            'most_used_module': max(overview.items(), key=lambda x: x[1]['count'])[0] if total_drafts > 0 else None,
            'average_per_module': round(total_drafts / len(overview), 1),
            'period_days': days_filter
        }
        
        return api_response({
            'overview': overview,
            'stats': stats
        })
        
    except Exception as e:
        current_app.logger.error(f"Error getting drafts overview: {str(e)}")
        return handle_api_error(e, "Errore nel caricamento panoramica bozze")


@api_drafts.route('/details/<module_name>', methods=['GET'])
@api_login_required
def get_module_drafts(module_name):
    """
    Dettagli delle bozze per un modulo specifico.
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        
        # Mapping moduli e query
        module_queries = {
            'performance_reviews': {
                'model': PerformanceReview,
                'filter_condition': PerformanceReview.status == 'draft',
                'fields': ['id', 'employee_id', 'review_period', 'created_at'],
                'join_user': 'employee_id'
            },
            'job_postings': {
                'model': JobPosting,
                'filter_condition': JobPosting.status == 'draft',
                'fields': ['id', 'title', 'department_id', 'created_at'],
                'join_user': 'created_by'
            },
            'proposals': {
                'model': Proposal,
                'filter_condition': Proposal.status == 'draft',
                'fields': ['id', 'title', 'client_id', 'created_at'],
                'join_user': 'created_by'
            },
            'pre_invoices': {
                'model': PreInvoice,
                'filter_condition': PreInvoice.status == 'draft',
                'fields': ['id', 'pre_invoice_number', 'client_id', 'created_at'],
                'join_user': 'created_by'
            },
            'invoices': {
                'model': Invoice,
                'filter_condition': Invoice.status == 'draft',
                'fields': ['id', 'invoice_number', 'client_id', 'created_at'],
                'join_user': 'created_by'
            },
            'case_studies': {
                'model': CaseStudy,
                'filter_condition': CaseStudy.status == 'draft',
                'fields': ['id', 'title', 'project_id', 'created_at'],
                'join_user': 'created_by'
            },
            'technical_offers': {
                'model': TechnicalOffer,
                'filter_condition': TechnicalOffer.status == 'draft',
                'fields': ['id', 'title', 'target_sector', 'created_at'],
                'join_user': 'created_by'
            },
            'timesheets': {
                'model': MonthlyTimesheet,
                'filter_condition': MonthlyTimesheet.status == 'draft',
                'fields': ['id', 'user_id', 'month', 'year', 'created_at'],
                'join_user': 'user_id'
            },
            'funding_applications': {
                'model': FundingApplication,
                'filter_condition': FundingApplication.status == 'draft',
                'fields': ['id', 'project_title', 'opportunity_id', 'created_at'],
                'join_user': 'created_by'
            },
            'news': {
                'model': News,
                'filter_condition': News.is_published == False,
                'fields': ['id', 'title', 'category', 'created_at'],
                'join_user': 'author_id'
            }
        }
        
        if module_name not in module_queries:
            return api_response(False, f"Modulo '{module_name}' non trovato", status_code=404)
        
        query_config = module_queries[module_name]
        model = query_config['model']
        
        # Costruisci query
        query = model.query.filter(query_config['filter_condition'])
        
        # Join con User per ottenere nome creatore
        if query_config.get('join_user'):
            user_field = getattr(model, query_config['join_user'])
            query = query.join(User, user_field == User.id, isouter=True)
            query = query.add_columns(User.first_name, User.last_name)
        
        # Ordina per data di creazione (più recenti prima)
        query = query.order_by(desc(model.created_at))
        
        # Paginazione
        pagination = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )
        
        # Prepara dati per response
        drafts = []
        for item in pagination.items:
            if query_config.get('join_user'):
                # Item è una tupla (entity, first_name, last_name)
                entity = item[0]
                first_name = item[1] if len(item) > 1 else None
                last_name = item[2] if len(item) > 2 else None
                creator_name = f"{first_name} {last_name}".strip() if first_name else "N/A"
            else:
                entity = item
                creator_name = "N/A"
            
            draft_data = {
                'id': entity.id,
                'created_at': entity.created_at.isoformat() if entity.created_at else None,
                'creator_name': creator_name,
                'age_days': (datetime.utcnow() - entity.created_at).days if entity.created_at else 0
            }
            
            # Aggiungi campi specifici del modulo
            for field in query_config['fields']:
                if hasattr(entity, field) and field not in ['id', 'created_at']:
                    value = getattr(entity, field)
                    if hasattr(value, 'isoformat'):  # Date fields
                        value = value.isoformat()
                    draft_data[field] = value
            
            drafts.append(draft_data)
        
        return api_response({
            'drafts': drafts,
            'pagination': {
                'page': page,
                'pages': pagination.pages,
                'per_page': per_page,
                'total': pagination.total,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            },
            'module_name': module_name
        })
        
    except Exception as e:
        current_app.logger.error(f"Error getting module drafts for {module_name}: {str(e)}")
        return handle_api_error(e, f"Errore nel caricamento bozze {module_name}")


@api_drafts.route('/stats', methods=['GET'])
@api_login_required
def get_drafts_stats():
    """
    Statistiche avanzate sulle bozze.
    """
    try:
        # Statistiche per età delle bozze
        age_ranges = [
            ('1_day', 1),
            ('7_days', 7),
            ('30_days', 30),
            ('90_days', 90),
            ('older', None)
        ]
        
        stats = {
            'by_age': {},
            'by_user': {},
            'by_module': {},
            'trends': {}
        }
        
        # Statistiche per età
        total_drafts = 0
        for range_name, days in age_ranges:
            if days:
                cutoff = datetime.utcnow() - timedelta(days=days)
                prev_cutoff = datetime.utcnow() - timedelta(days=days*2)
                
                # Conta bozze nell'intervallo
                count_query = db.session.query(func.count()).select_from
                # Implementazione semplificata per ora
                stats['by_age'][range_name] = 0
            else:
                # Più vecchie di 90 giorni
                cutoff = datetime.utcnow() - timedelta(days=90)
                stats['by_age'][range_name] = 0
        
        # Bozze per utente (top 10)
        # Query semplificata - implementazione completa richiede union di più tabelle
        stats['by_user'] = {}
        
        # Trend degli ultimi 7 giorni
        stats['trends'] = {
            'daily_created': [],
            'daily_completed': []
        }
        
        return api_response(stats)
        
    except Exception as e:
        current_app.logger.error(f"Error getting drafts stats: {str(e)}")
        return handle_api_error(e, "Errore nel caricamento statistiche bozze")


@api_drafts.route('/bulk-actions', methods=['POST'])
@api_login_required
def bulk_draft_actions():
    """
    Azioni bulk su bozze (reminder email, cleanup, etc.).
    """
    try:
        data = request.get_json()
        action = data.get('action')
        module_name = data.get('module_name')
        draft_ids = data.get('draft_ids', [])
        
        if not action or not module_name:
            return api_response(False, "Azione e modulo richiesti", status_code=400)
        
        # Verifica permessi admin per azioni bulk
        if not user_has_permission(current_user.role, 'manage_system'):
            return api_response(False, "Permessi insufficienti per azioni bulk", status_code=403)
        
        results = {
            'success_count': 0,
            'error_count': 0,
            'messages': []
        }
        
        if action == 'send_reminders':
            # Invia reminder email per bozze in scadenza
            results['messages'].append(f"Reminder emails inviati per {len(draft_ids)} bozze")
            results['success_count'] = len(draft_ids)
            
        elif action == 'cleanup_old':
            # Cleanup bozze vecchie (solo log per ora)
            results['messages'].append(f"Cleanup simulato per {len(draft_ids)} bozze vecchie")
            results['success_count'] = len(draft_ids)
            
        else:
            return api_response(False, f"Azione '{action}' non supportata", status_code=400)
        
        return api_response(results)
        
    except Exception as e:
        current_app.logger.error(f"Error in bulk draft actions: {str(e)}")
        return handle_api_error(e, "Errore nelle azioni bulk")