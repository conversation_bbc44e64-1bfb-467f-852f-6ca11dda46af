"""
API Blueprint per la gestione delle approvazioni mensili dei timesheet.
Task 3.1 - Timesheet Management System
"""

from flask import Blueprint, request, jsonify
from flask_login import current_user, login_required
from sqlalchemy import and_, extract, func
from datetime import datetime, date
from calendar import monthrange

from models import MonthlyTimesheet, TimesheetEntry, User
from utils.api_utils import api_response, handle_api_error
from utils.permissions import user_has_permission
from extensions import db, csrf
from blueprints.api.notifications import create_notification_for_user, notify_admins

api_monthly_timesheets = Blueprint('api_monthly_timesheets', __name__)


@api_monthly_timesheets.route('/', methods=['GET'])
@login_required
def get_monthly_timesheets():
    """Recupera lista monthly timesheet con filtri"""
    try:
        # Parametri filtro
        user_id = request.args.get('user_id', type=int)
        year = request.args.get('year', type=int)
        month = request.args.get('month', type=int)
        status = request.args.get('status')  # draft, submitted, approved, rejected
        
        # Paginazione
        page = request.args.get('page', type=int, default=1)
        per_page = request.args.get('per_page', type=int, default=50)
        
        # Query base
        query = MonthlyTimesheet.query
        
        # Controllo permessi
        if not user_has_permission(current_user.role, 'view_all_timesheets'):
            # L'utente può vedere solo i propri monthly timesheet
            query = query.filter(MonthlyTimesheet.user_id == current_user.id)
        
        # Applica filtri
        if user_id:
            # Verifica permessi per vedere timesheet di altri utenti
            if not user_has_permission(current_user.role, 'view_all_timesheets') and user_id != current_user.id:
                return api_response(False, 'Non puoi visualizzare timesheet di altri utenti', status_code=403)
            query = query.filter(MonthlyTimesheet.user_id == user_id)
            
        if year:
            query = query.filter(MonthlyTimesheet.year == year)
            
        if month:
            query = query.filter(MonthlyTimesheet.month == month)
            
        if status:
            query = query.filter(MonthlyTimesheet.status == status)
        
        # Ordina per anno/mese (più recenti prima)
        query = query.order_by(MonthlyTimesheet.year.desc(), MonthlyTimesheet.month.desc())
        
        # Applica paginazione
        paginated = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        # Prepara dati
        timesheets_data = []
        for ts in paginated.items:
            timesheets_data.append({
                'id': ts.id,
                'user_id': ts.user_id,
                'user': {
                    'id': ts.user.id,
                    'first_name': ts.user.first_name,
                    'last_name': ts.user.last_name,
                    'full_name': ts.user.full_name
                } if ts.user else None,
                'year': ts.year,
                'month': ts.month,
                'status': ts.status,
                'total_hours': ts.total_hours,
                'billable_hours': ts.billable_hours,
                'submission_date': ts.submission_date.isoformat() if ts.submission_date else None,
                'approval_date': ts.approval_date.isoformat() if ts.approval_date else None,
                'approved_by': ts.approved_by,
                'approver': {
                    'id': ts.approver.id,
                    'first_name': ts.approver.first_name,
                    'last_name': ts.approver.last_name,
                    'full_name': ts.approver.full_name
                } if ts.approver else None,
                'rejection_reason': ts.rejection_reason,
                'created_at': ts.created_at.isoformat(),
                'updated_at': ts.updated_at.isoformat()
            })
        
        return api_response(
            data={
                'timesheets': timesheets_data,
                'pagination': {
                    'page': paginated.page,
                    'pages': paginated.pages,
                    'per_page': paginated.per_page,
                    'total': paginated.total,
                    'has_next': paginated.has_next,
                    'has_prev': paginated.has_prev
                }
            },
            message=f"Recuperati {len(timesheets_data)} monthly timesheet"
        )
        
    except Exception as e:
        return handle_api_error(e)


@api_monthly_timesheets.route('/<int:timesheet_id>', methods=['GET'])
@login_required
def get_monthly_timesheet(timesheet_id):
    """Recupera dettaglio singolo monthly timesheet con entries"""
    try:
        monthly_timesheet = MonthlyTimesheet.query.get_or_404(timesheet_id)
        
        # Controllo permessi
        if not user_has_permission(current_user.role, 'view_all_timesheets'):
            if monthly_timesheet.user_id != current_user.id:
                return api_response(False, 'Non puoi visualizzare questo timesheet', status_code=403)
        
        # Recupera entries del mese
        entries = TimesheetEntry.query.filter(
            and_(
                TimesheetEntry.user_id == monthly_timesheet.user_id,
                extract('year', TimesheetEntry.date) == monthly_timesheet.year,
                extract('month', TimesheetEntry.date) == monthly_timesheet.month
            )
        ).order_by(TimesheetEntry.date.desc()).all()
        
        entries_data = []
        for entry in entries:
            entries_data.append({
                'id': entry.id,
                'project_id': entry.project_id,
                'project': {
                    'id': entry.project.id,
                    'name': entry.project.name
                } if entry.project else None,
                'task_id': entry.task_id,
                'task': {
                    'id': entry.task.id,
                    'title': entry.task.title
                } if entry.task else None,
                'date': entry.date.isoformat(),
                'hours': entry.hours,
                'description': entry.description,
                'billable': entry.billable,
                'billing_rate': entry.billing_rate,
                'billing_status': entry.billing_status,
                'status': entry.status
            })
        
        return api_response(
            data={
                'id': monthly_timesheet.id,
                'user_id': monthly_timesheet.user_id,
                'user': {
                    'id': monthly_timesheet.user.id,
                    'first_name': monthly_timesheet.user.first_name,
                    'last_name': monthly_timesheet.user.last_name,
                    'full_name': monthly_timesheet.user.full_name
                },
                'year': monthly_timesheet.year,
                'month': monthly_timesheet.month,
                'status': monthly_timesheet.status,
                'total_hours': monthly_timesheet.total_hours,
                'billable_hours': monthly_timesheet.billable_hours,
                'submission_date': monthly_timesheet.submission_date.isoformat() if monthly_timesheet.submission_date else None,
                'approval_date': monthly_timesheet.approval_date.isoformat() if monthly_timesheet.approval_date else None,
                'approved_by': monthly_timesheet.approved_by,
                'approver': {
                    'id': monthly_timesheet.approver.id,
                    'first_name': monthly_timesheet.approver.first_name,
                    'last_name': monthly_timesheet.approver.last_name,
                    'full_name': monthly_timesheet.approver.full_name
                } if monthly_timesheet.approver else None,
                'rejection_reason': monthly_timesheet.rejection_reason,
                'entries': entries_data,
                'entries_count': len(entries_data),
                'created_at': monthly_timesheet.created_at.isoformat(),
                'updated_at': monthly_timesheet.updated_at.isoformat()
            },
            message="Dettaglio monthly timesheet recuperato con successo"
        )
        
    except Exception as e:
        return handle_api_error(e)


@api_monthly_timesheets.route('/generate', methods=['POST'])
@csrf.exempt
@login_required
def generate_monthly_timesheet():
    """Genera/recupera monthly timesheet per un mese specifico"""
    try:
        data = request.get_json()
        
        # Validazione campi richiesti
        required_fields = ['year', 'month']
        for field in required_fields:
            if field not in data:
                return api_response(
                    False,
                    f'Campo {field} richiesto',
                    status_code=400
                )
        
        year = data['year']
        month = data['month']
        user_id = data.get('user_id', current_user.id)
        
        # Controllo permessi per generare timesheet di altri utenti
        if user_id != current_user.id and not user_has_permission(current_user.role, 'manage_timesheets'):
            return api_response(False, 'Non puoi generare timesheet per altri utenti', status_code=403)
        
        # Validazione mese/anno
        if not (1 <= month <= 12):
            return api_response(False, 'Mese non valido (1-12)', status_code=400)
        
        if year < 2020 or year > datetime.now().year + 1:
            return api_response(False, 'Anno non valido', status_code=400)
        
        # Verifica se esiste già
        existing = MonthlyTimesheet.query.filter(
            and_(
                MonthlyTimesheet.user_id == user_id,
                MonthlyTimesheet.year == year,
                MonthlyTimesheet.month == month
            )
        ).first()
        
        if existing:
            return api_response(
                data={
                    'id': existing.id,
                    'status': existing.status,
                    'total_hours': existing.total_hours,
                    'billable_hours': existing.billable_hours
                },
                message='Monthly timesheet già esistente'
            )
        
        # Verifica che ci siano timesheet entries per il mese
        entries_count = TimesheetEntry.query.filter(
            and_(
                TimesheetEntry.user_id == user_id,
                extract('year', TimesheetEntry.date) == year,
                extract('month', TimesheetEntry.date) == month
            )
        ).count()
        
        if entries_count == 0:
            return api_response(
                False,
                f'Nessuna entry timesheet trovata per {month}/{year}',
                status_code=400
            )
        
        # Crea nuovo monthly timesheet
        monthly_timesheet = MonthlyTimesheet(
            user_id=user_id,
            year=year,
            month=month,
            status='draft'
        )
        
        db.session.add(monthly_timesheet)
        db.session.commit()
        
        # Collega le entries esistenti al monthly timesheet
        TimesheetEntry.query.filter(
            and_(
                TimesheetEntry.user_id == user_id,
                extract('year', TimesheetEntry.date) == year,
                extract('month', TimesheetEntry.date) == month
            )
        ).update({'monthly_timesheet_id': monthly_timesheet.id})
        
        db.session.commit()
        
        return api_response(
            data={
                'id': monthly_timesheet.id,
                'year': monthly_timesheet.year,
                'month': monthly_timesheet.month,
                'status': monthly_timesheet.status,
                'total_hours': monthly_timesheet.total_hours,
                'billable_hours': monthly_timesheet.billable_hours,
                'entries_count': entries_count
            },
            message='Monthly timesheet generato con successo'
        )
        
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)


@api_monthly_timesheets.route('/<int:timesheet_id>/submit', methods=['PUT'])
@csrf.exempt
@login_required
def submit_monthly_timesheet(timesheet_id):
    """Sottometti monthly timesheet per approvazione"""
    try:
        monthly_timesheet = MonthlyTimesheet.query.get_or_404(timesheet_id)

        # Solo il proprietario può sottomettere
        if monthly_timesheet.user_id != current_user.id:
            return api_response(False, 'Puoi sottomettere solo i tuoi timesheet', status_code=403)

        # Verifica stato
        if monthly_timesheet.status != 'draft':
            return api_response(
                False,
                f'Il timesheet è già stato {monthly_timesheet.status}',
                status_code=400
            )

        # Verifica che ci siano entries
        if monthly_timesheet.total_hours == 0:
            return api_response(
                False,
                'Non è possibile sottomettere un timesheet senza ore registrate',
                status_code=400
            )

        # Sottometti
        monthly_timesheet.status = 'submitted'
        monthly_timesheet.submission_date = datetime.utcnow()

        db.session.commit()

        # Notifica agli admin del timesheet sottomesso
        user = User.query.get(monthly_timesheet.user_id)
        notify_admins(
            title="Timesheet sottomesso per approvazione",
            message=f"{user.full_name} ha sottomesso il timesheet per {monthly_timesheet.month}/{monthly_timesheet.year} ({monthly_timesheet.total_hours}h)",
            notification_type="info",
            link=f"/timesheets/monthly/{monthly_timesheet.id}"
        )

        return api_response(
            data={
                'id': monthly_timesheet.id,
                'status': monthly_timesheet.status,
                'submission_date': monthly_timesheet.submission_date.isoformat(),
                'total_hours': monthly_timesheet.total_hours
            },
            message='Timesheet sottomesso per approvazione'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)


@api_monthly_timesheets.route('/<int:timesheet_id>/approve', methods=['PUT'])
@csrf.exempt
@login_required
def approve_monthly_timesheet(timesheet_id):
    """Approva monthly timesheet"""
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, 'approve_timesheets'):
            return api_response(False, 'Non hai i permessi per approvare timesheet', status_code=403)

        monthly_timesheet = MonthlyTimesheet.query.get_or_404(timesheet_id)

        # Verifica stato
        if monthly_timesheet.status not in ['submitted', 'confirmed']:
            return api_response(
                False,
                f'Il timesheet deve essere in stato "submitted" o "confirmed" per essere approvato (attuale: {monthly_timesheet.status})',
                status_code=400
            )

        # Approva timesheet
        monthly_timesheet.status = 'approved'
        monthly_timesheet.approved_by = current_user.id
        monthly_timesheet.approval_date = datetime.utcnow()
        monthly_timesheet.rejection_reason = None

        # Approva anche tutte le entries collegate
        TimesheetEntry.query.filter(
            TimesheetEntry.monthly_timesheet_id == monthly_timesheet.id
        ).update({'status': 'approved'})

        db.session.commit()

        # Notifica all'utente del timesheet approvato
        create_notification_for_user(
            user_id=monthly_timesheet.user_id,
            title="Timesheet approvato",
            message=f"Il tuo timesheet per {monthly_timesheet.month}/{monthly_timesheet.year} è stato approvato da {current_user.full_name}",
            notification_type="success",
            link=f"/timesheets/monthly/{monthly_timesheet.id}"
        )

        return api_response(
            data={
                'id': monthly_timesheet.id,
                'status': monthly_timesheet.status,
                'approved_by': monthly_timesheet.approved_by,
                'approval_date': monthly_timesheet.approval_date.isoformat(),
                'total_hours': monthly_timesheet.total_hours
            },
            message=f'Timesheet {monthly_timesheet.month}/{monthly_timesheet.year} approvato con successo'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)


@api_monthly_timesheets.route('/<int:timesheet_id>/reject', methods=['PUT'])
@csrf.exempt
@login_required
def reject_monthly_timesheet(timesheet_id):
    """Rifiuta monthly timesheet"""
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, 'approve_timesheets'):
            return api_response(False, 'Non hai i permessi per rifiutare timesheet', status_code=403)

        monthly_timesheet = MonthlyTimesheet.query.get_or_404(timesheet_id)

        # Verifica stato
        if monthly_timesheet.status not in ['submitted', 'confirmed']:
            return api_response(
                False,
                f'Il timesheet deve essere in stato "submitted" o "confirmed" per essere rifiutato (attuale: {monthly_timesheet.status})',
                status_code=400
            )

        data = request.get_json() or {}
        rejection_reason = data.get('reason', '')

        if not rejection_reason:
            return api_response(
                False,
                'Motivo del rifiuto richiesto',
                status_code=400
            )

        # Rifiuta timesheet
        monthly_timesheet.status = 'rejected'
        monthly_timesheet.approved_by = current_user.id
        monthly_timesheet.approval_date = datetime.utcnow()
        monthly_timesheet.rejection_reason = rejection_reason

        # Rimetti le entries in pending
        TimesheetEntry.query.filter(
            TimesheetEntry.monthly_timesheet_id == monthly_timesheet.id
        ).update({'status': 'pending'})

        db.session.commit()

        # Notifica all'utente del timesheet rifiutato
        create_notification_for_user(
            user_id=monthly_timesheet.user_id,
            title="Timesheet rifiutato",
            message=f"Il tuo timesheet per {monthly_timesheet.month}/{monthly_timesheet.year} è stato rifiutato da {current_user.full_name}. Motivo: {rejection_reason}",
            notification_type="danger",
            link=f"/timesheets/monthly/{monthly_timesheet.id}"
        )

        return api_response(
            data={
                'id': monthly_timesheet.id,
                'status': monthly_timesheet.status,
                'approved_by': monthly_timesheet.approved_by,
                'approval_date': monthly_timesheet.approval_date.isoformat(),
                'rejection_reason': monthly_timesheet.rejection_reason
            },
            message=f'Timesheet {monthly_timesheet.month}/{monthly_timesheet.year} rifiutato'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)


@api_monthly_timesheets.route('/<int:timesheet_id>/reopen', methods=['PUT'])
@csrf.exempt
@login_required
def reopen_monthly_timesheet(timesheet_id):
    """Riapre monthly timesheet rifiutato per modifiche"""
    try:
        monthly_timesheet = MonthlyTimesheet.query.get_or_404(timesheet_id)

        # Solo il proprietario può riaprire
        if monthly_timesheet.user_id != current_user.id:
            return api_response(False, 'Puoi riaprire solo i tuoi timesheet', status_code=403)

        # Solo timesheet rifiutati possono essere riaperti
        if monthly_timesheet.status != 'rejected':
            return api_response(
                False,
                'Solo timesheet rifiutati possono essere riaperti',
                status_code=400
            )

        # Riapri
        monthly_timesheet.status = 'draft'
        monthly_timesheet.submission_date = None
        monthly_timesheet.approval_date = None
        monthly_timesheet.approved_by = None
        monthly_timesheet.rejection_reason = None

        db.session.commit()

        return api_response(
            data={
                'id': monthly_timesheet.id,
                'status': monthly_timesheet.status
            },
            message='Timesheet riaperto per modifiche'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)
