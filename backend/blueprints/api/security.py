"""
API endpoints for Security Dashboard.
Provides REST API for security reports and vulnerability management.
"""

import os
import json
import glob
from datetime import datetime
from flask import Blueprint, request, current_app
from flask_login import login_required, current_user

from utils.api_utils import api_response, handle_api_error, api_permission_required
from utils.decorators import require_feature

# Create blueprint
api_security = Blueprint('api_security', __name__)

# Security reports path
SECURITY_PATH = os.path.join(os.path.dirname(__file__), '..', '..', '..', 'security')

@api_security.route('/reports', methods=['GET'])
@login_required
@api_permission_required('admin')
@require_feature('security_dashboard')
def get_security_reports():
    """
    Get all security reports (static analysis + dynamic testing).
    """
    try:
        current_app.logger.info(f"🛡️ [Security API] Admin {current_user.id} accessing security reports")
        
        # Read static analysis reports (SonarQube)
        static_reports = read_static_reports()
        
        # Read dynamic testing reports (Pentest)
        dynamic_reports = read_dynamic_reports()
        
        # Calculate summary metrics
        summary = calculate_security_summary(static_reports, dynamic_reports)
        
        return api_response(
            data={
                'static_analysis': static_reports,
                'dynamic_testing': dynamic_reports,
                'summary': summary,
                'last_updated': datetime.utcnow().isoformat()
            },
            message="Security reports retrieved successfully"
        )
        
    except Exception as e:
        current_app.logger.error(f"❌ [Security API] Error retrieving reports: {str(e)}")
        return handle_api_error(e)

@api_security.route('/reports/static', methods=['GET'])
@login_required  
@api_permission_required('admin')
@require_feature('security_dashboard')
def get_static_reports():
    """Get static analysis reports (SonarQube)."""
    try:
        reports = read_static_reports()
        
        return api_response(
            data={'reports': reports},
            message=f"Retrieved {len(reports)} static analysis reports"
        )
        
    except Exception as e:
        current_app.logger.error(f"❌ [Security API] Error retrieving static reports: {str(e)}")
        return handle_api_error(e)

@api_security.route('/reports/dynamic', methods=['GET'])
@login_required
@api_permission_required('admin')
@require_feature('security_dashboard')
def get_dynamic_reports():
    """Get dynamic testing reports (Pentest)."""
    try:
        reports = read_dynamic_reports()
        
        return api_response(
            data={'reports': reports},
            message=f"Retrieved {len(reports)} dynamic testing reports"
        )
        
    except Exception as e:
        current_app.logger.error(f"❌ [Security API] Error retrieving dynamic reports: {str(e)}")
        return handle_api_error(e)

@api_security.route('/scan-status', methods=['GET'])
@login_required
@api_permission_required('admin')
@require_feature('security_dashboard')
def get_scan_status():
    """Get current security scan status."""
    try:
        # Check for running scans (placeholder for now)
        status = {
            'static_analysis': {
                'status': 'idle',
                'last_scan': get_last_scan_date('static')
            },
            'dynamic_testing': {
                'status': 'idle', 
                'last_scan': get_last_scan_date('dynamic')
            }
        }
        
        return api_response(
            data=status,
            message="Scan status retrieved successfully"
        )
        
    except Exception as e:
        current_app.logger.error(f"❌ [Security API] Error retrieving scan status: {str(e)}")
        return handle_api_error(e)

@api_security.route('/scan/start', methods=['POST'])
@login_required
@api_permission_required('admin')
@require_feature('security_dashboard')
def start_security_scan():
    """Start a dynamic security scan (penetration testing)."""
    try:
        current_app.logger.info(f"🔍 [Security API] Admin {current_user.id} starting security scan")
        
        # Get scan parameters from request
        scan_type = request.json.get('scan_type', 'dynamic')
        target_url = request.json.get('target_url', 'http://localhost:5000')
        
        if scan_type == 'dynamic':
            # Start dynamic penetration testing
            scan_id = start_dynamic_scan(target_url)
            
            return api_response(
                data={
                    'scan_id': scan_id,
                    'scan_type': scan_type,
                    'target_url': target_url,
                    'status': 'started',
                    'estimated_duration': '2-5 minutes'
                },
                message="Dynamic security scan started successfully"
            )
        else:
            return api_response(
                data={},
                message="Static analysis scans are not yet implemented",
                success=False
            )
        
    except Exception as e:
        current_app.logger.error(f"❌ [Security API] Error starting security scan: {str(e)}")
        return handle_api_error(e)

@api_security.route('/export', methods=['POST'])
@login_required
@api_permission_required('admin')
@require_feature('security_dashboard')
def export_security_report():
    """
    Export security report as PDF.
    """
    try:
        data = request.get_json()
        report_type = data.get('report_type', 'summary')  # 'summary', 'static', 'dynamic', 'single_report'
        report_data = data.get('report_data')
        
        current_app.logger.info(f"📄 [Security API] Admin {current_user.id} exporting {report_type} report")
        
        if report_type == 'single_report' and not report_data:
            return api_response(
                message="Report data required for single report export",
                success=False
            ), 400
        
        # Generate PDF content based on report type
        if report_type == 'summary':
            # Get all reports for summary
            static_reports = read_static_reports()
            dynamic_reports = read_dynamic_reports()
            summary = calculate_security_summary(static_reports, dynamic_reports)
            
            pdf_content = generate_summary_pdf(static_reports, dynamic_reports, summary)
        elif report_type == 'single_report':
            pdf_content = generate_single_report_pdf(report_data)
        else:
            return api_response(
                message="Invalid report type",
                success=False
            ), 400
        
        # For now, return a placeholder response
        # In a real implementation, you would generate the actual PDF
        return api_response(
            data={
                'export_id': f"export_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
                'download_url': f"/api/security/download/{report_type}",
                'size_kb': len(str(pdf_content)) / 1024 if pdf_content else 0
            },
            message="Report export prepared successfully"
        )
        
    except Exception as e:
        current_app.logger.error(f"❌ [Security API] Error exporting report: {str(e)}")
        return handle_api_error(e)

@api_security.route('/fix-vulnerability', methods=['POST'])
@login_required
@api_permission_required('admin')
@require_feature('security_dashboard')
def fix_vulnerability():
    """
    Apply automatic fix for a vulnerability.
    """
    try:
        data = request.get_json()
        vulnerability = data.get('vulnerability')
        
        if not vulnerability:
            return api_response(
                message="Vulnerability data required",
                success=False
            ), 400
        
        current_app.logger.info(f"🔧 [Security API] Admin {current_user.id} applying fix for {vulnerability.get('type', 'unknown')} vulnerability")
        
        # Determine fix strategy based on vulnerability type and source
        fix_result = apply_vulnerability_fix(vulnerability)
        
        return api_response(
            data=fix_result,
            message="Vulnerability fix applied successfully" if fix_result.get('success') else "Fix could not be applied automatically"
        )
        
    except Exception as e:
        current_app.logger.error(f"❌ [Security API] Error fixing vulnerability: {str(e)}")
        return handle_api_error(e)

def apply_vulnerability_fix(vulnerability):
    """Apply automatic fix for a vulnerability."""
    try:
        vuln_type = vulnerability.get('type', '').lower()
        vuln_source = vulnerability.get('source', '')
        
        current_app.logger.info(f"🔧 [Security] Attempting to fix {vuln_type} from {vuln_source}")
        
        if vuln_source == 'static':
            return apply_static_vulnerability_fix(vulnerability)
        elif vuln_source == 'dynamic':
            return apply_dynamic_vulnerability_fix(vulnerability)
        else:
            return {
                'success': False,
                'reason': 'Unknown vulnerability source',
                'recommendations': ['Manual review required', 'Consult security documentation']
            }
            
    except Exception as e:
        current_app.logger.error(f"❌ [Security] Error in apply_vulnerability_fix: {str(e)}")
        return {
            'success': False,
            'reason': str(e),
            'recommendations': ['Manual intervention required']
        }

def apply_static_vulnerability_fix(vulnerability):
    """Apply fix for static analysis vulnerability."""
    try:
        rule = vulnerability.get('rule', '').lower()
        file_path = vulnerability.get('component') or vulnerability.get('file')
        
        # Common static analysis fixes
        if 'sql' in rule or 'injection' in rule:
            return {
                'success': False,
                'reason': 'SQL injection requires manual code review',
                'recommendations': [
                    'Use parameterized queries or prepared statements',
                    'Validate and sanitize all user inputs',
                    'Use ORM frameworks with built-in protection'
                ],
                'file': file_path,
                'auto_fixable': False
            }
        elif 'xss' in rule or 'cross-site' in rule:
            return {
                'success': False,
                'reason': 'XSS vulnerabilities require manual code review',
                'recommendations': [
                    'Escape all user output',
                    'Use Content Security Policy (CSP)',
                    'Validate input on both client and server side'
                ],
                'file': file_path,
                'auto_fixable': False
            }
        elif 'hardcoded' in rule or 'secret' in rule:
            return {
                'success': False,
                'reason': 'Hardcoded secrets require manual remediation',
                'recommendations': [
                    'Move secrets to environment variables',
                    'Use secure secret management systems',
                    'Rotate compromised credentials immediately'
                ],
                'file': file_path,
                'auto_fixable': False
            }
        else:
            return {
                'success': False,
                'reason': 'Unknown static analysis rule',
                'recommendations': [
                    'Review SonarQube documentation for this rule',
                    'Consult security best practices',
                    'Consider manual code review'
                ],
                'file': file_path,
                'auto_fixable': False
            }
            
    except Exception as e:
        current_app.logger.error(f"❌ [Security] Error fixing static vulnerability: {str(e)}")
        return {
            'success': False,
            'reason': str(e),
            'recommendations': ['Manual review required']
        }

def apply_dynamic_vulnerability_fix(vulnerability):
    """Apply fix for dynamic testing vulnerability."""
    try:
        vuln_type = vulnerability.get('type', '').lower()
        endpoint = vulnerability.get('endpoint', '')
        
        # Common dynamic vulnerability fixes
        if 'sql injection' in vuln_type:
            return {
                'success': False,
                'reason': 'SQL injection requires application-level fixes',
                'recommendations': [
                    'Implement parameterized queries',
                    'Add input validation middleware',
                    'Enable SQL injection protection in WAF'
                ],
                'endpoint': endpoint,
                'auto_fixable': False
            }
        elif 'cross-site scripting' in vuln_type or 'xss' in vuln_type:
            return {
                'success': False,
                'reason': 'XSS requires application-level fixes',
                'recommendations': [
                    'Implement output encoding',
                    'Add Content Security Policy headers',
                    'Validate and sanitize inputs'
                ],
                'endpoint': endpoint,
                'auto_fixable': False
            }
        elif 'csrf' in vuln_type:
            return {
                'success': False,
                'reason': 'CSRF requires application-level protection',
                'recommendations': [
                    'Implement CSRF tokens',
                    'Verify referrer headers',
                    'Use SameSite cookie attributes'
                ],
                'endpoint': endpoint,
                'auto_fixable': False
            }
        elif 'authentication bypass' in vuln_type:
            return {
                'success': False,
                'reason': 'Authentication issues require security architecture review',
                'recommendations': [
                    'Review authentication mechanisms',
                    'Implement proper session management',
                    'Add multi-factor authentication'
                ],
                'endpoint': endpoint,
                'auto_fixable': False
            }
        else:
            return {
                'success': False,
                'reason': 'Unknown dynamic vulnerability type',
                'recommendations': [
                    'Consult OWASP guidelines',
                    'Perform manual security testing',
                    'Consider security architecture review'
                ],
                'endpoint': endpoint,
                'auto_fixable': False
            }
            
    except Exception as e:
        current_app.logger.error(f"❌ [Security] Error fixing dynamic vulnerability: {str(e)}")
        return {
            'success': False,
            'reason': str(e),
            'recommendations': ['Manual review required']
        }

def generate_summary_pdf(static_reports, dynamic_reports, summary):
    """Generate PDF content for summary report."""
    # Placeholder for PDF generation
    # In a real implementation, you would use a library like reportlab or weasyprint
    content = {
        'title': 'Security Dashboard Summary Report',
        'generated_at': datetime.utcnow().isoformat(),
        'summary': summary,
        'static_reports_count': len(static_reports),
        'dynamic_reports_count': len(dynamic_reports)
    }
    return content

def generate_single_report_pdf(report_data):
    """Generate PDF content for a single report."""
    # Placeholder for PDF generation
    content = {
        'title': f'Security Report - {report_data.get("tool", "Unknown")}',
        'generated_at': datetime.utcnow().isoformat(),
        'report': report_data
    }
    return content

def read_static_reports():
    """Read and parse SonarQube static analysis reports."""
    try:
        static_dir = os.path.join(SECURITY_PATH, 'static', 'reports')
        reports = []
        
        if not os.path.exists(static_dir):
            current_app.logger.warning(f"⚠️ [Security API] Static reports directory not found: {static_dir}")
            return []
        
        # Find JSON reports
        json_files = glob.glob(os.path.join(static_dir, 'sonarqube_report_*.json'))
        
        for json_file in sorted(json_files, reverse=True):  # Most recent first
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    report_data = json.load(f)
                    
                # Standardize report format
                standardized_report = standardize_static_report(report_data, json_file)
                reports.append(standardized_report)
                
            except Exception as e:
                current_app.logger.error(f"❌ [Security API] Error parsing {json_file}: {str(e)}")
                continue
        
        current_app.logger.info(f"📊 [Security API] Loaded {len(reports)} static analysis reports")
        return reports
        
    except Exception as e:
        current_app.logger.error(f"❌ [Security API] Error reading static reports: {str(e)}")
        return []

def read_dynamic_reports():
    """Read and parse dynamic testing reports (Pentest)."""
    try:
        dynamic_dir = os.path.join(SECURITY_PATH, 'dynamic', 'reports')
        reports = []
        
        if not os.path.exists(dynamic_dir):
            current_app.logger.warning(f"⚠️ [Security API] Dynamic reports directory not found: {dynamic_dir}")
            return []
        
        # Find JSON reports
        json_files = glob.glob(os.path.join(dynamic_dir, 'pentest_report_*.json'))
        
        for json_file in sorted(json_files, reverse=True):  # Most recent first
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    report_data = json.load(f)
                    
                # Standardize report format
                standardized_report = standardize_dynamic_report(report_data, json_file)
                reports.append(standardized_report)
                
            except Exception as e:
                current_app.logger.error(f"❌ [Security API] Error parsing {json_file}: {str(e)}")
                continue
        
        current_app.logger.info(f"🔍 [Security API] Loaded {len(reports)} dynamic testing reports")
        return reports
        
    except Exception as e:
        current_app.logger.error(f"❌ [Security API] Error reading dynamic reports: {str(e)}")
        return []

def standardize_static_report(report_data, file_path):
    """Standardize SonarQube report to common format."""
    try:
        filename = os.path.basename(file_path)
        
        # Extract timestamp from filename if possible
        timestamp = extract_timestamp_from_filename(filename)
        
        # Parse SonarQube data
        issues = report_data.get('issues', [])
        
        # Count issues by severity
        severity_counts = {'critical': 0, 'high': 0, 'medium': 0, 'low': 0}
        
        for issue in issues:
            severity = issue.get('severity', '').lower()
            if severity in ['blocker', 'critical']:
                severity_counts['critical'] += 1
            elif severity == 'major':
                severity_counts['high'] += 1
            elif severity == 'minor':
                severity_counts['medium'] += 1
            elif severity == 'info':
                severity_counts['low'] += 1
        
        return {
            'scan_type': 'static',
            'tool': 'SonarQube',
            'timestamp': timestamp,
            'file_path': file_path,
            'summary': {
                'total_issues': len(issues),
                'critical': severity_counts['critical'],
                'high': severity_counts['high'], 
                'medium': severity_counts['medium'],
                'low': severity_counts['low']
            },
            'vulnerabilities': issues[:50],  # Limit for performance
            'raw_data': report_data
        }
        
    except Exception as e:
        current_app.logger.error(f"❌ [Security API] Error standardizing static report: {str(e)}")
        return {
            'scan_type': 'static',
            'tool': 'SonarQube',
            'timestamp': datetime.utcnow().isoformat(),
            'error': str(e)
        }

def standardize_dynamic_report(report_data, file_path):
    """Standardize dynamic testing report to common format."""
    try:
        filename = os.path.basename(file_path)
        
        # Extract timestamp from filename if possible  
        timestamp = extract_timestamp_from_filename(filename)
        
        # If report already in standard format, use as-is
        if report_data.get('scan_type') == 'dynamic':
            return report_data
            
        # Otherwise, create standardized format
        vulnerabilities = report_data.get('vulnerabilities', [])
        
        # Count by severity if available
        severity_counts = {'critical': 0, 'high': 0, 'medium': 0, 'low': 0}
        
        for vuln in vulnerabilities:
            severity = vuln.get('severity', 'medium').lower()
            if severity in severity_counts:
                severity_counts[severity] += 1
            else:
                severity_counts['medium'] += 1
        
        return {
            'scan_type': 'dynamic',
            'tool': 'DatPortal Pentest',
            'timestamp': timestamp,
            'file_path': file_path,
            'summary': {
                'total_issues': len(vulnerabilities),
                'critical': severity_counts['critical'],
                'high': severity_counts['high'],
                'medium': severity_counts['medium'], 
                'low': severity_counts['low']
            },
            'vulnerabilities': vulnerabilities,
            'raw_data': report_data
        }
        
    except Exception as e:
        current_app.logger.error(f"❌ [Security API] Error standardizing dynamic report: {str(e)}")
        return {
            'scan_type': 'dynamic',
            'tool': 'DatPortal Pentest',
            'timestamp': datetime.utcnow().isoformat(),
            'error': str(e)
        }

def extract_timestamp_from_filename(filename):
    """Extract timestamp from report filename."""
    try:
        # Try to extract from filename like sonarqube_report_20250708_010507.json
        if '_' in filename:
            parts = filename.split('_')
            if len(parts) >= 3:
                date_part = parts[-2]  # 20250708
                time_part = parts[-1].split('.')[0]  # 010507
                
                if len(date_part) == 8 and len(time_part) == 6:
                    year = date_part[:4]
                    month = date_part[4:6] 
                    day = date_part[6:8]
                    hour = time_part[:2]
                    minute = time_part[2:4]
                    second = time_part[4:6]
                    
                    timestamp_str = f"{year}-{month}-{day}T{hour}:{minute}:{second}Z"
                    # Validate by parsing
                    datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                    return timestamp_str
        
    except Exception:
        pass
    
    # Fallback to current time
    return datetime.utcnow().isoformat() + 'Z'

def get_last_scan_date(scan_type):
    """Get the date of the last scan for given type."""
    try:
        if scan_type == 'static':
            reports = read_static_reports()
        else:
            reports = read_dynamic_reports()
        
        if reports:
            # Return most recent timestamp
            return reports[0].get('timestamp')
        
        return None
        
    except Exception as e:
        current_app.logger.error(f"❌ [Security API] Error getting last scan date: {str(e)}")
        return None

def calculate_security_summary(static_reports, dynamic_reports):
    """Calculate overall security summary from all reports."""
    try:
        total_issues = 0
        severity_totals = {'critical': 0, 'high': 0, 'medium': 0, 'low': 0}
        
        # Sum static analysis issues
        for report in static_reports:
            if 'summary' in report:
                summary = report['summary']
                total_issues += summary.get('total_issues', 0)
                for severity in severity_totals:
                    severity_totals[severity] += summary.get(severity, 0)
        
        # Sum dynamic testing issues
        for report in dynamic_reports:
            if 'summary' in report:
                summary = report['summary']
                total_issues += summary.get('total_issues', 0)
                for severity in severity_totals:
                    severity_totals[severity] += summary.get(severity, 0)
        
        # Calculate risk score (weighted by severity)
        risk_score = (
            severity_totals['critical'] * 10 +
            severity_totals['high'] * 5 +
            severity_totals['medium'] * 2 +
            severity_totals['low'] * 1
        )
        
        # Determine overall status
        if severity_totals['critical'] > 0:
            status = 'critical'
        elif severity_totals['high'] > 0:
            status = 'warning'
        elif severity_totals['medium'] > 5:
            status = 'warning'
        else:
            status = 'good'
        
        return {
            'total_issues': total_issues,
            'by_severity': severity_totals,
            'risk_score': risk_score,
            'status': status,
            'reports_count': {
                'static': len(static_reports),
                'dynamic': len(dynamic_reports)
            }
        }
        
    except Exception as e:
        current_app.logger.error(f"❌ [Security API] Error calculating summary: {str(e)}")
        return {
            'total_issues': 0,
            'by_severity': {'critical': 0, 'high': 0, 'medium': 0, 'low': 0},
            'risk_score': 0,
            'status': 'unknown',
            'error': str(e)
        }

# Register blueprint function
def start_dynamic_scan(target_url):
    """Start a dynamic penetration testing scan."""
    import subprocess
    import threading
    import uuid
    from datetime import datetime
    
    scan_id = str(uuid.uuid4())
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Path to pentest script
    pentest_script = os.path.join(SECURITY_PATH, 'dynamic', 'pentest.py')
    output_dir = os.path.join(SECURITY_PATH, 'dynamic', 'reports')
    
    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)
    
    current_app.logger.info(f"🔍 [Security] Starting dynamic scan {scan_id} for {target_url}")
    
    def run_scan():
        """Run the penetration test in a separate thread."""
        try:
            # Run pentest.py script
            cmd = [
                'python', pentest_script,
                '--target', target_url,
                '--output-dir', output_dir,
                '--format', 'both'
            ]
            
            current_app.logger.info(f"🔍 [Security] Executing: {' '.join(cmd)}")
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minutes timeout
            )
            
            if result.returncode == 0:
                current_app.logger.info(f"✅ [Security] Scan {scan_id} completed successfully")
            else:
                current_app.logger.error(f"❌ [Security] Scan {scan_id} failed: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            current_app.logger.error(f"❌ [Security] Scan {scan_id} timed out")
        except Exception as e:
            current_app.logger.error(f"❌ [Security] Scan {scan_id} error: {str(e)}")
    
    # Start scan in background thread
    scan_thread = threading.Thread(target=run_scan)
    scan_thread.daemon = True
    scan_thread.start()
    
    return scan_id

def register_security_routes(app):
    """Register security routes in Flask app."""
    app.register_blueprint(api_security, url_prefix='/api/security')