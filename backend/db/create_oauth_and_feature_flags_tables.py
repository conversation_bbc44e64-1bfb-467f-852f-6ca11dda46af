#!/usr/bin/env python3
"""
Database update script: Create OAuth and Feature Flags tables
Adds support for OAuth authentication and feature flags system.
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from extensions import db
from models import FeatureFlag, OAuthAccount

def create_tables():
    """Create OAuth and Feature Flags tables"""
    app = create_app()
    
    with app.app_context():
        try:
            # Create FeatureFlag table
            print("Creating FeatureFlag table...")
            db.engine.execute("""
                CREATE TABLE IF NOT EXISTS feature_flags (
                    id SERIAL PRIMARY KEY,
                    feature_key VARCHAR(100) UNIQUE NOT NULL,
                    display_name VARCHAR(200) NOT NULL,
                    description TEXT,
                    category VARCHAR(100) DEFAULT 'general',
                    is_enabled BOOLEAN DEFAULT TRUE NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    notes TEXT
                );
            """)
            
            # Create index on feature_key for performance
            db.engine.execute("""
                CREATE INDEX IF NOT EXISTS idx_feature_flags_key 
                ON feature_flags(feature_key);
            """)
            
            # Create OAuthAccount table
            print("Creating OAuthAccount table...")
            db.engine.execute("""
                CREATE TABLE IF NOT EXISTS oauth_accounts (
                    id SERIAL PRIMARY KEY,
                    user_id INTEGER NOT NULL,
                    provider VARCHAR(50) NOT NULL,
                    provider_user_id VARCHAR(255) NOT NULL,
                    provider_email VARCHAR(255),
                    linked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_used TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    UNIQUE(provider, provider_user_id)
                );
            """)
            
            # Create indexes for OAuth table
            db.engine.execute("""
                CREATE INDEX IF NOT EXISTS idx_oauth_accounts_user_id 
                ON oauth_accounts(user_id);
            """)
            
            db.engine.execute("""
                CREATE INDEX IF NOT EXISTS idx_oauth_accounts_provider 
                ON oauth_accounts(provider);
            """)
            
            print("✅ OAuth and Feature Flags tables created successfully!")
            
            # Insert some default feature flags
            print("Inserting default feature flags...")
            
            default_flags = [
                {
                    'feature_key': 'oauth_google_enabled',
                    'display_name': 'Google OAuth Login',
                    'description': 'Consenti login tramite account Google',
                    'category': 'authentication',
                    'is_enabled': True
                },
                {
                    'feature_key': 'oauth_microsoft_enabled', 
                    'display_name': 'Microsoft OAuth Login',
                    'description': 'Consenti login tramite account Microsoft',
                    'category': 'authentication',
                    'is_enabled': True
                },
                {
                    'feature_key': 'recruiting_module',
                    'display_name': 'Modulo Recruiting',
                    'description': 'Abilita il modulo di recruiting e gestione candidati',
                    'category': 'modules',
                    'is_enabled': True
                },
                {
                    'feature_key': 'ai_assistant',
                    'display_name': 'Assistente AI',
                    'description': 'Abilita l\'assistente AI per CEO e analisi strategiche',
                    'category': 'ai',
                    'is_enabled': True
                },
                {
                    'feature_key': 'advanced_analytics',
                    'display_name': 'Analytics Avanzate',
                    'description': 'Abilita dashboard e report analytics avanzati',
                    'category': 'analytics',
                    'is_enabled': True
                }
            ]
            
            for flag_data in default_flags:
                # Check if flag already exists
                existing = db.engine.execute(
                    "SELECT id FROM feature_flags WHERE feature_key = %s",
                    (flag_data['feature_key'],)
                ).fetchone()
                
                if not existing:
                    db.engine.execute("""
                        INSERT INTO feature_flags 
                        (feature_key, display_name, description, category, is_enabled)
                        VALUES (%s, %s, %s, %s, %s)
                    """, (
                        flag_data['feature_key'],
                        flag_data['display_name'], 
                        flag_data['description'],
                        flag_data['category'],
                        flag_data['is_enabled']
                    ))
                    print(f"✅ Created feature flag: {flag_data['feature_key']}")
                else:
                    print(f"⚠️  Feature flag already exists: {flag_data['feature_key']}")
            
            print("✅ Database update completed successfully!")
            
        except Exception as e:
            print(f"❌ Error creating tables: {str(e)}")
            sys.exit(1)

if __name__ == '__main__':
    create_tables()