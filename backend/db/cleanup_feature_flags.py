#!/usr/bin/env python3
"""
Script per pulire i feature flags esistenti dal database.
Rimuove tutti i flag "inventati" e mantiene solo l'infrastruttura.
"""

import sys
import os

# Add backend to path and set DB URL
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '../'))
if not os.environ.get('DATABASE_URL'):
    os.environ['DATABASE_URL'] = 'sqlite:///instance/datportal.db'

from app import create_app
from models_split.settings import FeatureFlag
from extensions import db

# Crea l'istanza dell'app
app = create_app()

def cleanup_feature_flags():
    """
    Rimuove tutti i feature flags esistenti dal database.
    Questo è necessario perché i flag attuali sono "inventati" e non corrispondono
    alle vere funzionalità dell'applicazione.
    """
    with app.app_context():
        try:
            # Conta i flag esistenti
            existing_count = FeatureFlag.query.count()
            print(f"🔍 Trovati {existing_count} feature flags esistenti nel database")
            
            if existing_count == 0:
                print("✅ Nessun feature flag da rimuovere")
                return
            
            # Lista tutti i flag esistenti prima della rimozione
            existing_flags = FeatureFlag.query.all()
            print("\n📋 Feature flags che verranno rimossi:")
            for flag in existing_flags:
                print(f"  - {flag.feature_key}: {flag.display_name} (categoria: {flag.category})")
            
            # Conferma rimozione
            confirm = input(f"\n⚠️  Vuoi davvero rimuovere tutti i {existing_count} feature flags? [y/N]: ")
            if confirm.lower() != 'y':
                print("❌ Operazione annullata")
                return
            
            # Rimuovi tutti i feature flags
            deleted_count = FeatureFlag.query.delete()
            db.session.commit()
            
            print(f"✅ Rimossi {deleted_count} feature flags dal database")
            print("🧹 Database pulito e pronto per i nuovi feature flags basati sui moduli reali")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante la pulizia: {str(e)}")
            raise

if __name__ == "__main__":
    cleanup_feature_flags()