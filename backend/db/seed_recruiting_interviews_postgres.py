#!/usr/bin/env python3
"""
Seed script per aggiungere interview sessions e completare workflow recruiting
Versione per PostgreSQL
"""
import sys
import os
from datetime import datetime, timedelta
import random

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Carica le variabili d'ambiente dal file .env
try:
    from dotenv import load_dotenv
    load_dotenv(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), '.env'))
except ImportError:
    # Se python-dotenv non è disponibile, prova a usare load_env.py
    try:
        sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
        from load_env import load_env
        load_env()
    except ImportError:
        print("⚠️  Attenzione: Impossibile caricare le variabili d'ambiente")

from app import create_app
from extensions import db
from models import (
    InterviewSession, Application, Candidate, JobPosting, User,
    CandidateSkill
)

def seed_recruiting_interviews():
    """Crea interview sessions e aggiorna workflow recruiting."""
    
    print("🎯 Seeding recruiting interviews and workflow...")
    
    # Get existing data
    candidates = Candidate.query.all()
    applications = Application.query.all()
    job_postings = JobPosting.query.filter_by(status='active').all()
    interviewers = User.query.filter(User.role.in_(['admin', 'manager', 'hr'])).all()
    
    if not candidates or not applications or not interviewers:
        print("❌ Missing required data: candidates, applications, or interviewers")
        return
    
    print(f"📊 Found: {len(candidates)} candidates, {len(applications)} applications, {len(interviewers)} interviewers")
    
    # Update application statuses to show pipeline progression
    pipeline_steps = ['application_received', 'screening', 'interview_1', 'interview_2', 'offer']
    
    for i, application in enumerate(applications[:5]):  # Update first 5 applications
        # Progress applications through different pipeline stages
        step_index = i % len(pipeline_steps)
        application.current_step = pipeline_steps[step_index]
        
        if step_index >= 1:  # screening or beyond
            application.status = 'in_progress'
        if step_index >= 4:  # offer stage
            application.status = 'pending'
            application.overall_score = random.randint(7, 10)
        
        print(f"📝 Updated application {application.id}: {application.current_step}")
    
    # Create interview sessions for applications in interview stages
    interview_types = ['phone_screening', 'video_technical', 'onsite_cultural', 'final_executive']
    
    interviews_created = 0
    for application in applications:
        if application.current_step in ['interview_1', 'interview_2']:
            # Create 1-2 interviews per application
            num_interviews = 1 if application.current_step == 'interview_1' else 2
            
            for i in range(num_interviews):
                # Skip if interview already exists
                existing = InterviewSession.query.filter_by(application_id=application.id).first()
                if existing:
                    continue
                
                # Random interviewer
                interviewer = random.choice(interviewers)
                
                # Interview type based on sequence
                interview_type = interview_types[i] if i < len(interview_types) else 'final_executive'
                
                # Random date: some past (completed), some future (scheduled)
                is_completed = random.choice([True, False])
                
                if is_completed:
                    # Past interview (1-10 days ago)
                    scheduled_date = datetime.now() - timedelta(days=random.randint(1, 10))
                    status = 'completed'
                    score = random.randint(6, 10)
                    feedback = f"Candidato interessante. {'Raccomandato per il prossimo step.' if score >= 7 else 'Necessario approfondimento.'}"
                    recommendation = 'hire' if score >= 8 else ('maybe' if score >= 6 else 'reject')
                    completed_at = scheduled_date + timedelta(hours=1)
                else:
                    # Future interview (1-14 days from now)
                    scheduled_date = datetime.now() + timedelta(days=random.randint(1, 14))
                    status = 'scheduled'
                    score = None
                    feedback = None
                    recommendation = None
                    completed_at = None
                
                interview = InterviewSession(
                    application_id=application.id,
                    interview_type=interview_type,
                    scheduled_date=scheduled_date,
                    duration_minutes=60,
                    location='Video Call' if 'video' in interview_type else 'Sede Milano',
                    interviewer_id=interviewer.id,
                    status=status,
                    score=score,
                    notes=f"Colloquio {interview_type.replace('_', ' ').title()}",
                    feedback=feedback,
                    recommendation=recommendation,
                    completed_at=completed_at,
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                
                db.session.add(interview)
                interviews_created += 1
                
                print(f"🎤 Created {status} interview: {interview_type} for application {application.id}")
    
    # Add some candidate skills from CV analysis simulation
    skills_list = [
        'Python', 'JavaScript', 'React', 'Vue.js', 'Node.js', 'SQL', 'MongoDB',
        'Machine Learning', 'Project Management', 'Agile', 'Scrum', 'Leadership',
        'Communication', 'Problem Solving', 'Team Work', 'Critical Thinking'
    ]
    
    categories_map = {
        'Python': 'programming', 'JavaScript': 'programming', 'React': 'frontend',
        'Vue.js': 'frontend', 'Node.js': 'backend', 'SQL': 'database',
        'MongoDB': 'database', 'Machine Learning': 'data_science',
        'Project Management': 'management', 'Agile': 'methodology',
        'Scrum': 'methodology', 'Leadership': 'soft_skills',
        'Communication': 'soft_skills', 'Problem Solving': 'soft_skills',
        'Team Work': 'soft_skills', 'Critical Thinking': 'soft_skills'
    }
    
    skills_created = 0
    for candidate in candidates[:3]:  # Add skills to first 3 candidates
        # Add 3-6 random skills per candidate
        num_skills = random.randint(3, 6)
        candidate_skills = random.sample(skills_list, num_skills)
        
        for skill_name in candidate_skills:
            # Skip if skill already exists
            existing = CandidateSkill.query.filter_by(
                candidate_id=candidate.id,
                skill_name=skill_name
            ).first()
            if existing:
                continue
            
            skill = CandidateSkill(
                candidate_id=candidate.id,
                skill_name=skill_name,
                skill_category=categories_map.get(skill_name, 'other'),
                skill_level=random.randint(2, 5),  # Beginner to Expert
                years_experience=random.randint(1, 8),
                extracted_from_cv=True,  # Simulate CV extraction
                confidence_score=round(random.uniform(0.7, 0.95), 2),
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            db.session.add(skill)
            skills_created += 1
        
        print(f"💡 Added {len(candidate_skills)} skills for candidate {candidate.id}")
    
    # Commit all changes
    try:
        db.session.commit()
        print(f"✅ Successfully created:")
        print(f"   - {interviews_created} interview sessions")
        print(f"   - {skills_created} candidate skills")
        print(f"   - Updated {len(applications)} application statuses")
        
    except Exception as e:
        db.session.rollback()
        print(f"❌ Error seeding data: {e}")
        raise

if __name__ == '__main__':
    app = create_app()
    with app.app_context():
        seed_recruiting_interviews()