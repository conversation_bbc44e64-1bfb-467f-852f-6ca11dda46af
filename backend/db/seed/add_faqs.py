#!/usr/bin/env python3
"""
Script per aggiungere FAQ complete al Help Center
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime
from app import create_app, db

def add_comprehensive_faqs():
    """Aggiunge FAQ complete per tutti i moduli di DatPortal"""
    app = create_app()
    
    with app.app_context():
        from models_split.help import HelpCategory, HelpContent
        from models import User
        
        print("❓ Aggiunta FAQ complete...")
        
        # Get FAQ category
        faq_category = HelpCategory.query.filter_by(slug='faq').first()
        if not faq_category:
            print("❌ Categoria FAQ non trovata")
            return False
        
        # Get admin user for content authoring
        admin_user = User.query.filter_by(role='admin').first()
        if not admin_user:
            admin_user = User.query.first()
        
        author_id = admin_user.id if admin_user else None
        
        faqs = [
            {
                'title': 'Come posso reimpostare la mia password?',
                'slug': 'reset-password-faq',
                'content': '''# Come reimpostare la password

## Procedura di reset

1. **Dalla pagina di login**:
   - Clicca su "Password dimenticata?"
   - Inserisci la tua email aziendale
   - Controlla la tua casella email

2. **Dall'email di reset**:
   - Clicca sul link ricevuto
   - Inserisci la nuova password
   - Conferma la nuova password

## Requisiti password

- Minimo 8 caratteri
- Almeno una lettera maiuscola
- Almeno un numero
- Almeno un carattere speciale

## Problemi comuni

**Non ricevo l'email di reset?**
- Controlla la cartella spam
- Verifica che l'email sia corretta
- Contatta l'amministratore sistema

**Il link è scaduto?**
- I link scadono dopo 24 ore
- Richiedi un nuovo reset password'''
            },
            {
                'title': 'Come faccio a registrare le ore nel timesheet?',
                'slug': 'timesheet-registrazione-faq',
                'content': '''# Registrazione ore timesheet

## Accesso al timesheet

1. Vai alla sezione **Timesheet** nella sidebar
2. Seleziona il mese corrente
3. Clicca sul giorno da compilare

## Compilazione giornaliera

**Informazioni richieste:**
- **Progetto**: Seleziona dal progetto attivo
- **Attività**: Specifica il tipo di lavoro
- **Ore**: Numero di ore lavorate
- **Descrizione**: Breve descrizione dell'attività

## Tipi di ore

- **Lavoro**: Ore regolari su progetti
- **Straordinari**: Ore oltre l'orario standard
- **Ferie**: Giorni di vacanza
- **Malattia**: Assenze per malattia
- **Formazione**: Ore di training

## Approvazione

- Compila tutte le giornate del mese
- Clicca "Invia per approvazione"
- Il manager riceverà notifica per la revisione'''
            },
            {
                'title': 'Come creo un nuovo progetto?',
                'slug': 'nuovo-progetto-faq',
                'content': '''# Creazione nuovo progetto

## Accesso alla creazione

1. Vai su **Progetti** nella sidebar
2. Clicca il pulsante **+ Nuovo Progetto**
3. Compila il form di creazione

## Informazioni obbligatorie

**Dati base:**
- Nome progetto (univoco)
- Descrizione e obiettivi
- Cliente di riferimento
- Date inizio e fine previste

**Configurazione:**
- Budget totale
- Valuta di riferimento
- Priorità (Alta/Media/Bassa)
- Stato iniziale (Planning/Active)

## Assegnazione team

- Seleziona il Project Manager
- Aggiungi membri del team
- Definisci ruoli e responsabilità
- Imposta permessi di accesso

## Best practices

- Usa nomi descrittivi e chiari
- Definisci milestone intermedie  
- Pianifica buffer per imprevisti
- Coinvolgi stakeholder chiave'''
            },
            {
                'title': 'Come funziona il processo di recruiting?',
                'slug': 'recruiting-processo-faq',
                'content': '''# Processo di recruiting

## Fasi del processo

1. **Job Posting**: Pubblicazione posizione
2. **Candidature**: Raccolta CV e lettere
3. **Screening**: Prima selezione
4. **Colloqui**: Interviste strutturate  
5. **Valutazione**: Decision making
6. **Offerta**: Proposta contrattuale

## Gestione candidature

**Pipeline standard:**
- Nuove candidature
- In screening
- Colloquio programmato
- In valutazione
- Offerta inviata
- Assunto/Rifiutato

## Strumenti disponibili

- **Parsing CV**: Estrazione automatica dati
- **Score matching**: Valutazione AI
- **Calendar integrato**: Gestione colloqui
- **Template email**: Comunicazioni standard

## Documentazione

- Salva note per ogni candidato
- Registra feedback colloqui
- Mantieni storico comunicazioni
- Rispetta privacy e GDPR'''
            },
            {
                'title': 'Come gestisco i clienti nel CRM?',
                'slug': 'crm-clienti-faq',
                'content': '''# Gestione clienti CRM

## Creazione nuovo cliente

1. Vai su **CRM** → **Clienti**
2. Clicca **+ Nuovo Cliente**
3. Compila dati anagrafici
4. Salva e procedi con i contatti

## Informazioni cliente

**Dati aziendali:**
- Ragione sociale
- Partita IVA / Codice fiscale
- Indirizzo sede legale
- Settore di attività

**Dati contatto:**
- Email principale
- Telefono/Fax
- Sito web
- Note commerciali

## Gestione contatti

- Aggiungi referenti aziendali
- Definisci ruoli (CEO, Procurement, etc.)
- Mantieni storico comunicazioni
- Programma follow-up

## Contratti e proposte

- Crea proposte commerciali
- Gestisci negoziazioni
- Finalizza contratti
- Monitora scadenze'''
            },
            {
                'title': 'Cosa sono le certificazioni ISO?',
                'slug': 'certificazioni-iso-faq',
                'content': '''# Certificazioni ISO

## Cos'è una certificazione ISO

Le certificazioni ISO sono standard internazionali che attestano la conformità dei processi aziendali a requisiti specifici di qualità, sicurezza e gestione.

## Certificazioni supportate

**ISO 9001** - Qualità
- Gestione della qualità
- Soddisfazione cliente
- Miglioramento continuo

**ISO 27001** - Sicurezza informatica
- Protezione dati
- Gestione rischi IT
- Controlli sicurezza

**ISO 14001** - Ambiente
- Gestione ambientale
- Sostenibilità
- Riduzione impatto

## Processo di certificazione

1. **Planning**: Analisi gap e pianificazione
2. **Implementazione**: Adeguamento processi
3. **Audit interno**: Verifica conformità
4. **Certificazione**: Audit ente terzo
5. **Mantenimento**: Audit periodici

## Gestione in DatPortal

- Monitora stato certificazioni
- Gestisci scadenze e rinnovi
- Pianifica audit interni
- Traccia azioni correttive'''
            },
            {
                'title': 'Come accedo ai bandi di finanziamento?',
                'slug': 'bandi-finanziamento-faq',
                'content': '''# Bandi di finanziamento

## Ricerca opportunità

1. Vai su **Bandi** nella sidebar
2. Usa filtri per settore e tipologia
3. Consulta dettagli opportunità
4. Verifica requisiti di partecipazione

## Tipologie di bando

**Ricerca e sviluppo:**
- Progetti innovativi
- Trasferimento tecnologico
- Collaborazioni università-impresa

**Internazionalizzazione:**
- Export e marketing
- Partecipazione fiere
- Apertura mercati esteri

**Sostenibilità:**
- Efficienza energetica
- Economia circolare
- Riduzione impatto ambientale

## Processo di candidatura

1. **Analisi bando**: Verifica requisiti
2. **Preparazione**: Raccolta documentazione
3. **Compilazione**: Form online
4. **Invio**: Rispetto deadline
5. **Follow-up**: Monitoraggio esito

## Gestione progetti approvati

- Rendicontazione spese
- Report avanzamento
- Comunicazioni con ente
- Gestione milestone'''
            },
            {
                'title': 'Come funziona Human CEO AI?',
                'slug': 'human-ceo-ai-faq',
                'content': '''# Human CEO AI Assistant

## Cos'è Human CEO

Human CEO è un assistente AI specializzato per supportare dirigenti e decision maker con analisi strategiche, insights di business e supporto decisionale basato sui dati aziendali.

## Funzionalità principali

**Dashboard strategico:**
- KPI aziendali in tempo reale
- Trend e performance
- Alert e anomalie
- Previsioni business

**Assistente conversazionale:**
- Analisi dati complesse
- Ricerche di mercato
- Supporto decisionale
- Insights strategici

## Modalità di utilizzo

**Ricerca veloce:**
- Query rapide su dati
- Analisi immediate
- Risposte concise

**Analisi approfondita:**
- Ricerche complete
- Report dettagliati
- Raccomandazioni strutturate

## Configurazione

- Profilo aziendale personalizzato
- Settori di interesse
- Stile comunicativo preferito
- Argomenti di ricerca ricorrenti

## Privacy e sicurezza

- Dati sempre protetti
- Accesso solo a dirigenti autorizzati
- Conformità GDPR
- Audit log completo'''
            },
            {
                'title': 'Come gestisco le notifiche?',
                'slug': 'notifiche-gestione-faq',
                'content': '''# Gestione notifiche

## Tipi di notifiche

**Sistema:**
- Scadenze importanti
- Aggiornamenti sistema
- Manutenzioni programmate

**Progetti:**
- Nuovi task assegnati
- Deadline in scadenza
- Milestone raggiunte
- Aggiornamenti team

**HR:**
- Approvazioni richieste
- Timesheet da approvare
- Nuove candidature

## Configurazione preferenze

1. Clicca l'icona profilo (in alto a destra)
2. Vai su **Impostazioni**
3. Sezione **Notifiche**
4. Personalizza per categoria

## Canali di notifica

**In-app:**
- Badge numerici
- Popup informativi
- Dashboard alerts

**Email:**
- Riepiloghi giornalieri
- Notifiche urgenti
- Report settimanali

**Browser:**
- Notifiche push
- Alert desktop
- Promemoria sonori

## Gestione notifiche ricevute

- Marca come lette
- Archivia o elimina
- Rispondi direttamente
- Programma promemoria'''
            }
        ]
        
        created_count = 0
        for faq_data in faqs:
            # Check if FAQ already exists
            existing = HelpContent.query.filter_by(slug=faq_data['slug']).first()
            if existing:
                print(f"  ↻ FAQ già esistente: {faq_data['title']}")
                continue
            
            faq = HelpContent(
                title=faq_data['title'],
                slug=faq_data['slug'],
                content=faq_data['content'],
                category_id=faq_category.id,
                content_type='faq',
                difficulty_level='beginner',
                estimated_read_time=5,
                is_published=True,
                featured=False,
                created_by=author_id,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            db.session.add(faq)
            created_count += 1
            print(f"  ✅ FAQ creata: {faq_data['title']}")
        
        db.session.commit()
        print(f"✅ FAQ aggiunte: {created_count} nuove FAQ create")
        return True

if __name__ == "__main__":
    add_comprehensive_faqs()