"""
Script per popolare il database con dati seed per il sistema di prefatturazione.
Crea clienti, contratti, progetti, timesheet entries e pre-fatture di esempio.
"""

from app import create_app, db
import sqlalchemy as sa
from datetime import date, datetime, timedelta
from decimal import Decimal
import random

# Crea l'applicazione
app = create_app()

def create_seed_clients():
    """Crea clienti di esempio con dati fiscali italiani"""
    with app.app_context():
        try:
            print("👥 Creazione clienti seed...")
            
            # Verifica se esistono già clienti
            result = db.session.execute(sa.text("SELECT COUNT(*) FROM client")).fetchone()
            client_count = result[0] if result else 0
            
            if client_count >= 5:
                print(f"  ⚠️  Trovati {client_count} clienti esistenti. Salto la creazione.")
                return True
            
            clients_data = [
                {
                    'name': 'TechStart Milano SRL',
                    'email': '<EMAIL>',
                    'phone': '+39 02 12345678',
                    'address': 'Via Brera 15, 20121 Milano (MI)',
                    'vat_number': '*************',
                    'fiscal_code': '12345678901',
                    'industry': 'Software Development',
                    'notes': 'Startup tecnologica specializzata in AI'
                },
                {
                    'name': 'Digital Innovations SpA',
                    'email': '<EMAIL>',
                    'phone': '+39 06 87654321',
                    'address': 'Corso Vittorio Emanuele II 78, 00186 Roma (RM)',
                    'vat_number': '*************',
                    'fiscal_code': '98765432109',
                    'industry': 'Digital Transformation',
                    'notes': 'Azienda leader nella trasformazione digitale'
                },
                {
                    'name': 'GreenTech Solutions SRL',
                    'email': '<EMAIL>',
                    'phone': '+39 011 5551234',
                    'address': 'Via Po 47, 10124 Torino (TO)',
                    'vat_number': '*************',
                    'fiscal_code': '11223344556',
                    'industry': 'CleanTech',
                    'notes': 'Soluzioni tecnologiche per energia rinnovabile'
                },
                {
                    'name': 'Fashion Forward SRL',
                    'email': '<EMAIL>',
                    'phone': '+39 ***********',
                    'address': 'Via del Corso 123, 50122 Firenze (FI)',
                    'vat_number': '*************',
                    'fiscal_code': '66778899001',
                    'industry': 'Fashion & Retail',
                    'notes': 'Brand di moda sostenibile'
                },
                {
                    'name': 'HealthTech Napoli SpA',
                    'email': '<EMAIL>',
                    'phone': '+39 ***********',
                    'address': 'Via Chiaia 88, 80132 Napoli (NA)',
                    'vat_number': '*************',
                    'fiscal_code': '33445566778',
                    'industry': 'HealthTech',
                    'notes': 'Innovazione tecnologica nel settore sanitario'
                }
            ]
            
            created_clients = []
            for client_data in clients_data:
                # Verifica se il cliente esiste già (per email)
                existing = db.session.execute(
                    sa.text("SELECT id FROM client WHERE email = :email"),
                    {"email": client_data['email']}
                ).fetchone()
                
                if not existing:
                    insert_sql = """
                    INSERT INTO client (name, email, phone, address, vat_number, fiscal_code, industry, notes, created_at)
                    VALUES (:name, :email, :phone, :address, :vat_number, :fiscal_code, :industry, :notes, CURRENT_TIMESTAMP)
                    RETURNING id
                    """
                    result = db.session.execute(sa.text(insert_sql), client_data)
                    client_id = result.fetchone()[0]
                    created_clients.append(client_id)
                    print(f"  ✅ Cliente '{client_data['name']}' creato (ID: {client_id})")
                else:
                    created_clients.append(existing[0])
                    print(f"  ⚠️  Cliente '{client_data['name']}' già esistente")
            
            db.session.commit()
            print(f"🎉 Clienti seed completati! ({len(created_clients)} clienti disponibili)")
            return created_clients
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante la creazione clienti seed: {e}")
            return []

def create_seed_contracts():
    """Crea contratti di esempio per i clienti"""
    with app.app_context():
        try:
            print("📝 Creazione contratti seed...")
            
            # Ottieni clienti esistenti
            clients = db.session.execute(sa.text("SELECT id, name FROM client LIMIT 5")).fetchall()
            if not clients:
                print("  ❌ Nessun cliente trovato. Crea prima i clienti.")
                return []
            
            contracts_data = [
                {
                    'client_id': clients[0][0],
                    'contract_number': 'CTR-2024-001',
                    'title': 'Sviluppo Piattaforma E-commerce',
                    'hourly_rate': Decimal('75.00'),
                    'start_date': date(2024, 1, 15),
                    'end_date': date(2024, 12, 31),
                    'status': 'active',
                    'budget': Decimal('50000.00'),
                    'description': 'Sviluppo completo piattaforma e-commerce con integrazione pagamenti'
                },
                {
                    'client_id': clients[1][0],
                    'contract_number': 'CTR-2024-002',
                    'title': 'Consulenza Digital Transformation',
                    'hourly_rate': Decimal('95.00'),
                    'start_date': date(2024, 2, 1),
                    'end_date': date(2024, 11, 30),
                    'status': 'active',
                    'budget': Decimal('75000.00'),
                    'description': 'Consulenza strategica per trasformazione digitale processi aziendali'
                },
                {
                    'client_id': clients[2][0],
                    'contract_number': 'CTR-2024-003',
                    'title': 'App Mobile GreenTech',
                    'hourly_rate': Decimal('65.00'),
                    'start_date': date(2024, 3, 1),
                    'end_date': date(2024, 10, 31),
                    'status': 'active',
                    'budget': Decimal('35000.00'),
                    'description': 'Sviluppo applicazione mobile per monitoraggio consumi energetici'
                },
                {
                    'client_id': clients[3][0],
                    'contract_number': 'CTR-2024-004',
                    'title': 'Piattaforma Fashion E-commerce',
                    'hourly_rate': Decimal('70.00'),
                    'start_date': date(2024, 2, 15),
                    'end_date': date(2024, 12, 15),
                    'status': 'active',
                    'budget': Decimal('42000.00'),
                    'description': 'E-commerce specializzato per moda sostenibile con AR try-on'
                },
                {
                    'client_id': clients[4][0],
                    'contract_number': 'CTR-2024-005',
                    'title': 'Sistema Gestione Pazienti',
                    'hourly_rate': Decimal('85.00'),
                    'start_date': date(2024, 1, 20),
                    'end_date': date(2024, 9, 30),
                    'status': 'active',
                    'budget': Decimal('60000.00'),
                    'description': 'Sistema integrato per gestione pazienti e cartelle cliniche digitali'
                }
            ]
            
            created_contracts = []
            for contract_data in contracts_data:
                # Verifica se il contratto esiste già
                existing = db.session.execute(
                    sa.text("SELECT id FROM contract WHERE contract_number = :contract_number"),
                    {"contract_number": contract_data['contract_number']}
                ).fetchone()
                
                if not existing:
                    insert_sql = """
                    INSERT INTO contract (
                        client_id, contract_number, title, hourly_rate, start_date, end_date, 
                        status, budget, description, created_at
                    ) VALUES (
                        :client_id, :contract_number, :title, :hourly_rate, :start_date, :end_date,
                        :status, :budget, :description, CURRENT_TIMESTAMP
                    ) RETURNING id
                    """
                    result = db.session.execute(sa.text(insert_sql), contract_data)
                    contract_id = result.fetchone()[0]
                    created_contracts.append(contract_id)
                    print(f"  ✅ Contratto '{contract_data['contract_number']}' creato (ID: {contract_id})")
                else:
                    created_contracts.append(existing[0])
                    print(f"  ⚠️  Contratto '{contract_data['contract_number']}' già esistente")
            
            db.session.commit()
            print(f"🎉 Contratti seed completati! ({len(created_contracts)} contratti disponibili)")
            return created_contracts
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante la creazione contratti seed: {e}")
            return []

def create_seed_projects():
    """Crea progetti collegati ai contratti"""
    with app.app_context():
        try:
            print("🚀 Creazione progetti seed...")
            
            # Ottieni contratti esistenti
            contracts = db.session.execute(sa.text("SELECT id, title, client_id FROM contract LIMIT 5")).fetchall()
            if not contracts:
                print("  ❌ Nessun contratto trovato. Crea prima i contratti.")
                return []
            
            projects_data = [
                # Progetti per primo contratto (E-commerce)
                {
                    'contract_id': contracts[0][0],
                    'name': 'Frontend E-commerce',
                    'description': 'Sviluppo interfaccia utente e user experience',
                    'status': 'active',
                    'start_date': date(2024, 1, 15),
                    'estimated_hours': 400
                },
                {
                    'contract_id': contracts[0][0],
                    'name': 'Backend E-commerce',
                    'description': 'API, database e integrazioni pagamenti',
                    'status': 'active',
                    'start_date': date(2024, 2, 1),
                    'estimated_hours': 350
                },
                # Progetti per secondo contratto (Digital Transformation)
                {
                    'contract_id': contracts[1][0],
                    'name': 'Analisi Processi Aziendali',
                    'description': 'Assessment e mappatura processi esistenti',
                    'status': 'completed',
                    'start_date': date(2024, 2, 1),
                    'estimated_hours': 200
                },
                {
                    'contract_id': contracts[1][0],
                    'name': 'Implementazione CRM',
                    'description': 'Setup e customizzazione sistema CRM',
                    'status': 'active',
                    'start_date': date(2024, 3, 15),
                    'estimated_hours': 300
                },
                # Progetti per terzo contratto (App Mobile)
                {
                    'contract_id': contracts[2][0],
                    'name': 'App Mobile GreenTech',
                    'description': 'Sviluppo app iOS/Android per monitoraggio energia',
                    'status': 'active',
                    'start_date': date(2024, 3, 1),
                    'estimated_hours': 500
                },
                # Progetti per quarto contratto (Fashion)
                {
                    'contract_id': contracts[3][0],
                    'name': 'Fashion E-commerce Platform',
                    'description': 'Piattaforma e-commerce con tecnologia AR',
                    'status': 'active',
                    'start_date': date(2024, 2, 15),
                    'estimated_hours': 600
                },
                # Progetti per quinto contratto (HealthTech)
                {
                    'contract_id': contracts[4][0],
                    'name': 'Sistema Gestione Pazienti',
                    'description': 'Backend per gestione cartelle cliniche',
                    'status': 'active',
                    'start_date': date(2024, 1, 20),
                    'estimated_hours': 450
                },
                {
                    'contract_id': contracts[4][0],
                    'name': 'Dashboard Medici',
                    'description': 'Interfaccia per professionisti sanitari',
                    'status': 'active',
                    'start_date': date(2024, 3, 1),
                    'estimated_hours': 250
                }
            ]
            
            created_projects = []
            for project_data in projects_data:
                # Verifica se il progetto esiste già
                existing = db.session.execute(
                    sa.text("SELECT id FROM project WHERE name = :name AND contract_id = :contract_id"),
                    {"name": project_data['name'], "contract_id": project_data['contract_id']}
                ).fetchone()
                
                if not existing:
                    insert_sql = """
                    INSERT INTO project (
                        contract_id, name, description, status, start_date, estimated_hours, created_at
                    ) VALUES (
                        :contract_id, :name, :description, :status, :start_date, :estimated_hours, CURRENT_TIMESTAMP
                    ) RETURNING id
                    """
                    result = db.session.execute(sa.text(insert_sql), project_data)
                    project_id = result.fetchone()[0]
                    created_projects.append(project_id)
                    print(f"  ✅ Progetto '{project_data['name']}' creato (ID: {project_id})")
                else:
                    created_projects.append(existing[0])
                    print(f"  ⚠️  Progetto '{project_data['name']}' già esistente")
            
            db.session.commit()
            print(f"🎉 Progetti seed completati! ({len(created_projects)} progetti disponibili)")
            return created_projects
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante la creazione progetti seed: {e}")
            return []

def create_seed_timesheet_entries():
    """Crea timesheet entries fatturabili per i progetti"""
    with app.app_context():
        try:
            print("⏰ Creazione timesheet entries seed...")
            
            # Ottieni utenti e progetti
            users = db.session.execute(sa.text("SELECT id, first_name, last_name FROM \"user\" WHERE is_active = TRUE LIMIT 8")).fetchall()
            projects = db.session.execute(sa.text("SELECT id, name, contract_id FROM project")).fetchall()
            
            if not users or not projects:
                print("  ❌ Nessun utente o progetto trovato.")
                return False
            
            print(f"  📊 Generazione timesheet per {len(users)} utenti su {len(projects)} progetti")
            
            # Genera entries per ultimi 2 mesi
            start_date = date.today() - timedelta(days=60)
            end_date = date.today() - timedelta(days=5)  # Fino a 5 giorni fa
            
            created_entries = 0
            current_date = start_date
            
            while current_date <= end_date:
                # Solo giorni lavorativi
                if current_date.weekday() < 5:
                    for user in users:
                        user_id = user[0]
                        
                        # 70% probabilità che l'utente lavori
                        if random.random() < 0.7:
                            # Scegli 1-2 progetti casuali
                            num_projects = random.choice([1, 1, 2])
                            selected_projects = random.sample(projects, min(num_projects, len(projects)))
                            
                            total_daily_hours = random.uniform(6, 8.5)
                            hours_per_project = total_daily_hours / len(selected_projects)
                            
                            for project in selected_projects:
                                project_id = project[0]
                                contract_id = project[2]
                                
                                # Ore per questo progetto
                                hours = round(hours_per_project * random.uniform(0.8, 1.2), 1)
                                
                                if hours >= 0.5:
                                    # Ottieni tariffa dal contratto
                                    contract_rate = db.session.execute(
                                        sa.text("SELECT hourly_rate FROM contract WHERE id = :contract_id"),
                                        {"contract_id": contract_id}
                                    ).fetchone()
                                    
                                    billing_rate = float(contract_rate[0]) if contract_rate and contract_rate[0] else 50.0
                                    
                                    # Varia la tariffa leggermente per simulare seniority diverse
                                    if random.random() < 0.3:  # 30% senior rates
                                        billing_rate *= random.uniform(1.2, 1.5)
                                    elif random.random() < 0.2:  # 20% junior rates  
                                        billing_rate *= random.uniform(0.7, 0.9)
                                    
                                    billing_rate = round(billing_rate, 2)
                                    
                                    descriptions = [
                                        f"Sviluppo feature per {project[1]}",
                                        f"Bugfix e ottimizzazioni {project[1]}",
                                        f"Testing e documentazione {project[1]}",
                                        f"Meeting e planning {project[1]}",
                                        f"Code review e refactor {project[1]}",
                                        f"Implementazione API {project[1]}",
                                        f"UI/UX development {project[1]}"
                                    ]
                                    
                                    description = random.choice(descriptions)
                                    
                                    # 90% fatturabili, 10% non fatturabili
                                    billable = random.random() < 0.9
                                    billing_status = 'unbilled' if billable else 'non-billable'
                                    
                                    entry = {
                                        'user_id': user_id,
                                        'project_id': project_id,
                                        'date': current_date,
                                        'hours': hours,
                                        'description': description,
                                        'billable': billable,
                                        'billing_status': billing_status,
                                        'billing_rate': billing_rate if billable else None
                                    }
                                    
                                    insert_sql = """
                                    INSERT INTO timesheet_entry (
                                        user_id, project_id, date, hours, description, 
                                        billable, billing_status, billing_rate, created_at
                                    ) VALUES (
                                        :user_id, :project_id, :date, :hours, :description,
                                        :billable, :billing_status, :billing_rate, CURRENT_TIMESTAMP
                                    )
                                    """
                                    db.session.execute(sa.text(insert_sql), entry)
                                    created_entries += 1
                
                current_date += timedelta(days=1)
            
            db.session.commit()
            print(f"  ✅ Creati {created_entries} timesheet entries")
            
            # Statistiche
            stats = db.session.execute(sa.text("""
                SELECT 
                    COUNT(*) as total_entries,
                    SUM(hours) as total_hours,
                    SUM(CASE WHEN billable = TRUE THEN hours ELSE 0 END) as billable_hours,
                    SUM(CASE WHEN billable = TRUE THEN hours * billing_rate ELSE 0 END) as total_amount
                FROM timesheet_entry
                WHERE billing_status = 'unbilled'
            """)).fetchone()
            
            print(f"  📊 Statistiche timesheet fatturabili:")
            print(f"     - {stats[0]} entries totali")
            print(f"     - {stats[1]:.1f} ore totali")
            print(f"     - {stats[2]:.1f} ore fatturabili")
            print(f"     - €{stats[3]:.2f} importo totale non fatturato")
            
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante la creazione timesheet entries: {e}")
            return False

def create_sample_pre_invoices():
    """Crea alcune pre-fatture di esempio"""
    with app.app_context():
        try:
            print("💰 Creazione pre-fatture di esempio...")
            
            # Ottieni clienti e utente per created_by
            clients = db.session.execute(sa.text("SELECT id, name FROM client LIMIT 3")).fetchall()
            admin_user = db.session.execute(sa.text("SELECT id FROM \"user\" WHERE role = 'admin' LIMIT 1")).fetchone()
            
            if not clients or not admin_user:
                print("  ❌ Nessun cliente o admin trovato.")
                return False
            
            created_by = admin_user[0]
            
            # Crea 3 pre-fatture di esempio
            pre_invoices_data = [
                {
                    'client_id': clients[0][0],
                    'pre_invoice_number': 'PRE-2024-001',
                    'billing_period_start': date(2024, 4, 1),
                    'billing_period_end': date(2024, 4, 30),
                    'status': 'draft',
                    'subtotal': Decimal('3500.00'),
                    'vat_rate': Decimal('22.0'),
                    'vat_amount': Decimal('770.00'),
                    'retention_rate': Decimal('20.0'),
                    'retention_amount': Decimal('700.00'),
                    'total_amount': Decimal('3570.00'),
                    'notes': 'Pre-fattura aprile 2024 - Sviluppo E-commerce'
                },
                {
                    'client_id': clients[1][0],
                    'pre_invoice_number': 'PRE-2024-002',
                    'billing_period_start': date(2024, 5, 1),
                    'billing_period_end': date(2024, 5, 31),
                    'status': 'ready',
                    'subtotal': Decimal('4750.00'),
                    'vat_rate': Decimal('22.0'),
                    'vat_amount': Decimal('1045.00'),
                    'retention_rate': Decimal('20.0'),
                    'retention_amount': Decimal('950.00'),
                    'total_amount': Decimal('4845.00'),
                    'notes': 'Pre-fattura maggio 2024 - Consulenza Digital Transformation'
                },
                {
                    'client_id': clients[2][0],
                    'pre_invoice_number': 'PRE-2024-003',
                    'billing_period_start': date(2024, 5, 15),
                    'billing_period_end': date(2024, 6, 14),
                    'status': 'sent_external',
                    'subtotal': Decimal('2890.00'),
                    'vat_rate': Decimal('22.0'),
                    'vat_amount': Decimal('635.80'),
                    'retention_rate': Decimal('20.0'),
                    'retention_amount': Decimal('578.00'),
                    'total_amount': Decimal('2947.80'),
                    'external_invoice_id': 'FIC-2024-0123',
                    'external_status': 'sent',
                    'notes': 'Pre-fattura App Mobile GreenTech'
                }
            ]
            
            created_pre_invoices = []
            for pi_data in pre_invoices_data:
                # Verifica se esiste già
                existing = db.session.execute(
                    sa.text("SELECT id FROM pre_invoices WHERE pre_invoice_number = :number"),
                    {"number": pi_data['pre_invoice_number']}
                ).fetchone()
                
                if not existing:
                    pi_data['created_by'] = created_by
                    pi_data['generated_date'] = date.today()
                    
                    insert_sql = """
                    INSERT INTO pre_invoices (
                        client_id, pre_invoice_number, billing_period_start, billing_period_end,
                        generated_date, status, subtotal, vat_rate, vat_amount, retention_rate,
                        retention_amount, total_amount, external_invoice_id, external_status,
                        notes, created_by, created_at, updated_at
                    ) VALUES (
                        :client_id, :pre_invoice_number, :billing_period_start, :billing_period_end,
                        :generated_date, :status, :subtotal, :vat_rate, :vat_amount, :retention_rate,
                        :retention_amount, :total_amount, :external_invoice_id, :external_status,
                        :notes, :created_by, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
                    ) RETURNING id
                    """
                    result = db.session.execute(sa.text(insert_sql), pi_data)
                    pre_invoice_id = result.fetchone()[0]
                    created_pre_invoices.append(pre_invoice_id)
                    
                    # Crea righe di esempio per questa pre-fattura
                    lines_data = [
                        {
                            'pre_invoice_id': pre_invoice_id,
                            'description': f'Servizi professionali - {pi_data["notes"].split("-")[-1].strip()}',
                            'total_hours': Decimal('50.0'),
                            'hourly_rate': pi_data['subtotal'] / Decimal('50.0'),
                            'total_amount': pi_data['subtotal']
                        }
                    ]
                    
                    for line_data in lines_data:
                        insert_line_sql = """
                        INSERT INTO pre_invoice_lines (
                            pre_invoice_id, description, total_hours, hourly_rate, total_amount, created_at
                        ) VALUES (
                            :pre_invoice_id, :description, :total_hours, :hourly_rate, :total_amount, CURRENT_TIMESTAMP
                        )
                        """
                        db.session.execute(sa.text(insert_line_sql), line_data)
                    
                    print(f"  ✅ Pre-fattura '{pi_data['pre_invoice_number']}' creata")
                else:
                    print(f"  ⚠️  Pre-fattura '{pi_data['pre_invoice_number']}' già esistente")
            
            db.session.commit()
            print(f"🎉 Pre-fatture di esempio create! ({len(created_pre_invoices)} nuove)")
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante la creazione pre-fatture: {e}")
            return False

def run_complete_seed():
    """Esegue il seed completo del sistema di fatturazione"""
    print("🌱 SEED COMPLETO SISTEMA PREFATTURAZIONE")
    print("=" * 60)
    
    success = True
    
    # 1. Crea clienti
    clients = create_seed_clients()
    if not clients:
        success = False
    
    # 2. Crea contratti
    if success:
        contracts = create_seed_contracts()
        if not contracts:
            success = False
    
    # 3. Crea progetti
    if success:
        projects = create_seed_projects()
        if not projects:
            success = False
    
    # 4. Crea timesheet entries
    if success:
        if not create_seed_timesheet_entries():
            success = False
    
    # 5. Crea pre-fatture di esempio
    if success:
        if not create_sample_pre_invoices():
            success = False
    
    if success:
        print("\n🎉 SEED SISTEMA PREFATTURAZIONE COMPLETATO!")
        print("📊 Dati creati:")
        print("   - ✅ Clienti con dati fiscali italiani")
        print("   - ✅ Contratti con tariffe orarie")
        print("   - ✅ Progetti collegati ai contratti")
        print("   - ✅ Timesheet entries fatturabili (2 mesi)")
        print("   - ✅ Pre-fatture di esempio con calcoli fiscali")
        print("\n📋 Sistema pronto per:")
        print("   - Generazione nuove pre-fatture")
        print("   - Dashboard fatturazione con dati reali")
        print("   - Test API pre-fatturazione")
    else:
        print("\n❌ SEED FALLITO - Controlla gli errori sopra")
    
    return success

if __name__ == "__main__":
    run_complete_seed()