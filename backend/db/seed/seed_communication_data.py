#!/usr/bin/env python3
"""
Script per popolare il database con dati di test per il modulo comunicazione.
Uso: python seed_communication_data.py [--clear]
"""

import sys
import argparse
from datetime import datetime, timedelta
from werkzeug.security import generate_password_hash

# Aggiungi il path del progetto
sys.path.insert(0, '/home/<USER>/workspace/backend')

from app import create_app, db
from models import (
    User, ForumTopic, ForumComment, Poll, PollOption, PollVote,
    DirectMessage, CommunicationReaction, CompanyEvent, 
    CompanyEventRegistration, News
)


def clear_communication_data():
    """Cancella tutti i dati del modulo comunicazione."""
    print("🗑️  Cancellazione dati comunicazione esistenti...")

    # Ordine importante per rispettare le foreign key
    db.session.query(PollVote).delete()
    db.session.query(PollOption).delete()
    db.session.query(Poll).delete()
    db.session.query(ForumComment).delete()
    db.session.query(ForumTopic).delete()
    db.session.query(DirectMessage).delete()
    db.session.query(CommunicationReaction).delete()
    db.session.query(CompanyEventRegistration).delete()
    db.session.query(CompanyEvent).delete()
    db.session.query(News).delete()

    db.session.commit()
    print("✅ Dati comunicazione puliti!")


def ensure_users():
    """Assicura che esistano utenti per i test."""
    if User.query.count() == 0:
        print("👥 Creazione utenti di base per i test...")
        
        users_data = [
            {
                'username': 'admin',
                'email': '<EMAIL>',
                'first_name': 'Mario',
                'last_name': 'Rossi',
                'role': 'admin'
            },
            {
                'username': 'manager',
                'email': '<EMAIL>',
                'first_name': 'Giulia',
                'last_name': 'Bianchi',
                'role': 'manager'
            },
            {
                'username': 'employee1',
                'email': '<EMAIL>',
                'first_name': 'Luca',
                'last_name': 'Verdi',
                'role': 'employee'
            },
            {
                'username': 'employee2',
                'email': '<EMAIL>',
                'first_name': 'Sara',
                'last_name': 'Neri',
                'role': 'employee'
            },
            {
                'username': 'employee3',
                'email': '<EMAIL>',
                'first_name': 'Marco',
                'last_name': 'Ferrari',
                'role': 'employee'
            }
        ]

        for user_data in users_data:
            user = User(
                username=user_data['username'],
                email=user_data['email'],
                first_name=user_data['first_name'],
                last_name=user_data['last_name'],
                password_hash=generate_password_hash('password123'),
                role=user_data['role'],
                is_active=True,
                hire_date=datetime.utcnow() - timedelta(days=365)
            )
            db.session.add(user)
        
        db.session.commit()
        print("✅ Utenti di base creati!")


def seed_forum_data():
    """Crea dati di test per il forum."""
    print("💬 Creazione dati forum...")
    
    users = User.query.all()
    if len(users) < 3:
        print("❌ Servono almeno 3 utenti per i dati forum")
        return

    admin_user = User.query.filter_by(role='admin').first()
    manager_user = User.query.filter_by(role='manager').first()
    employee_users = User.query.filter_by(role='employee').all()

    # Topic del forum
    topics_data = [
        {
            'title': 'Benvenuti nel nuovo forum aziendale!',
            'content': 'Ciao a tutti! Questo è il nuovo spazio per le discussioni aziendali. Sentitevi liberi di condividere idee, fare domande e collaborare.',
            'author': admin_user,
            'category': 'annunci',
            'is_pinned': True,
            'views_count': 45
        },
        {
            'title': 'Proposte per migliorare il workflow di sviluppo',
            'content': 'Aprendo questa discussione per raccogliere feedback e proposte su come ottimizzare i nostri processi di sviluppo. Quali tools vi piacciono di più?',
            'author': manager_user,
            'category': 'sviluppo',
            'views_count': 23
        },
        {
            'title': 'Organizzazione evento team building Q1',
            'content': 'È arrivato il momento di organizzare il nostro team building trimestrale! Raccogliamo idee per attività divertenti e coinvolgenti.',
            'author': employee_users[0] if employee_users else admin_user,
            'category': 'eventi',
            'views_count': 31
        },
        {
            'title': 'Discussione su nuove tecnologie da adottare',
            'content': 'Che ne pensate di valutare React Server Components per i nostri progetti futuri? Vediamo pro e contro insieme.',
            'author': employee_users[1] if len(employee_users) > 1 else admin_user,
            'category': 'tecnologia',
            'views_count': 18
        },
        {
            'title': 'Feedback sul nuovo sistema di timesheet',
            'content': 'Come vi trovate con il nuovo sistema di tracciamento ore? Condividete i vostri feedback per migliorarlo.',
            'author': manager_user,
            'category': 'feedback',
            'views_count': 27
        }
    ]

    created_topics = []
    for topic_data in topics_data:
        topic = ForumTopic(
            title=topic_data['title'],
            description=topic_data['content'],
            author_id=topic_data['author'].id,
            category=topic_data['category'],
            is_pinned=topic_data.get('is_pinned', False),
            view_count=topic_data.get('views_count', 0),
            created_at=datetime.utcnow() - timedelta(days=len(created_topics) * 2)
        )
        db.session.add(topic)
        created_topics.append(topic)

    db.session.flush()  # Per ottenere gli ID

    # Commenti sui topic
    comments_data = [
        {
            'topic': created_topics[0],
            'author': employee_users[0] if employee_users else admin_user,
            'content': 'Fantastico! Era ora che avessimo uno spazio dedicato alle discussioni.'
        },
        {
            'topic': created_topics[0],
            'author': employee_users[1] if len(employee_users) > 1 else admin_user,
            'content': 'Concordo! Questo ci aiuterà molto a coordinare meglio il lavoro.'
        },
        {
            'topic': created_topics[1],
            'author': admin_user,
            'content': 'Ottima iniziativa! Personalmente suggerisco di valutare anche GitHub Actions per CI/CD.'
        },
        {
            'topic': created_topics[1],
            'author': employee_users[0] if employee_users else manager_user,
            'content': 'Sono d\'accordo sui GitHub Actions. Anche Docker Compose potrebbe essere utile per lo sviluppo locale.'
        },
        {
            'topic': created_topics[2],
            'author': manager_user,
            'content': 'Che ne pensate di una giornata di paintball o escape room?'
        },
        {
            'topic': created_topics[3],
            'author': admin_user,
            'content': 'I Server Components sono interessanti, ma valuterei prima la curva di apprendimento del team.'
        }
    ]

    for comment_data in comments_data:
        comment = ForumComment(
            topic_id=comment_data['topic'].id,
            author_id=comment_data['author'].id,
            content=comment_data['content'],
            created_at=datetime.utcnow() - timedelta(hours=len(comments_data))
        )
        db.session.add(comment)

    db.session.commit()
    print(f"✅ Creati {len(created_topics)} topic e {len(comments_data)} commenti!")


def seed_polls_data():
    """Crea dati di test per i sondaggi."""
    print("📊 Creazione dati sondaggi...")
    
    users = User.query.all()
    if len(users) < 3:
        print("❌ Servono almeno 3 utenti per i dati sondaggi")
        return

    admin_user = User.query.filter_by(role='admin').first()
    manager_user = User.query.filter_by(role='manager').first()
    employee_users = User.query.filter_by(role='employee').all()

    # Sondaggi
    polls_data = [
        {
            'title': 'Preferenza orario riunioni settimanali',
            'description': 'Quale orario preferite per le riunioni team settimanali?',
            'creator': manager_user,
            'is_active': True,
            'end_date': datetime.utcnow() + timedelta(days=7),
            'options': ['9:00-10:00', '10:00-11:00', '14:00-15:00', '15:00-16:00']
        },
        {
            'title': 'Scelta tecnologia frontend per nuovo progetto',
            'description': 'Su quale framework frontend dovremmo puntare per il prossimo progetto?',
            'creator': admin_user,
            'is_active': True,
            'multiple_choice': False,
            'end_date': datetime.utcnow() + timedelta(days=14),
            'options': ['React', 'Vue.js', 'Angular', 'Svelte']
        },
        {
            'title': 'Valutazione evento team building',
            'description': 'Come avete trovato l\'ultimo evento di team building?',
            'creator': manager_user,
            'is_active': False,
            'end_date': datetime.utcnow() - timedelta(days=5),
            'options': ['Eccellente', 'Buono', 'Sufficiente', 'Da migliorare']
        },
        {
            'title': 'Benefit aziendali desiderati',
            'description': 'Quali benefit vorreste vedere implementati in azienda?',
            'creator': admin_user,
            'is_active': True,
            'multiple_choice': True,
            'end_date': datetime.utcnow() + timedelta(days=30),
            'options': ['Buoni pasto', 'Palestra', 'Corsi di formazione', 'Smart working', 'Assicurazione sanitaria']
        }
    ]

    created_polls = []
    for poll_data in polls_data:
        poll = Poll(
            title=poll_data['title'],
            description=poll_data['description'],
            author_id=poll_data['creator'].id,
            is_active=poll_data['is_active'],
            multiple_choice=poll_data.get('multiple_choice', False),
            expires_at=poll_data['end_date'],
            created_at=datetime.utcnow() - timedelta(days=len(created_polls) * 3)
        )
        db.session.add(poll)
        db.session.flush()  # Per ottenere l'ID

        # Aggiungi opzioni
        for i, option_text in enumerate(poll_data['options']):
            option = PollOption(
                poll_id=poll.id,
                option_text=option_text
            )
            db.session.add(option)

        created_polls.append(poll)

    db.session.flush()

    # Voti sui sondaggi
    import random
    for poll in created_polls:
        options = PollOption.query.filter_by(poll_id=poll.id).all()
        
        # Simula voti di alcuni utenti
        voters = random.sample(users, min(len(users), random.randint(2, 4)))
        
        for voter in voters:
            if poll.multiple_choice:
                # Per scelta multipla, vota su 1-3 opzioni
                selected_options = random.sample(options, random.randint(1, min(3, len(options))))
            else:
                # Per scelta singola, vota solo una opzione
                selected_options = [random.choice(options)]
            
            for option in selected_options:
                vote = PollVote(
                    poll_id=poll.id,
                    option_id=option.id,
                    user_id=voter.id,
                    created_at=datetime.utcnow() - timedelta(hours=random.randint(1, 72))
                )
                db.session.add(vote)

    db.session.commit()
    print(f"✅ Creati {len(created_polls)} sondaggi con opzioni e voti!")


def seed_messages_data():
    """Crea dati di test per i messaggi diretti."""
    print("💌 Creazione dati messaggi...")
    
    users = User.query.all()
    if len(users) < 3:
        print("❌ Servono almeno 3 utenti per i dati messaggi")
        return

    admin_user = User.query.filter_by(role='admin').first()
    manager_user = User.query.filter_by(role='manager').first()
    employee_users = User.query.filter_by(role='employee').all()

    # Messaggi diretti
    messages_data = [
        {
            'sender': admin_user,
            'recipient': manager_user,
            'subject': 'Aggiornamento budget Q1',
            'content': 'Ciao Giulia, puoi inviarmi l\'aggiornamento sul budget del primo trimestre? Grazie!',
            'is_read': True
        },
        {
            'sender': manager_user,
            'recipient': admin_user,
            'subject': 'Re: Aggiornamento budget Q1',
            'content': 'Ciao Mario, certo! Ti invio il file entro domani. Il budget è in linea con le previsioni.',
            'is_read': False
        },
        {
            'sender': employee_users[0] if employee_users else admin_user,
            'recipient': manager_user,
            'subject': 'Richiesta permesso',
            'content': 'Buongiorno, volevo chiedere un permesso per venerdì pomeriggio per motivi personali.',
            'is_read': True
        },
        {
            'sender': manager_user,
            'recipient': employee_users[0] if employee_users else admin_user,
            'subject': 'Re: Richiesta permesso',
            'content': 'Nessun problema Luca, il permesso è approvato. Ricordati di segnarlo nel sistema.',
            'is_read': True
        },
        {
            'sender': admin_user,
            'recipient': employee_users[1] if len(employee_users) > 1 else manager_user,
            'subject': 'Feedback progetto cliente X',
            'content': 'Ottimo lavoro sul progetto del cliente X! Il feedback è molto positivo.',
            'is_read': False
        }
    ]

    for i, msg_data in enumerate(messages_data):
        message = DirectMessage(
            sender_id=msg_data['sender'].id,
            recipient_id=msg_data['recipient'].id,
            message=f"{msg_data['subject']}: {msg_data['content']}",
            is_read=msg_data['is_read'],
            created_at=datetime.utcnow() - timedelta(days=i, hours=i*2)
        )
        if msg_data['is_read']:
            message.read_at = message.created_at + timedelta(minutes=30)
        
        db.session.add(message)

    db.session.commit()
    print(f"✅ Creati {len(messages_data)} messaggi diretti!")


def seed_events_data():
    """Crea dati di test per gli eventi aziendali."""
    print("📅 Creazione dati eventi...")
    
    users = User.query.all()
    if len(users) < 3:
        print("❌ Servono almeno 3 utenti per i dati eventi")
        return

    admin_user = User.query.filter_by(role='admin').first()
    manager_user = User.query.filter_by(role='manager').first()
    employee_users = User.query.filter_by(role='employee').all()

    # Eventi aziendali
    events_data = [
        {
            'title': 'All Hands Meeting Q1',
            'description': 'Riunione trimestrale con tutto il team per fare il punto sui risultati e gli obiettivi.',
            'start_time': datetime.utcnow() + timedelta(days=7, hours=9),
            'end_time': datetime.utcnow() + timedelta(days=7, hours=11),
            'location': 'Sala conferenze principale',
            'event_type': 'meeting',
            'creator': admin_user,
            'is_company_wide': True,
            'max_participants': 50,
            'registration_required': True,
            'registration_deadline': datetime.utcnow() + timedelta(days=5)
        },
        {
            'title': 'Workshop: Introduzione a Docker',
            'description': 'Workshop tecnico per imparare i fondamenti di Docker e la containerizzazione.',
            'start_time': datetime.utcnow() + timedelta(days=14, hours=14),
            'end_time': datetime.utcnow() + timedelta(days=14, hours=17),
            'location': 'Lab informatico',
            'event_type': 'workshop',
            'creator': manager_user,
            'is_company_wide': False,
            'max_participants': 12,
            'registration_required': True,
            'registration_deadline': datetime.utcnow() + timedelta(days=10)
        },
        {
            'title': 'Happy Hour di fine mese',
            'description': 'Aperitivo informale per festeggiare i risultati del mese e socializzare.',
            'start_time': datetime.utcnow() + timedelta(days=21, hours=18),
            'end_time': datetime.utcnow() + timedelta(days=21, hours=21),
            'location': 'Terrazza ufficio',
            'event_type': 'social',
            'creator': employee_users[0] if employee_users else admin_user,
            'is_company_wide': True,
            'max_participants': None,
            'registration_required': False
        },
        {
            'title': 'Formazione Sicurezza sul Lavoro',
            'description': 'Corso obbligatorio annuale sulla sicurezza e prevenzione negli ambienti di lavoro.',
            'start_time': datetime.utcnow() + timedelta(days=30, hours=9),
            'end_time': datetime.utcnow() + timedelta(days=30, hours=13),
            'location': 'Aula formazione',
            'event_type': 'training',
            'creator': admin_user,
            'is_company_wide': True,
            'max_participants': 25,
            'registration_required': True,
            'registration_deadline': datetime.utcnow() + timedelta(days=25)
        }
    ]

    created_events = []
    for event_data in events_data:
        event = CompanyEvent(
            title=event_data['title'],
            description=event_data['description'],
            start_time=event_data['start_time'],
            end_time=event_data['end_time'],
            location=event_data['location'],
            event_type=event_data['event_type'],
            created_by=event_data['creator'].id,
            is_company_wide=event_data['is_company_wide'],
            max_participants=event_data['max_participants'],
            registration_required=event_data['registration_required'],
            registration_deadline=event_data.get('registration_deadline'),
            is_public=True,
            allow_comments=True
        )
        db.session.add(event)
        created_events.append(event)

    db.session.flush()

    # Registrazioni agli eventi
    import random
    for event in created_events:
        if event.registration_required:
            # Simula registrazioni di alcuni utenti
            registrants = random.sample(users, min(len(users), random.randint(2, 5)))
            
            for user in registrants:
                registration = CompanyEventRegistration(
                    event_id=event.id,
                    user_id=user.id,
                    registered_at=datetime.utcnow() - timedelta(days=random.randint(1, 10)),
                    status='registered'
                )
                db.session.add(registration)

    db.session.commit()
    print(f"✅ Creati {len(created_events)} eventi con registrazioni!")


def seed_news_data():
    """Crea dati di test per le news aziendali."""
    print("📰 Creazione dati news...")
    
    admin_user = User.query.filter_by(role='admin').first()
    manager_user = User.query.filter_by(role='manager').first()
    
    if not admin_user:
        print("❌ Serve almeno un utente admin per le news")
        return

    # News aziendali
    news_data = [
        {
            'title': 'Benvenuti nel nuovo sistema DatPortal!',
            'content': '''Siamo entusiasti di annunciare il lancio del nuovo sistema DatPortal! 
            
Questa piattaforma integrata ci permetterà di gestire meglio tutti i nostri processi aziendali, dalla gestione del personale ai progetti, dal CRM al time tracking.

Principali funzionalità:
• Dashboard personalizzate per ogni ruolo
• Sistema di comunicazione interno
• Gestione progetti e task avanzata
• Time tracking semplificato
• Modulo CRM completo

Nei prossimi giorni organizzeremo delle sessioni di formazione per tutti i team. Stay tuned!''',
            'author': admin_user,
            'is_published': True,
            'image_url': None
        },
        {
            'title': 'Nuove assunzioni nel team Development',
            'content': '''Siamo felici di annunciare l\'arrivo di due nuovi sviluppatori nel nostro team!
            
Marco Ferrari si unirà come Frontend Developer, portando la sua esperienza in React e Vue.js.
Sara Neri invece rafforzerà il team Backend con le sue competenze in Python e microservizi.

Diamogli il benvenuto e aiutiamoli a integrarsi velocemente nel team! 🎉''',
            'author': manager_user or admin_user,
            'is_published': True,
            'image_url': None
        },
        {
            'title': 'Aggiornamento policy smart working',
            'content': '''A seguito dei feedback ricevuti, abbiamo aggiornato la nostra policy di smart working.

Novità principali:
• Possibilità di lavorare da remoto fino a 3 giorni a settimana
• Flessibilità negli orari con core hours 10-16
• Nuovi strumenti per la collaborazione remota

La nuova policy entra in vigore dal 1° del prossimo mese. Trovate tutti i dettagli nel documento allegato sul drive aziendale.''',
            'author': admin_user,
            'is_published': True,
            'image_url': None
        },
        {
            'title': 'Risultati Q4 - Record di fatturato!',
            'content': '''È con grande orgoglio che condividiamo i risultati del Q4: abbiamo raggiunto un record storico di fatturato!
            
Numeri chiave:
📈 +35% rispetto al Q4 dell\'anno precedente
🎯 110% dell\'obiettivo trimestrale raggiunto
👥 +5 nuovi clienti acquisiti
⭐ 98% customer satisfaction

Questo risultato è frutto del lavoro di squadra di tutto il team. Complimenti a tutti! 🏆

A breve comunicheremo i dettagli del bonus trimestrale.''',
            'author': admin_user,
            'is_published': True,
            'image_url': None
        }
    ]

    for i, news_item in enumerate(news_data):
        news = News(
            title=news_item['title'],
            content=news_item['content'],
            author_id=news_item['author'].id,
            is_published=news_item['is_published'],
            image_url=news_item['image_url'],
            created_at=datetime.utcnow() - timedelta(days=(len(news_data) - i) * 5)
        )
        db.session.add(news)

    db.session.commit()
    print(f"✅ Create {len(news_data)} news aziendali!")


def main():
    """Funzione principale per il seeding del modulo comunicazione."""
    parser = argparse.ArgumentParser(description='Seed data per modulo comunicazione')
    parser.add_argument('--clear', action='store_true', help='Cancella i dati esistenti prima del seed')
    args = parser.parse_args()

    print("🚀 Avvio seed dati modulo comunicazione...")
    
    app = create_app()
    with app.app_context():
        if args.clear:
            clear_communication_data()
        
        ensure_users()
        seed_forum_data()
        seed_polls_data()
        seed_messages_data()
        seed_events_data()
        seed_news_data()
        
        print("\n🎉 Seed completato con successo!")
        print("\nDati creati:")
        print(f"📰 News: {News.query.count()}")
        print(f"💬 Topic Forum: {ForumTopic.query.count()}")
        print(f"💭 Commenti: {ForumComment.query.count()}")
        print(f"📊 Sondaggi: {Poll.query.count()}")
        print(f"🗳️  Voti: {PollVote.query.count()}")
        print(f"💌 Messaggi: {DirectMessage.query.count()}")
        print(f"📅 Eventi: {CompanyEvent.query.count()}")
        print(f"📝 Registrazioni: {CompanyEventRegistration.query.count()}")


if __name__ == '__main__':
    main()