#!/usr/bin/env python3
"""
Script per popolare il database con dati di test.
Uso: python seed_data.py [--clear]
"""

import sys
import os
import argparse
from datetime import datetime, timedelta
from werkzeug.security import generate_password_hash

# Add backend to path and set DB URL
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '../../'))
if not os.environ.get('DATABASE_URL'):
    os.environ['DATABASE_URL'] = 'sqlite:///app.db'

from app import create_app, db
from models import (
    User, Client, Project, Task, KPI, ProjectKPI,
    TimesheetEntry, CompanyEvent, Contact, Skill, AdminLog, Notification
)

def clear_test_data():
    """Cancella solo i dati di test, preservando gli utenti esistenti."""
    print("🗑️  Cancellazione dati di test esistenti...")

    # Ordine importante per rispettare le foreign key
    # NON cancelliamo AdminLog e Notification per preservare la cronologia
    db.session.query(ProjectKPI).delete()
    db.session.query(TimesheetEntry).delete()
    db.session.query(Task).delete()
    db.session.query(CompanyEvent).delete()
    db.session.query(Contact).delete()

    # Rimuovi associazioni many-to-many
    for project in Project.query.all():
        project.team_members.clear()

    db.session.query(Project).delete()
    db.session.query(Client).delete()
    db.session.query(KPI).delete()
    db.session.query(Skill).delete()

    # NON cancelliamo gli utenti esistenti!

    db.session.commit()
    print("✅ Dati di test puliti (utenti preservati)!")

def seed_users():
    """Crea utenti di test solo se non esistono già."""
    print("👥 Verifica/Creazione utenti...")

    users_data = [
        {
            'username': 'admin',
            'email': '<EMAIL>',
            'first_name': 'Admin',
            'last_name': 'User',
            'role': 'admin',
            'password': 'admin123'
        },
        {
            'username': 'mario.rossi',
            'email': '<EMAIL>',
            'first_name': 'Mario',
            'last_name': 'Rossi',
            'role': 'manager',
            'password': 'manager123'
        },
        {
            'username': 'giulia.bianchi',
            'email': '<EMAIL>',
            'first_name': 'Giulia',
            'last_name': 'Bianchi',
            'role': 'employee',
            'password': 'dev123'
        },
        {
            'username': 'marco.verdi',
            'email': '<EMAIL>',
            'first_name': 'Marco',
            'last_name': 'Verdi',
            'role': 'employee',
            'password': 'dev123'
        },
        {
            'username': 'sara.neri',
            'email': '<EMAIL>',
            'first_name': 'Sara',
            'last_name': 'Neri',
            'role': 'employee',
            'password': 'design123'
        }
    ]

    users = []
    created_count = 0

    for user_data in users_data:
        # Controlla se l'utente esiste già (per email o username)
        existing_user = User.query.filter(
            (User.email == user_data['email']) |
            (User.username == user_data['username'])
        ).first()
        if existing_user:
            users.append(existing_user)
            print(f"  ✓ Utente {user_data['email']} già esistente")
        else:
            user = User(
                username=user_data['username'],
                email=user_data['email'],
                first_name=user_data['first_name'],
                last_name=user_data['last_name'],
                role=user_data['role'],
                password_hash=generate_password_hash(user_data['password']),
                is_active=True,
                created_at=datetime.utcnow()
            )
            db.session.add(user)
            users.append(user)
            created_count += 1
            print(f"  + Creato utente {user_data['email']}")

    db.session.commit()
    print(f"✅ {created_count} nuovi utenti creati, {len(users)} totali disponibili")
    return users

def seed_clients():
    """Crea clienti di test."""
    print("🏢 Creazione clienti...")

    clients_data = [
        {
            'name': 'TechCorp S.r.l.',
            'industry': 'Technology',
            'description': 'Azienda leader nel settore tecnologico',
            'website': 'https://techcorp.example.com',
            'address': 'Via Roma 123, Milano'
        },
        {
            'name': 'Fashion House',
            'industry': 'Fashion',
            'description': 'Brand di moda di lusso',
            'website': 'https://fashionhouse.example.com',
            'address': 'Via Montenapoleone 45, Milano'
        },
        {
            'name': 'Green Energy Solutions',
            'industry': 'Energy',
            'description': 'Soluzioni per energie rinnovabili',
            'website': 'https://greenenergy.example.com',
            'address': 'Corso Buenos Aires 78, Milano'
        }
    ]

    clients = []
    for client_data in clients_data:
        client = Client(
            name=client_data['name'],
            industry=client_data['industry'],
            description=client_data['description'],
            website=client_data['website'],
            address=client_data['address'],
            created_at=datetime.utcnow()
        )
        db.session.add(client)
        clients.append(client)

    db.session.commit()
    print(f"✅ Creati {len(clients)} clienti")
    return clients

def seed_skills():
    """Crea competenze di test."""
    print("🎯 Creazione competenze...")

    skills_data = [
        {'name': 'Python', 'category': 'Programming', 'description': 'Linguaggio di programmazione Python'},
        {'name': 'JavaScript', 'category': 'Programming', 'description': 'Linguaggio di programmazione JavaScript'},
        {'name': 'React', 'category': 'Frontend', 'description': 'Libreria React per UI'},
        {'name': 'Vue.js', 'category': 'Frontend', 'description': 'Framework Vue.js'},
        {'name': 'Node.js', 'category': 'Backend', 'description': 'Runtime Node.js'},
        {'name': 'PostgreSQL', 'category': 'Database', 'description': 'Database PostgreSQL'},
        {'name': 'Docker', 'category': 'DevOps', 'description': 'Containerizzazione con Docker'},
        {'name': 'Project Management', 'category': 'Management', 'description': 'Gestione progetti'},
        {'name': 'UI/UX Design', 'category': 'Design', 'description': 'Design interfacce utente'},
        {'name': 'Digital Marketing', 'category': 'Marketing', 'description': 'Marketing digitale'}
    ]

    skills = []
    for skill_data in skills_data:
        skill = Skill(
            name=skill_data['name'],
            category=skill_data['category'],
            description=skill_data['description']
        )
        db.session.add(skill)
        skills.append(skill)

    db.session.commit()
    print(f"✅ Creati {len(skills)} competenze")
    return skills

def seed_kpis():
    """Crea KPI di test."""
    print("📊 Creazione KPI...")

    kpis_data = [
        {
            'name': 'Fatturato Mensile',
            'description': 'Fatturato totale mensile dell\'azienda',
            'category': 'Finanziario',
            'frequency': 'monthly',
            'unit': 'EUR'
        },
        {
            'name': 'Soddisfazione Cliente',
            'description': 'Punteggio medio di soddisfazione clienti',
            'category': 'Qualità',
            'frequency': 'quarterly',
            'unit': 'score'
        },
        {
            'name': 'Progetti Completati',
            'description': 'Numero di progetti completati nel periodo',
            'category': 'Operativo',
            'frequency': 'monthly',
            'unit': 'count'
        },
        {
            'name': 'Ore Fatturabili',
            'description': 'Ore fatturabili per progetto',
            'category': 'Operativo',
            'frequency': 'weekly',
            'unit': 'hours'
        },
        {
            'name': 'ROI Progetto',
            'description': 'Return on Investment del progetto',
            'category': 'Finanziario',
            'frequency': 'project_end',
            'unit': 'percentage'
        }
    ]

    kpis = []
    for kpi_data in kpis_data:
        kpi = KPI(
            name=kpi_data['name'],
            description=kpi_data['description'],
            category=kpi_data['category'],
            frequency=kpi_data['frequency'],
            unit=kpi_data['unit'],
            created_at=datetime.utcnow()
        )
        db.session.add(kpi)
        kpis.append(kpi)

    db.session.commit()
    print(f"✅ Creati {len(kpis)} KPI")
    return kpis

def seed_projects(users, clients, kpis):
    """Crea progetti di test."""
    print("🚀 Creazione progetti...")

    projects_data = [
        {
            'name': 'E-commerce Platform',
            'description': 'Sviluppo di una piattaforma e-commerce completa con gestione ordini, pagamenti e inventario',
            'client_id': clients[0].id,
            'status': 'active',
            'budget': 50000.0,
            'expenses': 15000.0,
            'start_date': datetime.now().date() - timedelta(days=30),
            'end_date': datetime.now().date() + timedelta(days=60),
            'team_members': [users[1], users[2], users[3]]  # Manager + 2 dev
        },
        {
            'name': 'Brand Redesign',
            'description': 'Redesign completo del brand e creazione di nuova identità visiva',
            'client_id': clients[1].id,
            'status': 'planning',
            'budget': 25000.0,
            'expenses': 2000.0,
            'start_date': datetime.now().date() + timedelta(days=7),
            'end_date': datetime.now().date() + timedelta(days=45),
            'team_members': [users[1], users[4]]  # Manager + Designer
        },
        {
            'name': 'Energy Management System',
            'description': 'Sistema di gestione e monitoraggio per impianti di energia rinnovabile',
            'client_id': clients[2].id,
            'status': 'active',
            'budget': 75000.0,
            'expenses': 25000.0,
            'start_date': datetime.now().date() - timedelta(days=15),
            'end_date': datetime.now().date() + timedelta(days=90),
            'team_members': [users[1], users[2], users[3], users[4]]  # Tutto il team
        },
        {
            'name': 'Internal CRM',
            'description': 'Sistema CRM interno per gestione clienti e opportunità',
            'client_id': None,  # Progetto interno
            'status': 'completed',
            'budget': 30000.0,
            'expenses': 28000.0,
            'start_date': datetime.now().date() - timedelta(days=120),
            'end_date': datetime.now().date() - timedelta(days=30),
            'team_members': [users[2], users[3]]  # Solo dev
        }
    ]

    projects = []
    for project_data in projects_data:
        project = Project(
            name=project_data['name'],
            description=project_data['description'],
            client_id=project_data['client_id'],
            status=project_data['status'],
            budget=project_data['budget'],
            expenses=project_data['expenses'],
            start_date=project_data['start_date'],
            end_date=project_data['end_date'],
            created_at=datetime.utcnow()
        )

        # Aggiungi team members
        for member in project_data['team_members']:
            project.team_members.append(member)

        db.session.add(project)
        projects.append(project)

    db.session.commit()

    # Aggiungi KPI ai progetti
    print("📈 Collegamento KPI ai progetti...")
    project_kpis_data = [
        {'project': projects[0], 'kpi': kpis[0], 'target_value': 45000.0, 'current_value': 30000.0},
        {'project': projects[0], 'kpi': kpis[2], 'target_value': 1.0, 'current_value': 0.0},
        {'project': projects[1], 'kpi': kpis[1], 'target_value': 9.0, 'current_value': 8.5},
        {'project': projects[2], 'kpi': kpis[3], 'target_value': 500.0, 'current_value': 320.0},
        {'project': projects[3], 'kpi': kpis[4], 'target_value': 150.0, 'current_value': 140.0},
    ]

    for pk_data in project_kpis_data:
        project_kpi = ProjectKPI(
            project_id=pk_data['project'].id,
            kpi_id=pk_data['kpi'].id,
            target_value=pk_data['target_value'],
            current_value=pk_data['current_value']
        )
        db.session.add(project_kpi)

    db.session.commit()
    print(f"✅ Creati {len(projects)} progetti con KPI collegati")

    # Aggiungi task ai progetti
    print("📋 Creazione task di esempio...")
    seed_tasks(projects, users)

    # Aggiungi timesheet di esempio
    print("⏱️ Creazione timesheet di esempio...")
    seed_timesheets(projects, users)

    return projects

def seed_tasks(projects, users):
    """Crea task di esempio per i progetti."""
    from random import choice

    tasks_data = [
        # Tasks per E-commerce Platform
        {'name': 'Setup Database', 'description': 'Configurazione database PostgreSQL', 'priority': 'high', 'status': 'done'},
        {'name': 'User Authentication', 'description': 'Sistema di login e registrazione', 'priority': 'high', 'status': 'done'},
        {'name': 'Product Catalog', 'description': 'Gestione catalogo prodotti', 'priority': 'medium', 'status': 'in-progress'},
        {'name': 'Shopping Cart', 'description': 'Carrello della spesa', 'priority': 'medium', 'status': 'todo'},
        {'name': 'Payment Integration', 'description': 'Integrazione gateway pagamenti', 'priority': 'high', 'status': 'todo'},

        # Tasks per Brand Redesign
        {'name': 'Brand Research', 'description': 'Ricerca e analisi brand esistente', 'priority': 'high', 'status': 'in-progress'},
        {'name': 'Logo Design', 'description': 'Progettazione nuovo logo', 'priority': 'high', 'status': 'todo'},
        {'name': 'Color Palette', 'description': 'Definizione palette colori', 'priority': 'medium', 'status': 'todo'},

        # Tasks per Energy Management System
        {'name': 'System Architecture', 'description': 'Architettura del sistema', 'priority': 'high', 'status': 'done'},
        {'name': 'Sensor Integration', 'description': 'Integrazione sensori IoT', 'priority': 'high', 'status': 'in-progress'},
        {'name': 'Dashboard Development', 'description': 'Sviluppo dashboard monitoraggio', 'priority': 'medium', 'status': 'in-progress'},
        {'name': 'Alert System', 'description': 'Sistema di allerte', 'priority': 'medium', 'status': 'todo'},

        # Tasks per Internal CRM
        {'name': 'Contact Management', 'description': 'Gestione contatti clienti', 'priority': 'high', 'status': 'done'},
        {'name': 'Opportunity Tracking', 'description': 'Tracciamento opportunità', 'priority': 'medium', 'status': 'done'},
        {'name': 'Reporting Module', 'description': 'Modulo di reportistica', 'priority': 'low', 'status': 'done'},
    ]

    # Assegna task ai progetti
    project_task_mapping = [
        (0, [0, 1, 2, 3, 4]),      # E-commerce Platform
        (1, [5, 6, 7]),            # Brand Redesign
        (2, [8, 9, 10, 11]),       # Energy Management System
        (3, [12, 13, 14])          # Internal CRM
    ]

    task_count = 0
    for project_idx, task_indices in project_task_mapping:
        project = projects[project_idx]

        for task_idx in task_indices:
            task_data = tasks_data[task_idx]

            # Assegna task a un membro del team casuale
            assignee = choice(project.team_members) if project.team_members else None

            # Calcola date basate sullo status
            if task_data['status'] == 'done':
                due_date = project.start_date + timedelta(days=7)
            elif task_data['status'] == 'in-progress':
                due_date = project.start_date + timedelta(days=15)
            else:  # todo
                due_date = project.start_date + timedelta(days=25)

            task = Task(
                name=task_data['name'],
                description=task_data['description'],
                project_id=project.id,
                assignee_id=assignee.id if assignee else None,
                priority=task_data['priority'],
                status=task_data['status'],
                due_date=due_date,
                created_at=datetime.utcnow()
            )
            db.session.add(task)
            task_count += 1

    db.session.commit()
    print(f"✅ Creati {task_count} task di esempio")

def seed_timesheets(projects, users):
    """Crea timesheet di esempio per gli ultimi 3 mesi."""
    from random import randint, choice

    timesheet_count = 0

    # Genera timesheet per gli ultimi 3 mesi
    for month_offset in range(3):
        current_date = datetime.now() - timedelta(days=30 * month_offset)
        year = current_date.year
        month = current_date.month

        # Giorni del mese
        from calendar import monthrange
        days_in_month = monthrange(year, month)[1]

        # Per ogni progetto attivo
        for project in projects:  # Tutti i progetti
            if not project.team_members:
                continue

            # Per ogni membro del team
            for member in project.team_members:
                # Genera timesheet per giorni casuali del mese (15-22 giorni lavorativi)
                working_days = randint(15, 22)
                worked_days = []

                # Seleziona giorni casuali (evita weekend)
                for _ in range(working_days):
                    day = randint(1, days_in_month)
                    if day not in worked_days:
                        worked_days.append(day)

                # Per ogni giorno lavorato
                for day in worked_days:
                    work_date = datetime(year, month, day).date()

                    # Seleziona task casuali del progetto
                    project_tasks = [t for t in Task.query.filter_by(project_id=project.id).all()]
                    if not project_tasks:
                        continue

                    # Lavora su 1-3 task per giorno
                    daily_tasks = choice([1, 1, 2, 2, 3])  # Più probabilità di 1-2 task
                    selected_tasks = []

                    for _ in range(min(daily_tasks, len(project_tasks))):
                        task = choice(project_tasks)
                        if task not in selected_tasks:
                            selected_tasks.append(task)

                    # Distribuisci 6-9 ore tra i task del giorno
                    total_daily_hours = randint(6, 9)
                    hours_per_task = total_daily_hours / len(selected_tasks)

                    for task in selected_tasks:
                        # Varia leggermente le ore per task
                        task_hours = round(hours_per_task + randint(-1, 1) * 0.5, 1)
                        if task_hours <= 0:
                            task_hours = 0.5

                        timesheet = TimesheetEntry(
                            user_id=member.id,
                            project_id=project.id,
                            task_id=task.id,
                            date=work_date,
                            hours=task_hours,
                            description=f"Lavoro su {task.name}",
                            created_at=datetime.utcnow()
                        )
                        db.session.add(timesheet)
                        timesheet_count += 1

    db.session.commit()
    print(f"✅ Creati {timesheet_count} timesheet di esempio per 3 mesi")

def main():
    parser = argparse.ArgumentParser(description='Popola il database con dati di test')
    parser.add_argument('--clear', action='store_true', help='Cancella i dati di test esistenti prima di creare i nuovi (preserva utenti)')
    args = parser.parse_args()

    app = create_app()

    with app.app_context():
        print("🌱 Avvio seeding del database...")

        if args.clear:
            clear_test_data()

        # Crea i dati in ordine di dipendenza
        users = seed_users()
        clients = seed_clients()
        skills = seed_skills()
        kpis = seed_kpis()
        projects = seed_projects(users, clients, kpis)

        print("\n🎉 Seeding completato!")
        print("\n📋 Credenziali di accesso (se creati):")
        print("Admin: <EMAIL> / admin123")
        print("Manager: <EMAIL> / manager123")
        print("Employee: <EMAIL> / dev123")
        print("\n🔗 Ora puoi testare l'applicazione con dati realistici!")

if __name__ == '__main__':
    main()
