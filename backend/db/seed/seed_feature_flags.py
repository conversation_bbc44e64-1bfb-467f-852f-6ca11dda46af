#!/usr/bin/env python3
"""
Seed per Feature Flags - Configurazione iniziale delle feature flags del sistema
Crea feature flags per controllo granulare delle funzionalità
"""

import sys
import os

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '../../'))

# Carica le variabili d'ambiente dal file .env
try:
    from dotenv import load_dotenv
    load_dotenv(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), '.env'))
except ImportError:
    # Se python-dotenv non è disponibile, prova a usare load_env.py
    try:
        sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
        from load_env import load_env
        load_env()
    except ImportError:
        print("⚠️  Attenzione: Impossibile caricare le variabili d'ambiente")

from app import create_app
from extensions import db
from models import FeatureFlag, User
from datetime import datetime

def seed_feature_flags():
    """Crea feature flags iniziali per il sistema"""
    print("🚩 Creazione feature flags...")
    
    # Get admin user for audit trail
    admin_user = User.query.filter_by(role='admin').first()
    admin_id = admin_user.id if admin_user else None
    
    # Core system feature flags
    core_flags = [
        {
            'feature_key': 'oauth_enabled',
            'display_name': 'OAuth Authentication',
            'description': 'Abilita autenticazione OAuth con provider esterni (Google, Microsoft)',
            'category': 'auth',
            'is_enabled': True,
            'updated_by': admin_id
        },
        {
            'feature_key': 'google_auth',
            'display_name': 'Google Authentication',
            'description': 'Abilita login con account Google',
            'category': 'auth',
            'is_enabled': True,
            'updated_by': admin_id
        },
        {
            'feature_key': 'microsoft_auth',
            'display_name': 'Microsoft Authentication',
            'description': 'Abilita login con account Microsoft/Office 365',
            'category': 'auth',
            'is_enabled': True,
            'updated_by': admin_id
        }
    ]
    
    # Module feature flags
    module_flags = [
        {
            'feature_key': 'dashboard_module',
            'display_name': 'Dashboard Principale',
            'description': 'Abilita il dashboard principale dell\'applicazione',
            'category': 'modules',
            'is_enabled': True,
            'updated_by': admin_id
        },
        {
            'feature_key': 'recruiting_module',
            'display_name': 'Modulo Recruiting',
            'description': 'Abilita il modulo di recruiting e gestione candidati',
            'category': 'modules',
            'is_enabled': True,
            'updated_by': admin_id
        },
        {
            'feature_key': 'ceo_assistant',
            'display_name': 'Human CEO Assistant',
            'description': 'Abilita l\'assistente AI per CEO e insights strategici',
            'category': 'modules',
            'is_enabled': True,
            'updated_by': admin_id
        },
        {
            'feature_key': 'certifications_module',
            'display_name': 'Modulo Certificazioni',
            'description': 'Abilita il modulo di gestione certificazioni ISO e compliance',
            'category': 'modules',
            'is_enabled': True,
            'updated_by': admin_id
        },
        {
            'feature_key': 'engagement_module',
            'display_name': 'Modulo Engagement',
            'description': 'Abilita il modulo di gamification e engagement team',
            'category': 'modules',
            'is_enabled': True,
            'updated_by': admin_id
        },
        {
            'feature_key': 'communications_module',
            'display_name': 'Modulo Comunicazioni',
            'description': 'Abilita forum, news, eventi e comunicazione interna',
            'category': 'modules',
            'is_enabled': True,
            'updated_by': admin_id
        },
        {
            'feature_key': 'personnel_module',
            'display_name': 'Modulo Personale',
            'description': 'Abilita gestione personale, performance e organizzazione',
            'category': 'modules',
            'is_enabled': True,
            'updated_by': admin_id
        },
        {
            'feature_key': 'timesheet_module',
            'display_name': 'Modulo Timesheet',
            'description': 'Abilita gestione ore, progetti e attività',
            'category': 'modules',
            'is_enabled': True,
            'updated_by': admin_id
        },
        {
            'feature_key': 'sales_module',
            'display_name': 'Modulo Sales',
            'description': 'Abilita CRM, clienti, contratti e proposte',
            'category': 'modules',
            'is_enabled': True,
            'updated_by': admin_id
        },
        {
            'feature_key': 'business_intelligence_module',
            'display_name': 'Business Intelligence',
            'description': 'Abilita analytics, case studies e market intelligence',
            'category': 'modules',
            'is_enabled': True,
            'updated_by': admin_id
        },
        {
            'feature_key': 'ceo_module',
            'display_name': 'Human CEO',
            'description': 'Abilita dashboard strategico e AI assistant per CEO',
            'category': 'modules',
            'is_enabled': True,
            'updated_by': admin_id
        },
        {
            'feature_key': 'governance_module',
            'display_name': 'Modulo Governance',
            'description': 'Abilita compliance, audit, policies e risk management',
            'category': 'modules',
            'is_enabled': True,
            'updated_by': admin_id
        },
        {
            'feature_key': 'funding_module',
            'display_name': 'Modulo Bandi',
            'description': 'Abilita ricerca bandi, candidature e rendicontazione',
            'category': 'modules',
            'is_enabled': True,
            'updated_by': admin_id
        },
        {
            'feature_key': 'help_module',
            'display_name': 'Sistema Help',
            'description': 'Abilita help center, documentazione e assistente AI',
            'category': 'modules',
            'is_enabled': True,
            'updated_by': admin_id
        }
    ]
    
    # AI and advanced features
    ai_flags = [
        {
            'feature_key': 'ai_resources',
            'display_name': 'AI Resources',
            'description': 'Abilita funzionalità AI per analisi CV e raccomandazioni',
            'category': 'integrations',
            'is_enabled': True,
            'updated_by': admin_id
        },
        {
            'feature_key': 'project_analytics',
            'display_name': 'Project Analytics',
            'description': 'Abilita analytics avanzate per progetti e performance',
            'category': 'core',
            'is_enabled': True,
            'updated_by': admin_id
        },
        {
            'feature_key': 'advanced_analytics',
            'display_name': 'Advanced Analytics',
            'description': 'Abilita dashboard analytics avanzate e reporting personalizzato',
            'category': 'core',
            'is_enabled': False,
            'updated_by': admin_id
        }
    ]
    
    # UI and UX features
    ui_flags = [
        {
            'feature_key': 'dark_mode',
            'display_name': 'Dark Mode',
            'description': 'Abilita tema scuro per l\'interfaccia utente',
            'category': 'ui',
            'is_enabled': True,
            'updated_by': admin_id
        },
        {
            'feature_key': 'notifications',
            'display_name': 'Notifiche Push',
            'description': 'Abilita notifiche push per aggiornamenti importanti',
            'category': 'ui',
            'is_enabled': True,
            'updated_by': admin_id
        },
        {
            'feature_key': 'real_time_updates',
            'display_name': 'Aggiornamenti Real-time',
            'description': 'Abilita aggiornamenti in tempo reale tramite WebSocket',
            'category': 'ui',
            'is_enabled': False,
            'updated_by': admin_id
        }
    ]
    
    # Business features
    business_flags = [
        {
            'feature_key': 'timesheet_approval',
            'display_name': 'Approvazione Timesheet',
            'description': 'Abilita workflow di approvazione per timesheet',
            'category': 'core',
            'is_enabled': True,
            'updated_by': admin_id
        },
        {
            'feature_key': 'expense_tracking',
            'display_name': 'Tracking Spese',
            'description': 'Abilita gestione spese e rimborsi avanzata',
            'category': 'core',
            'is_enabled': True,
            'updated_by': admin_id
        },
        {
            'feature_key': 'invoice_automation',
            'display_name': 'Automazione Fatture',
            'description': 'Abilita creazione automatica fatture da timesheet',
            'category': 'integrations',
            'is_enabled': False,
            'updated_by': admin_id
        }
    ]
    
    # Admin and system features
    admin_flags = [
        {
            'feature_key': 'user_management',
            'display_name': 'Gestione Utenti Avanzata',
            'description': 'Abilita funzioni avanzate di gestione utenti e ruoli',
            'category': 'admin',
            'is_enabled': True,
            'updated_by': admin_id
        },
        {
            'feature_key': 'system_settings',
            'display_name': 'Configurazioni Sistema',
            'description': 'Abilita configurazioni avanzate del sistema',
            'category': 'admin',
            'is_enabled': True,
            'updated_by': admin_id
        },
        {
            'feature_key': 'audit_logs',
            'display_name': 'Log di Audit',
            'description': 'Abilita logging dettagliato delle azioni utente',
            'category': 'admin',
            'is_enabled': True,
            'updated_by': admin_id
        }
    ]
    
    # Experimental features
    experimental_flags = [
        {
            'feature_key': 'beta_features',
            'display_name': 'Features Beta',
            'description': 'Abilita accesso a funzionalità in fase di testing',
            'category': 'experimental',
            'is_enabled': False,
            'updated_by': admin_id
        },
        {
            'feature_key': 'experimental_ui',
            'display_name': 'UI Sperimentale',
            'description': 'Abilita componenti UI in fase sperimentale',
            'category': 'experimental',
            'is_enabled': False,
            'updated_by': admin_id
        },
        {
            'feature_key': 'api_v2',
            'display_name': 'API v2',
            'description': 'Abilita accesso alle API di nuova generazione',
            'category': 'experimental',
            'is_enabled': False,
            'updated_by': admin_id
        }
    ]
    
    # Combine all flags
    all_flags = core_flags + module_flags + ai_flags + ui_flags + business_flags + admin_flags + experimental_flags
    
    created_count = 0
    updated_count = 0
    
    for flag_data in all_flags:
        # Check if flag already exists
        existing_flag = FeatureFlag.query.filter_by(feature_key=flag_data['feature_key']).first()
        
        if existing_flag:
            # Update existing flag (preserve is_enabled state)
            existing_flag.display_name = flag_data['display_name']
            existing_flag.description = flag_data['description']
            existing_flag.category = flag_data['category']
            existing_flag.updated_at = datetime.utcnow()
            if flag_data['updated_by']:
                existing_flag.updated_by = flag_data['updated_by']
            updated_count += 1
            print(f"  ↻ Aggiornato: {flag_data['feature_key']}")
        else:
            # Create new flag
            flag = FeatureFlag(
                feature_key=flag_data['feature_key'],
                display_name=flag_data['display_name'],
                description=flag_data['description'],
                category=flag_data['category'],
                is_enabled=flag_data['is_enabled'],
                updated_by=flag_data['updated_by']
            )
            db.session.add(flag)
            created_count += 1
            print(f"  ✓ Creato: {flag_data['feature_key']} ({'enabled' if flag_data['is_enabled'] else 'disabled'})")
    
    # Commit all changes
    try:
        db.session.commit()
        print(f"\n✅ Feature flags configurati con successo!")
        print(f"   📊 Creati: {created_count}")
        print(f"   🔄 Aggiornati: {updated_count}")
        print(f"   📁 Totale: {len(all_flags)}")
        
        # Summary by category
        categories = {}
        for flag in all_flags:
            cat = flag['category']
            if cat not in categories:
                categories[cat] = {'total': 0, 'enabled': 0}
            categories[cat]['total'] += 1
            if flag['is_enabled']:
                categories[cat]['enabled'] += 1
        
        print(f"\n📂 Riepilogo per categoria:")
        for cat, stats in categories.items():
            enabled_pct = (stats['enabled'] / stats['total']) * 100
            print(f"   {cat.capitalize()}: {stats['enabled']}/{stats['total']} abilitati ({enabled_pct:.0f}%)")
            
    except Exception as e:
        db.session.rollback()
        print(f"❌ Errore nel seed feature flags: {str(e)}")
        raise

def main():
    """Main function per eseguire il seed"""
    app = create_app()
    
    with app.app_context():
        print("🚀 Avvio seed Feature Flags...")
        
        # Ensure tables exist
        db.create_all()
        
        # Run seed
        seed_feature_flags()
        
        print("🎯 Seed Feature Flags completato!")

if __name__ == '__main__':
    main()