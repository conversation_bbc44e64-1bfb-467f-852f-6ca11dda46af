#!/usr/bin/env python3
"""
Help Center Seed Data Script
Populates the database with comprehensive help content for production use.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from datetime import datetime, timedelta
from models_split.help import HelpCategory, HelpContent
from models_split.user import User
from app import create_app, db

def seed_help_categories():
    """Create help categories with comprehensive Italian content."""
    categories = [
        {
            'name': 'Primi Passi',
            'description': 'Guida per iniziare con DatPortal',
            'slug': 'primi-passi',
            'icon': 'academic-cap',
            'color': '#3B82F6',
            'sort_order': 1,
            'is_featured': True
        },
        {
            'name': 'Gestione Progetti',
            'description': 'Come gestire progetti, task e team',
            'slug': 'gestione-progetti',
            'icon': 'folder',
            'color': '#10B981',
            'sort_order': 2,
            'is_featured': True
        },
        {
            'name': 'Timesheet',
            'description': 'Registrazione ore e gestione tempo',
            'slug': 'timesheet',
            'icon': 'clock',
            'color': '#F59E0B',
            'sort_order': 3,
            'is_featured': True
        },
        {
            'name': 'Recruiting',
            'description': 'Gestione candidature e colloqui',
            'slug': 'recruiting',
            'icon': 'user-group',
            'color': '#8B5CF6',
            'sort_order': 4,
            'is_featured': True
        },
        {
            'name': 'CRM',
            'description': 'Gestione clienti e contratti',
            'slug': 'crm',
            'icon': 'building-office',
            'color': '#EF4444',
            'sort_order': 5,
            'is_featured': False
        },
        {
            'name': 'Certificazioni',
            'description': 'Gestione certificazioni ISO',
            'slug': 'certificazioni',
            'icon': 'shield-check',
            'color': '#06B6D4',
            'sort_order': 6,
            'is_featured': False
        },
        {
            'name': 'Finanziamenti',
            'description': 'Gestione bandi e finanziamenti',
            'slug': 'finanziamenti',
            'icon': 'banknotes',
            'color': '#84CC16',
            'sort_order': 7,
            'is_featured': False
        },
        {
            'name': 'Human CEO',
            'description': 'AI Assistant per dirigenti',
            'slug': 'human-ceo',
            'icon': 'cpu-chip',
            'color': '#F97316',
            'sort_order': 8,
            'is_featured': False
        },
        {
            'name': 'Amministrazione',
            'description': 'Configurazione e gestione utenti',
            'slug': 'amministrazione',
            'icon': 'cog-6-tooth',
            'color': '#6B7280',
            'sort_order': 9,
            'is_featured': False
        },
        {
            'name': 'Risoluzione Problemi',
            'description': 'FAQ e troubleshooting',
            'slug': 'troubleshooting',
            'icon': 'exclamation-triangle',
            'color': '#DC2626',
            'sort_order': 10,
            'is_featured': False
        }
    ]
    
    created_categories = {}
    
    for cat_data in categories:
        # Check if category already exists
        existing = HelpCategory.query.filter_by(slug=cat_data['slug']).first()
        if existing:
            print(f"Category '{cat_data['name']}' already exists, skipping...")
            created_categories[cat_data['slug']] = existing
            continue
            
        category = HelpCategory(
            name=cat_data['name'],
            description=cat_data['description'],
            slug=cat_data['slug'],
            icon=cat_data['icon'],
            color=cat_data['color'],
            sort_order=cat_data['sort_order'],
            is_featured=cat_data['is_featured']
        )
        
        db.session.add(category)
        created_categories[cat_data['slug']] = category
        print(f"Created category: {cat_data['name']}")
    
    db.session.commit()
    return created_categories

def seed_help_content(categories):
    """Create comprehensive help content for each category."""
    
    # Get admin user for content authoring
    admin_user = User.query.filter_by(role='admin').first()
    if not admin_user:
        admin_user = User.query.first()
    
    if not admin_user:
        print("Warning: No users found, content will be created without author")
        author_id = None
    else:
        author_id = admin_user.id
    
    contents = [
        # Primi Passi
        {
            'category': 'primi-passi',
            'title': 'Benvenuto in DatPortal',
            'slug': 'benvenuto-datportal',
            'content': """# Benvenuto in DatPortal

Benvenuto nella tua nuova piattaforma di gestione aziendale! DatPortal è progettato per semplificare la gestione dei tuoi processi aziendali.

## Cosa puoi fare con DatPortal

- **Gestire progetti** e monitorare i progressi
- **Tracciare il tempo** con timesheet intelligenti
- **Gestire candidature** e processi di recruiting
- **Organizzare clienti** e contratti CRM
- **Monitorare certificazioni** ISO
- **Accedere a finanziamenti** e bandi
- **Utilizzare l'AI** per decisioni strategiche

## Primi passi

1. Completa il tuo profilo utente
2. Familiarizza con la dashboard
3. Esplora i moduli principali
4. Configura le tue preferenze

Per qualsiasi domanda, consulta questa sezione help o contatta il supporto.""",
            'type': 'guide',
            'difficulty': 'beginner',
            'read_time': 3,
            'is_featured': True,
            'tags': ['introduzione', 'setup', 'dashboard']
        },
        {
            'category': 'primi-passi',
            'title': 'Navigare la Dashboard',
            'slug': 'navigare-dashboard',
            'content': """# Navigare la Dashboard

La dashboard è il centro di controllo di DatPortal. Ecco come orientarti.

## Elementi principali

### Sidebar di navigazione
- **Dashboard**: Panoramica generale
- **Progetti**: Gestione progetti e task
- **Timesheet**: Registrazione ore
- **Recruiting**: Candidature e colloqui
- **CRM**: Clienti e contratti
- **Altri moduli**: Certificazioni, finanziamenti, etc.

### Header
- **Ricerca globale**: Trova rapidamente contenuti
- **Notifiche**: Alert e aggiornamenti
- **Profilo utente**: Impostazioni personali

### Area principale
- **Widget informativi**: KPI e metriche
- **Azioni rapide**: Accesso diretto alle funzioni
- **Contenuti recenti**: Ultimi elementi visualizzati

## Personalizzazione

Puoi personalizzare la dashboard secondo le tue esigenze:
- Riorganizza i widget
- Filtra i contenuti per priorità
- Configura le notifiche""",
            'type': 'tutorial',
            'difficulty': 'beginner',
            'read_time': 5,
            'is_featured': True,
            'tags': ['dashboard', 'navigazione', 'ui']
        },
        
        # Gestione Progetti
        {
            'category': 'gestione-progetti',
            'title': 'Creare un Nuovo Progetto',
            'slug': 'creare-nuovo-progetto',
            'content': """# Creare un Nuovo Progetto

Impara a creare e configurare un nuovo progetto in DatPortal.

## Passaggi per creare un progetto

### 1. Accesso alla sezione progetti
- Clicca su **Progetti** nella sidebar
- Seleziona **Nuovo Progetto**

### 2. Informazioni base
- **Nome progetto**: Scegli un nome descrittivo
- **Descrizione**: Dettagli e obiettivi
- **Cliente**: Seleziona o crea un nuovo cliente
- **Data inizio/fine**: Tempistiche del progetto

### 3. Configurazione avanzata
- **Budget**: Imposta il budget totale
- **Team**: Assegna membri del team
- **Priorità**: Definisci l'importanza
- **Status**: Stato iniziale (planning, active, etc.)

### 4. Template e metodologie
- Scegli un template predefinito
- Seleziona la metodologia (Agile, Waterfall, etc.)
- Configura milestone e deliverable

## Best practices

- Usa nomi progetti chiari e univoci
- Definisci sempre un budget realistico
- Assegna un project manager responsabile
- Pianifica milestone intermedie""",
            'type': 'tutorial',
            'difficulty': 'intermediate',
            'read_time': 8,
            'is_featured': True,
            'tags': ['progetti', 'creazione', 'setup']
        },
        {
            'category': 'gestione-progetti',
            'title': 'Gestione Task e Milestone',
            'slug': 'gestione-task-milestone',
            'content': """# Gestione Task e Milestone

Organizza il lavoro con task e milestone efficaci.

## Creazione Task

### Informazioni essenziali
- **Titolo task**: Breve e descrittivo
- **Descrizione**: Dettagli di implementazione
- **Assegnatario**: Chi è responsabile
- **Scadenza**: Deadline del task
- **Priorità**: Alta, media, bassa

### Dipendenze
- **Task prerequisiti**: Cosa deve essere completato prima
- **Task bloccanti**: Cosa questo task sblocca
- **Sequenze**: Ordine logico di esecuzione

## Milestone

### Definizione milestone
- **Obiettivi chiari**: Cosa deve essere raggiunto
- **Criteri di successo**: Come misurare il completamento
- **Date target**: Scadenze realistiche
- **Deliverable**: Cosa viene consegnato

### Monitoraggio progresso
- Dashboard task completati/totali
- Timeline visuale del progetto
- Report di avanzamento
- Alert per ritardi

## Metodologie supportate

- **Kanban**: Board con colonne di stato
- **Agile**: Sprint e user stories
- **Waterfall**: Fasi sequenziali
- **Hybrid**: Combinazione personalizzata""",
            'type': 'guide',
            'difficulty': 'intermediate',
            'read_time': 10,
            'is_featured': False,
            'tags': ['task', 'milestone', 'workflow']
        },
        
        # Timesheet
        {
            'category': 'timesheet',
            'title': 'Registrare le Ore',
            'slug': 'registrare-ore',
            'content': """# Registrare le Ore

Impara a tracciare il tempo in modo efficace con i timesheet.

## Accesso ai Timesheet

### Dashboard timesheet
- Vai a **Timesheet** nella sidebar
- Visualizza il calendario mensile
- Accedi alle funzioni di registrazione

### Modalità di registrazione
- **Giornaliera**: Registra ore giorno per giorno
- **Settimanale**: Vista completa della settimana
- **Bulk entry**: Inserimento multiplo

## Registrazione ore

### Informazioni richieste
- **Progetto**: Seleziona il progetto attivo
- **Task**: Specifica il task (opzionale)
- **Ore**: Numero di ore lavorate
- **Descrizione**: Breve descrizione dell'attività
- **Data**: Giorno di lavoro

### Tipologie di ore
- **Lavoro standard**: Ore regolari
- **Straordinari**: Ore extra
- **Ferie**: Giorni di vacanza
- **Malattia**: Assenze per malattia
- **Formazione**: Ore di training

## Approvazione

### Processo di approvazione
1. **Invio timesheet**: Fine mese/settimana
2. **Revisione manager**: Controllo e validazione
3. **Approvazione/Rifiuto**: Feedback e correzioni
4. **Finalizzazione**: Timesheet bloccato

### Best practices
- Registra le ore quotidianamente
- Sii specifico nelle descrizioni
- Rispetta le deadline di invio
- Comunica eventuali problemi""",
            'type': 'tutorial',
            'difficulty': 'beginner',
            'read_time': 7,
            'is_featured': True,
            'tags': ['timesheet', 'ore', 'registrazione']
        },
        
        # Recruiting
        {
            'category': 'recruiting',
            'title': 'Processo di Recruiting',
            'slug': 'processo-recruiting',
            'content': """# Processo di Recruiting

Gestisci candidature e colloqui in modo professionale.

## Panoramica del processo

### Fasi del recruiting
1. **Job posting**: Pubblicazione posizione
2. **Candidature**: Raccolta CV
3. **Screening**: Prima selezione
4. **Colloqui**: Interviste strutturate
5. **Valutazione**: Decision making
6. **Offerta**: Proposta contratto

## Creazione Job Posting

### Informazioni essenziali
- **Titolo posizione**: Ruolo specifico
- **Descrizione**: Responsabilità e requisiti
- **Sede di lavoro**: Location o remoto
- **Contratto**: Tipologia e dettagli
- **Scadenza**: Deadline candidature

### Canali di pubblicazione
- **Portale aziendale**: Sezione carriere
- **Job boards**: LinkedIn, Indeed, etc.
- **Social media**: Promozione organica
- **Network**: Referral e contatti

## Gestione candidature

### Pipeline candidature
- **Nuove**: Candidature ricevute
- **In screening**: Prima valutazione
- **Colloquio**: Interviste programmate
- **Valutazione**: Decision in corso
- **Offerta**: Proposta inviata
- **Assunto/Rifiutato**: Esito finale

### Valutazione CV
- Parsing automatico con AI
- Score di matching
- Note e feedback
- Storico comunicazioni""",
            'type': 'guide',
            'difficulty': 'intermediate',
            'read_time': 12,
            'is_featured': True,
            'tags': ['recruiting', 'candidature', 'colloqui']
        },
        
        # Troubleshooting
        {
            'category': 'troubleshooting',
            'title': 'Problemi Comuni e Soluzioni',
            'slug': 'problemi-comuni',
            'content': """# Problemi Comuni e Soluzioni

Risolvi rapidamente i problemi più frequenti.

## Problemi di accesso

### Non riesco a fare login
**Sintomi**: Errore di autenticazione
**Soluzioni**:
- Verifica username e password
- Controlla caps lock
- Richiedi reset password
- Contatta l'amministratore

### Sessione scaduta
**Sintomi**: Logout automatico
**Soluzioni**:
- Rieffettua login
- Verifica connessione internet
- Controlla impostazioni browser
- Salva lavoro frequentemente

## Problemi di performance

### Caricamento lento
**Sintomi**: Pagine lente
**Soluzioni**:
- Aggiorna il browser
- Svuota cache e cookies
- Verifica connessione internet
- Chiudi tab non utilizzate

### Errori di salvataggio
**Sintomi**: Dati non salvati
**Soluzioni**:
- Verifica connessione
- Riprova l'operazione
- Copia dati importanti
- Segnala il problema

## Problemi browser

### Browser supportati
- Chrome (consigliato)
- Firefox
- Safari
- Edge

### Configurazione ottimale
- JavaScript abilitato
- Cookies permessi
- Pop-up consentiti per il dominio
- Cache aggiornata

## Contatto supporto

Se il problema persiste:
- Email: <EMAIL>
- Telefono: 02-1234-5678
- Chat interno: Icona help
- Ticket system: Sezione supporto""",
            'type': 'faq',
            'difficulty': 'beginner',
            'read_time': 6,
            'is_featured': False,
            'tags': ['troubleshooting', 'problemi', 'supporto']
        }
    ]
    
    for content_data in contents:
        category = categories.get(content_data['category'])
        if not category:
            print(f"Warning: Category {content_data['category']} not found")
            continue
            
        # Check if content already exists
        existing = HelpContent.query.filter_by(slug=content_data['slug']).first()
        if existing:
            print(f"Content '{content_data['title']}' already exists, skipping...")
            continue
        
        content = HelpContent(
            title=content_data['title'],
            slug=content_data['slug'],
            content=content_data['content'],
            category_id=category.id,
            author_id=author_id,
            type=content_data['type'],
            difficulty=content_data['difficulty'],
            read_time=content_data['read_time'],
            is_featured=content_data['is_featured'],
            tags=','.join(content_data['tags']),
            is_published=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        db.session.add(content)
        print(f"Created content: {content_data['title']}")
    
    db.session.commit()

def seed_help_data():
    """Main function to seed all help data."""
    try:
        print("🚀 Starting Help Center data seeding...")
        
        # Create categories first
        print("\n📁 Creating help categories...")
        categories = seed_help_categories()
        
        # Create content for each category
        print("\n📝 Creating help content...")
        seed_help_content(categories)
        
        print("\n✅ Help Center seeding completed successfully!")
        
        # Print summary
        total_categories = HelpCategory.query.count()
        total_content = HelpContent.query.count()
        featured_content = HelpContent.query.filter_by(is_featured=True).count()
        
        print(f"\n📊 Summary:")
        print(f"   - Categories created: {total_categories}")
        print(f"   - Content items created: {total_content}")
        print(f"   - Featured content: {featured_content}")
        
    except Exception as e:
        print(f"❌ Error during seeding: {str(e)}")
        db.session.rollback()
        raise

if __name__ == "__main__":
    app = create_app()
    
    with app.app_context():
        # Create tables if they don't exist
        db.create_all()
        
        # Run seeding
        seed_help_data()