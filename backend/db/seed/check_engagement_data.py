#!/usr/bin/env python3
"""
Script per controllare e creare dati di engagement di esempio
"""
from dotenv import load_dotenv
load_dotenv()

from app import create_app
from models import db, User
from models_split.engagement import EngagementUserProfile, EngagementPoint, EngagementCampaign, EngagementReward
from datetime import datetime, timedelta
import random

def check_engagement_data():
    """Controlla i dati di engagement esistenti"""
    print('=== ENGAGEMENT DATA CHECK ===')
    print(f'Users: {User.query.count()}')
    print(f'Engagement User Profiles: {EngagementUserProfile.query.count()}')
    print(f'Engagement Points: {EngagementPoint.query.count()}')
    print(f'Engagement Campaigns: {EngagementCampaign.query.count()}')
    print(f'Engagement Rewards: {EngagementReward.query.count()}')

    print('\n=== USER PROFILES ===')
    profiles = EngagementUserProfile.query.all()
    for profile in profiles:
        user = User.query.get(profile.user_id)
        print(f'User {user.username if user else "Unknown"} (ID {profile.user_id}): {profile.total_points} points, level {profile.current_level}')

    print('\n=== POINTS ENTRIES (last 10) ===')
    points = EngagementPoint.query.order_by(EngagementPoint.created_at.desc()).limit(10).all()
    for point in points:
        user = User.query.get(point.user_id)
        print(f'User {user.username if user else "Unknown"}: {point.points} points - {point.description}')
    
    return len(profiles) > 0

def create_sample_engagement_data():
    """Crea dati di engagement di esempio"""
    print('\n=== CREATING SAMPLE ENGAGEMENT DATA ===')
    
    # Ottieni tutti gli utenti
    users = User.query.all()
    if not users:
        print("No users found! Create some users first.")
        return False
    
    print(f'Found {len(users)} users')
    
    # Crea campagne di esempio
    campaigns = [
        {
            'name': 'Onboarding Challenge',
            'description': 'Completa il tuo profilo e le prime attività',
            'start_date': datetime.now() - timedelta(days=30),
            'end_date': datetime.now() + timedelta(days=30),
            'status': 'active',
            'points_multiplier': 1.5,
            'max_participants': 100
        },
        {
            'name': 'Knowledge Week',
            'description': 'Partecipa ai corsi di formazione e ottieni punti extra',
            'start_date': datetime.now() - timedelta(days=7),
            'end_date': datetime.now() + timedelta(days=7),
            'status': 'active',
            'points_multiplier': 2.0,
            'max_participants': 50
        },
        {
            'name': 'Team Collaboration',
            'description': 'Lavora in team e completa progetti insieme',
            'start_date': datetime.now() - timedelta(days=14),
            'end_date': datetime.now() + timedelta(days=14),
            'status': 'active',
            'points_multiplier': 1.2,
            'max_participants': 75
        }
    ]
    
    for campaign_data in campaigns:
        existing = EngagementCampaign.query.filter_by(name=campaign_data['name']).first()
        if not existing:
            campaign = EngagementCampaign(**campaign_data)
            db.session.add(campaign)
            print(f'Created campaign: {campaign_data["name"]}')
    
    # Crea premi di esempio
    rewards = [
        {
            'name': 'Voucher Caffetteria',
            'description': 'Buono per un caffè gratuito',
            'points_cost': 50,
            'is_active': True,
            'reward_type': 'voucher',
            'max_redemptions': 100
        },
        {
            'name': 'Half Day Off',
            'description': 'Mezza giornata di permesso extra',
            'points_cost': 200,
            'is_active': True,
            'reward_type': 'time_off',
            'max_redemptions': 20
        },
        {
            'name': 'Libro Amazon',
            'description': 'Scegli un libro su Amazon fino a 25€',
            'points_cost': 150,
            'is_active': True,
            'reward_type': 'gift',
            'max_redemptions': 50
        },
        {
            'name': 'Team Lunch',
            'description': 'Pranzo team offerto dalla azienda',
            'points_cost': 300,
            'is_active': True,
            'reward_type': 'experience',
            'max_redemptions': 10
        }
    ]
    
    for reward_data in rewards:
        existing = EngagementReward.query.filter_by(name=reward_data['name']).first()
        if not existing:
            reward = EngagementReward(**reward_data)
            db.session.add(reward)
            print(f'Created reward: {reward_data["name"]}')
    
    db.session.commit()
    
    # Crea profili utente e punti per ogni utente
    activities = [
        'Completamento profilo', 'Partecipazione meeting', 'Corso di formazione',
        'Collaborazione progetto', 'Feedback fornito', 'Mentoring',
        'Proposta miglioramento', 'Condivisione conoscenza', 'Problem solving',
        'Innovazione processo', 'Supporto collega', 'Certificazione ottenuta'
    ]
    
    levels = ['Novizio', 'Beginner', 'Intermediate', 'Advanced', 'Expert', 'Master']
    
    for user in users:
        # Verifica se l'utente ha già un profilo engagement
        profile = EngagementUserProfile.query.filter_by(user_id=user.id).first()
        
        if not profile:
            # Crea profilo con dati casuali ma realistici
            total_points = random.randint(50, 1000)
            current_level = levels[min(len(levels) - 1, total_points // 150)]
            activities_completed = random.randint(5, 50)
            achievements_count = random.randint(1, 10)
            current_streak_days = random.randint(0, 30)
            
            profile = EngagementUserProfile(
                user_id=user.id,
                total_points=total_points,
                current_level=current_level,
                activities_completed=activities_completed,
                achievements_count=achievements_count,
                current_streak_days=current_streak_days,
                last_activity=datetime.now() - timedelta(days=random.randint(0, 7))
            )
            db.session.add(profile)
            
            # Crea alcune voci di punti per questo utente
            num_point_entries = random.randint(3, 15)
            for i in range(num_point_entries):
                points = random.randint(5, 50)
                activity = random.choice(activities)
                date = datetime.now() - timedelta(days=random.randint(0, 30))
                
                point_entry = EngagementPoint(
                    user_id=user.id,
                    points=points,
                    description=activity,
                    activity_type='manual',
                    source='system',
                    created_at=date
                )
                db.session.add(point_entry)
            
            print(f'Created engagement data for user: {user.username} ({total_points} points)')
    
    db.session.commit()
    print('\n✅ Sample engagement data created successfully!')
    return True

def main():
    app = create_app()
    
    with app.app_context():
        # Controlla dati esistenti
        has_data = check_engagement_data()
        
        if not has_data:
            print('\n❌ No engagement data found!')
            create_sample = input('\nDo you want to create sample engagement data? (y/n): ')
            if create_sample.lower() == 'y':
                create_sample_engagement_data()
                print('\n=== FINAL DATA CHECK ===')
                check_engagement_data()
        else:
            print('\n✅ Engagement data already exists!')
            recreate = input('\nDo you want to recreate sample data? (y/n): ')
            if recreate.lower() == 'y':
                # Elimina dati esistenti
                EngagementPoint.query.delete()
                EngagementUserProfile.query.delete()
                EngagementCampaign.query.delete() 
                EngagementReward.query.delete()
                db.session.commit()
                
                create_sample_engagement_data()
                print('\n=== FINAL DATA CHECK ===')
                check_engagement_data()

if __name__ == '__main__':
    main()