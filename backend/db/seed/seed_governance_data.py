#!/usr/bin/env python3
"""
Script per popolare il database con dati di test per Governance & Compliance.
Uso: python seed_governance_data.py [--clear]
"""

import sys
import argparse
import os
from datetime import datetime, timedelta, date
from random import choice, randint

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '../../'))

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv(os.path.join(os.path.dirname(os.path.abspath(__file__)), '../../../.env'))
except ImportError:
    print("⚠️  python-dotenv not available, make sure DATABASE_URL is set")

from app import create_app, db
from models_split.audit_compliance import CompliancePolicy, ComplianceEvent, Risk
from models_split.user import User

def clear_governance_data():
    """Cancella tutti i dati di governance esistenti."""
    print("🗑️  Cancellazione dati Governance esistenti...")
    
    # Ordine importante per rispettare le foreign key
    db.session.query(ComplianceEvent).delete()
    db.session.query(Risk).delete()
    db.session.query(CompliancePolicy).delete()
    
    db.session.commit()
    print("✅ Dati Governance puliti!")

def seed_compliance_policies():
    """Crea policy di compliance di test."""
    print("📋 Creazione Policy di Compliance...")
    
    # Ottieni un admin user per associare le policy
    admin_user = User.query.filter_by(role='admin').first()
    if not admin_user:
        print("⚠️  Nessun utente admin trovato. Saltando associazione reviewer.")
    
    policies_data = [
        {
            'name': 'Politica di Sicurezza Informatica',
            'description': 'Definisce le linee guida per la sicurezza dei sistemi informatici aziendali, inclusi access control, password policy e gestione degli incidenti di sicurezza.',
            'policy_type': 'security',
            'framework': 'ISO27001',
            'article_reference': 'ISO 27001:2013 A.9.1.1',
            'rules_config': {
                'password_complexity': True,
                'mfa_required': True,
                'session_timeout': 30,
                'failed_login_attempts': 5
            },
            'trigger_conditions': {
                'events': ['login_failure', 'suspicious_access', 'privilege_escalation'],
                'thresholds': {'failed_logins': 3, 'unusual_hours': True}
            },
            'actions': {
                'log_event': True,
                'notify_admin': True,
                'auto_lock_account': True
            },
            'version': '2.1',
            'next_review_date': datetime.now() + timedelta(days=180)
        },
        {
            'name': 'Politica Privacy e GDPR',
            'description': 'Regole per il trattamento dei dati personali in conformità al GDPR, inclusi consensi, diritti degli interessati e data retention.',
            'policy_type': 'privacy',
            'framework': 'GDPR',
            'article_reference': 'Art. 6, 7, 17 GDPR',
            'rules_config': {
                'consent_required': True,
                'data_retention_months': 24,
                'anonymization_required': True,
                'right_to_erasure': True
            },
            'trigger_conditions': {
                'events': ['data_access', 'data_export', 'data_deletion'],
                'data_types': ['PII', 'sensitive_personal_data']
            },
            'actions': {
                'log_detailed': True,
                'notify_dpo': True,
                'audit_trail': True
            },
            'version': '1.3',
            'next_review_date': datetime.now() + timedelta(days=90)
        },
        {
            'name': 'Politica Operativa Progetti',
            'description': 'Linee guida per la gestione operativa dei progetti, inclusi processi di approvazione, documentazione e controllo qualità.',
            'policy_type': 'operational',
            'framework': 'ISO27001',
            'article_reference': 'ISO 27001:2013 A.14.1.1',
            'rules_config': {
                'project_approval_required': True,
                'documentation_mandatory': True,
                'quality_gates': 3,
                'risk_assessment_required': True
            },
            'trigger_conditions': {
                'events': ['project_creation', 'budget_change', 'timeline_change'],
                'thresholds': {'budget_variance': 10, 'timeline_delay': 7}
            },
            'actions': {
                'notify_manager': True,
                'escalate_to_admin': True,
                'require_justification': True
            },
            'version': '1.0',
            'next_review_date': datetime.now() + timedelta(days=365)
        },
        {
            'name': 'Politica Gestione Accessi',
            'description': 'Definisce i controlli di accesso per sistemi, applicazioni e dati aziendali, inclusi criteri di autorizzazione e revoca.',
            'policy_type': 'access',
            'framework': 'SOC2',
            'article_reference': 'SOC 2 CC6.1',
            'rules_config': {
                'principle_of_least_privilege': True,
                'regular_access_review': True,
                'access_review_frequency_days': 90,
                'automatic_deprovisioning': True
            },
            'trigger_conditions': {
                'events': ['role_change', 'department_change', 'termination'],
                'schedule': 'quarterly'
            },
            'actions': {
                'review_access_rights': True,
                'notify_hr': True,
                'audit_permissions': True
            },
            'version': '1.1',
            'next_review_date': datetime.now() + timedelta(days=120)
        },
        {
            'name': 'Politica Gestione Risorse Umane',
            'description': 'Procedure per la gestione del personale, inclusi hiring, onboarding, performance management e offboarding.',
            'policy_type': 'hr',
            'framework': 'ISO27001',
            'article_reference': 'ISO 27001:2013 A.7.1.1',
            'rules_config': {
                'background_check_required': True,
                'security_training_mandatory': True,
                'nda_required': True,
                'exit_interview_required': True
            },
            'trigger_conditions': {
                'events': ['new_hire', 'role_change', 'performance_review', 'termination']
            },
            'actions': {
                'schedule_training': True,
                'update_access_rights': True,
                'notify_security_team': True
            },
            'version': '2.0',
            'next_review_date': datetime.now() + timedelta(days=275)
        }
    ]
    
    policies = []
    for policy_data in policies_data:
        policy = CompliancePolicy(
            name=policy_data['name'],
            description=policy_data['description'],
            policy_type=policy_data['policy_type'],
            framework=policy_data['framework'],
            article_reference=policy_data['article_reference'],
            rules_config=policy_data['rules_config'],
            trigger_conditions=policy_data['trigger_conditions'],
            actions=policy_data['actions'],
            version=policy_data['version'],
            is_active=True,
            effective_date=datetime.now() - timedelta(days=randint(30, 180)),
            next_review_date=policy_data['next_review_date'],
            reviewed_by=admin_user.id if admin_user else None,
            last_review_date=datetime.now() - timedelta(days=randint(1, 30))
        )
        db.session.add(policy)
        policies.append(policy)
    
    db.session.commit()
    print(f"✅ Creati {len(policies)} Policy di Compliance")
    return policies

def seed_compliance_events():
    """Crea eventi di compliance di test."""
    print("🚨 Creazione Eventi di Compliance...")
    
    # Ottieni utenti per associare agli eventi
    users = User.query.all()
    if not users:
        print("⚠️  Nessun utente trovato. Gli eventi non avranno utenti associati.")
    
    events_data = [
        {
            'event_type': 'access_violation',
            'event_category': 'security',
            'severity': 'high',
            'title': 'Tentativo di accesso non autorizzato ai dati finanziari',
            'description': 'Rilevato tentativo di accesso a documenti finanziari riservati da parte di un utente senza le necessarie autorizzazioni. L\'accesso è stato bloccato automaticamente.',
            'compliance_framework': 'SOC2',
            'policy_reference': 'ACC-001',
            'regulatory_impact': True,
            'source_system': 'web',
            'affected_resources': ['financial_documents', 'budget_reports'],
            'risk_score': 85,
            'event_metadata': {
                'attempted_resource': '/api/financial/reports',
                'access_level_required': 'admin',
                'user_access_level': 'employee'
            },
            'status': 'investigating'
        },
        {
            'event_type': 'data_breach',
            'event_category': 'privacy',
            'severity': 'critical',
            'title': 'Possibile esposizione dati clienti',
            'description': 'Sistema di monitoraggio ha rilevato un\'anomalia nell\'accesso ai dati dei clienti. Sono stati esposti potenzialmente 50 record con informazioni personali.',
            'compliance_framework': 'GDPR',
            'policy_reference': 'PRI-002',
            'regulatory_impact': True,
            'source_system': 'api',
            'affected_resources': ['client_database', 'contact_information'],
            'risk_score': 95,
            'event_metadata': {
                'records_affected': 50,
                'data_types': ['email', 'phone', 'address'],
                'detection_method': 'automated_monitoring'
            },
            'status': 'open'
        },
        {
            'event_type': 'policy_violation',
            'event_category': 'operational',
            'severity': 'medium',
            'title': 'Violazione policy approvazione progetti',
            'description': 'Progetto avviato senza seguire la procedura di approvazione standard. Budget di €15.000 allocato senza autorizzazione del manager.',
            'compliance_framework': 'ISO27001',
            'policy_reference': 'OPE-003',
            'regulatory_impact': False,
            'source_system': 'web',
            'affected_resources': ['project_management', 'budget_allocation'],
            'risk_score': 60,
            'event_metadata': {
                'project_id': 'PROJ-2024-001',
                'budget_amount': 15000,
                'missing_approvals': ['manager_approval', 'budget_approval']
            },
            'status': 'resolved'
        },
        {
            'event_type': 'unauthorized_access',
            'event_category': 'security',
            'severity': 'warning',
            'title': 'Accesso fuori orario lavorativo',
            'description': 'Rilevato accesso al sistema durante orari non lavorativi (domenica 02:30). Attività monitorata per pattern sospetti.',
            'compliance_framework': 'SOC2',
            'policy_reference': 'SEC-004',
            'regulatory_impact': False,
            'source_system': 'web',
            'affected_resources': ['user_portal', 'project_data'],
            'risk_score': 35,
            'event_metadata': {
                'access_time': '02:30:15',
                'day_of_week': 'Sunday',
                'session_duration_minutes': 45
            },
            'status': 'investigating'
        },
        {
            'event_type': 'system_anomaly',
            'event_category': 'operational',
            'severity': 'info',
            'title': 'Anomalia nel pattern di utilizzo sistema',
            'description': 'Rilevato pattern di utilizzo inusuale: 300% di incremento nelle richieste API nell\'ultima ora. Possibile automazione non autorizzata.',
            'compliance_framework': 'ISO27001',
            'policy_reference': 'SYS-005',
            'regulatory_impact': False,
            'source_system': 'api',
            'affected_resources': ['api_endpoints', 'database_queries'],
            'risk_score': 45,
            'event_metadata': {
                'request_count': 1250,
                'normal_baseline': 420,
                'peak_endpoints': ['/api/projects', '/api/timesheets']
            },
            'status': 'resolved'
        }
    ]
    
    events = []
    for i, event_data in enumerate(events_data):
        # Assegna utenti casuali ad alcuni eventi
        user = choice(users) if users and randint(1, 3) == 1 else None
        
        # Calcola date realistiche
        created_days_ago = randint(1, 30)
        created_at = datetime.now() - timedelta(days=created_days_ago)
        
        # Se l'evento è resolved, aggiungi resolution data
        resolved_at = None
        resolved_by = None
        resolution_notes = None
        
        if event_data['status'] == 'resolved':
            resolved_at = created_at + timedelta(hours=randint(2, 48))
            resolved_by = choice(users).id if users else None
            resolution_notes = "Evento risolto dopo investigazione. Implementate misure correttive."
        
        event = ComplianceEvent(
            event_type=event_data['event_type'],
            event_category=event_data['event_category'],
            severity=event_data['severity'],
            title=event_data['title'],
            description=event_data['description'],
            compliance_framework=event_data['compliance_framework'],
            policy_reference=event_data['policy_reference'],
            regulatory_impact=event_data['regulatory_impact'],
            source_ip=f"192.168.1.{randint(100, 200)}",
            source_system=event_data['source_system'],
            affected_resources=event_data['affected_resources'],
            risk_score=event_data['risk_score'],
            event_metadata=event_data['event_metadata'],
            evidence_data={
                'detection_time': created_at.isoformat(),
                'automated_response': True,
                'investigation_id': f"INV-{i+1:04d}"
            },
            created_at=created_at,
            detected_at=created_at,
            occurred_at=created_at - timedelta(minutes=randint(0, 30)),
            status=event_data['status'],
            resolved_at=resolved_at,
            resolved_by=resolved_by,
            resolution_notes=resolution_notes,
            user_id=user.id if user else None,
            notification_sent=True if event_data['severity'] in ['critical', 'high'] else False,
            escalated=True if event_data['severity'] == 'critical' else False,
            escalated_at=created_at + timedelta(hours=1) if event_data['severity'] == 'critical' else None
        )
        db.session.add(event)
        events.append(event)
    
    db.session.commit()
    print(f"✅ Creati {len(events)} Eventi di Compliance")
    return events

def seed_risks():
    """Crea rischi aziendali di test."""
    print("⚠️  Creazione Rischi Aziendali...")
    
    # Ottieni utenti per associare come owner dei rischi
    users = User.query.all()
    managers_and_admins = [u for u in users if u.role in ['admin', 'manager']] if users else []
    
    risks_data = [
        {
            'title': 'Vulnerabilità Sistema di Autenticazione',
            'description': 'Il sistema di autenticazione presenta potenziali vulnerabilità che potrebbero essere sfruttate per accessi non autorizzati. Rischio di compromissione account utente.',
            'category': 'security',
            'risk_type': 'cybersecurity',
            'probability': 3,  # Media
            'impact': 5,       # Molto alto
            'mitigation_strategy': 'Implementazione autenticazione multi-fattore, audit di sicurezza completo, aggiornamento protocolli di sicurezza.',
            'mitigation_actions': [
                'Audit sicurezza completo del sistema auth',
                'Implementazione MFA per tutti gli utenti',
                'Aggiornamento password policy',
                'Formazione security awareness per utenti'
            ],
            'mitigation_cost': 15000.0,
            'compliance_framework': 'ISO27001',
            'regulatory_requirements': ['ISO 27001:2013 A.9.1.2', 'SOC 2 CC6.1'],
            'responsible_department': 'IT Security',
            'status': 'under_review',
            'tags': ['cybersecurity', 'authentication', 'high_priority']
        },
        {
            'title': 'Dipendenza da Fornitori Critici',
            'description': 'Eccessiva dipendenza da fornitori chiave per servizi critici. Interruzione del servizio potrebbe impattare significativamente le operazioni.',
            'category': 'operational',
            'risk_type': 'supply_chain',
            'probability': 2,  # Bassa
            'impact': 4,       # Alto
            'mitigation_strategy': 'Diversificazione fornitori, definizione piani di contingenza, accordi SLA più stringenti.',
            'mitigation_actions': [
                'Identificazione fornitori alternativi',
                'Negoziazione contratti backup',
                'Sviluppo piani di business continuity',
                'Test scenari di fallimento fornitori'
            ],
            'mitigation_cost': 25000.0,
            'compliance_framework': 'SOC2',
            'regulatory_requirements': ['SOC 2 CC9.1'],
            'responsible_department': 'Operations',
            'status': 'identified',
            'tags': ['supply_chain', 'business_continuity', 'operational']
        },
        {
            'title': 'Non Conformità GDPR',
            'description': 'Processi di gestione dati personali potrebbero non essere pienamente conformi ai requisiti GDPR. Rischio di sanzioni e danni reputazionali.',
            'category': 'compliance',
            'risk_type': 'regulatory',
            'probability': 3,  # Media
            'impact': 4,       # Alto
            'mitigation_strategy': 'Gap analysis GDPR, aggiornamento procedure, formazione specifica, nomina DPO.',
            'mitigation_actions': [
                'Gap analysis completa GDPR',
                'Aggiornamento privacy policy',
                'Implementazione consent management',
                'Formazione team su data protection'
            ],
            'mitigation_cost': 8000.0,
            'compliance_framework': 'GDPR',
            'regulatory_requirements': ['Art. 5 GDPR', 'Art. 25 GDPR', 'Art. 32 GDPR'],
            'responsible_department': 'Legal & Compliance',
            'status': 'mitigated',
            'tags': ['gdpr', 'privacy', 'regulatory']
        },
        {
            'title': 'Concentrazione Rischio Clientela',
            'description': 'Il 60% del fatturato dipende da 3 clienti principali. Perdita di un cliente importante comporterebbe significativo impatto finanziario.',
            'category': 'financial',
            'risk_type': 'revenue_concentration',
            'probability': 2,  # Bassa
            'impact': 5,       # Molto alto
            'mitigation_strategy': 'Diversificazione portfolio clienti, sviluppo nuovi mercati, contratti pluriennali.',
            'mitigation_actions': [
                'Piano acquisizione nuovi clienti',
                'Sviluppo prodotti per nuovi segmenti',
                'Negoziazione contratti long-term',
                'Analisi rischio controparte clienti'
            ],
            'mitigation_cost': 50000.0,
            'compliance_framework': 'SOC2',
            'regulatory_requirements': ['Risk Management Framework'],
            'responsible_department': 'Sales & Business Development',
            'status': 'under_review',
            'tags': ['financial', 'client_concentration', 'revenue']
        },
        {
            'title': 'Perdita Competenze Chiave',
            'description': 'Rischio di perdita di personale altamente specializzato in tecnologie critiche. Knowledge gap potrebbe impattare delivery progetti.',
            'category': 'strategic',
            'risk_type': 'human_resources',
            'probability': 4,  # Alta
            'impact': 3,       # Medio
            'mitigation_strategy': 'Piani di retention, knowledge sharing, documentazione processi, cross-training.',
            'mitigation_actions': [
                'Implementazione knowledge management system',
                'Programmi retention per key personnel',
                'Cross-training tra team members',
                'Documentazione best practices'
            ],
            'mitigation_cost': 12000.0,
            'compliance_framework': 'ISO27001',
            'regulatory_requirements': ['ISO 27001:2013 A.7.2.2'],
            'responsible_department': 'Human Resources',
            'status': 'accepted',
            'tags': ['hr', 'knowledge_management', 'retention']
        }
    ]
    
    risks = []
    for risk_data in risks_data:
        # Assegna owner casuale tra manager/admin
        owner = choice(managers_and_admins) if managers_and_admins else None
        
        # Calcola risk score
        risk_score = risk_data['probability'] * risk_data['impact']
        
        # Determina risk level basato su score
        if risk_score >= 20:
            risk_level = 'critical'
        elif risk_score >= 15:
            risk_level = 'high'
        elif risk_score >= 8:
            risk_level = 'medium'
        else:
            risk_level = 'low'
        
        # Date realistiche
        identified_date = datetime.now() - timedelta(days=randint(10, 90))
        last_review_date = identified_date + timedelta(days=randint(1, 30))
        next_review_date = datetime.now() + timedelta(days=randint(30, 180))
        
        # Se il rischio è mitigated, aggiungi resolved date
        resolved_date = None
        if risk_data['status'] == 'mitigated':
            resolved_date = last_review_date + timedelta(days=randint(1, 15))
        
        # Deadline per mitigation
        mitigation_deadline = datetime.now() + timedelta(days=randint(60, 365))
        
        risk = Risk(
            title=risk_data['title'],
            description=risk_data['description'],
            category=risk_data['category'],
            risk_type=risk_data['risk_type'],
            probability=risk_data['probability'],
            impact=risk_data['impact'],
            risk_level=risk_level,
            risk_score=risk_score,
            status=risk_data['status'],
            owner_id=owner.id if owner else None,
            responsible_department=risk_data['responsible_department'],
            mitigation_strategy=risk_data['mitigation_strategy'],
            mitigation_actions=risk_data['mitigation_actions'],
            mitigation_deadline=mitigation_deadline,
            mitigation_cost=risk_data['mitigation_cost'],
            regulatory_requirements=risk_data['regulatory_requirements'],
            compliance_framework=risk_data['compliance_framework'],
            identified_date=identified_date,
            last_review_date=last_review_date,
            next_review_date=next_review_date,
            resolved_date=resolved_date,
            tags=risk_data['tags'],
            external_references={
                'documentation': f'/docs/risk_{risk_data["category"]}_assessment.pdf',
                'tools': ['Risk Management Platform', 'Compliance Dashboard']
            }
        )
        db.session.add(risk)
        risks.append(risk)
    
    db.session.commit()
    print(f"✅ Creati {len(risks)} Rischi Aziendali")
    return risks

def main():
    parser = argparse.ArgumentParser(description='Popola il database con dati di test per Governance & Compliance')
    parser.add_argument('--clear', action='store_true', help='Cancella i dati Governance esistenti prima di creare i nuovi')
    args = parser.parse_args()
    
    app = create_app()
    
    with app.app_context():
        print("🏛️  Avvio seeding Governance & Compliance...")
        
        if args.clear:
            clear_governance_data()
        
        # Crea i dati in ordine di dipendenza
        policies = seed_compliance_policies()
        events = seed_compliance_events()
        risks = seed_risks()
        
        print(f"\n🎉 Seeding Governance completato!")
        print(f"\n📊 Dati creati:")
        print(f"  • {len(policies)} Policy di Compliance")
        print(f"  • {len(events)} Eventi di Compliance")
        print(f"  • {len(risks)} Rischi Aziendali")
        print(f"\n🔗 I dati sono ora disponibili nelle pagine Governance!")

if __name__ == '__main__':
    main()