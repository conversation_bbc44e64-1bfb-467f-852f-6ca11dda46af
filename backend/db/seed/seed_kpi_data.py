#!/usr/bin/env python3
"""
Script per popolare la tabella KPI con dati di esempio.
Utilizzato per testare il dashboard dopo la migrazione del schema.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timedelta
from extensions import db
from app import create_app

def seed_kpi_data():
    """Popola la tabella KPI con dati di esempio"""
    
    app = create_app()
    
    with app.app_context():
        try:
            # Import here to avoid circular imports
            from models_split.business import KPI
            from models import User, Project
            
            print("📊 Seeding KPI data...")
            
            # Check if KPIs already exist
            existing_kpis = KPI.query.count()
            if existing_kpis > 0:
                print(f"ℹ️  Found {existing_kpis} existing KPIs. Skipping seed.")
                return
            
            # Get first user and project for FK references
            first_user = User.query.first()
            first_project = Project.query.first()
            
            user_id = first_user.id if first_user else None
            project_id = first_project.id if first_project else None
            
            # Sample KPI data
            sample_kpis = [
                {
                    'name': 'Revenue Growth',
                    'description': 'Quarterly revenue growth percentage',
                    'target_value': 15.0,
                    'current_value': 12.5,
                    'progress': 83.3,
                    'unit': '%',
                    'kpi_type': 'financial',
                    'status': 'on_track',
                    'owner_id': user_id,
                    'project_id': project_id
                },
                {
                    'name': 'Customer Satisfaction',
                    'description': 'Average customer satisfaction score',
                    'target_value': 4.5,
                    'current_value': 4.2,
                    'progress': 93.3,
                    'unit': 'stars',
                    'kpi_type': 'quality',
                    'status': 'on_track',
                    'owner_id': user_id,
                    'project_id': project_id
                },
                {
                    'name': 'Project Completion Rate',
                    'description': 'Percentage of projects completed on time',
                    'target_value': 90.0,
                    'current_value': 78.0,
                    'progress': 86.7,
                    'unit': '%',
                    'kpi_type': 'operational',
                    'status': 'at_risk',
                    'owner_id': user_id,
                    'project_id': project_id
                },
                {
                    'name': 'Team Productivity',
                    'description': 'Average story points completed per sprint',
                    'target_value': 45.0,
                    'current_value': 52.0,
                    'progress': 115.6,
                    'unit': 'points',
                    'kpi_type': 'performance',
                    'status': 'exceeding',
                    'owner_id': user_id,
                    'project_id': project_id
                },
                {
                    'name': 'Cost Reduction',
                    'description': 'Operational cost reduction target',
                    'target_value': 10.0,
                    'current_value': 6.5,
                    'progress': 65.0,
                    'unit': '%',
                    'kpi_type': 'financial',
                    'status': 'behind',
                    'owner_id': user_id,
                    'project_id': project_id
                }
            ]
            
            # Create KPI objects
            created_kpis = []
            for kpi_data in sample_kpis:
                kpi = KPI(
                    name=kpi_data['name'],
                    description=kpi_data['description'],
                    target_value=kpi_data['target_value'],
                    current_value=kpi_data['current_value'],
                    unit=kpi_data['unit'],
                    kpi_type=kpi_data['kpi_type'],
                    status=kpi_data['status'],
                    created_at=datetime.utcnow() - timedelta(days=30),
                    updated_at=datetime.utcnow()
                )
                
                # Add new fields if they exist
                if hasattr(kpi, 'progress'):
                    kpi.progress = kpi_data['progress']
                if hasattr(kpi, 'owner_id'):
                    kpi.owner_id = kpi_data['owner_id']
                if hasattr(kpi, 'project_id'):
                    kpi.project_id = kpi_data['project_id']
                
                db.session.add(kpi)
                created_kpis.append(kpi)
            
            db.session.commit()
            
            print(f"✅ Created {len(created_kpis)} sample KPIs:")
            for kpi in created_kpis:
                print(f"  - {kpi.name}: {kpi.current_value}/{kpi.target_value} {kpi.unit} ({kpi.status})")
            
        except Exception as e:
            print(f"❌ Error seeding KPI data: {str(e)}")
            db.session.rollback()
            raise

if __name__ == "__main__":
    seed_kpi_data()