#!/usr/bin/env python3
"""
Script per inizializzare il database delle certificazioni.
Sincronizza il catalogo JSON con il database SQLAlchemy.
"""
import os
import sys
import json
from datetime import datetime

# Aggiungi il percorso della directory backend al path Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from extensions import db
from models import CertificationStandard

def load_catalog():
    """Carica il catalogo dal file JSON."""
    catalog_path = os.path.join(os.path.dirname(__file__), 'config', 'certifications_catalog.json')
    
    try:
        with open(catalog_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ Errore nel caricamento del catalogo: {e}")
        return {}

def sync_standards_to_database(app):
    """Sincronizza il catalogo JSON con il database."""
    catalog = load_catalog()
    
    if not catalog:
        print("❌ Catalogo vuoto, interruzione.")
        return False
    
    with app.app_context():
        print("🔄 Sincronizzazione standard di certificazione...")
        
        total_standards = 0
        created_standards = 0
        updated_standards = 0
        
        # Processa ogni categoria nel catalogo
        for category_name, standards in catalog.items():
            print(f"📂 Categoria: {category_name}")
            
            for standard_code, standard_data in standards.items():
                total_standards += 1
                
                # Controlla se lo standard esiste già
                existing_standard = CertificationStandard.query.get(standard_code)
                
                if existing_standard:
                    # Aggiorna standard esistente
                    print(f"   🔄 Aggiornamento: {standard_code}")
                    existing_standard.name = standard_data.get('name')
                    existing_standard.version = standard_data.get('version')
                    existing_standard.category = standard_data.get('category')
                    existing_standard.description = standard_data.get('description')
                    existing_standard.requirements = standard_data.get('requirements', [])
                    existing_standard.preparatory_tasks = standard_data.get('preparatory_tasks', [])
                    existing_standard.documentation_required = standard_data.get('documentation_required', [])
                    updated_standards += 1
                else:
                    # Crea nuovo standard
                    print(f"   ➕ Creazione: {standard_code}")
                    cost_data = standard_data.get('estimated_cost', {})
                    new_standard = CertificationStandard(
                        code=standard_code,
                        name=standard_data.get('name'),
                        version=standard_data.get('version'),
                        category=standard_data.get('category'),
                        industry_sector=', '.join(standard_data.get('industry_sectors', [])),
                        typical_validity_years=standard_data.get('typical_validity_years', 3),
                        renewal_notice_months=standard_data.get('renewal_notice_months', 6),
                        audit_frequency_months=standard_data.get('audit_frequency_months', 12),
                        estimated_cost_min=cost_data.get('min'),
                        estimated_cost_max=cost_data.get('max'),
                        currency=cost_data.get('currency', 'EUR'),
                        requirements=standard_data.get('requirements', []),
                        preparatory_tasks=standard_data.get('preparatory_tasks', []),
                        documentation_required=standard_data.get('documentation_required', []),
                        description=standard_data.get('description'),
                        issuing_body=standard_data.get('issuing_body'),
                        website_url=standard_data.get('website_url'),
                        is_active=True
                    )
                    db.session.add(new_standard)
                    created_standards += 1
        
        try:
            db.session.commit()
            print(f"✅ Sincronizzazione completata!")
            print(f"   📊 Standard totali processati: {total_standards}")
            print(f"   ➕ Standard creati: {created_standards}")
            print(f"   🔄 Standard aggiornati: {updated_standards}")
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante il commit: {e}")
            return False

def main():
    """Funzione principale."""
    print("🚀 Inizializzazione sistema certificazioni DatPortal")
    print("=" * 50)
    
    # Crea l'app Flask
    app = create_app()
    
    # Sincronizza il catalogo
    success = sync_standards_to_database(app)
    
    if success:
        print("\n✅ Setup completato con successo!")
        print("💡 Il sistema di certificazioni è ora pronto per l'uso.")
    else:
        print("\n❌ Setup fallito!")
        sys.exit(1)

if __name__ == "__main__":
    main()