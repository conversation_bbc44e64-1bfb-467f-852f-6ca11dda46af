#!/usr/bin/env python3
"""
Script per aggiungere feature flag per sistema self-healing
"""

import sys
import os

# Add backend to path (seguendo il pattern di update.py)
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '../'))

# Carica le variabili d'ambiente dal file .env
try:
    from dotenv import load_dotenv
    load_dotenv(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env'))
except ImportError:
    # Se python-dotenv non è disponibile, prova a usare load_env.py
    try:
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from load_env import load_env
        load_env()
    except ImportError:
        print("⚠️  Attenzione: Impossibile caricare le variabili d'ambiente")

from app import create_app
from extensions import db
from models_split.settings import FeatureFlag
from datetime import datetime

def add_self_healing_feature_flag():
    """Aggiunge feature flag per sistema self-healing."""
    
    # Crea l'applicazione (seguendo il pattern di update.py)
    app = create_app()
    
    with app.app_context():
        try:
            # Controlla se esiste già
            existing_flag = FeatureFlag.query.filter_by(feature_key='self_healing_system').first()
            
            if existing_flag:
                print(f"✅ Feature flag 'self_healing_system' già esistente (enabled: {existing_flag.is_enabled})")
                return
            
            # Crea nuovo feature flag
            feature_flag = FeatureFlag(
                feature_key='self_healing_system',
                display_name='Sistema Self-Healing',
                description='Sistema di auto-diagnosi e healing automatico errori con AI',
                is_enabled=True,  # Abilitato di default per testing
                updated_by=1  # Admin user ID (assumendo esista)
            )
            
            db.session.add(feature_flag)
            db.session.commit()
            
            print("✅ Feature flag 'self_healing_system' aggiunto con successo!")
            print(f"   - Display Name: {feature_flag.display_name}")
            print(f"   - Enabled: {feature_flag.is_enabled}")
            print(f"   - Description: {feature_flag.description}")
            
        except Exception as e:
            print(f"❌ Errore nell'aggiunta del feature flag: {str(e)}")
            db.session.rollback()
            return False
            
    return True

if __name__ == '__main__':
    success = add_self_healing_feature_flag()
    sys.exit(0 if success else 1)