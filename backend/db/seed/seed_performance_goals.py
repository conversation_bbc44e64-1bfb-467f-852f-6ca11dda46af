"""
Script per popolare il database con template di obiettivi e obiettivi di esempio
python db/update.py seed_performance_goals
"""

from models import db
from models_split.performance import PerformanceGoal, PerformanceReview
from models_split.user import User
from datetime import datetime, date
import random

def seed_performance_goals():
    """Crea template di obiettivi e obiettivi di esempio"""
    try:
        print("🎯 Creazione template obiettivi...")

        # Template obiettivi per categoria
        goal_templates = [
            # Technical Skills
            {
                'title': 'Miglioramento Competenze Frontend',
                'description': 'Acquisire competenze avanzate in Vue.js 3 e Composition API per migliorare l\'architettura delle applicazioni frontend.',
                'category': 'technical',
                'priority': 'high',
                'weight': 0.3,
                'success_criteria': 'Completare 2 progetti utilizzando Vue.js 3 Composition API, implementare almeno 3 custom composables riutilizzabili',
                'measurable_outcomes': 'Certificazione Vue.js, riduzione del 20% del tempo di sviluppo frontend, code review score > 8/10',
                'visibility': 'public',
                'is_template': True
            },
            {
                'title': 'Architettura Backend Scalabile',
                'description': 'Progettare e implementare architetture backend scalabili utilizzando microservizi e pattern moderni.',
                'category': 'technical',
                'priority': 'high',
                'weight': 0.25,
                'success_criteria': 'Implementare almeno 2 microservizi, utilizzare Redis per caching, implementare CI/CD pipeline',
                'measurable_outcomes': 'Riduzione latenza API del 30%, miglioramento uptime al 99.9%, documentazione architetturale completa',
                'visibility': 'public',
                'is_template': True
            },
            {
                'title': 'Database Optimization & Performance',
                'description': 'Ottimizzare le performance del database e implementare strategie di monitoring avanzate.',
                'category': 'technical',
                'priority': 'medium',
                'weight': 0.2,
                'success_criteria': 'Identificare e ottimizzare 5 query lente, implementare indexing strategico, setup monitoring',
                'measurable_outcomes': 'Riduzione tempo query del 40%, zero downtime per manutenzione DB, alerts proattivi',
                'visibility': 'public',
                'is_template': True
            },
            
            # Soft Skills
            {
                'title': 'Leadership e Gestione Team',
                'description': 'Sviluppare competenze di leadership per gestire efficacemente team di sviluppo.',
                'category': 'soft_skills',
                'priority': 'high',
                'weight': 0.25,
                'success_criteria': 'Guidare team di 3-5 persone, condurre retrospective settimanali, mentorare 1 junior developer',
                'measurable_outcomes': 'Team satisfaction score > 8/10, delivery rate migliorato del 15%, junior promosso a mid-level',
                'visibility': 'public',
                'is_template': True
            },
            {
                'title': 'Comunicazione Efficace',
                'description': 'Migliorare le competenze di comunicazione tecnica e presentazione.',
                'category': 'soft_skills',
                'priority': 'medium',
                'weight': 0.2,
                'success_criteria': 'Presentare 2 tech talks interni, scrivere 4 blog post tecnici, condurre training per team',
                'measurable_outcomes': 'Feedback presentazioni > 8/10, engagement blog posts > 100 views, training effectiveness > 85%',
                'visibility': 'public',
                'is_template': True
            },
            
            # Business Goals
            {
                'title': 'Customer Satisfaction Improvement',
                'description': 'Migliorare la soddisfazione del cliente attraverso deliverable di qualità superiore.',
                'category': 'business',
                'priority': 'high',
                'weight': 0.3,
                'success_criteria': 'Implementare feedback loop con clienti, ridurre bug in produzione, migliorare documentazione',
                'measurable_outcomes': 'Customer satisfaction > 9/10, riduzione bug del 50%, documentazione completa al 100%',
                'visibility': 'public',
                'is_template': True
            },
            {
                'title': 'Process Innovation',
                'description': 'Innovare i processi di sviluppo per aumentare efficienza e qualità.',
                'category': 'business',
                'priority': 'medium',
                'weight': 0.25,
                'success_criteria': 'Automatizzare 3 processi manuali, implementare quality gates, ridurre time-to-market',
                'measurable_outcomes': 'Automazione 80% tasks ripetitivi, zero release con critical bugs, delivery time -25%',
                'visibility': 'public',
                'is_template': True
            },
            
            # Career Development
            {
                'title': 'Crescita Professionale Senior',
                'description': 'Obiettivi per l\'avanzamento al livello senior con competenze tecniche e di leadership.',
                'category': 'career_development',
                'priority': 'high',
                'weight': 0.35,
                'success_criteria': 'Completare certificazioni tecniche, guidare progetto end-to-end, mentorare junior',
                'measurable_outcomes': '2 certificazioni ottenute, progetto completato in tempo/budget, junior promosso',
                'visibility': 'public',
                'is_template': True
            },
            {
                'title': 'Specializzazione Tecnica',
                'description': 'Sviluppare expertise approfondita in un\'area tecnica specifica.',
                'category': 'career_development',
                'priority': 'medium',
                'weight': 0.25,
                'success_criteria': 'Diventare subject matter expert, contribuire a open source, speaking a conferenze',
                'measurable_outcomes': 'Riconoscimento come expert interno, 3 contributi OSS accettati, 1 talk a conferenza',
                'visibility': 'public',
                'is_template': True
            }
        ]

        # Crea template
        admin_user = User.query.filter_by(role='admin').first()
        if not admin_user:
            print("⚠️ Nessun utente admin trovato. Creazione template saltata.")
            return

        templates_created = []
        for template_data in goal_templates:
            # Verifica se template già esiste
            existing = PerformanceGoal.query.filter_by(
                title=template_data['title'], 
                is_template=True
            ).first()
            
            if existing:
                print(f"Template '{template_data['title']}' già esistente, saltato")
                templates_created.append(existing)
                continue

            template = PerformanceGoal(
                title=template_data['title'],
                description=template_data['description'],
                category=template_data['category'],
                priority=template_data['priority'],
                weight=template_data['weight'],
                success_criteria=template_data['success_criteria'],
                measurable_outcomes=template_data['measurable_outcomes'],
                visibility=template_data['visibility'],
                is_template=template_data['is_template'],
                year=datetime.now().year,
                target_year=datetime.now().year,
                created_by=admin_user.id,
                status='active'
            )
            
            db.session.add(template)
            templates_created.append(template)
            print(f"✅ Template creato: {template_data['title']}")

        db.session.commit()
        print(f"🎯 {len(templates_created)} template obiettivi creati/verificati")

        # Ora crea obiettivi di esempio per alcuni dipendenti
        print("\n📋 Creazione obiettivi di esempio...")
        
        # Prendi alcuni dipendenti (non admin)
        employees = User.query.filter(User.role.in_(['employee', 'manager'])).limit(3).all()
        
        if not employees:
            print("⚠️ Nessun dipendente trovato per obiettivi di esempio")
            return

        current_year = datetime.now().year
        goals_created = 0

        for employee in employees:
            print(f"\n👤 Creazione obiettivi per {employee.full_name}...")
            
            # Assegna 2-4 template casuali a questo dipendente
            assigned_templates = random.sample(templates_created, random.randint(2, 4))
            
            for template in assigned_templates:
                # Verifica se già assegnato
                existing_goal = PerformanceGoal.query.filter_by(
                    employee_id=employee.id,
                    template_id=template.id,
                    year=current_year
                ).first()
                
                if existing_goal:
                    continue

                # Crea obiettivo da template
                new_goal = PerformanceGoal.create_from_template(
                    template_id=template.id,
                    employee_id=employee.id,
                    assigned_by_id=admin_user.id,
                    year=current_year
                )
                
                # Simula progress realistico
                if random.random() > 0.3:  # 70% chance di avere progress
                    new_goal.progress_percentage = random.randint(10, 90)
                    if new_goal.progress_percentage >= 80:  # Se quasi completato, potrebbe essere completato
                        if random.random() > 0.5:
                            new_goal.status = 'completed'
                            new_goal.completion_date = date.today()
                            new_goal.achievement_rating = round(random.uniform(3.5, 5.0), 1)
                            new_goal.completion_notes = "Obiettivo completato con successo durante il periodo di valutazione."
                
                # Aggiungi assessment se progress > 50%
                if new_goal.progress_percentage and new_goal.progress_percentage > 50:
                    new_goal.employee_self_assessment = f"Sto facendo buoni progressi verso il completamento. Attualmente al {new_goal.progress_percentage}% dell'obiettivo."
                
                db.session.add(new_goal)
                goals_created += 1
                print(f"  ✅ Obiettivo assegnato: {template.title} (Progress: {new_goal.progress_percentage}%)")

        db.session.commit()
        print(f"\n📋 {goals_created} obiettivi di esempio creati")

        # Summary finale
        total_templates = PerformanceGoal.query.filter_by(is_template=True).count()
        total_goals = PerformanceGoal.query.filter_by(is_template=False).count()
        
        print(f"\n📊 RIASSUNTO:")
        print(f"   🎯 Template obiettivi: {total_templates}")
        print(f"   📋 Obiettivi personali: {total_goals}")
        print(f"   👥 Dipendenti con obiettivi: {len(employees)}")

    except Exception as e:
        db.session.rollback()
        print(f"❌ Errore durante il seeding: {str(e)}")
        raise

if __name__ == "__main__":
    seed_performance_goals()