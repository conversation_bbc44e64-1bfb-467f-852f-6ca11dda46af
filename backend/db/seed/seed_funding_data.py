#!/usr/bin/env python3
"""
Seed script per popolare il database con dati di esempio per i bandi di finanziamento.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime, date, timedelta
import random
from decimal import Decimal

from app import create_app
from extensions import db
from models import (
    FundingOpportunity, FundingApplication, FundingExpense, 
    User, Project, ProjectFundingLink
)

def create_funding_opportunities(users):
    """Crea opportunità di bando realistiche"""
    
    opportunities = [
        {
            'title': 'Bando Innovazione Digitale 2024',
            'description': 'Finanziamento per progetti di trasformazione digitale delle PMI italiane. Il bando sostiene investimenti in tecnologie 4.0, digitalizzazione dei processi e sviluppo di competenze digitali.',
            'source_entity': 'Ministero dello Sviluppo Economico',
            'program_name': 'Piano Transizione 4.0',
            'call_identifier': 'MISE-TD-2024-001',
            'total_budget': Decimal('50000000.00'),
            'max_grant_amount': Decimal('500000.00'),
            'min_grant_amount': Decimal('50000.00'),
            'contribution_percentage': 60,
            'application_deadline': date.today() + timedelta(days=45),
            'funding_period_start': date.today() + timedelta(days=90),
            'project_duration_months': 12,
            'geographic_scope': 'nazionale',
            'status': 'open',
            'target_sectors': ['ICT', 'Manifatturiero', 'Servizi'],
            'eligibility_criteria': ['PMI', 'Sede in Italia', 'Bilancio certificato'],
            'required_documents': ['Progetto tecnico', 'Piano finanziario', 'Visura camerale'],
            'evaluation_criteria': ['Innovatività', 'Sostenibilità economica', 'Impatto occupazionale'],
            'is_active': True
        },
        {
            'title': 'Horizon Europe - Green Deal',
            'description': 'Programma europeo per progetti di ricerca e innovazione nel settore della sostenibilità ambientale e dell\'economia circolare.',
            'source_entity': 'Commissione Europea',
            'program_name': 'Horizon Europe',
            'call_identifier': 'HORIZON-EU-GD-2024-02',
            'total_budget': Decimal('100000000.00'),
            'max_grant_amount': Decimal('2000000.00'),
            'min_grant_amount': Decimal('200000.00'),
            'contribution_percentage': 100,
            'application_deadline': date.today() + timedelta(days=75),
            'funding_period_start': date.today() + timedelta(days=120),
            'project_duration_months': 36,
            'geographic_scope': 'europeo',
            'status': 'open',
            'target_sectors': ['Energia rinnovabile', 'Economia circolare', 'Biotecnologie'],
            'eligibility_criteria': ['Consorzio internazionale', 'Almeno 3 paesi UE', 'Partner di ricerca'],
            'required_documents': ['Proposal', 'Budget dettagliato', 'Accordo di consorzio'],
            'evaluation_criteria': ['Eccellenza scientifica', 'Impatto', 'Qualità implementazione'],
            'is_active': True
        },
        {
            'title': 'Bando Startup Innovative Regione Lombardia',
            'description': 'Sostegno alla nascita e sviluppo di startup innovative in Lombardia con focus su tecnologie emergenti e sostenibilità.',
            'source_entity': 'Regione Lombardia',
            'program_name': 'Lombardia Startup',
            'call_identifier': 'RL-START-2024-03',
            'total_budget': Decimal('15000000.00'),
            'max_grant_amount': Decimal('150000.00'),
            'min_grant_amount': Decimal('25000.00'),
            'contribution_percentage': 70,
            'application_deadline': date.today() + timedelta(days=30),
            'funding_period_start': date.today() + timedelta(days=60),
            'project_duration_months': 12,
            'geographic_scope': 'regionale',
            'status': 'open',
            'target_sectors': ['Fintech', 'Healthtech', 'Greentech', 'AI'],
            'eligibility_criteria': ['Startup innovativa', 'Sede in Lombardia', 'Costituita da meno di 5 anni'],
            'required_documents': ['Business plan', 'Pitch deck', 'CV team'],
            'evaluation_criteria': ['Innovazione', 'Scalabilità', 'Team', 'Market potential'],
            'is_active': True
        },
        {
            'title': 'PNRR - Transizione Ecologica PMI',
            'description': 'Finanziamenti PNRR per progetti di transizione ecologica delle piccole e medie imprese con focus su efficienza energetica e fonti rinnovabili.',
            'source_entity': 'Ministero della Transizione Ecologica',
            'program_name': 'PNRR Missione 2',
            'call_identifier': 'PNRR-M2-TE-2024-01',
            'total_budget': Decimal('200000000.00'),
            'max_grant_amount': Decimal('1000000.00'),
            'min_grant_amount': Decimal('100000.00'),
            'contribution_percentage': 80,
            'application_deadline': date.today() + timedelta(days=60),
            'funding_period_start': date.today() + timedelta(days=90),
            'project_duration_months': 18,
            'geographic_scope': 'nazionale',
            'status': 'open',
            'target_sectors': ['Energia', 'Manifatturiero', 'Edilizia'],
            'eligibility_criteria': ['PMI', 'Investimenti green', 'Target emissioni CO2'],
            'required_documents': ['Audit energetico', 'Piano investimenti', 'Certificazioni ambientali'],
            'evaluation_criteria': ['Riduzione CO2', 'Efficienza energetica', 'Sostenibilità economica'],
            'is_active': True
        },
        {
            'title': 'Voucher Digitalizzazione Camera di Commercio',
            'description': 'Voucher per supportare la digitalizzazione delle micro e piccole imprese attraverso consulenze specializzate e soluzioni tecnologiche.',
            'source_entity': 'Camera di Commercio Milano',
            'program_name': 'Voucher Digitali I4.0',
            'call_identifier': 'CCIAA-VD-2024-05',
            'total_budget': Decimal('5000000.00'),
            'max_grant_amount': Decimal('25000.00'),
            'min_grant_amount': Decimal('5000.00'),
            'contribution_percentage': 50,
            'application_deadline': date.today() + timedelta(days=15),  # Scadenza imminente
            'funding_period_start': date.today() + timedelta(days=30),
            'project_duration_months': 6,
            'geographic_scope': 'locale',
            'status': 'open',
            'target_sectors': ['Commercio', 'Servizi', 'Artigianato'],
            'eligibility_criteria': ['Micro/piccola impresa', 'Sede in Lombardia', 'Iscrizione CCIAA'],
            'required_documents': ['Domanda online', 'Preventivi fornitori', 'DURC'],
            'evaluation_criteria': ['Ordine cronologico', 'Completezza documentazione'],
            'is_active': True
        },
        {
            'title': 'Bando Ricerca Industriale - Chiuso',
            'description': 'Bando per progetti di ricerca industriale nel settore automotive. Scaduto il mese scorso.',
            'source_entity': 'Ministero Università e Ricerca',
            'program_name': 'Automotive Innovation',
            'call_identifier': 'MUR-AUTO-2024-01',
            'total_budget': Decimal('30000000.00'),
            'max_grant_amount': Decimal('800000.00'),
            'min_grant_amount': Decimal('100000.00'),
            'contribution_percentage': 75,
            'application_deadline': date.today() - timedelta(days=30),  # Scaduto
            'funding_period_start': date.today() + timedelta(days=60),
            'project_duration_months': 24,
            'geographic_scope': 'nazionale',
            'status': 'closed',
            'target_sectors': ['Automotive', 'Componentistica', 'Mobilità sostenibile'],
            'eligibility_criteria': ['Imprese manifatturiere', 'Centri di ricerca', 'Partnership industriale'],
            'required_documents': ['Progetto di ricerca', 'Partnership agreement', 'Budget analitico'],
            'evaluation_criteria': ['Innovazione tecnologica', 'Impatto industriale', 'Sostenibilità'],
            'is_active': True
        }
    ]
    
    created_opportunities = []
    for i, opp_data in enumerate(opportunities):
        # Aggiungi created_by ciclando tra gli utenti
        opp_data['created_by'] = users[i % len(users)].id
        opportunity = FundingOpportunity(**opp_data)
        db.session.add(opportunity)
        created_opportunities.append(opportunity)
    
    db.session.commit()
    print(f"✅ Creato {len(created_opportunities)} opportunità di bando")
    return created_opportunities

def create_funding_applications(opportunities, users, projects):
    """Crea candidature realistiche"""
    
    applications = []
    
    # Prendi alcuni progetti esistenti
    available_projects = projects[:4] if len(projects) >= 4 else projects
    
    # Candidatura 1: Progetto digitale per bando innovazione
    if len(opportunities) > 0 and len(available_projects) > 0:
        app1 = FundingApplication(
            opportunity_id=opportunities[0].id,  # Bando Innovazione Digitale
            linked_project_id=available_projects[0].id,
            project_title=f"Digitalizzazione {available_projects[0].name}",
            project_description="Progetto di trasformazione digitale con implementazione di sistema ERP cloud-based, automazione processi e formazione del personale su tecnologie 4.0.",
            requested_amount=Decimal('350000.00'),
            project_manager_id=users[1].id if len(users) > 1 else users[0].id,
            team_composition="Project Manager, 2 Sviluppatori Senior, 1 Data Analyst, 1 UX Designer",
            budget_breakdown={
                "personale": 200000,
                "software": 80000,
                "hardware": 50000,
                "formazione": 20000
            },
            project_duration_months=12,
            status='submitted',
            submission_date=datetime.now() - timedelta(days=10),
            created_by=users[0].id,
            created_at=datetime.now() - timedelta(days=15),
            updated_at=datetime.now() - timedelta(days=10)
        )
        applications.append(app1)
    
    # Candidatura 2: Progetto green per PNRR
    if len(opportunities) > 3 and len(available_projects) > 1:
        app2 = FundingApplication(
            opportunity_id=opportunities[3].id,  # PNRR Transizione Ecologica
            linked_project_id=available_projects[1].id,
            project_title=f"Green Transition {available_projects[1].name}",
            project_description="Implementazione di sistema fotovoltaico, efficientamento energetico edifici e sostituzione macchinari con tecnologie a basso impatto ambientale.",
            requested_amount=Decimal('750000.00'),
            project_manager_id=users[2].id if len(users) > 2 else users[0].id,
            team_composition="Project Manager, Ingegnere Energetico, Responsabile Ambiente, Tecnico Impianti",
            budget_breakdown={
                "impianti_fotovoltaici": 400000,
                "efficientamento": 200000,
                "macchinari": 100000,
                "consulenze": 50000
            },
            project_duration_months=18,
            status='under_evaluation',
            submission_date=datetime.now() - timedelta(days=25),
            created_by=users[1].id if len(users) > 1 else users[0].id,
            created_at=datetime.now() - timedelta(days=30),
            updated_at=datetime.now() - timedelta(days=5)
        )
        applications.append(app2)
    
    # Candidatura 3: Startup per Regione Lombardia
    if len(opportunities) > 2 and len(available_projects) > 2:
        app3 = FundingApplication(
            opportunity_id=opportunities[2].id,  # Startup Lombardia
            linked_project_id=available_projects[2].id,
            project_title="AI-Powered Analytics Platform",
            project_description="Sviluppo di piattaforma SaaS per analytics predittivi basata su machine learning per PMI del settore manifatturiero.",
            requested_amount=Decimal('120000.00'),
            project_manager_id=users[0].id,
            team_composition="CEO/CTO, 2 Data Scientists, 1 Full-stack Developer, 1 Business Developer",
            budget_breakdown={
                "sviluppo": 70000,
                "infrastruttura": 25000,
                "marketing": 15000,
                "legale": 10000
            },
            project_duration_months=12,
            status='draft',
            created_by=users[0].id,
            created_at=datetime.now() - timedelta(days=5),
            updated_at=datetime.now() - timedelta(days=1)
        )
        applications.append(app3)
    
    # Candidatura 4: Approvata (per mostrare caso di successo)
    if len(opportunities) > 5 and len(available_projects) > 3:
        app4 = FundingApplication(
            opportunity_id=opportunities[5].id,  # Bando chiuso automotive
            project_id=available_projects[3].id if len(available_projects) > 3 else available_projects[0].id,
            project_title="Smart Mobility Components",
            project_description="Ricerca e sviluppo di componenti intelligenti per veicoli elettrici con focus su sistemi di ricarica wireless.",
            requested_amount=Decimal('650000.00'),
            approved_amount=Decimal('650000.00'),
            project_manager_id=users[1].id if len(users) > 1 else users[0].id,
            team_composition="Principal Investigator, 3 Ricercatori, 2 Ingegneri, 1 Project Coordinator",
            budget_breakdown={
                "ricerca": 300000,
                "prototipazione": 200000,
                "testing": 100000,
                "disseminazione": 50000
            },
            project_duration_months=24,
            status='approved',
            submission_date=datetime.now() - timedelta(days=90),
            approval_date=datetime.now() - timedelta(days=30),
            created_by=users[1].id if len(users) > 1 else users[0].id,
            created_at=datetime.now() - timedelta(days=100),
            updated_at=datetime.now() - timedelta(days=30)
        )
        applications.append(app4)
    
    for app in applications:
        db.session.add(app)
    
    db.session.commit()
    print(f"✅ Creato {len(applications)} candidature")
    return applications

def create_project_funding_links(applications, projects):
    """Crea collegamenti tra progetti e finanziamenti"""
    
    links = []
    for app in applications:
        if app.project_id and app.status in ['approved', 'funded']:
            link = ProjectFundingLink(
                project_id=app.project_id,
                funding_application_id=app.id,
                percentage_allocation=app.opportunity.contribution_percentage,
                notes=f"Finanziamento da {app.opportunity.source_entity}",
                created_by=app.created_by,
                created_at=datetime.now()
            )
            links.append(link)
            db.session.add(link)
    
    db.session.commit()
    print(f"✅ Creato {len(links)} collegamenti progetto-finanziamento")
    return links

def create_funding_expenses(applications):
    """Crea spese di esempio per le candidature approvate"""
    
    expenses = []
    
    # Solo per candidature approvate
    approved_apps = [app for app in applications if app.status == 'approved']
    
    for app in approved_apps:
        # Crea alcune spese di esempio
        sample_expenses = [
            {
                'description': 'Acquisto server per sviluppo',
                'amount': Decimal('15000.00'),
                'category': 'equipment',
                'expense_date': date.today() - timedelta(days=20),
                'status': 'approved',
                'notes': 'Server Dell PowerEdge per ambiente di sviluppo'
            },
            {
                'description': 'Consulenza specialistica AI/ML',
                'amount': Decimal('8500.00'),
                'category': 'external',
                'expense_date': date.today() - timedelta(days=15),
                'status': 'approved',
                'notes': 'Consulenza senior per algoritmi machine learning'
            },
            {
                'description': 'Licenze software sviluppo',
                'amount': Decimal('3200.00'),
                'category': 'other',
                'expense_date': date.today() - timedelta(days=10),
                'status': 'pending',
                'notes': 'Licenze annuali IDE e tools di sviluppo'
            },
            {
                'description': 'Trasferta per meeting progetto',
                'amount': Decimal('1250.00'),
                'category': 'travel',
                'expense_date': date.today() - timedelta(days=5),
                'status': 'pending',
                'notes': 'Viaggio Milano-Roma per kick-off meeting'
            }
        ]
        
        for exp_data in sample_expenses:
            expense = FundingExpense(
                application_id=app.id,
                **exp_data,
                created_by=app.created_by,
                created_at=datetime.now() - timedelta(days=random.randint(1, 30)),
                updated_at=datetime.now() - timedelta(days=random.randint(0, 5))
            )
            expenses.append(expense)
            db.session.add(expense)
    
    db.session.commit()
    print(f"✅ Creato {len(expenses)} spese di finanziamento")
    return expenses

def seed_funding_data():
    """Funzione principale per il seeding"""
    
    from app import create_app
    app = create_app()
    
    with app.app_context():
        print("🌱 Avvio seeding dati bandi...")
        
        # Verifica che ci siano utenti e progetti
        users = User.query.all()
        projects = Project.query.all()
        
        if not users:
            print("❌ Nessun utente trovato. Esegui prima seed_data.py")
            return
            
        if not projects:
            print("❌ Nessun progetto trovato. Esegui prima seed_data.py")
            return
        
        print(f"📊 Trovati {len(users)} utenti e {len(projects)} progetti")
        
        # Crea dati
        opportunities = create_funding_opportunities(users)
        applications = create_funding_applications(opportunities, users, projects)
        links = create_project_funding_links(applications, projects)
        expenses = create_funding_expenses(applications)
        
        print("🎉 Seeding completato con successo!")
        print(f"📈 Statistiche:")
        print(f"   - {len(opportunities)} opportunità di bando")
        print(f"   - {len(applications)} candidature")
        print(f"   - {len(links)} collegamenti progetto-finanziamento")
        print(f"   - {len(expenses)} spese")

def main():
    """Wrapper per esecuzione diretta"""
    seed_funding_data()

if __name__ == '__main__':
    main() 