#!/usr/bin/env python3

import os
import sys
sys.path.append('.')

from models import User, PerformanceReview, PerformanceGoal, PerformanceTemplate, db
from app import create_app

app = create_app()

with app.app_context():
    print('=== USERS AVAILABLE ===')
    users = User.query.all()
    for user in users:
        print(f'ID: {user.id}, Name: {user.first_name} {user.last_name}, Email: {user.email}, Role: {user.role}')
    
    print(f'\nTotal users: {len(users)}')
    
    print('\n=== PERFORMANCE REVIEWS ===')
    reviews = PerformanceReview.query.all()
    for review in reviews:
        employee_name = f'{review.employee.first_name} {review.employee.last_name}' if review.employee else 'N/A'
        print(f'Review ID: {review.id}, Employee: {employee_name}, Year: {review.review_year}, Status: {review.status}, Type: {review.review_type}')
    
    print(f'\nTotal reviews: {len(reviews)}')
    
    print('\n=== PERFORMANCE GOALS ===')
    goals = PerformanceGoal.query.all()
    for goal in goals:
        employee_name = f'{goal.employee.first_name} {goal.employee.last_name}' if goal.employee else 'N/A'
        print(f'Goal ID: {goal.id}, Title: {goal.title}, Employee: {employee_name}, Year: {goal.year}, Status: {goal.status}, Progress: {goal.progress}%')
    
    print(f'\nTotal goals: {len(goals)}')
    
    print('\n=== PERFORMANCE TEMPLATES ===')
    templates = PerformanceTemplate.query.all()
    for template in templates:
        print(f'Template ID: {template.id}, Name: {template.name}, Type: {template.template_type}, Default: {template.is_default}')
    
    print(f'\nTotal templates: {len(templates)}')
    
    # Check for specific test users
    print('\n=== TEST USERS WITH PERFORMANCE DATA ===')
    test_users = User.query.filter(User.role.in_(['employee', 'manager'])).all()
    for user in test_users:
        user_reviews = PerformanceReview.query.filter_by(employee_id=user.id).count()
        user_goals = PerformanceGoal.query.filter_by(employee_id=user.id).count()
        print(f'User: {user.first_name} {user.last_name} (ID: {user.id}) - Reviews: {user_reviews}, Goals: {user_goals}')