"""
Seed data for performance management system.
Creates sample performance reviews, goals, templates, and evaluations.
"""

from extensions import db
from models import User
from models_split.performance import (
    PerformanceReview, PerformanceGoal, PerformanceTemplate,
    PerformanceFeedback, PerformanceKPI
)
from datetime import datetime, date, timedelta
import random
import json


def seed_performance_templates():
    """Create sample performance review templates."""
    print("🎯 Creating performance templates...")
    
    templates = [
        {
            'name': 'Valutazione Annuale Standard',
            'description': 'Template standard per valutazioni annuali dei dipendenti',
            'template_type': 'annual_review',
            'rating_scale': json.dumps({'scale': '1-5', 'description': 'Scala da 1 (insufficiente) a 5 (eccellente)'}),
            'is_default': True
        },
        {
            'name': 'Valutazione Periodo di Prova',
            'description': 'Template per valutazioni al termine del periodo di prova',
            'template_type': 'probation_review',
            'rating_scale': json.dumps({'scale': '1-5', 'description': 'Scala da 1 (insufficiente) a 5 (eccellente)'}),
            'is_default': False
        },
        {
            'name': 'Valutazione Trimestrale Manager',
            'description': 'Template specifico per valutazioni trimestrali dei manager',
            'template_type': 'quarterly_review',
            'rating_scale': json.dumps({'scale': '1-5', 'description': 'Scala da 1 (insufficiente) a 5 (eccellente)'}),
            'is_default': False
        }
    ]
    
    for template_data in templates:
        existing = PerformanceTemplate.query.filter_by(name=template_data['name']).first()
        if not existing:
            # Create template with only valid fields
            template = PerformanceTemplate(
                name=template_data['name'],
                description=template_data['description'],
                template_type=template_data['template_type'],
                rating_scale=template_data['rating_scale'],
                is_default=template_data['is_default'],
                is_active=True
            )
            db.session.add(template)
            print(f"  ✅ Created template: {template_data['name']}")
    
    db.session.commit()


def seed_performance_goals():
    """Create sample performance goals."""
    print("🎯 Creating performance goals...")
    
    # Get users to assign goals to
    users = User.query.filter(User.role.in_(['employee', 'manager'])).limit(10).all()
    if not users:
        print("  ⚠️ No users found, creating basic goals without assignment")
        return
    
    goal_templates = [
        {
            'title': 'Certificazione AWS Solutions Architect',
            'description': 'Ottenere la certificazione AWS Solutions Architect Associate entro fine anno',
            'category': 'technical',
            'priority': 'high',
            'success_criteria': 'Superare l\'esame con punteggio >= 720/1000',
            'target_date': date.today() + timedelta(days=120),
            'progress_percentage': random.randint(20, 80)
        },
        {
            'title': 'Miglioramento Soft Skills',
            'description': 'Partecipare a corsi di comunicazione e public speaking',
            'category': 'soft_skills', 
            'priority': 'medium',
            'success_criteria': 'Completare almeno 2 corsi e presentare 1 sessione al team',
            'target_date': date.today() + timedelta(days=90),
            'progress_percentage': random.randint(30, 70)
        },
        {
            'title': 'Incremento Produttività Team',
            'description': 'Aumentare la produttività del team del 20% implementando metodologie agili',
            'category': 'leadership',
            'priority': 'high',
            'success_criteria': 'Metriche di velocity aumentate del 20% rispetto a Q1',
            'target_date': date.today() + timedelta(days=60),
            'progress_percentage': random.randint(40, 90)
        },
        {
            'title': 'Sviluppo Business Acumen',
            'description': 'Approfondire la conoscenza dei processi business aziendali',
            'category': 'business',
            'priority': 'medium',
            'success_criteria': 'Completare corso business analysis e applicare insights in 2 progetti',
            'target_date': date.today() + timedelta(days=150),
            'progress_percentage': random.randint(10, 50)
        },
        {
            'title': 'Mentoring Junior Developers',
            'description': 'Guidare e formare 2 sviluppatori junior nel team',
            'category': 'teamwork',
            'priority': 'medium',
            'success_criteria': 'Junior developers raggiungono autonomia operativa',
            'target_date': date.today() + timedelta(days=180),
            'progress_percentage': random.randint(25, 75)
        },
        {
            'title': 'Innovazione Tecnologica',
            'description': 'Ricercare e proporre 3 nuove tecnologie per migliorare il nostro stack',
            'category': 'technical',
            'priority': 'low',
            'success_criteria': 'Presentare 3 POC approvati dal tech team',
            'target_date': date.today() + timedelta(days=200),
            'progress_percentage': random.randint(0, 40)
        }
    ]
    
    current_year = datetime.now().year
    
    for user in users:
        # Create 2-4 goals per user
        num_goals = random.randint(2, 4)
        user_goals = random.sample(goal_templates, min(num_goals, len(goal_templates)))
        
        for goal_data in user_goals:
            goal = PerformanceGoal(
                employee_id=user.id,
                title=f"{goal_data['title']} - {user.first_name}",
                description=goal_data['description'],
                category=goal_data['category'],
                priority=goal_data['priority'],
                success_criteria=goal_data['success_criteria'],
                measurable_outcomes=[],  # Empty array - SQLAlchemy JSON column will serialize it
                start_date=date.today() - timedelta(days=random.randint(30, 90)),
                target_date=goal_data['target_date'],
                target_year=current_year,
                year=current_year,
                quarter=f"Q{((datetime.now().month - 1) // 3) + 1}",
                status='active',
                progress_percentage=goal_data['progress_percentage'],
                # Add some evaluation data for demonstration
                achievement_rating=random.choice([None, None, random.uniform(3.0, 5.0)]),
                manager_assessment=random.choice([
                    None, 
                    None,
                    "Ottimi progressi, obiettivo ben allineato con la crescita professionale",
                    "Buon impegno mostrato, continua così per raggiungere il target",
                    "Performance eccellente, supera le aspettative"
                ]),
                created_by=user.id,
                set_by_id=random.choice([u.id for u in users if u.role in ['manager', 'admin']] + [user.id])
            )
            db.session.add(goal)
            print(f"  ✅ Created goal: {goal.title}")
    
    db.session.commit()


def seed_performance_reviews():
    """Create sample performance reviews."""
    print("🎯 Creating performance reviews...")
    
    # Get users and templates
    employees = User.query.filter_by(role='employee').limit(8).all()
    managers = User.query.filter(User.role.in_(['manager', 'admin'])).all()
    templates = PerformanceTemplate.query.all()
    
    if not employees or not managers or not templates:
        print("  ⚠️ Missing required data (employees, managers, or templates)")
        return
    
    current_year = datetime.now().year
    
    for employee in employees:
        # Create 1-2 reviews per employee (current year and possibly previous)
        years_to_create = [current_year]
        if random.choice([True, False]):
            years_to_create.append(current_year - 1)
        
        for year in years_to_create:
            reviewer = random.choice(managers)
            template = random.choice(templates)
            
            # Determine review status and completion
            status = random.choice(['draft', 'in_progress', 'employee_review', 'manager_review', 'hr_review', 'completed', 'archived'])
            
            # Set dates based on status
            if year == current_year:
                review_start = date(year, 1, 1)
                review_end = date(year, 12, 31)
                due_date = date(year, 12, 15)
            else:
                review_start = date(year, 1, 1)
                review_end = date(year, 12, 31)
                due_date = date(year, 12, 15)
            
            # Create review
            review = PerformanceReview(
                employee_id=employee.id,
                reviewer_id=reviewer.id,
                template_id=template.id,
                review_year=year,
                review_period_start=review_start,
                review_period_end=review_end,
                due_date=due_date,
                status=status,
                # Ratings (only if review is completed/archived)
                overall_rating=random.uniform(3.0, 5.0) if status in ['completed', 'archived'] else None,
                technical_skills_rating=random.uniform(3.0, 5.0) if status in ['completed', 'archived'] else None,
                soft_skills_rating=random.uniform(3.0, 5.0) if status in ['completed', 'archived'] else None,
                goals_achievement_rating=random.uniform(3.0, 5.0) if status in ['completed', 'archived'] else None,
                communication_rating=random.uniform(3.0, 5.0) if status in ['completed', 'archived'] else None,
                teamwork_rating=random.uniform(3.0, 5.0) if status in ['completed', 'archived'] else None,
                leadership_rating=random.uniform(2.0, 5.0) if status in ['completed', 'archived'] and employee.role == 'manager' else None,
                # Comments
                achievements=f"Ottimi risultati raggiunti durante l'anno {year}. Ha dimostrato professionalità e dedizione." if status in ['completed', 'archived'] else None,
                areas_improvement="Continuare a sviluppare competenze di leadership e comunicazione" if status in ['completed', 'archived'] else None,
                strengths="Forte competenza tecnica, proattività, spirito di squadra" if status in ['completed', 'archived'] else None,
                reviewer_comments=f"Valutazione complessiva positiva per {employee.first_name}. Ha raggiunto tutti gli obiettivi principali." if status in ['completed', 'archived'] else None,
                manager_comments="Dipendente affidabile con grande potenziale di crescita" if status in ['completed', 'archived'] else None,
                # Dates based on status
                submitted_date=datetime.now() - timedelta(days=random.randint(5, 30)) if status in ['completed', 'archived'] else None,
                completed_date=datetime.now() - timedelta(days=random.randint(1, 15)) if status in ['completed', 'archived'] else None,
                approved_date=datetime.now() - timedelta(days=random.randint(1, 7)) if status == 'archived' else None,
                approved_by=reviewer.id if status == 'archived' else None,
                created_by=reviewer.id
            )
            
            db.session.add(review)
            print(f"  ✅ Created review: {employee.first_name} {employee.last_name} - {year} ({status})")
    
    db.session.commit()
    
    # Link some goals to reviews
    print("🔗 Linking goals to reviews...")
    reviews = PerformanceReview.query.all()
    goals = PerformanceGoal.query.all()
    
    for review in reviews:
        # Find goals for same employee and year
        employee_goals = [g for g in goals if g.employee_id == review.employee_id and g.year == review.review_year]
        
        # Link 1-3 goals to this review
        goals_to_link = random.sample(employee_goals, min(random.randint(1, 3), len(employee_goals)))
        
        for goal in goals_to_link:
            goal.review_id = review.id
            print(f"  🔗 Linked goal '{goal.title}' to review {review.id}")
    
    db.session.commit()


def seed_performance_kpis():
    """Create sample KPIs for performance tracking."""
    print("🎯 Creating performance KPIs...")
    
    # Get some existing goals to attach KPIs to
    goals = PerformanceGoal.query.limit(10).all()
    if not goals:
        print("  ⚠️ No goals found, skipping KPI creation")
        return
    
    kpi_templates = [
        {
            'name': 'Velocity Sprint',
            'description': 'Story points completati per sprint',
            'unit': 'points',
            'target_value': 40.0,
            'frequency': 'weekly'
        },
        {
            'name': 'Code Quality Score',
            'description': 'Punteggio qualità codice da SonarQube',
            'unit': 'score',
            'target_value': 95.0,
            'frequency': 'monthly'
        },
        {
            'name': 'Customer Satisfaction',
            'description': 'Punteggio soddisfazione cliente',
            'unit': 'rating',
            'target_value': 4.5,
            'frequency': 'quarterly'
        }
    ]
    
    # Create 1-2 KPIs per goal
    for goal in goals:
        num_kpis = random.randint(1, min(2, len(kpi_templates)))
        selected_kpis = random.sample(kpi_templates, num_kpis)
        
        for kpi_data in selected_kpis:
            kpi = PerformanceKPI(
                goal_id=goal.id,
                name=f"{kpi_data['name']} - {goal.title[:30]}",
                description=kpi_data['description'],
                unit=kpi_data['unit'],
                target_value=kpi_data['target_value'],
                current_value=random.uniform(kpi_data['target_value'] * 0.7, kpi_data['target_value'] * 1.2),
                measurement_frequency=kpi_data['frequency']
            )
            db.session.add(kpi)
            print(f"  ✅ Created KPI: {kpi.name}")
    
    db.session.commit()


def run_seed():
    """Run all performance seeding functions."""
    print("\n🌱 SEEDING PERFORMANCE DATA")
    print("=" * 50)
    
    try:
        # Seed in order of dependencies
        seed_performance_templates()
        seed_performance_goals()
        seed_performance_reviews()
        seed_performance_kpis()
        
        print("\n✅ Performance data seeding completed successfully!")
        print("📊 Created data for testing:")
        print(f"   - Templates: {PerformanceTemplate.query.count()}")
        print(f"   - Goals: {PerformanceGoal.query.count()}")
        print(f"   - Reviews: {PerformanceReview.query.count()}")
        print(f"   - KPIs: {PerformanceKPI.query.count()}")
        
    except Exception as e:
        print(f"\n❌ Error during seeding: {str(e)}")
        db.session.rollback()
        raise


if __name__ == "__main__":
    run_seed()