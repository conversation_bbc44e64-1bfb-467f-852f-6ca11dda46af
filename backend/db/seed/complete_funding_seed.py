#!/usr/bin/env python3
"""
Script per completare il seeding delle candidature funding
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime, timedelta
import json
from decimal import Decimal

from app import create_app
from extensions import db
from models import (
    FundingOpportunity, FundingApplication, FundingExpense, 
    User, Project
)

def complete_seeding():
    """Completa il seeding delle candidature e spese"""
    
    app = create_app()
    
    with app.app_context():
        print("🌱 Completamento seeding candidature...")
        
        # Recupera dati esistenti
        users = User.query.all()
        projects = Project.query.all()
        opportunities = FundingOpportunity.query.all()
        
        print(f"📊 Dati disponibili: {len(users)} utenti, {len(projects)} progetti, {len(opportunities)} opportunità")
        
        if len(opportunities) == 0 or len(users) == 0 or len(projects) == 0:
            print("❌ Dati insufficienti per creare candidature")
            return
        
        # Candidatura 1: Progetto digitale
        app1 = FundingApplication(
            opportunity_id=opportunities[0].id,
            linked_project_id=projects[0].id,
            project_title=f"Digitalizzazione {projects[0].name}",
            project_description="Progetto di trasformazione digitale con implementazione di sistema ERP cloud-based, automazione processi e formazione del personale su tecnologie 4.0.",
            requested_amount=350000.00,
            project_manager_id=users[1].id if len(users) > 1 else users[0].id,
            team_composition="Project Manager, 2 Sviluppatori Senior, 1 Data Analyst, 1 UX Designer",
            budget_breakdown=json.dumps({
                "personale": 200000,
                "software": 80000,
                "hardware": 50000,
                "formazione": 20000
            }),
            project_duration_months=12,
            status='submitted',
            submission_date=datetime.now() - timedelta(days=10),
            created_by=users[0].id,
            created_at=datetime.now() - timedelta(days=15),
            updated_at=datetime.now() - timedelta(days=10)
        )
        db.session.add(app1)
        
        # Candidatura 2: Progetto green se abbiamo abbastanza opportunità
        if len(opportunities) > 3 and len(projects) > 1:
            app2 = FundingApplication(
                opportunity_id=opportunities[3].id,  # PNRR Transizione Ecologica
                linked_project_id=projects[1].id,
                project_title=f"Green Transition {projects[1].name}",
                project_description="Progetto di transizione ecologica con installazione pannelli solari, efficientamento energetico e certificazioni ambientali.",
                requested_amount=275000.00,
                co_financing_amount=68750.00,
                project_manager_id=users[2].id if len(users) > 2 else users[0].id,
                team_composition="Project Manager, Energy Consultant, 1 Tecnico Impianti",
                budget_breakdown=json.dumps({
                    "personale": 120000,
                    "attrezzature": 100000,
                    "consulenze": 50000,
                    "certificazioni": 5000
                }),
                project_duration_months=18,
                status='under_evaluation',
                submission_date=datetime.now() - timedelta(days=25),
                created_by=users[0].id,
                created_at=datetime.now() - timedelta(days=30),
                updated_at=datetime.now() - timedelta(days=25)
            )
            db.session.add(app2)
        
        # Candidatura 3: Startup se abbiamo abbastanza opportunità
        if len(opportunities) > 2 and len(projects) > 2:
            app3 = FundingApplication(
                opportunity_id=opportunities[2].id,  # Bando Startup Lombardia
                linked_project_id=projects[2].id,
                project_title=f"Startup Innovation {projects[2].name}",
                project_description="Progetto di sviluppo startup innovativa con focus su AI e sostenibilità.",
                requested_amount=120000.00,
                co_financing_amount=51430.00,
                project_manager_id=users[0].id,
                team_composition="CEO/CTO, 2 Developers, 1 Business Developer",
                budget_breakdown=json.dumps({
                    "personale": 80000,
                    "marketing": 30000,
                    "tecnologia": 25000,
                    "legale": 10000,
                    "altri": 26430
                }),
                project_duration_months=12,
                status='draft',
                created_by=users[0].id,
                created_at=datetime.now() - timedelta(days=5),
                updated_at=datetime.now() - timedelta(days=2)
            )
            db.session.add(app3)
        
        # Commit candidature
        db.session.commit()
        
        # Verifica candidature create
        applications = FundingApplication.query.all()
        print(f"✅ Candidature create: {len(applications)}")
        
        # Crea alcune spese di esempio per le candidature approvate/sotto valutazione
        expenses_count = 0
        for app in applications:
            if app.status in ['approved', 'under_evaluation', 'submitted']:
                # Spesa personale
                exp1 = FundingExpense(
                    application_id=app.id,
                    project_id=app.linked_project_id,
                    description=f"Sviluppo software - {app.project_title}",
                    amount=5200.00,
                    expense_date=datetime.now().date() - timedelta(days=15),
                    category='personnel',
                    is_eligible=True,
                    approval_status='approved',
                    approved_by=users[0].id,
                    approved_date=datetime.now()
                )
                db.session.add(exp1)
                expenses_count += 1
                
                # Spesa attrezzature
                exp2 = FundingExpense(
                    application_id=app.id,
                    project_id=app.linked_project_id,
                    description=f"Hardware e licenze - {app.project_title}",
                    amount=1800.00,
                    expense_date=datetime.now().date() - timedelta(days=8),
                    category='equipment',
                    is_eligible=True,
                    approval_status='pending'
                )
                db.session.add(exp2)
                expenses_count += 1
        
        db.session.commit()
        print(f"✅ Spese create: {expenses_count}")
        
        # Riepilogo finale
        print("\n📊 RIEPILOGO SEEDING COMPLETATO:")
        print(f"✅ Opportunità: {FundingOpportunity.query.count()}")
        print(f"✅ Candidature: {FundingApplication.query.count()}")
        print(f"✅ Spese: {FundingExpense.query.count()}")

if __name__ == '__main__':
    complete_seeding() 