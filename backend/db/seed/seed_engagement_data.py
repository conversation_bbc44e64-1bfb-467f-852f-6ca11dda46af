#!/usr/bin/env python3
"""
Script per popolare il database con dati di test per Engagement & Gamification.
Uso: python seed_engagement_data.py [--clear]
"""

import sys
import argparse
import os
from datetime import datetime, timedelta, date
from random import choice, randint, random
from decimal import Decimal

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '../../'))

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv(os.path.join(os.path.dirname(os.path.abspath(__file__)), '../../../.env'))
except ImportError:
    print("⚠️  python-dotenv not available, make sure DATABASE_URL is set")

from app import create_app, db
from models_split.engagement import (
    EngagementCampaign, EngagementLevel, EngagementPoint, 
    EngagementReward, EngagementUserReward, EngagementLeaderboard, 
    EngagementUserProfile
)
from models_split.user import User

def clear_engagement_data():
    """Cancella tutti i dati di engagement esistenti."""
    print("🗑️  Cancellazione dati Engagement esistenti...")
    
    # Ordine importante per rispettare le foreign key
    db.session.query(EngagementLeaderboard).delete()
    db.session.query(EngagementUserReward).delete()
    db.session.query(EngagementUserProfile).delete()
    db.session.query(EngagementPoint).delete()
    db.session.query(EngagementReward).delete()
    db.session.query(EngagementLevel).delete()
    db.session.query(EngagementCampaign).delete()
    
    db.session.commit()
    print("✅ Dati Engagement puliti!")

def seed_engagement_levels():
    """Crea livelli di engagement."""
    print("🏆 Creazione Livelli Engagement...")
    
    levels_data = [
        {
            'name': 'Novizio',
            'description': 'Benvenuto nel sistema! Inizia a guadagnare punti completando le tue attività quotidiane.',
            'points_threshold': 0,
            'level_order': 1,
            'rewards_config': {
                'welcome_bonus': 50,
                'features_unlocked': ['basic_dashboard', 'timesheet_entry']
            },
            'color_hex': '#94A3B8',
            'icon_name': 'academic-cap'
        },
        {
            'name': 'Collaboratore',
            'description': 'Stai diventando più attivo! Continua così per sbloccare nuove funzionalità.',
            'points_threshold': 100,
            'level_order': 2,
            'rewards_config': {
                'level_bonus': 25,
                'features_unlocked': ['project_creation', 'team_collaboration']
            },
            'color_hex': '#10B981',
            'icon_name': 'user-group'
        },
        {
            'name': 'Esperto',
            'description': 'Ottimo lavoro! Hai dimostrato competenza e dedizione. Nuovi premi ti aspettano.',
            'points_threshold': 250,
            'level_order': 3,
            'rewards_config': {
                'level_bonus': 50,
                'features_unlocked': ['advanced_reporting', 'mentor_program'],
                'special_rewards': ['priority_support']
            },
            'color_hex': '#F59E0B',
            'icon_name': 'star'
        },
        {
            'name': 'Leader',
            'description': 'Sei diventato un punto di riferimento! Guida gli altri verso il successo.',
            'points_threshold': 500,
            'level_order': 4,
            'rewards_config': {
                'level_bonus': 100,
                'features_unlocked': ['leadership_dashboard', 'team_management'],
                'special_rewards': ['monthly_recognition', 'training_budget']
            },
            'color_hex': '#8B5CF6',
            'icon_name': 'crown'
        },
        {
            'name': 'Maestro',
            'description': 'Livello massimo raggiunto! Sei un vero maestro nell\'uso della piattaforma.',
            'points_threshold': 1000,
            'level_order': 5,
            'rewards_config': {
                'level_bonus': 200,
                'features_unlocked': ['all_features'],
                'special_rewards': ['vip_support', 'exclusive_events', 'company_recognition']
            },
            'color_hex': '#EF4444',
            'icon_name': 'fire'
        }
    ]
    
    levels = []
    for level_data in levels_data:
        level = EngagementLevel(
            name=level_data['name'],
            description=level_data['description'],
            points_threshold=level_data['points_threshold'],
            level_order=level_data['level_order'],
            rewards_config=level_data['rewards_config'],
            color_hex=level_data['color_hex'],
            icon_name=level_data['icon_name'],
            is_active=True
        )
        db.session.add(level)
        levels.append(level)
    
    db.session.commit()
    print(f"✅ Creati {len(levels)} Livelli Engagement")
    return levels

def seed_engagement_campaigns():
    """Crea campagne di engagement."""
    print("📢 Creazione Campagne Engagement...")
    
    # Ottieni un admin per creare le campagne
    admin_user = User.query.filter_by(role='admin').first()
    if not admin_user:
        print("⚠️  Nessun utente admin trovato. Saltando associazione creator.")
    
    campaigns_data = [
        {
            'name': 'Produttività Q1 2024',
            'description': 'Campagna per incentivare la produttività nel primo trimestre. Punti extra per completamento progetti e gestione timesheet.',
            'start_date': date.today() - timedelta(days=45),
            'end_date': date.today() + timedelta(days=45),
            'status': 'active',
            'points_multiplier': Decimal('1.5'),
            'objectives_config': {
                'project_completion_bonus': 50,
                'timesheet_consistency_bonus': 25,
                'team_collaboration_bonus': 30
            },
            'points_rules': {
                'project_completion': 100,
                'task_completion': 15,
                'timesheet_entry': 5,
                'team_meeting_participation': 10,
                'knowledge_sharing': 20
            }
        },
        {
            'name': 'Innovation Challenge',
            'description': 'Incoraggia l\'innovazione e la condivisione di idee. Premi speciali per contributi creativi e soluzioni innovative.',
            'start_date': date.today() + timedelta(days=15),
            'end_date': date.today() + timedelta(days=75),
            'status': 'draft',
            'points_multiplier': Decimal('2.0'),
            'objectives_config': {
                'idea_submission': 100,
                'innovation_implementation': 200,
                'peer_collaboration': 50
            },
            'points_rules': {
                'idea_creation': 50,
                'idea_feedback': 10,
                'prototype_development': 150,
                'peer_review': 15
            }
        },
        {
            'name': 'Team Building Autunno',
            'description': 'Campagna focalizzata su team building e collaborazione. Premi per attività di gruppo e supporto reciproco.',
            'start_date': date.today() - timedelta(days=120),
            'end_date': date.today() - timedelta(days=30),
            'status': 'completed',
            'points_multiplier': Decimal('1.2'),
            'objectives_config': {
                'team_activity_participation': 75,
                'peer_support': 25,
                'mentoring': 40
            },
            'points_rules': {
                'team_event_participation': 30,
                'peer_help': 10,
                'mentoring_session': 25,
                'feedback_provision': 15
            }
        }
    ]
    
    campaigns = []
    for campaign_data in campaigns_data:
        campaign = EngagementCampaign(
            name=campaign_data['name'],
            description=campaign_data['description'],
            start_date=campaign_data['start_date'],
            end_date=campaign_data['end_date'],
            status=campaign_data['status'],
            points_multiplier=campaign_data['points_multiplier'],
            objectives_config=campaign_data['objectives_config'],
            points_rules=campaign_data['points_rules'],
            created_by_id=admin_user.id if admin_user else None
        )
        db.session.add(campaign)
        campaigns.append(campaign)
    
    db.session.commit()
    print(f"✅ Creati {len(campaigns)} Campagne Engagement")
    return campaigns

def seed_engagement_rewards():
    """Crea catalogo premi."""
    print("🎁 Creazione Catalogo Premi...")
    
    # Ottieni admin e campagne attive
    admin_user = User.query.filter_by(role='admin').first()
    active_campaigns = EngagementCampaign.query.filter_by(status='active').all()
    
    rewards_data = [
        {
            'name': 'Buono Amazon €25',
            'description': 'Buono regalo Amazon del valore di 25€. Perfetto per acquisti online di libri, tecnologia e molto altro.',
            'points_cost': 150,
            'reward_type': 'digital',
            'max_redemptions': 20,
            'per_user_limit': 2,
            'image_url': '/static/img/rewards/amazon_25.png',
            'external_url': 'https://amazon.it',
            'campaign_id': active_campaigns[0].id if active_campaigns else None
        },
        {
            'name': 'Giornata di Smart Working Extra',
            'description': 'Una giornata aggiuntiva di smart working da utilizzare nel mese corrente. Flessibilità per il tuo work-life balance.',
            'points_cost': 100,
            'reward_type': 'experience',
            'max_redemptions': 50,
            'per_user_limit': 3,
            'image_url': '/static/img/rewards/smart_working.png',
            'campaign_id': None  # Disponibile sempre
        },
        {
            'name': 'Badge "Innovatore del Mese"',
            'description': 'Riconoscimento speciale per i dipendenti più innovativi. Il badge apparirà sul tuo profilo per tutto il mese.',
            'points_cost': 200,
            'reward_type': 'badge',
            'max_redemptions': 5,
            'per_user_limit': 1,
            'image_url': '/static/img/rewards/innovator_badge.png',
            'available_from': date.today(),
            'available_until': date.today() + timedelta(days=30)
        },
        {
            'name': 'Corso di Formazione Online',
            'description': 'Accesso a corso di formazione professionale online su Udemy o Coursera. Migliora le tue competenze!',
            'points_cost': 300,
            'reward_type': 'digital',
            'max_redemptions': 15,
            'per_user_limit': 2,
            'image_url': '/static/img/rewards/online_course.png',
            'external_url': 'https://udemy.com'
        },
        {
            'name': 'Pranzo di Team Offerto',
            'description': 'Pranzo di team in ristorante locale offerto dall\'azienda. Un momento di condivisione e relax con i colleghi.',
            'points_cost': 250,
            'reward_type': 'experience',
            'max_redemptions': 8,
            'per_user_limit': 1,
            'image_url': '/static/img/rewards/team_lunch.png'
        },
        {
            'name': 'Gadget Aziendale Premium',
            'description': 'Zaino tecnico, borraccia termica, o power bank con logo aziendale. Gadget di alta qualità per la vita quotidiana.',
            'points_cost': 80,
            'reward_type': 'physical',
            'max_redemptions': 30,
            'per_user_limit': 3,
            'image_url': '/static/img/rewards/premium_gadget.png'
        },
        {
            'name': 'Parcheggio VIP per una Settimana',
            'description': 'Posto auto riservato nel parcheggio aziendale per una settimana intera. Comodità garantita!',
            'points_cost': 120,
            'reward_type': 'experience',
            'max_redemptions': 10,
            'per_user_limit': 2,
            'image_url': '/static/img/rewards/vip_parking.png'
        },
        {
            'name': 'Subscription Spotify Premium (3 mesi)',
            'description': 'Abbonamento Spotify Premium per 3 mesi. Musica senza interruzioni per concentrarti al meglio.',
            'points_cost': 180,
            'reward_type': 'digital',
            'max_redemptions': 25,
            'per_user_limit': 1,
            'image_url': '/static/img/rewards/spotify_premium.png',
            'external_url': 'https://spotify.com'
        }
    ]
    
    rewards = []
    for reward_data in rewards_data:
        reward = EngagementReward(
            name=reward_data['name'],
            description=reward_data['description'],
            points_cost=reward_data['points_cost'],
            reward_type=reward_data['reward_type'],
            max_redemptions=reward_data['max_redemptions'],
            per_user_limit=reward_data['per_user_limit'],
            image_url=reward_data['image_url'],
            external_url=reward_data.get('external_url'),
            campaign_id=reward_data.get('campaign_id'),
            available_from=reward_data.get('available_from'),
            available_until=reward_data.get('available_until'),
            current_redemptions=randint(0, reward_data['max_redemptions'] // 3),  # Alcuni già riscattati
            is_active=True,
            created_by_id=admin_user.id if admin_user else None
        )
        db.session.add(reward)
        rewards.append(reward)
    
    db.session.commit()
    print(f"✅ Creati {len(rewards)} Premi")
    return rewards

def seed_engagement_points_and_profiles(campaigns, levels):
    """Crea punti e profili utente con dati realistici."""
    print("⭐ Creazione Punti e Profili Utente...")
    
    users = User.query.all()
    if not users:
        print("⚠️  Nessun utente trovato. Saltando creazione punti.")
        return [], []
    
    # Campagna attiva per assegnare punti
    active_campaign = next((c for c in campaigns if c.status == 'active'), None)
    
    # Azioni che generano punti
    point_actions = [
        ('login', 'user_portal', 5, 'Login giornaliero'),
        ('create', 'project', 50, 'Creazione nuovo progetto'),
        ('update', 'project', 15, 'Aggiornamento progetto'),
        ('create', 'task', 20, 'Creazione task'),
        ('complete', 'task', 25, 'Completamento task'),
        ('submit', 'timesheet', 10, 'Inserimento timesheet'),
        ('review', 'timesheet', 15, 'Revisione timesheet'),
        ('create', 'client', 30, 'Aggiunta nuovo cliente'),
        ('participate', 'meeting', 15, 'Partecipazione meeting'),
        ('share', 'knowledge', 20, 'Condivisione conoscenza'),
        ('help', 'colleague', 25, 'Supporto collega'),
        ('submit', 'idea', 40, 'Invio idea innovativa')
    ]
    
    points = []
    profiles = []
    
    for user in users:
        # Crea profilo utente
        total_points_earned = 0
        total_points_spent = 0
        
        # Genera attività degli ultimi 60 giorni
        activities_count = randint(20, 80) if user.role in ['admin', 'manager'] else randint(10, 50)
        
        for _ in range(activities_count):
            # Seleziona azione casuale
            action_type, resource_type, base_points, description = choice(point_actions)
            
            # Calcola punti con moltiplicatore campagna se applicabile
            multiplier = active_campaign.points_multiplier if active_campaign else Decimal('1.0')
            points_earned = int(base_points * float(multiplier))
            
            # Data casuale negli ultimi 60 giorni
            days_ago = randint(0, 60)
            earned_at = datetime.now() - timedelta(days=days_ago)
            
            point = EngagementPoint(
                user_id=user.id,
                campaign_id=active_campaign.id if active_campaign and randint(1, 3) == 1 else None,
                points_earned=points_earned,
                source_type='audit_log',
                source_id=randint(1000, 9999),  # Simula audit log ID
                action_type=action_type,
                resource_type=resource_type,
                resource_id=randint(1, 100),
                description=description,
                multiplier_applied=multiplier,
                earned_at=earned_at
            )
            db.session.add(point)
            points.append(point)
            total_points_earned += points_earned
        
        # Simula alcuni riscatti (spese punti)
        if total_points_earned > 100:
            redemptions = randint(0, 3)
            for _ in range(redemptions):
                points_spent = randint(50, min(200, total_points_earned // 3))
                total_points_spent += points_spent
        
        # Calcola punti disponibili
        available_points = total_points_earned - total_points_spent
        
        # Determina livello attuale
        current_level = None
        next_level = None
        
        for level in sorted(levels, key=lambda l: l.level_order, reverse=True):
            if total_points_earned >= level.points_threshold:
                current_level = level
                break
        
        # Trova prossimo livello
        for level in sorted(levels, key=lambda l: l.level_order):
            if level.points_threshold > total_points_earned:
                next_level = level
                break
        
        # Statistiche attività
        total_logins = randint(30, 120)
        total_actions = activities_count
        streak_days = randint(1, 15)
        last_activity = date.today() - timedelta(days=randint(0, 7))
        
        # Crea profilo utente
        profile = EngagementUserProfile(
            user_id=user.id,
            total_points=total_points_earned,
            total_points_spent=total_points_spent,
            available_points=available_points,
            current_level_id=current_level.id if current_level else levels[0].id,
            next_level_id=next_level.id if next_level else None,
            total_logins=total_logins,
            total_actions=total_actions,
            streak_days=streak_days,
            last_activity_date=last_activity
        )
        db.session.add(profile)
        profiles.append(profile)
    
    db.session.commit()
    print(f"✅ Creati {len(points)} Punti e {len(profiles)} Profili Utente")
    return points, profiles

def seed_user_rewards(rewards, users):
    """Crea alcuni riscatti di premi per utenti."""
    print("🏆 Creazione Riscatti Premi...")
    
    if not users or not rewards:
        print("⚠️  Nessun utente o premio trovato. Saltando creazione riscatti.")
        return []
    
    user_rewards = []
    
    # Simula alcuni riscatti casuali
    for _ in range(randint(5, 15)):
        user = choice(users)
        reward = choice(rewards)
        
        # Verifica se l'utente ha abbastanza punti (approssimativo)
        user_profile = EngagementUserProfile.query.filter_by(user_id=user.id).first()
        if user_profile and user_profile.available_points >= reward.points_cost:
            
            # Stati possibili per i riscatti
            statuses = ['pending', 'approved', 'delivered']
            status = choice(statuses)
            
            # Date realistiche
            redeemed_at = datetime.now() - timedelta(days=randint(1, 30))
            fulfilled_at = redeemed_at + timedelta(days=randint(1, 7)) if status == 'delivered' else None
            
            user_reward = EngagementUserReward(
                user_id=user.id,
                reward_id=reward.id,
                points_spent=reward.points_cost,
                redemption_status=status,
                redemption_notes=f"Riscattato da {user.first_name} {user.last_name}",
                admin_notes="Riscatto approvato automaticamente" if status != 'pending' else None,
                redeemed_at=redeemed_at,
                fulfilled_at=fulfilled_at
            )
            db.session.add(user_reward)
            user_rewards.append(user_reward)
            
            # Aggiorna counter reward
            reward.current_redemptions += 1
            
            # Aggiorna punti spesi utente
            user_profile.total_points_spent += reward.points_cost
            user_profile.available_points -= reward.points_cost
    
    db.session.commit()
    print(f"✅ Creati {len(user_rewards)} Riscatti Premi")
    return user_rewards

def seed_leaderboards(campaigns, users):
    """Crea classifiche per campagne."""
    print("🏅 Creazione Classifiche...")
    
    if not users:
        print("⚠️  Nessun utente trovato. Saltando creazione classifiche.")
        return []
    
    leaderboards = []
    
    # Classifica per campagna attiva
    active_campaign = next((c for c in campaigns if c.status == 'active'), None)
    if active_campaign:
        
        # Calcola punti per utente nella campagna
        user_points = []
        for user in users:
            # Simula punti nella campagna
            campaign_points = randint(50, 500) if user.role in ['admin', 'manager'] else randint(20, 300)
            user_points.append((user, campaign_points))
        
        # Ordina per punti decrescenti
        user_points.sort(key=lambda x: x[1], reverse=True)
        
        # Crea entries leaderboard
        for position, (user, points) in enumerate(user_points, 1):
            leaderboard = EngagementLeaderboard(
                user_id=user.id,
                campaign_id=active_campaign.id,
                ranking_position=position,
                total_points=points,
                period_type='campaign',
                period_start=active_campaign.start_date,
                period_end=active_campaign.end_date,
                calculated_at=datetime.now()
            )
            db.session.add(leaderboard)
            leaderboards.append(leaderboard)
    
    # Classifica mensile generale
    current_month_start = date.today().replace(day=1)
    
    user_monthly_points = []
    for user in users:
        monthly_points = randint(100, 800) if user.role in ['admin', 'manager'] else randint(50, 400)
        user_monthly_points.append((user, monthly_points))
    
    user_monthly_points.sort(key=lambda x: x[1], reverse=True)
    
    for position, (user, points) in enumerate(user_monthly_points, 1):
        leaderboard = EngagementLeaderboard(
            user_id=user.id,
            campaign_id=None,
            ranking_position=position,
            total_points=points,
            period_type='monthly',
            period_start=current_month_start,
            period_end=date.today(),
            calculated_at=datetime.now()
        )
        db.session.add(leaderboard)
        leaderboards.append(leaderboard)
    
    db.session.commit()
    print(f"✅ Creati {len(leaderboards)} Entries Leaderboard")
    return leaderboards

def main():
    parser = argparse.ArgumentParser(description='Popola il database con dati di test per Engagement & Gamification')
    parser.add_argument('--clear', action='store_true', help='Cancella i dati Engagement esistenti prima di creare i nuovi')
    args = parser.parse_args()
    
    app = create_app()
    
    with app.app_context():
        print("🎮 Avvio seeding Engagement & Gamification...")
        
        if args.clear:
            clear_engagement_data()
        
        # Ottieni utenti esistenti
        users = User.query.all()
        if not users:
            print("⚠️  Nessun utente trovato. Esegui prima seed_data.py per creare gli utenti.")
            return
        
        # Crea i dati in ordine di dipendenza
        levels = seed_engagement_levels()
        campaigns = seed_engagement_campaigns()
        rewards = seed_engagement_rewards()
        points, profiles = seed_engagement_points_and_profiles(campaigns, levels)
        user_rewards = seed_user_rewards(rewards, users)
        leaderboards = seed_leaderboards(campaigns, users)
        
        print(f"\n🎉 Seeding Engagement completato!")
        print(f"\n📊 Dati creati:")
        print(f"  • {len(levels)} Livelli Engagement")
        print(f"  • {len(campaigns)} Campagne")
        print(f"  • {len(rewards)} Premi disponibili")
        print(f"  • {len(points)} Punti assegnati")
        print(f"  • {len(profiles)} Profili utente")
        print(f"  • {len(user_rewards)} Riscatti premi")
        print(f"  • {len(leaderboards)} Entries leaderboard")
        print(f"\n🔗 I dati sono ora disponibili nelle pagine Engagement!")

if __name__ == '__main__':
    main()