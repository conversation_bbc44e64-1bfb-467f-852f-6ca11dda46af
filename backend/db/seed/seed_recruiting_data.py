#!/usr/bin/env python3
"""
Script per popolare il database con dati di test per il modulo Recruiting.
Uso: python seed_recruiting_data.py [--clear]
"""

import sys
import argparse
import os
from datetime import datetime, timedelta, date
from random import choice, randint, random, sample
from decimal import Decimal
import json

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '../../'))

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv(os.path.join(os.path.dirname(os.path.abspath(__file__)), '../../../.env'))
except ImportError:
    print("⚠️  python-dotenv not available, make sure DATABASE_URL is set")

from app import create_app, db
from models_split.recruiting import (
    JobPosting, Candidate, Application, InterviewSession,
    RecruitingWorkflow, CandidateSkill
)
from models_split.user import User
from models_split.hr import Department
from models_split.projects import Project
from models_split.crm import Proposal

def clear_recruiting_data():
    """Cancella tutti i dati di recruiting esistenti."""
    print("🗑️  Cancellazione dati Recruiting esistenti...")
    
    # Ordine importante per rispettare le foreign key
    db.session.query(RecruitingWorkflow).delete()
    db.session.query(InterviewSession).delete()
    db.session.query(CandidateSkill).delete()
    db.session.query(Application).delete()
    db.session.query(Candidate).delete()
    db.session.query(JobPosting).delete()
    
    db.session.commit()
    print("✅ Dati Recruiting puliti!")

def seed_job_postings():
    """Crea posizioni lavorative di esempio."""
    print("💼 Creazione Job Postings...")
    
    # Get existing data for relationships
    users = User.query.filter_by(is_active=True).all()
    departments = Department.query.all()
    projects = Project.query.all()
    
    if not users:
        print("⚠️  Nessun utente trovato, creare prima utenti di test")
        return []
    
    job_postings_data = [
        {
            'title': 'Senior Full Stack Developer',
            'description': 'Sviluppatore senior per progetti web enterprise con React e Python.',
            'requirements': 'Laurea in Informatica, 5+ anni esperienza, Python, React, PostgreSQL',
            'responsibilities': 'Sviluppo features, code review, mentoring junior developers',
            'location': 'Milano',
            'remote_allowed': True,
            'employment_type': 'full_time',
            'salary_min': Decimal('45000'),
            'salary_max': Decimal('60000'),
            'department_id': departments[0].id if departments else None,
            'project_id': projects[0].id if projects else None,
            'status': 'active',
            'is_public': True
        },
        {
            'title': 'Marketing Manager',
            'description': 'Responsabile marketing digitale e strategie di comunicazione.',
            'requirements': 'Laurea in Marketing, 3+ anni esperienza, Google Ads, SEO',
            'responsibilities': 'Pianificazione campagne, analisi ROI, gestione team',
            'location': 'Roma',
            'remote_allowed': False,
            'employment_type': 'full_time',
            'salary_min': Decimal('35000'),
            'salary_max': Decimal('45000'),
            'department_id': departments[1].id if len(departments) > 1 else departments[0].id if departments else None,
            'status': 'active',
            'is_public': True
        },
        {
            'title': 'Junior Data Analyst',
            'description': 'Analista dati junior per progetti di business intelligence.',
            'requirements': 'Laurea in Statistica/Informatica, Python, SQL, Excel avanzato',
            'responsibilities': 'Analisi dati, report, dashboard, supporto decision making',
            'location': 'Milano',
            'remote_allowed': True,
            'employment_type': 'full_time',
            'salary_min': Decimal('28000'),
            'salary_max': Decimal('35000'),
            'status': 'active',
            'is_public': True
        },
        {
            'title': 'Stage Sviluppo Frontend',
            'description': 'Tirocinio formativo per sviluppo applicazioni web moderne.',
            'requirements': 'Studente/Neolaureato, HTML, CSS, JavaScript, conoscenza Vue.js',
            'responsibilities': 'Sviluppo componenti UI, testing, supporto team tecnico',
            'location': 'Milano',
            'remote_allowed': True,
            'employment_type': 'intern',
            'salary_min': Decimal('800'),
            'salary_max': Decimal('1200'),
            'status': 'draft',
            'is_public': False
        },
        {
            'title': 'Project Manager',
            'description': 'Gestione progetti IT e coordinamento team multidisciplinari.',
            'requirements': 'Laurea, 4+ anni esperienza PM, PMP/Agile certification preferita',
            'responsibilities': 'Pianificazione progetti, gestione risorse, controllo qualità',
            'location': 'Roma',
            'remote_allowed': False,
            'employment_type': 'full_time',
            'salary_min': Decimal('40000'),
            'salary_max': Decimal('55000'),
            'status': 'paused',
            'is_public': False
        }
    ]
    
    job_postings = []
    for jp_data in job_postings_data:
        jp_data['created_by'] = choice(users).id
        jp_data['created_at'] = datetime.utcnow() - timedelta(days=randint(1, 30))
        
        if jp_data['status'] == 'active':
            jp_data['published_at'] = jp_data['created_at'] + timedelta(hours=randint(1, 24))
        
        job_posting = JobPosting(**jp_data)
        db.session.add(job_posting)
        job_postings.append(job_posting)
    
    db.session.flush()
    print(f"✅ Creati {len(job_postings)} Job Postings")
    return job_postings

def seed_candidates():
    """Crea candidati di esempio."""
    print("👥 Creazione Candidates...")
    
    candidates_data = [
        {
            'first_name': 'Marco',
            'last_name': 'Rossi',
            'email': '<EMAIL>',
            'phone': '+39 ************',
            'location': 'Milano',
            'linkedin_url': 'https://linkedin.com/in/marcorossi',
            'source': 'linkedin',
            'status': 'screening',
            'notes': 'Candidato molto promettente con ottima esperienza in React'
        },
        {
            'first_name': 'Laura',
            'last_name': 'Bianchi',
            'email': '<EMAIL>',
            'phone': '+39 ************',
            'location': 'Roma',
            'linkedin_url': 'https://linkedin.com/in/laurabianchi',
            'source': 'website',
            'status': 'interviewing',
            'notes': 'Esperienza marketing digitale, ottima comunicazione'
        },
        {
            'first_name': 'Giuseppe',
            'last_name': 'Verdi',
            'email': '<EMAIL>',
            'phone': '+39 ************',
            'location': 'Napoli',
            'source': 'referral',
            'status': 'new',
            'notes': 'Referral da dipendente, competenze data analysis'
        },
        {
            'first_name': 'Sofia',
            'last_name': 'Ferrari',
            'email': '<EMAIL>',
            'phone': '+39 ************',
            'location': 'Torino',
            'linkedin_url': 'https://linkedin.com/in/sofiaferrari',
            'source': 'agency',
            'status': 'offered',
            'notes': 'Studentessa magistrale, stage frontend molto promettente'
        },
        {
            'first_name': 'Alessandro',
            'last_name': 'Romano',
            'email': '<EMAIL>',
            'phone': '+39 ************',
            'location': 'Bologna',
            'source': 'linkedin',
            'status': 'rejected',
            'notes': 'Competenze non allineate ai requisiti'
        }
    ]
    
    candidates = []
    for c_data in candidates_data:
        # Simula dati CV analysis (riutilizzando formato del sistema esistente)
        cv_analysis = {
            'skills': [
                {'name': choice(['Python', 'JavaScript', 'React', 'Vue.js', 'SQL']), 
                 'level': choice(['intermediate', 'advanced']), 
                 'category': 'technical'},
                {'name': choice(['Project Management', 'Leadership', 'Communication']), 
                 'level': choice(['intermediate', 'advanced']), 
                 'category': 'soft'},
                {'name': choice(['English', 'Spanish', 'French']), 
                 'level': 'advanced', 
                 'category': 'language'}
            ],
            'experience_years': randint(2, 8),
            'education': choice(['Laurea Triennale', 'Laurea Magistrale', 'Master']),
            'extracted_at': datetime.utcnow().isoformat()
        }
        
        c_data['cv_analysis_data'] = json.dumps(cv_analysis)
        c_data['cv_last_updated'] = datetime.utcnow() - timedelta(days=randint(1, 15))
        c_data['current_cv_path'] = f"recruiting_cvs/cv_{c_data['first_name'].lower()}_{c_data['last_name'].lower()}.pdf"
        c_data['created_at'] = datetime.utcnow() - timedelta(days=randint(1, 45))
        
        candidate = Candidate(**c_data)
        db.session.add(candidate)
        candidates.append(candidate)
    
    db.session.flush()
    print(f"✅ Creati {len(candidates)} Candidates")
    return candidates

def seed_candidate_skills(candidates):
    """Crea skills estratte dai CV dei candidati."""
    print("🎯 Creazione Candidate Skills...")
    
    skills_by_category = {
        'technical': ['Python', 'JavaScript', 'React', 'Vue.js', 'SQL', 'PostgreSQL', 'Docker', 'AWS'],
        'soft': ['Leadership', 'Communication', 'Problem Solving', 'Team Work', 'Project Management'],
        'language': ['English', 'Spanish', 'French', 'German']
    }
    
    skills_count = 0
    for candidate in candidates:
        # Estrai skills dal cv_analysis_data se presente
        if candidate.cv_analysis_data:
            cv_data = json.loads(candidate.cv_analysis_data)
            for skill_data in cv_data.get('skills', []):
                level_map = {'beginner': 1, 'intermediate': 3, 'advanced': 4, 'expert': 5}
                skill = CandidateSkill(
                    candidate_id=candidate.id,
                    skill_name=skill_data['name'],
                    skill_category=skill_data['category'],
                    skill_level=level_map.get(skill_data['level'], 3),
                    years_experience=randint(1, 5),
                    extracted_from_cv=True,
                    confidence_score=random() * 0.3 + 0.7  # 0.7-1.0
                )
                db.session.add(skill)
                skills_count += 1
        
        # Aggiungi alcune skills aggiuntive casuali
        for category, skill_list in skills_by_category.items():
            if randint(1, 3) == 1:  # 33% probabilità
                skill_name = choice(skill_list)
                # Evita duplicati
                existing = CandidateSkill.query.filter_by(
                    candidate_id=candidate.id, 
                    skill_name=skill_name
                ).first()
                if not existing:
                    skill = CandidateSkill(
                        candidate_id=candidate.id,
                        skill_name=skill_name,
                        skill_category=category,
                        skill_level=randint(2, 5),
                        years_experience=randint(1, 4),
                        extracted_from_cv=False,
                        confidence_score=random() * 0.4 + 0.6  # 0.6-1.0
                    )
                    db.session.add(skill)
                    skills_count += 1
    
    db.session.flush()
    print(f"✅ Creati {skills_count} Candidate Skills")

def seed_applications(job_postings, candidates):
    """Crea candidature collegando job postings e candidati."""
    print("📋 Creazione Applications...")
    
    applications = []
    for candidate in candidates[:4]:  # Prime 4 candidati hanno candidature
        # Ogni candidato si candida per 1-2 posizioni
        num_applications = randint(1, 2)
        selected_postings = sample(job_postings, num_applications) if len(job_postings) >= num_applications else job_postings
        
        for job_posting in selected_postings:
            application = Application(
                job_posting_id=job_posting.id,
                candidate_id=candidate.id,
                applied_at=datetime.utcnow() - timedelta(days=randint(1, 20)),
                cover_letter=f"Motivazione per {job_posting.title} - Sono molto interessato a questa posizione...",
                current_step=choice(['application_received', 'screening', 'interview_1', 'interview_2', 'offer']),
                status=choice(['pending', 'in_progress', 'completed']),
                overall_score=randint(6, 10) if random() > 0.3 else None,
                interview_notes="Note colloquio..." if random() > 0.5 else None
            )
            db.session.add(application)
            applications.append(application)
    
    db.session.flush()
    print(f"✅ Creati {len(applications)} Applications")
    return applications

def seed_interview_sessions(applications):
    """Crea sessioni di colloquio per le candidature."""
    print("🎤 Creazione Interview Sessions...")
    
    users = User.query.filter_by(is_active=True).all()
    if not users:
        print("⚠️  Nessun utente per interviewer")
        return []
    
    interview_types = ['phone_screening', 'video_technical', 'onsite_cultural', 'final_executive']
    recommendations = ['hire', 'reject', 'continue']
    statuses = ['scheduled', 'completed', 'cancelled']
    
    interviews = []
    for application in applications:
        # 70% probabilità di avere almeno un colloquio
        if random() > 0.3:
            num_interviews = randint(1, 3)
            for i in range(num_interviews):
                interview_date = datetime.utcnow() + timedelta(days=randint(-10, 30))
                status = choice(statuses)
                
                interview = InterviewSession(
                    application_id=application.id,
                    interview_type=interview_types[min(i, len(interview_types)-1)],
                    scheduled_date=interview_date,
                    duration_minutes=choice([30, 45, 60, 90]),
                    location="Video Call" if randint(1, 2) == 1 else "Sede Milano",
                    interviewer_id=choice(users).id,
                    status=status,
                    score=randint(6, 10) if status == 'completed' else None,
                    notes="Colloquio positivo, buone competenze tecniche" if status == 'completed' else None,
                    feedback="Feedback dettagliato..." if status == 'completed' else None,
                    recommendation=choice(recommendations) if status == 'completed' else None,
                    completed_at=interview_date + timedelta(hours=1) if status == 'completed' else None
                )
                db.session.add(interview)
                interviews.append(interview)
    
    db.session.flush()
    print(f"✅ Creati {len(interviews)} Interview Sessions")
    return interviews

def seed_recruiting_workflow(applications):
    """Crea workflow steps per le candidature."""
    print("🔄 Creazione Recruiting Workflows...")
    
    users = User.query.filter_by(is_active=True).all()
    workflow_steps = [
        ('CV Review', 1),
        ('Phone Screening', 2),
        ('Technical Interview', 3),
        ('Cultural Fit', 4),
        ('Final Decision', 5)
    ]
    
    workflows_count = 0
    for application in applications:
        for step_name, step_order in workflow_steps:
            # Non tutti i workflow hanno tutti gli step
            if step_order <= 3 or random() > 0.4:
                status = 'completed' if step_order <= 2 else choice(['pending', 'in_progress', 'completed'])
                
                workflow = RecruitingWorkflow(
                    application_id=application.id,
                    step_name=step_name,
                    step_order=step_order,
                    status=status,
                    started_at=datetime.utcnow() - timedelta(days=randint(5, 20)) if status != 'pending' else None,
                    completed_at=datetime.utcnow() - timedelta(days=randint(1, 15)) if status == 'completed' else None,
                    due_date=datetime.utcnow() + timedelta(days=randint(1, 7)) if status == 'pending' else None,
                    assigned_to=choice(users).id if random() > 0.3 else None,
                    result=choice(['pass', 'fail']) if status == 'completed' else None,
                    notes=f"Note per {step_name}..." if random() > 0.5 else None
                )
                db.session.add(workflow)
                workflows_count += 1
    
    db.session.flush()
    print(f"✅ Creati {workflows_count} Recruiting Workflow steps")

def main():
    """Funzione principale."""
    parser = argparse.ArgumentParser(description='Popola database con dati recruiting di test')
    parser.add_argument('--clear', action='store_true', help='Cancella dati esistenti prima di popolare')
    args = parser.parse_args()
    
    # Crea applicazione Flask
    app = create_app()
    
    with app.app_context():
        print("🚀 Inizio popolamento dati Recruiting...")
        
        if args.clear:
            clear_recruiting_data()
        
        # Seed dei dati in ordine corretto
        job_postings = seed_job_postings()
        candidates = seed_candidates()
        seed_candidate_skills(candidates)
        applications = seed_applications(job_postings, candidates)
        seed_interview_sessions(applications)
        seed_recruiting_workflow(applications)
        
        # Commit finale
        db.session.commit()
        
        print("\n✅ Popolamento Recruiting completato!")
        print(f"📊 Riepilogo:")
        print(f"   - Job Postings: {len(job_postings)}")
        print(f"   - Candidates: {len(candidates)}")
        print(f"   - Applications: {len(applications)}")
        print(f"   - Skills totali: {CandidateSkill.query.count()}")
        print(f"   - Interview Sessions: {InterviewSession.query.count()}")
        print(f"   - Workflow Steps: {RecruitingWorkflow.query.count()}")

if __name__ == '__main__':
    main()