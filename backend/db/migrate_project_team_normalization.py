#!/usr/bin/env python3
"""
Script di migrazione per normalizzare il database:
1. Eliminare company_communications (tabella orfana)
2. Migrare project_team -> project_resources (eliminare ridondanza)

Eseguire con: python -m backend.db.migrate_project_team_normalization
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from extensions import db
from models import ProjectResource, Project, User
from sqlalchemy import text
from datetime import datetime

def backup_verification():
    """Verifica che sia stato fatto un backup prima di procedere"""
    print("⚠️  ATTENZIONE: Questo script modificherà la struttura del database!")
    print("📋 Assicurati di aver fatto un backup completo del database prima di procedere.")
    
    response = input("Hai fatto un backup del database? (sì/no): ").lower()
    if response not in ['sì', 'si', 'yes', 'y']:
        print("❌ Operazione annullata. Fai un backup prima di continuare.")
        sys.exit(1)
    
    print("✅ Backup confermato. Procedo con la migrazione...")

def analyze_current_state():
    """Analizza lo stato attuale delle tabelle"""
    print("\n🔍 ANALISI STATO ATTUALE")
    print("=" * 50)
    
    try:
        # Verifica esistenza tabelle
        company_comm_count = db.session.execute(
            text("SELECT COUNT(*) FROM company_communications")
        ).scalar()
        
        project_team_count = db.session.execute(
            text("SELECT COUNT(*) FROM project_team")
        ).scalar()
        
        project_resources_count = db.session.execute(
            text("SELECT COUNT(*) FROM project_resources")
        ).scalar()
        
        news_count = db.session.execute(
            text("SELECT COUNT(*) FROM news")
        ).scalar()
        
        print(f"📊 company_communications: {company_comm_count} record")
        print(f"📊 project_team: {project_team_count} record")
        print(f"📊 project_resources: {project_resources_count} record")
        print(f"📊 news: {news_count} record")
        
        # Verifica overlap tra project_team e project_resources
        overlap_query = text("""
            SELECT COUNT(*) FROM project_team pt
            INNER JOIN project_resources pr ON (
                pt.project_id = pr.project_id AND 
                pt.user_id = pr.user_id
            )
        """)
        overlap_count = db.session.execute(overlap_query).scalar()
        
        print(f"🔄 Overlap project_team <-> project_resources: {overlap_count} record")
        
        return {
            'company_comm_count': company_comm_count,
            'project_team_count': project_team_count,
            'project_resources_count': project_resources_count,
            'news_count': news_count,
            'overlap_count': overlap_count
        }
        
    except Exception as e:
        print(f"❌ Errore nell'analisi: {e}")
        return None

def cleanup_company_communications():
    """Rimuove tabella company_communications (orfana)"""
    print("\n🗑️  FASE 1: Rimozione company_communications")
    print("=" * 50)
    
    try:
        # Verifica se ci sono foreign key che puntano a company_communications
        fk_check = db.session.execute(text("""
            SELECT COUNT(*) FROM information_schema.table_constraints tc
            JOIN information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name
            WHERE tc.constraint_type = 'FOREIGN KEY' 
            AND kcu.referenced_table_name = 'company_communications'
        """)).scalar()
        
        if fk_check > 0:
            print(f"⚠️  Trovate {fk_check} foreign key che puntano a company_communications")
            print("🔧 Rimuovo le foreign key prima di eliminare la tabella...")
        
        # Rimuovi la tabella
        db.session.execute(text("DROP TABLE IF EXISTS company_communications CASCADE"))
        db.session.commit()
        
        print("✅ Tabella company_communications rimossa con successo")
        return True
        
    except Exception as e:
        print(f"❌ Errore rimozione company_communications: {e}")
        db.session.rollback()
        return False

def migrate_project_team_to_resources():
    """Migra dati da project_team a project_resources"""
    print("\n🔄 FASE 2: Migrazione project_team -> project_resources")
    print("=" * 50)
    
    try:
        # Query per trovare record in project_team non presenti in project_resources
        missing_records_query = text("""
            SELECT pt.project_id, pt.user_id, pt.role
            FROM project_team pt
            LEFT JOIN project_resources pr ON (
                pr.project_id = pt.project_id AND 
                pr.user_id = pt.user_id
            )
            WHERE pr.id IS NULL
        """)
        
        missing_records = db.session.execute(missing_records_query).fetchall()
        
        if not missing_records:
            print("✅ Nessun record da migrare - project_resources già completo")
            return 0
        
        print(f"📋 Trovati {len(missing_records)} record da migrare")
        
        # Migra i record mancanti
        migrated_count = 0
        for record in missing_records:
            project_id, user_id, role = record
            
            # Verifica che project e user esistano ancora
            project_exists = db.session.execute(
                text("SELECT COUNT(*) FROM projects WHERE id = :pid"),
                {'pid': project_id}
            ).scalar()
            
            user_exists = db.session.execute(
                text("SELECT COUNT(*) FROM users WHERE id = :uid"),
                {'uid': user_id}
            ).scalar()
            
            if project_exists and user_exists:
                # Inserisci in project_resources
                insert_query = text("""
                    INSERT INTO project_resources (project_id, user_id, role, allocation_percentage)
                    VALUES (:project_id, :user_id, :role, :allocation)
                """)
                
                db.session.execute(insert_query, {
                    'project_id': project_id,
                    'user_id': user_id,
                    'role': role or 'member',
                    'allocation': 100.0
                })
                
                migrated_count += 1
                print(f"✅ Migrato: Project {project_id}, User {user_id}, Role {role}")
            else:
                print(f"⚠️  Saltato record orfano: Project {project_id}, User {user_id}")
        
        db.session.commit()
        print(f"🎉 Migrazione completata: {migrated_count} record migrati")
        
        return migrated_count
        
    except Exception as e:
        print(f"❌ Errore migrazione: {e}")
        db.session.rollback()
        return -1

def verify_migration():
    """Verifica che la migrazione sia avvenuta correttamente"""
    print("\n🔍 FASE 3: Verifica migrazione")
    print("=" * 50)
    
    try:
        # Verifica che tutti i record di project_team siano in project_resources
        missing_check = db.session.execute(text("""
            SELECT COUNT(*) FROM project_team pt
            LEFT JOIN project_resources pr ON (
                pr.project_id = pt.project_id AND 
                pr.user_id = pt.user_id
            )
            WHERE pr.id IS NULL
        """)).scalar()
        
        if missing_check == 0:
            print("✅ Verifica superata: tutti i record di project_team sono in project_resources")
            return True
        else:
            print(f"❌ Verifica fallita: {missing_check} record mancanti in project_resources")
            return False
            
    except Exception as e:
        print(f"❌ Errore verifica: {e}")
        return False

def cleanup_project_team():
    """Rimuove tabella project_team (da eseguire dopo test completi)"""
    print("\n🗑️  FASE 4: Rimozione project_team")
    print("=" * 50)
    
    response = input("Sei sicuro di voler rimuovere la tabella project_team? (sì/no): ").lower()
    if response not in ['sì', 'si', 'yes', 'y']:
        print("⏸️  Rimozione project_team rimandata. Esegui test completi prima di procedere.")
        return False
    
    try:
        db.session.execute(text("DROP TABLE IF EXISTS project_team CASCADE"))
        db.session.commit()
        print("✅ Tabella project_team rimossa con successo")
        return True
        
    except Exception as e:
        print(f"❌ Errore rimozione project_team: {e}")
        db.session.rollback()
        return False

def update_database_documentation():
    """Aggiorna la documentazione del database"""
    print("\n📝 FASE 5: Aggiornamento documentazione")
    print("=" * 50)
    
    try:
        # Genera nuovo schema
        print("🔄 Rigenerazione schema database...")
        
        # Qui potresti aggiungere comandi per rigenerare:
        # - database/database_detailed.txt
        # - database/database_structure.txt
        # - database/foreign_keys.txt
        
        print("✅ Documentazione aggiornata (manuale)")
        print("📋 TODO: Rigenera database/database_detailed.txt")
        print("📋 TODO: Rigenera database/database_structure.txt")
        print("📋 TODO: Rigenera database/foreign_keys.txt")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore aggiornamento documentazione: {e}")
        return False

def main():
    """Funzione principale di migrazione"""
    print("🚀 MIGRAZIONE DATABASE - NORMALIZZAZIONE PROJECT TEAM")
    print("=" * 60)
    print(f"📅 Avviata il: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Verifica backup
    backup_verification()
    
    # Analisi stato attuale
    current_state = analyze_current_state()
    if not current_state:
        print("❌ Impossibile analizzare lo stato attuale. Operazione annullata.")
        sys.exit(1)
    
    # Fase 1: Rimuovi company_communications
    if current_state['company_comm_count'] > 0:
        print(f"\n⚠️  company_communications contiene {current_state['company_comm_count']} record")
        response = input("Vuoi comunque eliminarla? (sì/no): ").lower()
        if response not in ['sì', 'si', 'yes', 'y']:
            print("⏸️  Rimozione company_communications annullata")
        else:
            cleanup_company_communications()
    else:
        cleanup_company_communications()
    
    # Fase 2: Migra project_team -> project_resources
    if current_state['project_team_count'] > 0:
        migrated = migrate_project_team_to_resources()
        if migrated >= 0:
            # Fase 3: Verifica migrazione
            if verify_migration():
                # Fase 4: Cleanup project_team (opzionale)
                cleanup_project_team()
            else:
                print("❌ Verifica migrazione fallita. NON rimuovere project_team.")
        else:
            print("❌ Migrazione fallita. Operazione annullata.")
            sys.exit(1)
    else:
        print("✅ project_team è vuota, nessuna migrazione necessaria")
        cleanup_project_team()
    
    # Fase 5: Aggiorna documentazione
    update_database_documentation()
    
    print("\n🎉 MIGRAZIONE COMPLETATA!")
    print("=" * 60)
    print("📋 PROSSIMI PASSI:")
    print("1. Aggiorna modelli SQLAlchemy")
    print("2. Aggiorna blueprint API")
    print("3. Testa funzionalità team management")
    print("4. Aggiorna frontend (se necessario)")
    print("5. Rigenera documentazione database")

if __name__ == "__main__":
    main() 