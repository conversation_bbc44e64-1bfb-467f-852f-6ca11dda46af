import sys
import os
import sqlalchemy as sa

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '../'))

# Carica le variabili d'ambiente dal file .env
try:
    from dotenv import load_dotenv
    load_dotenv(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env'))
except ImportError:
    # Se python-dotenv non è disponibile, prova a usare load_env.py
    try:
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from load_env import load_env
        load_env()
    except ImportError:
        print("⚠️  Attenzione: Impossibile caricare le variabili d'ambiente")

from app import create_app
from extensions import db
from models import (
    InterviewSession, Application, Candidate, JobPosting, User,
    CandidateSkill, HelpCategory, HelpContent, HelpConversation, 
    HelpFeedback, HelpAnalytics
)
from datetime import datetime, timedelta
import random

# Crea l'applicazione
app = create_app()

def show_tables():
    """Mostra tutte le tabelle nel database"""
    with app.app_context():
        inspector = sa.inspect(db.engine)
        tables = inspector.get_table_names()
        print("\nTabelle nel database:")
        for table in sorted(tables):
            print(f"- {table}")
        print("")

def inspect_performance_kpis():
    """Ispeziona la struttura della tabella performance_kpis"""
    with app.app_context():
        inspector = sa.inspect(db.engine)
        
        if 'performance_kpis' in inspector.get_table_names():
            print("\n=== Struttura tabella performance_kpis (SQLAlchemy Inspector) ===")
            columns = inspector.get_columns('performance_kpis')
            for col in columns:
                print(f"  {col['name']} - {col['type']} {'(nullable)' if col['nullable'] else '(not null)'}")
            
            # Query SQL diretta per verificare le colonne
            print("\n=== Verifica con query SQL diretta ===")
            with db.engine.connect() as connection:
                # Ottieni informazioni sulle colonne direttamente da PostgreSQL
                result = connection.execute(sa.text("""
                    SELECT column_name, data_type, is_nullable 
                    FROM information_schema.columns 
                    WHERE table_name = 'performance_kpis' 
                    ORDER BY ordinal_position
                """))
                
                print("Colonne dal database PostgreSQL:")
                for row in result:
                    nullable = "nullable" if row[2] == 'YES' else "not null"
                    print(f"  {row[0]} - {row[1]} ({nullable})")
                
                # Test se possiamo fare SELECT su tutte le colonne del modello
                print("\n=== Test query colonne del modello ===")
                test_columns = [
                    'id', 'goal_id', 'name', 'description', 'target_value', 
                    'current_value', 'unit_of_measure', 'baseline_value',
                    'measurement_frequency', 'last_measured_at', 'is_active', 
                    'created_at', 'updated_at', 'measurement_unit', 
                    'is_achieved', 'achievement_date'
                ]
                
                for col in test_columns:
                    try:
                        result = connection.execute(sa.text(f"SELECT {col} FROM performance_kpis LIMIT 1"))
                        print(f"  ✅ {col} - OK")
                    except Exception as e:
                        print(f"  ❌ {col} - ERRORE: {str(e)}")
                
                # Check if there are any records
                result = connection.execute(sa.text("SELECT COUNT(*) FROM performance_kpis"))
                count = result.scalar()
                print(f"\nNumero di record: {count}")
                
                if count > 0:
                    print("\nPrimi 3 record:")
                    result = connection.execute(sa.text("SELECT * FROM performance_kpis LIMIT 3"))
                    for row in result:
                        print(f"  {dict(row._mapping)}")
        else:
            print("\nTabella performance_kpis non trovata!")

def cleanup_feature_flags():
    """Pulisce tutti i feature flags esistenti"""
    with app.app_context():
        try:
            from models_split.settings import FeatureFlag
            
            # Conta i flag esistenti
            existing_count = FeatureFlag.query.count()
            print(f"🔍 Trovati {existing_count} feature flags esistenti nel database")
            
            if existing_count == 0:
                print("✅ Nessun feature flag da rimuovere")
                return
            
            # Lista tutti i flag esistenti prima della rimozione
            existing_flags = FeatureFlag.query.all()
            print("\n📋 Feature flags che verranno rimossi:")
            for flag in existing_flags:
                category = getattr(flag, 'category', 'N/A')
                print(f"  - {flag.feature_key}: {flag.display_name} (categoria: {category})")
            
            print(f"\n🗑️  Rimozione automatica di {existing_count} feature flags...")
            
            # Rimuovi tutti i feature flags
            deleted_count = FeatureFlag.query.delete()
            db.session.commit()
            
            print(f"✅ Rimossi {deleted_count} feature flags dal database")
            print("🧹 Database pulito e pronto per i nuovi feature flags basati sui moduli reali")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante la pulizia: {str(e)}")
            raise

def list_feature_flags():
    """Lista tutti i feature flags esistenti"""
    with app.app_context():
        try:
            from models_split.settings import FeatureFlag
            
            flags = FeatureFlag.query.all()
            print(f"\n🚩 Trovati {len(flags)} feature flags:")
            
            if not flags:
                print("Nessun feature flag presente nel database")
                return
                
            for flag in flags:
                category = getattr(flag, 'category', 'N/A')
                enabled = '✅' if flag.is_enabled else '❌'
                print(f"  {enabled} {flag.feature_key}: {flag.display_name}")
                if category != 'N/A':
                    print(f"      Categoria: {category}")
                if flag.description:
                    print(f"      Descrizione: {flag.description}")
                print()
                
        except Exception as e:
            print(f"❌ Errore durante la lettura: {str(e)}")

def add_category_column():
    """Aggiunge la colonna category alla tabella feature_flags"""
    with app.app_context():
        try:
            # Check if column already exists
            result = db.session.execute(sa.text("SELECT column_name FROM information_schema.columns WHERE table_name='feature_flags' AND column_name='category'"))
            if result.fetchone():
                print("✅ Colonna 'category' già presente nella tabella feature_flags")
                return
                
            print("🔧 Aggiunta colonna 'category' alla tabella feature_flags...")
            
            # Add the category column with default value
            db.session.execute(sa.text("ALTER TABLE feature_flags ADD COLUMN category VARCHAR(50) DEFAULT 'core' NOT NULL"))
            db.session.commit()
            
            print("✅ Colonna 'category' aggiunta con successo")
            print("🔄 Aggiornamento valori di default per i flag esistenti...")
            
            # Update existing feature flags with appropriate categories
            db.session.execute(sa.text("""
                UPDATE feature_flags 
                SET category = CASE 
                    WHEN feature_key LIKE '%auth%' OR feature_key LIKE '%oauth%' THEN 'auth'
                    WHEN feature_key LIKE '%module%' THEN 'modules'
                    WHEN feature_key LIKE '%ai%' OR feature_key LIKE '%assistant%' THEN 'integrations'
                    WHEN feature_key LIKE '%ui%' OR feature_key LIKE '%dark%' OR feature_key LIKE '%notification%' THEN 'ui'
                    WHEN feature_key LIKE '%admin%' OR feature_key LIKE '%audit%' OR feature_key LIKE '%management%' THEN 'admin'
                    ELSE 'core'
                END
                WHERE category = 'core'
            """))
            db.session.commit()
            
            print("✅ Valori di categoria aggiornati per i flag esistenti")
                
        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante l'aggiunta colonna: {str(e)}")
            raise

def remove_category_column():
    """Rimuove la colonna category dalla tabella feature_flags"""
    with app.app_context():
        try:
            # Check if column exists first
            result = db.session.execute(sa.text("SELECT column_name FROM information_schema.columns WHERE table_name='feature_flags' AND column_name='category'"))
            if result.fetchone():
                print("🔧 Rimozione colonna 'category' dalla tabella feature_flags...")
                db.session.execute(sa.text("ALTER TABLE feature_flags DROP COLUMN category"))
                db.session.commit()
                print("✅ Colonna 'category' rimossa con successo")
            else:
                print("✅ Colonna 'category' già rimossa o non esistente")
                
        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante la rimozione colonna: {str(e)}")
            raise

def seed_real_feature_flags():
    """Crea feature flags basati sui veri moduli della sidebar"""
    with app.app_context():
        try:
            from models_split.settings import FeatureFlag
            from models import User
            
            print("🚩 Seeding feature flags per moduli reali della sidebar...")
            
            # Get admin user for audit trail
            admin_user = User.query.filter_by(role='admin').first()
            admin_id = admin_user.id if admin_user else None
            
            # Feature flags basati sui 12 moduli della sidebar
            sidebar_modules = [
                {
                    'feature_key': 'dashboard_module',
                    'display_name': 'Dashboard Principale',
                    'description': 'Abilita il dashboard principale con metriche aziendali'
                },
                {
                    'feature_key': 'personnel_module',
                    'display_name': 'Modulo Personale', 
                    'description': 'Abilita gestione personale, inquadramenti e performance'
                },
                {
                    'feature_key': 'timesheet_module',
                    'display_name': 'Modulo Attività',
                    'description': 'Abilita timesheet, progetti e reportistica attività'
                },
                {
                    'feature_key': 'funding_module',
                    'display_name': 'Modulo Bandi',
                    'description': 'Abilita ricerca bandi, gestione candidature e rendicontazione'
                },
                {
                    'feature_key': 'business_intelligence_module',
                    'display_name': 'Business Intelligence',
                    'description': 'Abilita dashboard BI, case studies e market intelligence'
                },
                {
                    'feature_key': 'certifications_module',
                    'display_name': 'Modulo Certificazioni',
                    'description': 'Abilita gestione certificazioni ISO e compliance'
                },
                {
                    'feature_key': 'communications_module',
                    'display_name': 'Modulo Comunicazioni',
                    'description': 'Abilita forum, messaggi, eventi e assistente HR'
                },
                {
                    'feature_key': 'engagement_module',
                    'display_name': 'Modulo Engagement',
                    'description': 'Abilita campagne, premi, classifica e sistema punti'
                },
                {
                    'feature_key': 'governance_module',
                    'display_name': 'Modulo Governance',
                    'description': 'Abilita compliance dashboard, audit trail e policy center'
                },
                {
                    'feature_key': 'ceo_module',
                    'display_name': 'Human CEO',
                    'description': 'Abilita dashboard strategico, AI assistant e insights'
                },
                {
                    'feature_key': 'recruiting_module',
                    'display_name': 'Modulo Recruiting',
                    'description': 'Abilita gestione candidati, pipeline e colloqui'
                },
                {
                    'feature_key': 'sales_module',
                    'display_name': 'Modulo Sales/CRM',
                    'description': 'Abilita CRM, clienti, contratti e pre-fatturazione'
                }
            ]
            
            created_count = 0
            updated_count = 0
            
            for flag_data in sidebar_modules:
                # Check if flag already exists
                existing_flag = FeatureFlag.query.filter_by(feature_key=flag_data['feature_key']).first()
                
                if existing_flag:
                    # Update existing
                    existing_flag.display_name = flag_data['display_name']
                    existing_flag.description = flag_data['description']
                    existing_flag.updated_by = admin_id
                    updated_count += 1
                    print(f"  📝 Aggiornato: {flag_data['feature_key']}")
                else:
                    # Create new
                    new_flag = FeatureFlag(
                        feature_key=flag_data['feature_key'],
                        display_name=flag_data['display_name'],
                        description=flag_data['description'],
                        is_enabled=True,  # Default abilitato
                        updated_by=admin_id
                    )
                    db.session.add(new_flag)
                    created_count += 1
                    print(f"  ✅ Creato: {flag_data['feature_key']}")
            
            db.session.commit()
            
            print(f"\n🎉 Seeding completato!")
            print(f"   - {created_count} feature flags creati")
            print(f"   - {updated_count} feature flags aggiornati")
            print(f"   - Totale: {len(sidebar_modules)} moduli della sidebar")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante il seeding: {str(e)}")
            raise

def execute_sql(sql_statement):
    """Esegue uno statement SQL"""
    with app.app_context():
        try:
            db.session.execute(sa.text(sql_statement))
            db.session.commit()
            print(f"SQL eseguito con successo: {sql_statement[:50]}...")
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Errore durante l'esecuzione SQL: {e}")
            return False

def seed_feature_flags():
    """Esegue il seeding delle feature flags"""
    with app.app_context():
        try:
            # Importa le classi necessarie
            from models import FeatureFlag, User
            from datetime import datetime
            
            print("🚩 Seeding feature flags...")
            
            # Get admin user for audit trail
            admin_user = User.query.filter_by(role='admin').first()
            admin_id = admin_user.id if admin_user else None
            
            # Core system feature flags
            core_flags = [
                {
                    'feature_key': 'oauth_enabled',
                    'display_name': 'OAuth Authentication',
                    'description': 'Abilita autenticazione OAuth con provider esterni (Google, Microsoft)',
                    'category': 'auth',
                    'is_enabled': True,
                    'updated_by': admin_id
                },
                {
                    'feature_key': 'google_auth',
                    'display_name': 'Google Authentication',
                    'description': 'Abilita login con account Google',
                    'category': 'auth',
                    'is_enabled': True,
                    'updated_by': admin_id
                },
                {
                    'feature_key': 'microsoft_auth',
                    'display_name': 'Microsoft Authentication',
                    'description': 'Abilita login con account Microsoft/Office 365',
                    'category': 'auth',
                    'is_enabled': True,
                    'updated_by': admin_id
                }
            ]
            
            # Module feature flags
            module_flags = [
                {
                    'feature_key': 'recruiting_module',
                    'display_name': 'Modulo Recruiting',
                    'description': 'Abilita il modulo di recruiting e gestione candidati',
                    'category': 'modules',
                    'is_enabled': True,
                    'updated_by': admin_id
                },
                {
                    'feature_key': 'ceo_assistant',
                    'display_name': 'Human CEO Assistant',
                    'description': 'Abilita l\'assistente AI per CEO e insights strategici',
                    'category': 'modules',
                    'is_enabled': True,
                    'updated_by': admin_id
                },
                {
                    'feature_key': 'certifications_module',
                    'display_name': 'Modulo Certificazioni',
                    'description': 'Abilita il modulo di gestione certificazioni ISO e compliance',
                    'category': 'modules',
                    'is_enabled': True,
                    'updated_by': admin_id
                },
                {
                    'feature_key': 'help_module',
                    'display_name': 'Sistema Help e Documentazione',
                    'description': 'Abilita il sistema help con documentazione e assistente AI',
                    'category': 'modules',
                    'is_enabled': True,
                    'updated_by': admin_id
                }
            ]
            
            # AI and advanced features
            ai_flags = [
                {
                    'feature_key': 'ai_resources',
                    'display_name': 'AI Resources',
                    'description': 'Abilita funzionalità AI per analisi CV e raccomandazioni',
                    'category': 'integrations',
                    'is_enabled': True,
                    'updated_by': admin_id
                },
                {
                    'feature_key': 'project_analytics',
                    'display_name': 'Project Analytics',
                    'description': 'Abilita analytics avanzate per progetti e performance',
                    'category': 'core',
                    'is_enabled': True,
                    'updated_by': admin_id
                }
            ]
            
            # UI and UX features
            ui_flags = [
                {
                    'feature_key': 'dark_mode',
                    'display_name': 'Dark Mode',
                    'description': 'Abilita tema scuro per l\'interfaccia utente',
                    'category': 'ui',
                    'is_enabled': True,
                    'updated_by': admin_id
                },
                {
                    'feature_key': 'notifications',
                    'display_name': 'Notifiche Push',
                    'description': 'Abilita notifiche push per aggiornamenti importanti',
                    'category': 'ui',
                    'is_enabled': True,
                    'updated_by': admin_id
                }
            ]
            
            # Business features
            business_flags = [
                {
                    'feature_key': 'timesheet_approval',
                    'display_name': 'Approvazione Timesheet',
                    'description': 'Abilita workflow di approvazione per timesheet',
                    'category': 'core',
                    'is_enabled': True,
                    'updated_by': admin_id
                },
                {
                    'feature_key': 'expense_tracking',
                    'display_name': 'Tracking Spese',
                    'description': 'Abilita gestione spese e rimborsi avanzata',
                    'category': 'core',
                    'is_enabled': True,
                    'updated_by': admin_id
                }
            ]
            
            # Admin features
            admin_flags = [
                {
                    'feature_key': 'user_management',
                    'display_name': 'Gestione Utenti Avanzata',
                    'description': 'Abilita funzioni avanzate di gestione utenti e ruoli',
                    'category': 'admin',
                    'is_enabled': True,
                    'updated_by': admin_id
                },
                {
                    'feature_key': 'audit_logs',
                    'display_name': 'Log di Audit',
                    'description': 'Abilita logging dettagliato delle azioni utente',
                    'category': 'admin',
                    'is_enabled': True,
                    'updated_by': admin_id
                }
            ]
            
            # Combine all flags
            all_flags = core_flags + module_flags + ai_flags + ui_flags + business_flags + admin_flags
            
            created_count = 0
            updated_count = 0
            
            for flag_data in all_flags:
                # Check if flag already exists
                existing_flag = FeatureFlag.query.filter_by(feature_key=flag_data['feature_key']).first()
                
                if existing_flag:
                    # Update existing flag (preserve is_enabled state)
                    existing_flag.display_name = flag_data['display_name']
                    existing_flag.description = flag_data['description']
                    existing_flag.category = flag_data['category']
                    existing_flag.updated_at = datetime.utcnow()
                    if flag_data['updated_by']:
                        existing_flag.updated_by = flag_data['updated_by']
                    updated_count += 1
                    print(f"  ↻ Aggiornato: {flag_data['feature_key']}")
                else:
                    # Create new flag
                    flag = FeatureFlag(
                        feature_key=flag_data['feature_key'],
                        display_name=flag_data['display_name'],
                        description=flag_data['description'],
                        category=flag_data['category'],
                        is_enabled=flag_data['is_enabled'],
                        updated_by=flag_data['updated_by']
                    )
                    db.session.add(flag)
                    created_count += 1
                    print(f"  ✓ Creato: {flag_data['feature_key']} ({'enabled' if flag_data['is_enabled'] else 'disabled'})")
            
            # Commit all changes
            db.session.commit()
            print(f"\n✅ Feature flags configurati con successo!")
            print(f"   📊 Creati: {created_count}")
            print(f"   🔄 Aggiornati: {updated_count}")
            print(f"   📁 Totale: {len(all_flags)}")
            
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore nel seed feature flags: {str(e)}")
            return False

def seed_security_feature_flag():
    """Crea feature flag per Security Dashboard"""
    with app.app_context():
        try:
            from models_split.settings import FeatureFlag
            from models import User
            
            print("🛡️ Seeding security dashboard feature flag...")
            
            # Get admin user for audit trail
            admin_user = User.query.filter_by(role='admin').first()
            admin_id = admin_user.id if admin_user else None
            
            # Security feature flag data
            flag_data = {
                'feature_key': 'security_dashboard',
                'display_name': 'Security Dashboard',
                'description': 'Enable admin-only security reports and penetration testing dashboard',
                'category': 'security',
                'is_enabled': False,  # Disabled by default for security
                'updated_by': admin_id
            }
            
            # Check if flag already exists
            existing_flag = FeatureFlag.query.filter_by(feature_key=flag_data['feature_key']).first()
            
            if existing_flag:
                # Update existing flag (preserve is_enabled state)
                existing_flag.display_name = flag_data['display_name']
                existing_flag.description = flag_data['description']
                existing_flag.category = flag_data['category']
                existing_flag.updated_at = datetime.utcnow()
                if flag_data['updated_by']:
                    existing_flag.updated_by = flag_data['updated_by']
                print(f"  ↻ Aggiornato: {flag_data['feature_key']}")
            else:
                # Create new flag
                flag = FeatureFlag(
                    feature_key=flag_data['feature_key'],
                    display_name=flag_data['display_name'],
                    description=flag_data['description'],
                    category=flag_data['category'],
                    is_enabled=flag_data['is_enabled'],
                    updated_by=flag_data['updated_by']
                )
                db.session.add(flag)
                print(f"  ✓ Creato: {flag_data['feature_key']} ({'enabled' if flag_data['is_enabled'] else 'disabled'})")
            
            # Commit changes
            db.session.commit()
            print("✅ Security feature flag configurato con successo!")
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore nel seed security feature flag: {str(e)}")
            return False

def run_sql_file(filename):
    """Esegue tutti gli statement SQL da un file"""
    try:
        with open(filename, 'r') as file:
            sql_script = file.read()
            statements = sql_script.split(';')
            success_count = 0

            for statement in statements:
                if statement.strip():
                    if execute_sql(statement):
                        success_count += 1

            print(f"\nEseguiti {success_count} statements SQL da {filename}")
            return True
    except Exception as e:
        print(f"Errore durante la lettura/esecuzione del file SQL: {e}")
        return False

def seed_recruiting_interviews():
    """Crea interview sessions e aggiorna workflow recruiting."""
    with app.app_context():
        try:
            print("🎯 Seeding recruiting interviews and workflow...")
            
            # Get existing data
            candidates = Candidate.query.all()
            applications = Application.query.all()
            job_postings = JobPosting.query.filter_by(status='active').all()
            interviewers = User.query.filter(User.role.in_(['admin', 'manager', 'hr'])).all()
            
            print(f"📊 Found: {len(candidates)} candidates, {len(applications)} applications, {len(interviewers)} interviewers")
            
            if not candidates or not interviewers:
                print("❌ Missing required data: candidates or interviewers")
                return False
            
            # Update application statuses and create interviews
            pipeline_steps = ['application_received', 'screening', 'interview_1', 'interview_2', 'offer']
            interview_types = ['phone_screening', 'video_technical', 'onsite_cultural', 'final_executive']
            
            updated_apps = 0
            interviews_created = 0
            
            for i, application in enumerate(applications[:5]):
                step_index = i % len(pipeline_steps)
                application.current_step = pipeline_steps[step_index]
                
                if step_index >= 1:
                    application.status = 'in_progress'
                if step_index >= 4:
                    application.status = 'pending'
                    application.overall_score = random.randint(7, 10)
                
                updated_apps += 1
                print(f"📝 Updated application {application.id}: {application.current_step}")
                
                # Create interview sessions for interview stages
                if application.current_step in ['interview_1', 'interview_2']:
                    # Skip if interview already exists
                    existing = InterviewSession.query.filter_by(application_id=application.id).first()
                    if existing:
                        print(f"   🔄 Interview already exists for application {application.id}")
                        continue
                    
                    interviewer = random.choice(interviewers)
                    interview_type = random.choice(interview_types)
                    
                    # Random date: some past (completed), some future (scheduled)
                    is_completed = random.choice([True, False])
                    
                    if is_completed:
                        scheduled_date = datetime.now() - timedelta(days=random.randint(1, 10))
                        status = 'completed'
                        score = random.randint(6, 10)
                        feedback = f"Candidato interessante. {'Raccomandato per il prossimo step.' if score >= 7 else 'Necessario approfondimento.'}"
                        recommendation = 'hire' if score >= 8 else ('maybe' if score >= 6 else 'reject')
                        completed_at = scheduled_date + timedelta(hours=1)
                    else:
                        scheduled_date = datetime.now() + timedelta(days=random.randint(1, 14))
                        status = 'scheduled'
                        score = None
                        feedback = None
                        recommendation = None
                        completed_at = None
                    
                    interview = InterviewSession(
                        application_id=application.id,
                        interview_type=interview_type,
                        scheduled_date=scheduled_date,
                        duration_minutes=60,
                        location='Video Call' if 'video' in interview_type else 'Sede Milano',
                        interviewer_id=interviewer.id,
                        status=status,
                        score=score,
                        notes=f"Colloquio {interview_type.replace('_', ' ').title()}",
                        feedback=feedback,
                        recommendation=recommendation,
                        completed_at=completed_at,
                        created_at=datetime.now(),
                        updated_at=datetime.now()
                    )
                    
                    db.session.add(interview)
                    interviews_created += 1
                    print(f"   🎤 Created {status} interview: {interview_type} for application {application.id}")
            
            # Add candidate skills
            skills_list = [
                'Python', 'JavaScript', 'React', 'Vue.js', 'Node.js', 'SQL', 'MongoDB',
                'Machine Learning', 'Project Management', 'Agile', 'Scrum', 'Leadership',
                'Communication', 'Problem Solving', 'Team Work', 'Critical Thinking'
            ]
            
            categories_map = {
                'Python': 'programming', 'JavaScript': 'programming', 'React': 'frontend',
                'Vue.js': 'frontend', 'Node.js': 'backend', 'SQL': 'database',
                'MongoDB': 'database', 'Machine Learning': 'data_science',
                'Project Management': 'management', 'Agile': 'methodology',
                'Scrum': 'methodology', 'Leadership': 'soft_skills',
                'Communication': 'soft_skills', 'Problem Solving': 'soft_skills',
                'Team Work': 'soft_skills', 'Critical Thinking': 'soft_skills'
            }
            
            skills_created = 0
            for candidate in candidates[:3]:
                num_skills = random.randint(3, 6)
                candidate_skills = random.sample(skills_list, min(num_skills, len(skills_list)))
                
                for skill_name in candidate_skills:
                    existing = CandidateSkill.query.filter_by(
                        candidate_id=candidate.id,
                        skill_name=skill_name
                    ).first()
                    if existing:
                        continue
                    
                    skill = CandidateSkill(
                        candidate_id=candidate.id,
                        skill_name=skill_name,
                        skill_category=categories_map.get(skill_name, 'other'),
                        skill_level=random.randint(2, 5),
                        years_experience=random.randint(1, 8),
                        extracted_from_cv=True,
                        confidence_score=round(random.uniform(0.7, 0.95), 2),
                        created_at=datetime.now(),
                        updated_at=datetime.now()
                    )
                    
                    db.session.add(skill)
                    skills_created += 1
                
                print(f"💡 Added {len(candidate_skills)} skills for candidate {candidate.id}")
            
            # Commit all changes
            db.session.commit()
            print(f"✅ Successfully created:")
            print(f"   - {interviews_created} interview sessions")
            print(f"   - {skills_created} candidate skills")
            print(f"   - Updated {updated_apps} application statuses")
            
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Error seeding recruiting interviews: {str(e)}")
            return False

def seed_performance_data():
    """Esegue il seeding dei dati performance per testing."""
    with app.app_context():
        try:
            from db.seed.seed_performance_data import run_seed
            run_seed()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante il seeding performance: {str(e)}")
            return False

def seed_performance_goals():
    """Esegue il seeding dei template obiettivi e obiettivi di esempio."""
    with app.app_context():
        try:
            from db.seed.seed_performance_goals import seed_performance_goals as run_goals_seed
            run_goals_seed()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante il seeding obiettivi performance: {str(e)}")
            return False

def add_performance_template_fields():
    """Aggiunge le colonne mancanti alla tabella performance_goals per il sistema template"""
    with app.app_context():
        try:
            print("🔧 Aggiunta colonne template al modello performance_goals...")
            
            # Lista delle colonne da aggiungere con i loro tipi
            columns_to_add = [
                ('is_template', 'BOOLEAN DEFAULT FALSE'),
                ('template_id', 'INTEGER'),
                ('assigned_by_id', 'INTEGER'),
                ('visibility', 'VARCHAR(20) DEFAULT \'private\''),
                ('year', 'INTEGER'),
                ('target_year', 'INTEGER')
            ]
            
            # Verifica quali colonne esistono già
            for column_name, column_def in columns_to_add:
                try:
                    result = db.session.execute(sa.text(f"""
                        SELECT column_name 
                        FROM information_schema.columns 
                        WHERE table_name='performance_goals' 
                        AND column_name='{column_name}'
                    """))
                    
                    if result.fetchone():
                        print(f"  ✅ Colonna '{column_name}' già presente")
                        continue
                    
                    # Aggiungi la colonna
                    print(f"  🔧 Aggiunta colonna '{column_name}'...")
                    db.session.execute(sa.text(f"ALTER TABLE performance_goals ADD COLUMN {column_name} {column_def}"))
                    
                    # Aggiungi foreign key constraints se necessario
                    if column_name == 'template_id':
                        print(f"  🔗 Aggiunta foreign key constraint per template_id...")
                        db.session.execute(sa.text("""
                            ALTER TABLE performance_goals 
                            ADD CONSTRAINT fk_performance_goals_template_id 
                            FOREIGN KEY (template_id) REFERENCES performance_goals(id)
                        """))
                    elif column_name == 'assigned_by_id':
                        print(f"  🔗 Aggiunta foreign key constraint per assigned_by_id...")
                        db.session.execute(sa.text("""
                            ALTER TABLE performance_goals 
                            ADD CONSTRAINT fk_performance_goals_assigned_by_id 
                            FOREIGN KEY (assigned_by_id) REFERENCES users(id)
                        """))
                    
                    print(f"  ✅ Colonna '{column_name}' aggiunta con successo")
                    
                except Exception as column_error:
                    print(f"  ❌ Errore aggiungendo colonna '{column_name}': {str(column_error)}")
                    # Non interrompere per una singola colonna
                    continue
            
            # Commit tutte le modifiche
            db.session.commit()
            print("✅ Tutte le colonne template sono state processate con successo")
            
            # Verifica finale
            result = db.session.execute(sa.text("""
                SELECT column_name, data_type, is_nullable 
                FROM information_schema.columns 
                WHERE table_name='performance_goals' 
                ORDER BY ordinal_position
            """))
            
            print("\n📋 Schema finale tabella performance_goals:")
            for row in result:
                print(f"  - {row[0]}: {row[1]} {'NULL' if row[2] == 'YES' else 'NOT NULL'}")
            
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante l'aggiunta colonne template: {str(e)}")
            return False

def create_help_tables():
    """Crea le tabelle del sistema help se non esistono"""
    with app.app_context():
        try:
            print("🔧 Creazione tabelle sistema help...")
            
            # Create all tables (SQLAlchemy will only create missing ones)
            db.create_all()
            
            print("✅ Tabelle help create con successo")
            return True
            
        except Exception as e:
            print(f"❌ Errore nella creazione tabelle help: {str(e)}")
            return False

def seed_help_categories():
    """Popola categorie help di base"""
    with app.app_context():
        try:
            print("🌱 Seeding categorie help...")
            
            # Get admin user for created_by
            admin = User.query.filter_by(role='admin').first()
            admin_id = admin.id if admin else 1
            
            categories_data = [
                {
                    'name': 'Primi Passi',
                    'slug': 'primi-passi',
                    'description': 'Guide per iniziare ad usare DatPortal',
                    'icon': 'academic-cap',
                    'color': 'text-blue-600',
                    'sort_order': 1,
                    'is_public': True
                },
                {
                    'name': 'Gestione Progetti',
                    'slug': 'gestione-progetti',
                    'description': 'Come gestire progetti, task e risorse',
                    'icon': 'briefcase',
                    'color': 'text-green-600',
                    'sort_order': 2,
                    'is_public': True
                },
                {
                    'name': 'Timesheet e Tempi',
                    'slug': 'timesheet-tempi',
                    'description': 'Registrazione e gestione timesheet',
                    'icon': 'clock',
                    'color': 'text-orange-600',
                    'sort_order': 3,
                    'is_public': True
                },
                {
                    'name': 'CRM e Clienti',
                    'slug': 'crm-clienti',
                    'description': 'Gestione clienti, contratti e proposte',
                    'icon': 'user-group',
                    'color': 'text-purple-600',
                    'sort_order': 4,
                    'is_public': True
                },
                {
                    'name': 'HR e Personale',
                    'slug': 'hr-personale',
                    'description': 'Gestione risorse umane e dipendenti',
                    'icon': 'users',
                    'color': 'text-indigo-600',
                    'sort_order': 5,
                    'is_public': True
                },
                {
                    'name': 'Amministrazione',
                    'slug': 'amministrazione',
                    'description': 'Configurazioni e gestione sistema',
                    'icon': 'cog',
                    'color': 'text-gray-600',
                    'sort_order': 10,
                    'is_public': False,
                    'required_permission': 'admin'
                },
                {
                    'name': 'FAQ',
                    'slug': 'faq',
                    'description': 'Domande frequenti',
                    'icon': 'question-mark-circle',
                    'color': 'text-yellow-600',
                    'sort_order': 99,
                    'is_public': True
                }
            ]
            
            created_count = 0
            
            for cat_data in categories_data:
                # Check if category already exists
                existing = HelpCategory.query.filter_by(slug=cat_data['slug']).first()
                
                if not existing:
                    category = HelpCategory(
                        name=cat_data['name'],
                        slug=cat_data['slug'],
                        description=cat_data['description'],
                        icon=cat_data['icon'],
                        color=cat_data['color'],
                        sort_order=cat_data['sort_order'],
                        is_public=cat_data['is_public'],
                        required_permission=cat_data.get('required_permission'),
                        created_by=admin_id
                    )
                    db.session.add(category)
                    created_count += 1
                    print(f"  ✓ Creata categoria: {cat_data['name']}")
                else:
                    print(f"  ↻ Categoria già esistente: {cat_data['name']}")
            
            db.session.commit()
            print(f"✅ Seed categorie help completato: {created_count} categorie create")
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore nel seed categorie help: {str(e)}")
            return False

def seed_help_content():
    """Popola contenuti help di esempio"""
    with app.app_context():
        try:
            print("📝 Seeding contenuti help...")
            
            # Get admin user and categories
            admin = User.query.filter_by(role='admin').first()
            admin_id = admin.id if admin else 1
            
            primi_passi_cat = HelpCategory.query.filter_by(slug='primi-passi').first()
            progetti_cat = HelpCategory.query.filter_by(slug='gestione-progetti').first()
            timesheet_cat = HelpCategory.query.filter_by(slug='timesheet-tempi').first()
            faq_cat = HelpCategory.query.filter_by(slug='faq').first()
            
            if not primi_passi_cat:
                print("❌ Categoria 'primi-passi' non trovata. Esegui prima seed_help_categories")
                return False
            
            content_data = [
                {
                    'title': 'Benvenuto in DatPortal',
                    'slug': 'benvenuto-datportal',
                    'category_id': primi_passi_cat.id,
                    'content_type': 'guide',
                    'difficulty_level': 'beginner',
                    'estimated_read_time': 5,
                    'excerpt': 'Una guida introduttiva per iniziare ad usare DatPortal',
                    'content': """# Benvenuto in DatPortal! 👋

DatPortal è la tua piattaforma aziendale completa per gestire progetti, timesheet, clienti e molto altro.

## Primi Passi

1. **Completa il tuo profilo** - Vai alla sezione Profilo per aggiornare le tue informazioni
2. **Esplora la dashboard** - La dashboard ti mostra una panoramica delle tue attività
3. **Configura le notifiche** - Personalizza come ricevere gli aggiornamenti
4. **Unisciti ai progetti** - Chiedi al tuo manager di assegnarti ai progetti pertinenti

## Aree Principali

- **Dashboard**: Panoramica delle tue attività
- **Progetti**: Gestione progetti e task
- **Timesheet**: Registrazione ore lavorate  
- **CRM**: Gestione clienti e contratti
- **Personale**: Informazioni dipendenti e HR

## Serve Aiuto?

Usa l'assistente AI cliccando sull'icona help o contatta il supporto interno.
                    """,
                    'keywords': 'benvenuto, introduzione, primi passi, guida',
                    'related_modules': '["dashboard", "personnel", "projects"]',
                    'featured': True
                },
                {
                    'title': 'Come Creare un Nuovo Progetto',
                    'slug': 'come-creare-progetto',
                    'category_id': progetti_cat.id if progetti_cat else primi_passi_cat.id,
                    'content_type': 'tutorial',
                    'difficulty_level': 'intermediate',
                    'estimated_read_time': 8,
                    'excerpt': 'Guida step-by-step per creare un nuovo progetto',
                    'content': """# Come Creare un Nuovo Progetto

## Prerequisiti
- Permessi di creazione progetti
- Informazioni di base del progetto

## Passaggi

### 1. Accedi alla sezione Progetti
- Clicca su "Progetti" nella sidebar
- Seleziona "Crea Nuovo Progetto"

### 2. Compila i Dettagli Base
- **Nome progetto**: Scegli un nome descrittivo
- **Descrizione**: Aggiungi dettagli del progetto
- **Cliente**: Seleziona il cliente associato
- **Budget**: Imposta budget iniziale

### 3. Configura il Team
- Aggiungi membri del team
- Assegna ruoli e responsabilità
- Imposta i permessi di accesso

### 4. Pianifica le Milestone
- Definisci milestone principali
- Imposta date di scadenza
- Collega deliverable

## Consigli

💡 **Suggerimento**: Usa template di progetto per velocizzare la creazione

⚠️ **Attenzione**: Verifica di avere budget approvato prima di iniziare
                    """,
                    'keywords': 'progetto, creare, nuovo, tutorial, gestione',
                    'related_modules': '["projects", "crm"]',
                    'featured': True
                },
                {
                    'title': 'Registrazione Timesheet',
                    'slug': 'registrazione-timesheet',
                    'category_id': timesheet_cat.id if timesheet_cat else primi_passi_cat.id,
                    'content_type': 'how_to',
                    'difficulty_level': 'beginner',
                    'estimated_read_time': 6,
                    'excerpt': 'Come registrare correttamente le ore lavorate',
                    'content': """# Registrazione Timesheet

## Accesso al Timesheet
1. Vai alla sezione "Timesheet" nella sidebar
2. Seleziona la settimana corrente

## Registrazione Ore

### Modalità Rapida
- Clicca su "Aggiungi Ore"
- Seleziona progetto e task
- Inserisci ore lavorate
- Aggiungi note (opzionale)

### Modalità Dettagliata
- Usa il timer integrato per tracking automatico
- Specifica attività dettagliate
- Categorizza il tipo di lavoro

## Best Practices

✅ **Registra quotidianamente** per maggiore precisione
✅ **Usa descrizioni chiare** delle attività svolte
✅ **Verifica i totali** prima di inviare per approvazione

## Approvazione

Il timesheet viene inviato automaticamente per approvazione ogni fine settimana.
                    """,
                    'keywords': 'timesheet, ore, registrazione, timer, approvazione',
                    'related_modules': '["timesheets", "projects"]',
                    'featured': False
                },
                {
                    'title': 'Come posso cambiare la mia password?',
                    'slug': 'come-cambiare-password',
                    'category_id': faq_cat.id if faq_cat else primi_passi_cat.id,
                    'content_type': 'faq',
                    'difficulty_level': 'beginner',
                    'estimated_read_time': 2,
                    'excerpt': 'Procedura per modificare la password del tuo account',
                    'content': """# Come Cambiare la Password

## Procedura Rapida

1. Clicca sul tuo nome utente in alto a destra
2. Seleziona "Profilo"
3. Vai alla sezione "Sicurezza"
4. Clicca su "Cambia Password"
5. Inserisci password attuale e nuova password
6. Conferma le modifiche

## Requisiti Password
- Minimo 8 caratteri
- Almeno una maiuscola
- Almeno un numero
- Caratteri speciali consigliati

## Problemi?
Se hai dimenticato la password attuale, contatta l'amministratore di sistema.
                    """,
                    'keywords': 'password, sicurezza, profilo, account, accesso',
                    'related_modules': '["auth", "personnel"]',
                    'featured': False
                }
            ]
            
            created_count = 0
            
            for content_data in content_data:
                # Check if content already exists
                existing = HelpContent.query.filter_by(slug=content_data['slug']).first()
                
                if not existing:
                    content = HelpContent(
                        title=content_data['title'],
                        slug=content_data['slug'],
                        content=content_data['content'],
                        excerpt=content_data['excerpt'],
                        category_id=content_data['category_id'],
                        content_type=content_data['content_type'],
                        difficulty_level=content_data['difficulty_level'],
                        estimated_read_time=content_data['estimated_read_time'],
                        keywords=content_data['keywords'],
                        related_modules=content_data['related_modules'],
                        status='published',
                        is_published=True,
                        published_at=datetime.utcnow(),
                        featured=content_data['featured'],
                        created_by=admin_id
                    )
                    db.session.add(content)
                    created_count += 1
                    print(f"  ✓ Creato contenuto: {content_data['title']}")
                else:
                    print(f"  ↻ Contenuto già esistente: {content_data['title']}")
            
            db.session.commit()
            print(f"✅ Seed contenuti help completato: {created_count} contenuti creati")
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore nel seed contenuti help: {str(e)}")
            return False

def setup_help_system():
    """Setup completo sistema help: tabelle + categorie + contenuti"""
    print("🚀 Setup completo sistema help...")
    
    # Step 1: Create tables
    if not create_help_tables():
        return False
    
    # Step 2: Seed categories  
    if not seed_help_categories():
        return False
        
    # Step 3: Seed sample content
    if not seed_help_content():
        return False
    
    print("🎉 Sistema help configurato con successo!")
    return True

def show_help_data():
    """Mostra i dati attualmente presenti nel sistema help"""
    with app.app_context():
        try:
            categories = HelpCategory.query.all()
            content = HelpContent.query.all()
            
            print(f"\n📂 Categorie Help trovate: {len(categories)}")
            for cat in categories:
                content_count = HelpContent.query.filter_by(category_id=cat.id).count()
                print(f"  - {cat.name} ({cat.slug}) - {content_count} contenuti")
            
            print(f"\n📄 Contenuti Help trovati: {len(content)}")
            for cont in content:
                cat_name = cont.category.name if cont.category else "Nessuna categoria"
                print(f"  - {cont.title} ({cont.content_type}) - Categoria: {cat_name}")
                
            if len(categories) == 0:
                print("⚠️  Nessuna categoria trovata. Esegui 'seed_help_categories'")
            if len(content) == 0:
                print("⚠️  Nessun contenuto trovato. Esegui 'seed_help_content'")
                
        except Exception as e:
            print(f"❌ Errore nel mostrare i dati help: {str(e)}")

def test_help_models():
    """Test dei modelli Help per verificare che funzionino correttamente"""
    print("🧪 TESTING HELP MODELS...")
    
    with app.app_context():
        try:
            # Test 1: Query categorie esistenti
            categories = HelpCategory.query.all()
            print(f"✅ Found {len(categories)} help categories")
            
            for cat in categories[:3]:
                print(f"  - {cat.name}: {cat.active_content_count} active content items")
                
                # Test to_dict method
                cat_dict = cat.to_dict()
                assert 'id' in cat_dict
                assert 'name' in cat_dict
                assert 'active_content_count' in cat_dict
                print(f"    ✅ to_dict() works with keys: {list(cat_dict.keys())}")
                
        except Exception as e:
            print(f"❌ Error testing categories: {e}")
            return False
        
        try:
            # Test 2: Query contenuti esistenti
            content_items = HelpContent.query.all()
            print(f"✅ Found {len(content_items)} help content items")
            
            if content_items:
                content = content_items[0]
                print(f"  - {content.title}")
                
                # Test to_dict method
                content_dict = content.to_dict()
                assert 'id' in content_dict
                assert 'title' in content_dict
                assert 'helpfulness_ratio' in content_dict
                print(f"    ✅ to_dict() works with keys: {list(content_dict.keys())}")
                
        except Exception as e:
            print(f"❌ Error testing content: {e}")
            return False
        
        # Test 3: Test the fixed active_content_count property
        try:
            categories = HelpCategory.query.all()
            for cat in categories:
                # This was the problematic line that was fixed
                count = cat.active_content_count
                print(f"  ✅ Category '{cat.name}' active_content_count: {count}")
                
        except Exception as e:
            print(f"❌ Error testing active_content_count property: {e}")
            return False
        
        print("🎉 ALL HELP MODEL TESTS PASSED!")
        return True

def add_comprehensive_faqs():
    """Aggiunge FAQ complete per tutti i moduli di DatPortal"""
    with app.app_context():
        try:
            print("❓ Aggiunta FAQ complete...")
            
            # Get FAQ category
            faq_category = HelpCategory.query.filter_by(slug='faq').first()
            if not faq_category:
                print("❌ Categoria FAQ non trovata")
                return False
            
            # Get admin user for content authoring
            admin_user = User.query.filter_by(role='admin').first()
            if not admin_user:
                admin_user = User.query.first()
            
            author_id = admin_user.id if admin_user else None
            
            faqs = [
                {
                    'title': 'Come posso reimpostare la mia password?',
                    'slug': 'reset-password-faq',
                    'content': '''# Come reimpostare la password

## Procedura di reset

1. **Dalla pagina di login**:
   - Clicca su "Password dimenticata?"
   - Inserisci la tua email aziendale
   - Controlla la tua casella email

2. **Dall'email di reset**:
   - Clicca sul link ricevuto
   - Inserisci la nuova password
   - Conferma la nuova password

## Requisiti password

- Minimo 8 caratteri
- Almeno una lettera maiuscola
- Almeno un numero
- Almeno un carattere speciale

## Problemi comuni

**Non ricevo l'email di reset?**
- Controlla la cartella spam
- Verifica che l'email sia corretta
- Contatta l'amministratore sistema

**Il link è scaduto?**
- I link scadono dopo 24 ore
- Richiedi un nuovo reset password'''
                },
                {
                    'title': 'Come faccio a registrare le ore nel timesheet?',
                    'slug': 'timesheet-registrazione-faq',
                    'content': '''# Registrazione ore timesheet

## Accesso al timesheet

1. Vai alla sezione **Timesheet** nella sidebar
2. Seleziona il mese corrente
3. Clicca sul giorno da compilare

## Compilazione giornaliera

**Informazioni richieste:**
- **Progetto**: Seleziona dal progetto attivo
- **Attività**: Specifica il tipo di lavoro
- **Ore**: Numero di ore lavorate
- **Descrizione**: Breve descrizione dell'attività

## Tipi di ore

- **Lavoro**: Ore regolari su progetti
- **Straordinari**: Ore oltre l'orario standard
- **Ferie**: Giorni di vacanza
- **Malattia**: Assenze per malattia
- **Formazione**: Ore di training

## Approvazione

- Compila tutte le giornate del mese
- Clicca "Invia per approvazione"
- Il manager riceverà notifica per la revisione'''
                },
                {
                    'title': 'Come creo un nuovo progetto?',
                    'slug': 'nuovo-progetto-faq',
                    'content': '''# Creazione nuovo progetto

## Accesso alla creazione

1. Vai su **Progetti** nella sidebar
2. Clicca il pulsante **+ Nuovo Progetto**
3. Compila il form di creazione

## Informazioni obbligatorie

**Dati base:**
- Nome progetto (univoco)
- Descrizione e obiettivi
- Cliente di riferimento
- Date inizio e fine previste

**Configurazione:**
- Budget totale
- Valuta di riferimento
- Priorità (Alta/Media/Bassa)
- Stato iniziale (Planning/Active)

## Assegnazione team

- Seleziona il Project Manager
- Aggiungi membri del team
- Definisci ruoli e responsabilità
- Imposta permessi di accesso

## Best practices

- Usa nomi descrittivi e chiari
- Definisci milestone intermedie  
- Pianifica buffer per imprevisti
- Coinvolgi stakeholder chiave'''
                },
                {
                    'title': 'Come funziona il processo di recruiting?',
                    'slug': 'recruiting-processo-faq',
                    'content': '''# Processo di recruiting

## Fasi del processo

1. **Job Posting**: Pubblicazione posizione
2. **Candidature**: Raccolta CV e lettere
3. **Screening**: Prima selezione
4. **Colloqui**: Interviste strutturate  
5. **Valutazione**: Decision making
6. **Offerta**: Proposta contrattuale

## Gestione candidature

**Pipeline standard:**
- Nuove candidature
- In screening
- Colloquio programmato
- In valutazione
- Offerta inviata
- Assunto/Rifiutato

## Strumenti disponibili

- **Parsing CV**: Estrazione automatica dati
- **Score matching**: Valutazione AI
- **Calendar integrato**: Gestione colloqui
- **Template email**: Comunicazioni standard

## Documentazione

- Salva note per ogni candidato
- Registra feedback colloqui
- Mantieni storico comunicazioni
- Rispetta privacy e GDPR'''
                },
                {
                    'title': 'Come gestisco i clienti nel CRM?',
                    'slug': 'crm-clienti-faq',
                    'content': '''# Gestione clienti CRM

## Creazione nuovo cliente

1. Vai su **CRM** → **Clienti**
2. Clicca **+ Nuovo Cliente**
3. Compila dati anagrafici
4. Salva e procedi con i contatti

## Informazioni cliente

**Dati aziendali:**
- Ragione sociale
- Partita IVA / Codice fiscale
- Indirizzo sede legale
- Settore di attività

**Dati contatto:**
- Email principale
- Telefono/Fax
- Sito web
- Note commerciali

## Gestione contatti

- Aggiungi referenti aziendali
- Definisci ruoli (CEO, Procurement, etc.)
- Mantieni storico comunicazioni
- Programma follow-up

## Contratti e proposte

- Crea proposte commerciali
- Gestisci negoziazioni
- Finalizza contratti
- Monitora scadenze'''
                },
                {
                    'title': 'Cosa sono le certificazioni ISO?',
                    'slug': 'certificazioni-iso-faq',
                    'content': '''# Certificazioni ISO

## Cos'è una certificazione ISO

Le certificazioni ISO sono standard internazionali che attestano la conformità dei processi aziendali a requisiti specifici di qualità, sicurezza e gestione.

## Certificazioni supportate

**ISO 9001** - Qualità
- Gestione della qualità
- Soddisfazione cliente
- Miglioramento continuo

**ISO 27001** - Sicurezza informatica
- Protezione dati
- Gestione rischi IT
- Controlli sicurezza

**ISO 14001** - Ambiente
- Gestione ambientale
- Sostenibilità
- Riduzione impatto

## Processo di certificazione

1. **Planning**: Analisi gap e pianificazione
2. **Implementazione**: Adeguamento processi
3. **Audit interno**: Verifica conformità
4. **Certificazione**: Audit ente terzo
5. **Mantenimento**: Audit periodici

## Gestione in DatPortal

- Monitora stato certificazioni
- Gestisci scadenze e rinnovi
- Pianifica audit interni
- Traccia azioni correttive'''
                },
                {
                    'title': 'Come accedo ai bandi di finanziamento?',
                    'slug': 'bandi-finanziamento-faq',
                    'content': '''# Bandi di finanziamento

## Ricerca opportunità

1. Vai su **Bandi** nella sidebar
2. Usa filtri per settore e tipologia
3. Consulta dettagli opportunità
4. Verifica requisiti di partecipazione

## Tipologie di bando

**Ricerca e sviluppo:**
- Progetti innovativi
- Trasferimento tecnologico
- Collaborazioni università-impresa

**Internazionalizzazione:**
- Export e marketing
- Partecipazione fiere
- Apertura mercati esteri

**Sostenibilità:**
- Efficienza energetica
- Economia circolare
- Riduzione impatto ambientale

## Processo di candidatura

1. **Analisi bando**: Verifica requisiti
2. **Preparazione**: Raccolta documentazione
3. **Compilazione**: Form online
4. **Invio**: Rispetto deadline
5. **Follow-up**: Monitoraggio esito

## Gestione progetti approvati

- Rendicontazione spese
- Report avanzamento
- Comunicazioni con ente
- Gestione milestone'''
                },
                {
                    'title': 'Come funziona Human CEO AI?',
                    'slug': 'human-ceo-ai-faq',
                    'content': '''# Human CEO AI Assistant

## Cos'è Human CEO

Human CEO è un assistente AI specializzato per supportare dirigenti e decision maker con analisi strategiche, insights di business e supporto decisionale basato sui dati aziendali.

## Funzionalità principali

**Dashboard strategico:**
- KPI aziendali in tempo reale
- Trend e performance
- Alert e anomalie
- Previsioni business

**Assistente conversazionale:**
- Analisi dati complesse
- Ricerche di mercato
- Supporto decisionale
- Insights strategici

## Modalità di utilizzo

**Ricerca veloce:**
- Query rapide su dati
- Analisi immediate
- Risposte concise

**Analisi approfondita:**
- Ricerche complete
- Report dettagliati
- Raccomandazioni strutturate

## Configurazione

- Profilo aziendale personalizzato
- Settori di interesse
- Stile comunicativo preferito
- Argomenti di ricerca ricorrenti

## Privacy e sicurezza

- Dati sempre protetti
- Accesso solo a dirigenti autorizzati
- Conformità GDPR
- Audit log completo'''
                },
                {
                    'title': 'Come gestisco le notifiche?',
                    'slug': 'notifiche-gestione-faq',
                    'content': '''# Gestione notifiche

## Tipi di notifiche

**Sistema:**
- Scadenze importanti
- Aggiornamenti sistema
- Manutenzioni programmate

**Progetti:**
- Nuovi task assegnati
- Deadline in scadenza
- Milestone raggiunte
- Aggiornamenti team

**HR:**
- Approvazioni richieste
- Timesheet da approvare
- Nuove candidature

## Configurazione preferenze

1. Clicca l'icona profilo (in alto a destra)
2. Vai su **Impostazioni**
3. Sezione **Notifiche**
4. Personalizza per categoria

## Canali di notifica

**In-app:**
- Badge numerici
- Popup informativi
- Dashboard alerts

**Email:**
- Riepiloghi giornalieri
- Notifiche urgenti
- Report settimanali

**Browser:**
- Notifiche push
- Alert desktop
- Promemoria sonori

## Gestione notifiche ricevute

- Marca come lette
- Archivia o elimina
- Rispondi direttamente
- Programma promemoria'''
                }
            ]
            
            created_count = 0
            for faq_data in faqs:
                # Check if FAQ already exists
                existing = HelpContent.query.filter_by(slug=faq_data['slug']).first()
                if existing:
                    print(f"  ↻ FAQ già esistente: {faq_data['title']}")
                    continue
                
                faq = HelpContent(
                    title=faq_data['title'],
                    slug=faq_data['slug'],
                    content=faq_data['content'],
                    category_id=faq_category.id,
                    content_type='faq',
                    difficulty_level='beginner',
                    estimated_read_time=5,
                    is_published=True,
                    featured=False,
                    created_by=author_id,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                
                db.session.add(faq)
                created_count += 1
                print(f"  ✅ FAQ creata: {faq_data['title']}")
            
            db.session.commit()
            print(f"✅ FAQ aggiunte: {created_count} nuove FAQ create")
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore nell'aggiunta FAQ: {str(e)}")
            return False

if __name__ == "__main__":
    # Mostra le tabelle all'inizio
    show_tables()

    # Se viene passato un file come argomento, eseguilo
    if len(sys.argv) > 1:
        # Gestione comandi per aggiornamenti database
        if sys.argv[1] == "seed_feature_flags":
            seed_feature_flags()
        elif sys.argv[1] == "list_feature_flags":
            list_feature_flags()
        elif sys.argv[1] == "cleanup_feature_flags":
            cleanup_feature_flags()
        elif sys.argv[1] == "add_category_column":
            add_category_column()
        elif sys.argv[1] == "remove_category_column":
            remove_category_column()
        elif sys.argv[1] == "seed_real_feature_flags":
            seed_real_feature_flags()
        elif sys.argv[1] == "seed_recruiting_interviews":
            seed_recruiting_interviews()
        elif sys.argv[1] == "seed_performance_data":
            seed_performance_data()
        elif sys.argv[1] == "seed_performance_goals":
            seed_performance_goals()
        elif sys.argv[1] == "add_performance_template_fields":
            add_performance_template_fields()
        elif sys.argv[1] == "create_help_tables":
            create_help_tables()
        elif sys.argv[1] == "seed_help_categories":
            seed_help_categories()
        elif sys.argv[1] == "seed_help_content":
            seed_help_content()
        elif sys.argv[1] == "setup_help_system":
            setup_help_system()
        elif sys.argv[1] == "show_help_data":
            show_help_data()
        elif sys.argv[1] == "test_help_models":
            test_help_models()
        elif sys.argv[1] == "add_comprehensive_faqs":
            add_comprehensive_faqs()
        elif sys.argv[1] == "seed_security_feature_flag":
            seed_security_feature_flag()
        elif sys.argv[1] == "inspect_performance_kpis":
            inspect_performance_kpis()
        #elif sys.argv[1] == "add_start_date":
        #    add_start_date_to_task()
        #elif sys.argv[1] == "setup_hr":
        #    setup_hr_module()
        #elif sys.argv[1] == "create_hr_tables":
        #    create_hr_tables()
        #elif sys.argv[1] == "migrate_skills":
        #    migrate_user_skills()
        #elif sys.argv[1] == "populate_orgchart":
        #    populate_orgchart_data()
        #elif sys.argv[1] == "create_timesheet_data":
        #    create_timesheet_test_data()
        #elif sys.argv[1] == "setup_invoicing":
        #    setup_invoicing_system()
        #elif sys.argv[1] == "create_invoicing_tables":
        #    create_invoicing_tables()
        #elif sys.argv[1] == "setup_invoicing_defaults":
        #    setup_invoicing_defaults()
        #elif sys.argv[1] == "seed_invoicing_data":
        #    seed_invoicing_data()
        #elif sys.argv[1] == "add_client_invoicing_fields":
        #    add_client_invoicing_fields()
        #elif sys.argv[1] == "setup_performance":
        #    setup_performance_system()
        #elif sys.argv[1] == "add_performance_columns":
        #    add_missing_performance_columns()
        #else:
        #    run_sql_file(sys.argv[1])

        # Mostra le tabelle dopo l'esecuzione
        show_tables()
    else:
        print("Uso: python db_update.py [comando | file_sql]")
        print("\nComandi disponibili:")
        print("  seed_feature_flags       - Crea feature flags per i moduli")
        print("  cleanup_feature_flags    - Pulisce tutti i feature flags")
        print("  seed_real_feature_flags  - Crea feature flags basati sui moduli reali")
        print("  seed_recruiting_interviews - Crea dati di test per recruiting")
        print("  seed_performance_data    - Crea dati di test per performance management")
        print("  seed_performance_goals   - Crea template obiettivi e obiettivi di esempio")
        print("  add_performance_template_fields - Aggiunge colonne template a performance_goals")
        print("  create_help_tables       - Crea tabelle sistema help")
        print("  seed_help_categories     - Popola categorie help di base")
        print("  seed_help_content        - Popola contenuti help di esempio")
        print("  setup_help_system        - Setup completo sistema help (tabelle + dati)")
        print("  seed_security_feature_flag - Crea feature flag per Security Dashboard")
        print("\nSe non viene specificato un comando, verranno mostrate solo le tabelle esistenti.")


