#!/usr/bin/env python3
"""
Field Validator Script
======================
Validates that models don't have unnecessary aliases or mappings
and that field names are consistent across the codebase.
"""

import os
import sys
import re
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

from app import create_app
from extensions import db
import inspect


def check_model_aliases(model_class):
    """Check if a model has @property aliases that shouldn't exist"""
    aliases_found = []
    
    # Get all properties of the class
    for name, obj in inspect.getmembers(model_class):
        if isinstance(obj, property):
            # Check if it's likely an alias (has simple getter that returns another attribute)
            if hasattr(obj.fget, '__code__'):
                code = obj.fget.__code__
                # Simple heuristic: if the property just returns another attribute
                if code.co_argcount == 1 and 'self.' in str(code.co_consts):
                    aliases_found.append(name)
    
    return aliases_found


def check_api_field_mappings(api_file_path):
    """Check for field mappings in API endpoints"""
    mappings_found = []
    
    try:
        with open(api_file_path, 'r') as f:
            content = f.read()
            
        # Look for patterns like field_mapping = {...}
        mapping_pattern = r'field_mapping\s*=\s*\{[^}]+\}'
        mappings = re.findall(mapping_pattern, content)
        
        # Look for patterns like data.get('old_name') mapped to new_name
        get_pattern = r'(\w+)\s*=\s*data\.get\([\'"](\w+)[\'"]\)'
        get_mappings = re.findall(get_pattern, content)
        
        # Check if field names are different
        for model_field, api_field in get_mappings:
            if model_field != api_field and not api_field.startswith('is_'):
                mappings_found.append(f"{api_field} -> {model_field}")
                
    except FileNotFoundError:
        pass
        
    return mappings_found


def check_to_dict_aliases(model_file_path):
    """Check for duplicate fields in to_dict methods"""
    aliases_found = []
    
    try:
        with open(model_file_path, 'r') as f:
            content = f.read()
            
        # Find to_dict methods
        to_dict_pattern = r'def to_dict\(self\):[^}]+\}'
        to_dict_matches = re.findall(to_dict_pattern, content, re.DOTALL)
        
        for match in to_dict_matches:
            # Look for patterns where same value is assigned to multiple keys
            lines = match.split('\n')
            field_values = {}
            
            for line in lines:
                # Match patterns like 'field_name': self.attribute
                field_match = re.match(r'\s*[\'"](\w+)[\'"]\s*:\s*self\.(\w+)', line)
                if field_match:
                    key, value = field_match.groups()
                    if value in field_values and key != value:
                        aliases_found.append(f"{key} duplicates {field_values[value]}")
                    field_values[value] = key
                    
    except FileNotFoundError:
        pass
        
    return aliases_found


def validate_all():
    """Run all validation checks"""
    app = create_app()
    
    with app.app_context():
        print("🔍 FIELD CONSISTENCY VALIDATOR")
        print("=" * 60)
        
        issues = []
        
        # Check models directory
        models_dir = Path(__file__).parent.parent.parent / 'models_split'
        api_dir = Path(__file__).parent.parent.parent / 'blueprints' / 'api'
        
        print("\n📋 Checking Models for Aliases...")
        print("-" * 40)
        
        # Import all models
        from models import (
            User, JobLevel, TimeOffRequest, Project, Task, 
            Client, Invoice, Department
        )
        
        for model_class in [User, JobLevel, TimeOffRequest, Project, Task]:
            aliases = check_model_aliases(model_class)
            if aliases:
                print(f"❌ {model_class.__name__}: Found property aliases: {aliases}")
                issues.append(f"{model_class.__name__} has property aliases")
            else:
                print(f"✅ {model_class.__name__}: No aliases found")
                
        print("\n📋 Checking API Endpoints for Field Mappings...")
        print("-" * 40)
        
        # Check API files
        api_files = ['personnel.py', 'timeoff_requests.py', 'projects.py', 'clients.py']
        
        for api_file in api_files:
            api_path = api_dir / api_file
            mappings = check_api_field_mappings(api_path)
            if mappings:
                print(f"❌ {api_file}: Found field mappings: {mappings}")
                issues.append(f"{api_file} has field mappings")
            else:
                print(f"✅ {api_file}: No mappings found")
                
        print("\n📋 Checking to_dict Methods for Duplicate Fields...")
        print("-" * 40)
        
        # Check model files
        model_files = list(models_dir.glob('*.py'))
        
        for model_file in model_files:
            if model_file.name not in ['__init__.py', 'base.py', 'associations.py']:
                aliases = check_to_dict_aliases(model_file)
                if aliases:
                    print(f"❌ {model_file.name}: Found duplicate fields: {aliases}")
                    issues.append(f"{model_file.name} has duplicate fields in to_dict")
                else:
                    print(f"✅ {model_file.name}: No duplicates found")
                    
        print("\n" + "=" * 60)
        print("📊 SUMMARY")
        print("-" * 20)
        
        if issues:
            print(f"❌ Found {len(issues)} issues:")
            for issue in issues:
                print(f"   - {issue}")
            print("\n💡 Recommendation: Remove all aliases and use consistent field names")
        else:
            print("✅ All checks passed! No field inconsistencies found.")
            print("   The codebase uses consistent field names without mappings.")


if __name__ == "__main__":
    validate_all()