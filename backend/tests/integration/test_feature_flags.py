#!/usr/bin/env python3

import sys
import os

# Add backend to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

from app import create_app
from extensions import db
from models_split.settings import FeatureFlag

def test_feature_flags():
    app = create_app()
    with app.app_context():
        try:
            flags = FeatureFlag.query.all()
            print(f'Found {len(flags)} feature flags in database:')
            for flag in flags:
                category = getattr(flag, 'category', 'N/A')
                print(f'  - {flag.feature_key}: enabled={flag.is_enabled}, category={category}')
            
            # Test the API response format
            print(f'\nTesting API response format:')
            flags_dict = {}
            for flag in flags:
                flags_dict[flag.feature_key] = flag.is_enabled
            
            print(f'API flags dict would contain {len(flags_dict)} entries:')
            for key, value in flags_dict.items():
                print(f'  - {key}: {value}')
                
        except Exception as e:
            print(f'Error: {e}')
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_feature_flags()