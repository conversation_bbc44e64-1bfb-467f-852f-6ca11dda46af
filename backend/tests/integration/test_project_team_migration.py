#!/usr/bin/env python3
"""
Test per verificare la migrazione da project_team a project_resources
"""

import unittest
import sys
import os

# Aggiungi il path per importare i moduli
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from extensions import db
from models import Project, User, ProjectResource
from sqlalchemy import text


class TestProjectTeamMigration(unittest.TestCase):
    """Test per verificare la migrazione project_team -> project_resources"""

    def setUp(self):
        """Setup del test"""
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        
        # Crea tutte le tabelle
        db.create_all()
        
        # Crea dati di test
        self.user1 = User(
            username='testuser1',
            email='<EMAIL>',
            first_name='Test',
            last_name='User1'
        )
        self.user2 = User(
            username='testuser2',
            email='<EMAIL>',
            first_name='Test',
            last_name='User2'
        )
        
        self.project = Project(
            name='Test Project',
            description='Test project description',
            status='active'
        )
        
        db.session.add_all([self.user1, self.user2, self.project])
        db.session.commit()

    def tearDown(self):
        """Cleanup del test"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()

    def test_project_team_members_via_resources(self):
        """Test che team_members funzioni tramite project_resources"""
        
        # Crea project_resources
        resource1 = ProjectResource(
            project_id=self.project.id,
            user_id=self.user1.id,
            role='Developer',
            allocation_percentage=80
        )
        resource2 = ProjectResource(
            project_id=self.project.id,
            user_id=self.user2.id,
            role='Designer',
            allocation_percentage=50
        )
        
        db.session.add_all([resource1, resource2])
        db.session.commit()
        
        # Verifica che team_members restituisca gli utenti corretti
        team_members = self.project.team_members
        self.assertEqual(len(team_members), 2)
        self.assertIn(self.user1, team_members)
        self.assertIn(self.user2, team_members)

    def test_add_team_member_method(self):
        """Test del metodo add_team_member"""
        
        # Aggiungi team member
        resource = self.project.add_team_member(
            self.user1, 
            role='Developer', 
            allocation_percentage=100
        )
        db.session.commit()
        
        # Verifica che sia stato creato il ProjectResource
        self.assertIsNotNone(resource)
        self.assertEqual(resource.user_id, self.user1.id)
        self.assertEqual(resource.role, 'Developer')
        self.assertEqual(resource.allocation_percentage, 100)
        
        # Verifica che sia in team_members
        self.assertIn(self.user1, self.project.team_members)

    def test_remove_team_member_method(self):
        """Test del metodo remove_team_member"""
        
        # Prima aggiungi
        self.project.add_team_member(self.user1, role='Developer')
        db.session.commit()
        
        # Verifica che sia stato aggiunto
        self.assertIn(self.user1, self.project.team_members)
        
        # Rimuovi
        success = self.project.remove_team_member(self.user1)
        db.session.commit()
        
        # Verifica rimozione
        self.assertTrue(success)
        self.assertNotIn(self.user1, self.project.team_members)
        
        # Verifica che ProjectResource sia stato rimosso
        resource = ProjectResource.query.filter_by(
            project_id=self.project.id,
            user_id=self.user1.id
        ).first()
        self.assertIsNone(resource)

    def test_user_projects_property(self):
        """Test della proprietà projects in User"""
        
        # Crea project_resources
        resource = ProjectResource(
            project_id=self.project.id,
            user_id=self.user1.id,
            role='Developer',
            allocation_percentage=80
        )
        db.session.add(resource)
        db.session.commit()
        
        # Verifica che user.projects funzioni
        projects = self.user1.projects
        self.assertEqual(len(projects), 1)
        self.assertEqual(projects[0].id, self.project.id)

    def test_get_project_role_and_allocation(self):
        """Test dei metodi get_project_role e get_project_allocation"""
        
        # Aggiungi team member con ruolo e allocazione specifici
        self.project.add_team_member(
            self.user1, 
            role='Lead Developer', 
            allocation_percentage=75
        )
        db.session.commit()
        
        # Test get_project_role
        role = self.user1.get_project_role(self.project)
        self.assertEqual(role, 'Lead Developer')
        
        # Test get_project_allocation
        allocation = self.user1.get_project_allocation(self.project)
        self.assertEqual(allocation, 75.0)

    def test_compatibility_with_existing_code(self):
        """Test compatibilità con codice esistente che usa project.team_members"""
        
        # Aggiungi team members
        self.project.add_team_member(self.user1, role='Developer')
        self.project.add_team_member(self.user2, role='Designer')
        db.session.commit()
        
        # Test che il codice esistente funzioni ancora
        team_count = len(self.project.team_members)
        self.assertEqual(team_count, 2)
        
        # Test iterazione su team_members
        user_ids = [member.id for member in self.project.team_members]
        self.assertIn(self.user1.id, user_ids)
        self.assertIn(self.user2.id, user_ids)
        
        # Test check membership
        self.assertIn(self.user1, self.project.team_members)
        self.assertIn(self.user2, self.project.team_members)

    def test_can_view_project_method(self):
        """Test del metodo can_view_project aggiornato"""
        
        # Aggiungi user1 al progetto
        self.project.add_team_member(self.user1, role='Developer')
        db.session.commit()
        
        # Test che user1 possa vedere il progetto
        # Nota: questo test richiede che il metodo can_view_project sia aggiornato
        # per usare project_resources invece di project_team
        can_view = self.user1.can_view_project(self.project)
        # Il test dipende dall'implementazione dei permessi
        # self.assertTrue(can_view)  # Decommenta se il metodo è implementato

    def test_duplicate_team_member_handling(self):
        """Test gestione duplicati"""
        
        # Aggiungi lo stesso utente due volte
        resource1 = self.project.add_team_member(self.user1, role='Developer')
        resource2 = self.project.add_team_member(self.user1, role='Lead Developer')
        db.session.commit()
        
        # Dovrebbe restituire lo stesso oggetto (aggiornato)
        self.assertEqual(resource1.id, resource2.id)
        self.assertEqual(resource2.role, 'Lead Developer')
        
        # Dovrebbe esserci solo un record in project_resources
        resources = ProjectResource.query.filter_by(
            project_id=self.project.id,
            user_id=self.user1.id
        ).all()
        self.assertEqual(len(resources), 1)


def run_migration_tests():
    """Esegue tutti i test di migrazione"""
    print("🧪 ESECUZIONE TEST MIGRAZIONE PROJECT_TEAM")
    print("=" * 50)
    
    # Crea test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestProjectTeamMigration)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    if result.wasSuccessful():
        print("\n✅ TUTTI I TEST SUPERATI!")
        print("La migrazione può procedere in sicurezza.")
        return True
    else:
        print("\n❌ ALCUNI TEST FALLITI!")
        print("Risolvi i problemi prima di procedere con la migrazione.")
        return False


if __name__ == "__main__":
    success = run_migration_tests()
    sys.exit(0 if success else 1) 