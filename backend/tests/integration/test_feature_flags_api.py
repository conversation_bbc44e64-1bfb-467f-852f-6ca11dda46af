#!/usr/bin/env python3
"""
Test script to verify feature flags API is working correctly
"""

import sys
import os
import json

# Add backend to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

from app import create_app
from extensions import db
from models import User
from models_split.settings import FeatureFlag

def test_feature_flags_with_auth():
    """Test feature flags API with proper authentication setup"""
    
    # Create test app with proper configuration
    app = create_app(config_overrides={
        'TESTING': True,
        'SQLALCHEMY_DATABASE_URI': 'sqlite:///:memory:',
        'WTF_CSRF_ENABLED': False,
        'LOGIN_DISABLED': False,
        'SECRET_KEY': 'test-secret-key'
    })
    
    with app.app_context():
        # Create tables
        db.create_all()
        
        # Create admin user
        admin = User(
            username='admin',
            email='<EMAIL>',
            first_name='Admin',
            last_name='User',
            role='admin',
            is_active=True
        )
        admin.set_password('password')
        db.session.add(admin)
        
        # Create test feature flags
        test_flags = [
            FeatureFlag(
                feature_key='test_module_1',
                display_name='Test Module 1',
                description='A test module that is enabled',
                is_enabled=True,
                category='modules'
            ),
            FeatureFlag(
                feature_key='test_module_2',
                display_name='Test Module 2', 
                description='A test module that is disabled',
                is_enabled=False,
                category='modules'
            ),
            FeatureFlag(
                feature_key='test_auth_feature',
                display_name='Test Auth Feature',
                description='A test authentication feature',
                is_enabled=True,
                category='auth'
            )
        ]
        
        for flag in test_flags:
            db.session.add(flag)
        
        db.session.commit()
        
        print(f"Created {len(test_flags)} test feature flags")
        
        # Test with client
        with app.test_client() as client:
            # Test 1: Unauthenticated request should fail
            print("\n=== Test 1: Unauthenticated Request ===")
            response = client.get('/api/feature-flags')
            print(f"Status: {response.status_code}")
            print(f"Response: {response.get_json()}")
            assert response.status_code == 401
            
            # Test 2: Login
            print("\n=== Test 2: Login ===")
            login_response = client.post('/api/auth/login', 
                json={'username': 'admin', 'password': 'password'},
                headers={'Content-Type': 'application/json'})
            print(f"Login Status: {login_response.status_code}")
            login_data = login_response.get_json()
            print(f"Login Response: {login_data}")
            
            if login_response.status_code == 200 and login_data.get('success'):
                print("✅ Login successful")
                
                # Test 3: Get all feature flags
                print("\n=== Test 3: Get All Feature Flags ===")
                all_flags_response = client.get('/api/feature-flags?enabled_only=false')
                print(f"All Flags Status: {all_flags_response.status_code}")
                all_flags_data = all_flags_response.get_json()
                print(f"All Flags Response: {all_flags_data}")
                
                if all_flags_data and all_flags_data.get('success'):
                    flags = all_flags_data.get('data', {}).get('flags', {})
                    print(f"✅ Found {len(flags)} total feature flags:")
                    for key, value in flags.items():
                        print(f"  - {key}: {value}")
                else:
                    print("❌ Failed to get all feature flags")
                
                # Test 4: Get enabled feature flags only
                print("\n=== Test 4: Get Enabled Feature Flags Only ===")
                enabled_flags_response = client.get('/api/feature-flags?enabled_only=true')
                print(f"Enabled Flags Status: {enabled_flags_response.status_code}")
                enabled_flags_data = enabled_flags_response.get_json()
                print(f"Enabled Flags Response: {enabled_flags_data}")
                
                if enabled_flags_data and enabled_flags_data.get('success'):
                    enabled_flags = enabled_flags_data.get('data', {}).get('flags', {})
                    print(f"✅ Found {len(enabled_flags)} enabled feature flags:")
                    for key, value in enabled_flags.items():
                        print(f"  - {key}: {value}")
                    
                    # Verify only enabled flags are returned
                    all_enabled = all(value for value in enabled_flags.values())
                    print(f"✅ All returned flags are enabled: {all_enabled}")
                else:
                    print("❌ Failed to get enabled feature flags")
                
                # Test 5: Check individual feature flag
                print("\n=== Test 5: Check Individual Feature Flag ===")
                check_response = client.get('/api/feature-flags/check/test_module_1')
                print(f"Check Status: {check_response.status_code}")
                check_data = check_response.get_json()
                print(f"Check Response: {check_data}")
                
                if check_data and check_data.get('success'):
                    flag_info = check_data.get('data', {})
                    print(f"✅ Flag test_module_1 - enabled: {flag_info.get('enabled')}, exists: {flag_info.get('exists')}")
                
            else:
                print("❌ Login failed")

if __name__ == '__main__':
    test_feature_flags_with_auth()