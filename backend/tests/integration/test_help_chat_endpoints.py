#!/usr/bin/env python3
"""
Test script per gli endpoint help/chat appena implementati
"""
import sys
sys.path.insert(0, '.')

from app import create_app
from extensions import db
from models import User, HelpConversation
import json

def test_help_chat_endpoints():
    app = create_app()
    
    with app.app_context():
        # Create tables
        db.create_all()
        
        # Create test user
        test_user = User(
            username='testuser',
            email='<EMAIL>',
            password='password123',
            full_name='Test User',
            role='employee'
        )
        db.session.add(test_user)
        db.session.commit()
        
        with app.test_client() as client:
            # Simulate login session
            with client.session_transaction() as sess:
                sess['_user_id'] = str(test_user.id)
                sess['_fresh'] = True
            
            print("=== Testing Help Chat Endpoints ===")
            
            # Test 1: Start chat session
            print("\n1. Testing /api/help/chat/start")
            response = client.post('/api/help/chat/start', 
                                 json={'current_module': 'personnel', 'title': 'Test Chat'})
            print(f"Status: {response.status_code}")
            
            if response.status_code == 201:
                data = response.get_json()
                print(f"Success: {data.get('success')}")
                print(f"Session ID: {data.get('data', {}).get('session_id')}")
                print(f"Messages: {len(data.get('data', {}).get('messages', []))}")
                
                session_id = data.get('data', {}).get('session_id')
                
                # Test 2: Send message
                print("\n2. Testing /api/help/chat/{session_id}/message")
                message_response = client.post(f'/api/help/chat/{session_id}/message',
                                             json={'message': 'Come posso gestire un nuovo dipendente?'})
                print(f"Status: {message_response.status_code}")
                
                if message_response.status_code == 200:
                    msg_data = message_response.get_json()
                    print(f"Success: {msg_data.get('success')}")
                    print(f"AI Response: {msg_data.get('data', {}).get('message', {}).get('content', '')[:100]}...")
                
                # Test 3: Get conversation
                print("\n3. Testing /api/help/chat/{session_id}")
                conv_response = client.get(f'/api/help/chat/{session_id}')
                print(f"Status: {conv_response.status_code}")
                
                if conv_response.status_code == 200:
                    conv_data = conv_response.get_json()
                    print(f"Success: {conv_data.get('success')}")
                    print(f"Messages count: {len(conv_data.get('data', {}).get('messages', []))}")
                
                # Test 4: Get user conversations
                print("\n4. Testing /api/help/chat/conversations")
                convs_response = client.get('/api/help/chat/conversations')
                print(f"Status: {convs_response.status_code}")
                
                if convs_response.status_code == 200:
                    convs_data = convs_response.get_json()
                    print(f"Success: {convs_data.get('success')}")
                    print(f"Conversations count: {len(convs_data.get('data', {}).get('conversations', []))}")
                
            else:
                print(f"Error: {response.get_json()}")
            
            print("\n=== Test Complete ===")
            
            # Cleanup
            db.session.query(HelpConversation).delete()
            db.session.query(User).delete()
            db.session.commit()

if __name__ == '__main__':
    test_help_chat_endpoints()