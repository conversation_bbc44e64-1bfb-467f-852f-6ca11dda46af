#!/bin/bash

# Script per eseguire i test con PostgreSQL invece di SQLite
# Configura automaticamente il database di test e lancia i test

set -e

echo "🐘 Configurazione test PostgreSQL per DatPortal"

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Verifica che PostgreSQL sia installato
if ! command -v psql &> /dev/null; then
    echo -e "${RED}❌ PostgreSQL non trovato. Installalo prima di continuare.${NC}"
    exit 1
fi

# Configurazione database di test
TEST_DB_NAME="datportal_test"
TEST_DB_USER="datportal_test"
TEST_DB_PASSWORD="test_password"
TEST_DB_HOST="localhost"
TEST_DB_PORT="5432"

echo -e "${BLUE}📋 Configurazione database di test:${NC}"
echo "  Database: $TEST_DB_NAME"
echo "  User: $TEST_DB_USER"
echo "  Host: $TEST_DB_HOST:$TEST_DB_PORT"

# Crea database e utente di test se non esistono
echo -e "${YELLOW}🔧 Setup database di test...${NC}"

# Crea utente di test
psql -h $TEST_DB_HOST -p $TEST_DB_PORT -U postgres -c "
CREATE USER $TEST_DB_USER WITH PASSWORD '$TEST_DB_PASSWORD' CREATEDB;
" 2>/dev/null || echo "User $TEST_DB_USER già esiste"

# Crea database di test
psql -h $TEST_DB_HOST -p $TEST_DB_PORT -U postgres -c "
CREATE DATABASE $TEST_DB_NAME OWNER $TEST_DB_USER;
" 2>/dev/null || echo "Database $TEST_DB_NAME già esiste"

# Grant permessi
psql -h $TEST_DB_HOST -p $TEST_DB_PORT -U postgres -c "
GRANT ALL PRIVILEGES ON DATABASE $TEST_DB_NAME TO $TEST_DB_USER;
" 2>/dev/null || true

# Costruisci URL database di test
TEST_DATABASE_URL="postgresql://$TEST_DB_USER:$TEST_DB_PASSWORD@$TEST_DB_HOST:$TEST_DB_PORT/$TEST_DB_NAME"

echo -e "${GREEN}✅ Database di test configurato${NC}"

# Esporta variabili ambiente per i test
export TEST_DATABASE_URL=$TEST_DATABASE_URL
export FLASK_ENV=testing

echo -e "${BLUE}🧪 Lancio test con PostgreSQL...${NC}"

# Pulisci il database di test prima di iniziare
echo -e "${YELLOW}🧹 Pulizia database di test...${NC}"
psql $TEST_DATABASE_URL -c "
DROP SCHEMA public CASCADE;
CREATE SCHEMA public;
GRANT ALL ON SCHEMA public TO $TEST_DB_USER;
GRANT ALL ON SCHEMA public TO public;
" 2>/dev/null || true

# Lancia i test specificati o tutti i test
if [ $# -eq 0 ]; then
    echo -e "${BLUE}🎯 Esecuzione di tutti i test...${NC}"
    python -m pytest tests/ -v --tb=short --durations=10
else
    echo -e "${BLUE}🎯 Esecuzione test specifici: $@${NC}"
    python -m pytest "$@" -v --tb=short
fi

# Verifica risultato
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Test completati con successo!${NC}"
    
    # Mostra riepilogo coverage se disponibile
    if command -v coverage &> /dev/null; then
        echo -e "${BLUE}📊 Generazione report coverage...${NC}"
        coverage report --show-missing
    fi
else
    echo -e "${RED}❌ Alcuni test sono falliti${NC}"
    exit 1
fi

# Opzionale: mantieni database per debug
if [ "$KEEP_TEST_DB" = "true" ]; then
    echo -e "${YELLOW}⚠️  Database di test mantenuto per debug${NC}"
    echo "  Connessione: $TEST_DATABASE_URL"
else
    echo -e "${YELLOW}🧹 Pulizia database di test...${NC}"
    psql $TEST_DATABASE_URL -c "
    DROP SCHEMA public CASCADE;
    CREATE SCHEMA public;
    " 2>/dev/null || true
fi

echo -e "${GREEN}🎉 Operazione completata!${NC}"