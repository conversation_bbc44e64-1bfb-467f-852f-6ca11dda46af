#!/usr/bin/env python3
import os
import sys
from datetime import datetime
from flask import Flask
from flask_mail import Mail
from services.email_service import recruiting_email_service

# Test recruiting email service with manual config
app = Flask(__name__)
app.config['MAIL_SERVER'] = 'mail.tophost.it'
app.config['MAIL_PORT'] = 587
app.config['MAIL_USE_TLS'] = True
app.config['MAIL_USERNAME'] = 'danielesabetta.it'
app.config['MAIL_PASSWORD'] = 'sabetta25..'
app.config['MAIL_DEFAULT_SENDER'] = '<EMAIL>'
app.config['COMPANY_NAME'] = 'DatPortal'

mail = Mail(app)

with app.app_context():
    print("🔧 Testing Recruiting Email Service...")
    
    test_interview_data = {
        'id': 999,
        'scheduled_date': datetime.now().isoformat(),
        'duration_minutes': 60,
        'interview_type': 'technical',
        'location': 'Ufficio principale - <PERSON><PERSON>',
        'notes': 'Test email recruiting DatPortal',
        'candidate': {
            'id': 1,
            'full_name': '<PERSON> Test',
            'email': '<EMAIL>'
        },
        'job_posting': {
            'id': 1,
            'title': 'Sviluppatore Full Stack (TEST)'
        },
        'interviewer': {
            'id': 1,
            'full_name': 'Daniele Sabetta',
            'email': '<EMAIL>'
        }
    }
    
    try:
        success = recruiting_email_service.send_interview_confirmation(test_interview_data)
        if success:
            print("✅ EMAIL RECRUITING INVIATA!")
            print("📧 Controlla <EMAIL>")
        else:
            print("❌ Errore invio email recruiting")
    except Exception as e:
        print(f"❌ ERRORE: {e}")