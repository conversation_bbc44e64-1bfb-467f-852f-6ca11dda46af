#!/usr/bin/env python3
"""
Script per creare errori di test nel sistema self-healing
"""

import sys
import os

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '../'))

# Carica le variabili d'ambiente
try:
    from dotenv import load_dotenv
    load_dotenv(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env'))
except ImportError:
    try:
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from load_env import load_env
        load_env()
    except ImportError:
        print("⚠️  Attenzione: Impossibile caricare le variabili d'ambiente")

from app import create_app
from services.healing_ai_service import HealingAIService

def create_test_errors():
    """Crea diversi errori di test per popolare il sistema"""
    
    app = create_app()
    
    with app.app_context():
        print("🧪 Creazione errori di test per Self-Healing System")
        print("=" * 60)
        
        healing_service = HealingAIService()
        
        # Lista di errori di test realistici
        test_errors = [
            {
                'error_type': 'javascript_error',
                'message': 'TypeError: Cannot read property \'id\' of undefined',
                'file': 'frontend/src/stores/projects.js',
                'stack': 'TypeError: Cannot read property \'id\' of undefined\n    at useProjectsStore (projects.js:45)\n    at ProjectView.vue:123',
                'url': 'http://localhost:5000/app/projects/123',
                'severity': 'high',
                'automatic': True
            },
            {
                'error_type': 'api_validation_error',
                'message': 'Validation failed: email field is required',
                'file': 'backend/blueprints/api/personnel.py',
                'stack': 'ValidationError in create_user endpoint',
                'url': 'http://localhost:5000/api/personnel/users',
                'severity': 'medium',
                'automatic': True
            },
            {
                'error_type': 'database_connection',
                'message': 'psycopg2.OperationalError: connection timeout',
                'file': 'backend/models.py',
                'stack': 'psycopg2.OperationalError: could not connect to server\n    at SQLAlchemy.create_engine',
                'url': 'http://localhost:5000/api/timesheets',
                'severity': 'critical',
                'automatic': True
            },
            {
                'error_type': 'vue_component_error',
                'message': 'Property \'userData\' was accessed during render but is not defined',
                'file': 'frontend/src/views/personnel/PersonnelProfile.vue',
                'stack': 'ReferenceError: userData is not defined\n    at PersonnelProfile.vue:67',
                'url': 'http://localhost:5000/app/personnel/profile',
                'severity': 'medium',
                'automatic': True
            },
            {
                'error_type': 'network_error',
                'message': 'Failed to fetch: ERR_NETWORK_TIMEOUT',
                'file': 'frontend/src/utils/api.js',
                'stack': 'TypeError: Failed to fetch\n    at apiRequest (api.js:23)',
                'url': 'http://localhost:5000/app/dashboard',
                'severity': 'high',
                'automatic': True
            },
            {
                'error_type': 'authentication_error',
                'message': 'Token expired: JWT signature verification failed',
                'file': 'backend/utils/token_utils.py',
                'stack': 'JWT DecodeError: Signature verification failed',
                'url': 'http://localhost:5000/api/auth/verify',
                'severity': 'high',
                'automatic': True
            },
            {
                'error_type': 'memory_leak',
                'message': 'JavaScript heap out of memory',
                'file': 'frontend/src/views/projects/ProjectView.vue',
                'stack': 'RangeError: Maximum call stack size exceeded',
                'url': 'http://localhost:5000/app/projects/dashboard',
                'severity': 'critical',
                'automatic': True
            },
            {
                'error_type': 'css_loading_error',
                'message': 'Failed to load CSS chunk for component',
                'file': 'frontend/src/views/admin/SelfHealingDashboard.vue',
                'stack': 'ChunkLoadError: Loading CSS chunk failed',
                'url': 'http://localhost:5000/app/admin/self-healing',
                'severity': 'medium',
                'automatic': True
            }
        ]
        
        created_patterns = []
        
        for i, error_data in enumerate(test_errors, 1):
            try:
                print(f"\n{i}. Creando errore: {error_data['error_type']}")
                
                # Simula occorrenze multiple per alcuni errori
                occurrences = 1
                if error_data['severity'] == 'critical':
                    occurrences = 5  # Errori critici si ripetono di più
                elif error_data['severity'] == 'high':
                    occurrences = 3
                elif error_data['severity'] == 'medium':
                    occurrences = 2
                
                pattern = None
                for occurrence in range(occurrences):
                    pattern = healing_service.create_error_pattern(error_data)
                
                created_patterns.append(pattern)
                
                print(f"   ✅ Pattern creato: {pattern.pattern_hash}")
                print(f"   📊 Severità: {pattern.severity}")
                print(f"   🔄 Occorrenze: {pattern.occurrence_count}")
                
            except Exception as e:
                print(f"   ❌ Errore nella creazione: {str(e)}")
        
        # Aggiorna system health con i nuovi errori
        print(f"\n📊 Aggiornamento System Health...")
        try:
            health_record = healing_service.update_system_health()
            if health_record:
                print(f"✅ System Health aggiornato:")
                print(f"   🏥 Health Score: {health_record.health_score}/100")
                print(f"   📈 Errori 24h: {health_record.error_count_24h}")
                print(f"   🚨 Errori critici: {health_record.critical_errors}")
                print(f"   ⚠️ Issues pendenti: {health_record.pending_issues}")
            else:
                print("❌ Errore nell'aggiornamento SystemHealth")
        except Exception as e:
            print(f"❌ Errore system health: {str(e)}")
        
        # Statistiche finali
        print(f"\n📋 Riepilogo:")
        print(f"   🎯 Pattern creati: {len(created_patterns)}")
        
        critical_count = sum(1 for p in created_patterns if p.severity == 'critical')
        high_count = sum(1 for p in created_patterns if p.severity == 'high')
        medium_count = sum(1 for p in created_patterns if p.severity == 'medium')
        
        print(f"   🚨 Critici: {critical_count}")
        print(f"   ⚠️ High: {high_count}")
        print(f"   📝 Medium: {medium_count}")
        
        total_occurrences = sum(p.occurrence_count for p in created_patterns)
        print(f"   🔄 Occorrenze totali: {total_occurrences}")
        
        print(f"\n🎉 Errori di test creati con successo!")
        print(f"💡 Ora puoi visualizzarli su: http://localhost:5000/app/admin/self-healing")
        
        return True

if __name__ == '__main__':
    success = create_test_errors()
    sys.exit(0 if success else 1)