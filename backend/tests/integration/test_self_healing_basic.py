#!/usr/bin/env python3
"""
Test basic per il sistema self-healing
"""

import sys
import os

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '../'))

# Carica le variabili d'ambiente dal file .env
try:
    from dotenv import load_dotenv
    load_dotenv(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env'))
except ImportError:
    try:
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from load_env import load_env
        load_env()
    except ImportError:
        print("⚠️  Attenzione: Impossibile caricare le variabili d'ambiente")

from app import create_app
from extensions import db
from models_split.system import ErrorPattern, SystemHealth, HealingSession
from services.healing_ai_service import HealingAIService

def test_self_healing_models():
    """Test basic dei modelli self-healing"""
    
    app = create_app()
    
    with app.app_context():
        try:
            print("🧪 Testing Self-Healing Models")
            print("=" * 50)
            
            # Test 1: Creazione ErrorPattern
            print("\n📋 Test 1: Creazione ErrorPattern")
            
            # Usa il service per creare un pattern
            healing_service = HealingAIService()
            
            test_error = {
                'type': 'javascript_error',
                'message': 'TypeError: Cannot read property of undefined',
                'file': 'frontend/src/components/test.vue',
                'stack': 'Error at line 42',
                'url': 'http://localhost:3000/test'
            }
            
            pattern = healing_service.create_error_pattern(test_error)
            print(f"✅ Pattern creato: {pattern.pattern_hash}")
            print(f"   - Tipo: {pattern.error_type}")
            print(f"   - Severity: {pattern.severity}")
            print(f"   - Occorrenze: {pattern.occurrence_count}")
            
            # Test 2: SystemHealth
            print("\n📊 Test 2: SystemHealth")
            
            health_record = healing_service.update_system_health()
            if health_record:
                print(f"✅ SystemHealth aggiornato:")
                print(f"   - Health Score: {health_record.health_score}/100")
                print(f"   - Errori 24h: {health_record.error_count_24h}")
                print(f"   - Errori critici: {health_record.critical_errors}")
            else:
                print("❌ Errore nell'aggiornamento SystemHealth")
            
            # Test 3: Verifica pattern duplicato
            print("\n🔍 Test 3: Gestione duplicati")
            
            # Stesso errore dovrebbe aggiornare il pattern esistente
            pattern2 = healing_service.create_error_pattern(test_error)
            print(f"✅ Pattern gestito correttamente:")
            print(f"   - Stesso hash: {pattern.pattern_hash == pattern2.pattern_hash}")
            print(f"   - Occorrenze incrementate: {pattern2.occurrence_count}")
            
            # Test 4: Verifica API endpoints
            print("\n🔌 Test 4: API Endpoints")
            
            with app.test_client() as client:
                # Test endpoint pubblico (senza auth per ora)
                try:
                    print("   - Testing submit-error endpoint...")
                    response = client.post('/api/self-healing/submit-error', 
                                         json=test_error,
                                         headers={'Content-Type': 'application/json'})
                    print(f"   - Response status: {response.status_code}")
                    if response.status_code == 401:
                        print("   ✅ Endpoint protetto correttamente (401 - auth required)")
                    elif response.status_code == 200:
                        print("   ✅ Endpoint funzionante")
                    else:
                        print(f"   ⚠️ Status inaspettato: {response.status_code}")
                        
                except Exception as e:
                    print(f"   ⚠️ Errore test endpoint: {str(e)}")
            
            print("\n✅ Test completati con successo!")
            print("🔧 Sistema Self-Healing funzionante")
            
            return True
            
        except Exception as e:
            print(f"\n❌ Errore durante i test: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == '__main__':
    success = test_self_healing_models()
    sys.exit(0 if success else 1)