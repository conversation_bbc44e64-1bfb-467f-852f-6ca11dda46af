#!/usr/bin/env python3
"""
Script per testare il flusso completo degli errori manuali
Verifica che le segnalazioni utente aggiornino le metriche di system health
"""

import os
import sys
import requests
import json
from datetime import datetime

# Aggiungi il percorso del backend al Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_manual_error_submission():
    """
    Testa l'invio di errori manuali e verifica l'aggiornamento delle metriche
    """
    base_url = "http://localhost:5000"
    
    print("🧪 Testing Manual Error Submission Flow")
    print("=" * 50)
    
    # Step 1: Login (simulato - in realtà serve autenticazione)
    print("📋 Step 1: Preparazione dati errore manuale")
    
    manual_error_data = {
        "error_type": "ui_bug",
        "message": "Test manual error submission - buttons not working in dashboard",
        "severity": "medium",
        "error_context": {
            "steps_to_reproduce": [
                "1. Open dashboard page",
                "2. Click on 'Export Data' button", 
                "3. Nothing happens"
            ],
            "expected_behavior": "Should open export modal",
            "actual_behavior": "Button appears frozen, no response",
            "url": "http://localhost:5000/app/dashboard",
            "timestamp": datetime.now().isoformat(),
            "user_agent": "Mozilla/5.0 (Test Browser)"
        },
        "automatic": False,
        "manual_report": True,
        "user_reported": True
    }
    
    print(f"✅ Error data prepared: {manual_error_data['error_type']} - {manual_error_data['severity']}")
    
    # Step 2: Get current health metrics (before)
    print("\n📊 Step 2: Getting current system health (BEFORE)")
    try:
        dashboard_response = requests.get(f"{base_url}/api/self-healing/dashboard")
        if dashboard_response.status_code == 200:
            before_data = dashboard_response.json().get('data', {})
            before_health = before_data.get('system_health', {})
            print(f"   Current Health Score: {before_health.get('current_score', 'N/A')}")
            print(f"   Error Count 24h: {before_health.get('error_count_24h', 'N/A')}")
            print(f"   Critical Errors: {before_health.get('critical_errors', 'N/A')}")
        else:
            print(f"   ❌ Failed to get dashboard: {dashboard_response.status_code}")
            before_health = {}
    except Exception as e:
        print(f"   ⚠️ Could not reach dashboard endpoint: {e}")
        before_health = {}
    
    # Step 3: Submit manual error
    print("\n🚀 Step 3: Submitting manual error")
    try:
        submit_response = requests.post(
            f"{base_url}/api/self-healing/submit-error",
            json=manual_error_data,
            headers={"Content-Type": "application/json"}
        )
        
        if submit_response.status_code == 200:
            submit_result = submit_response.json()
            print(f"   ✅ Error submitted successfully!")
            print(f"   Pattern ID: {submit_result.get('data', {}).get('pattern_id', 'N/A')}")
            print(f"   Pattern Hash: {submit_result.get('data', {}).get('pattern_hash', 'N/A')}")
            print(f"   Occurrence Count: {submit_result.get('data', {}).get('occurrence_count', 'N/A')}")
            print(f"   Health Updated: {submit_result.get('data', {}).get('health_updated', False)}")
            print(f"   Message: {submit_result.get('message', 'N/A')}")
        else:
            print(f"   ❌ Error submission failed: {submit_response.status_code}")
            print(f"   Response: {submit_response.text}")
            return
            
    except Exception as e:
        print(f"   ❌ Error during submission: {e}")
        return
    
    # Step 4: Get updated health metrics (after)
    print("\n📈 Step 4: Getting updated system health (AFTER)")
    try:
        after_response = requests.get(f"{base_url}/api/self-healing/dashboard")
        if after_response.status_code == 200:
            after_data = after_response.json().get('data', {})
            after_health = after_data.get('system_health', {})
            print(f"   Updated Health Score: {after_health.get('current_score', 'N/A')}")
            print(f"   Updated Error Count 24h: {after_health.get('error_count_24h', 'N/A')}")
            print(f"   Updated Critical Errors: {after_health.get('critical_errors', 'N/A')}")
            
            # Compare before/after
            if before_health and after_health:
                print("\n🔍 Step 5: Comparison")
                before_score = before_health.get('current_score', 0)
                after_score = after_health.get('current_score', 0)
                before_errors = before_health.get('error_count_24h', 0) 
                after_errors = after_health.get('error_count_24h', 0)
                
                print(f"   Health Score: {before_score} → {after_score} (Δ {after_score - before_score})")
                print(f"   Error Count: {before_errors} → {after_errors} (Δ +{after_errors - before_errors})")
                
                if after_errors > before_errors:
                    print("   ✅ SUCCESS: Manual error increased error count!")
                if after_score != before_score:
                    print("   ✅ SUCCESS: Manual error affected health score!")
                    
        else:
            print(f"   ❌ Failed to get updated dashboard: {after_response.status_code}")
            
    except Exception as e:
        print(f"   ⚠️ Could not reach dashboard endpoint: {e}")
    
    print("\n✨ Test completed!")
    print("\nℹ️  Note: This test requires the Flask backend to be running.")
    print("   Start with: cd backend && python app.py")

if __name__ == "__main__":
    test_manual_error_submission()