#!/usr/bin/env python3

"""Test script per FattureInCloudService"""

import sys
import os
from dotenv import load_dotenv

# Carica variabili d'ambiente
load_dotenv('/Users/<USER>/Code/datportal/.env')

# Aggiungi il path per gli import
sys.path.append('/Users/<USER>/Code/datportal/backend')

import fattureincloud_python_sdk
from fattureincloud_python_sdk import CompaniesApi, ClientsApi, ApiException

def test_service():
    print("🧪 Test FattureInCloud SDK Diretto")
    print("=" * 50)
    
    # Leggi credenziali da env
    access_token = os.getenv('FIC_ACCESS_TOKEN')
    company_id = int(os.getenv('FIC_APP_ID')) if os.getenv('FIC_APP_ID') else None
    
    print(f"✅ Access Token: {'CONFIGURATO' if access_token else '❌ MANCANTE'}")
    print(f"✅ Company ID: {company_id if company_id else '❌ MANCANTE'}")
    
    if not access_token or not company_id:
        print("❌ Configurazione incompleta. Controlla le variabili d'ambiente:")
        print("   - FIC_ACCESS_TOKEN")
        print("   - FIC_APP_ID")
        return
    
    # Inizializza SDK
    configuration = fattureincloud_python_sdk.Configuration()
    configuration.access_token = access_token
    
    with fattureincloud_python_sdk.ApiClient(configuration) as api_client:
        
        # Test connessione
        print("🔗 Test connessione...")
        try:
            companies_api = CompaniesApi(api_client)
            company_info = companies_api.get_company_info(company_id)
            
            if company_info and company_info.data:
                print("✅ Connessione riuscita!")
                print(f"   Azienda: {company_info.data.name}")
                print(f"   P.IVA: {getattr(company_info.data, 'vat_number', 'N/A')}")
                print(f"   Paese: {getattr(company_info.data, 'country', 'N/A')}")
                print(f"   Piano: {getattr(company_info.data, 'plan_name', 'N/A')}")
            else:
                print("❌ Risposta vuota dal server")
                return
                
        except ApiException as e:
            print(f"❌ Errore API connessione: {e.reason}")
            return
        except Exception as e:
            print(f"❌ Errore connessione: {str(e)}")
            return
        
        print()
        
        # Test lista clienti
        print("👥 Test lista clienti...")
        try:
            clients_api = ClientsApi(api_client)
            response = clients_api.list_clients(company_id, q='', page=1, per_page=5)
            
            if response and response.data:
                clients = response.data
                print(f"✅ Trovati {len(clients)} clienti:")
                
                for i, client in enumerate(clients, 1):
                    print(f"   {i}. {client.name} (ID: {client.id})")
                    if hasattr(client, 'vat_number') and client.vat_number:
                        print(f"      P.IVA: {client.vat_number}")
                    if hasattr(client, 'country') and client.country:
                        print(f"      Paese: {client.country}")
                    print()
            else:
                print("✅ Nessun cliente trovato")
                
        except ApiException as e:
            print(f"❌ Errore API clienti: {e.reason}")
        except Exception as e:
            print(f"❌ Errore lista clienti: {str(e)}")

if __name__ == "__main__":
    test_service()