#!/usr/bin/env python3
"""
Test completo per sistema self-healing.
Testa API endpoints e integrazione end-to-end.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set environment variables for testing
os.environ['DATABASE_URL'] = 'sqlite:///:memory:'

from app import create_app
from models import User
from extensions import db
import json
from datetime import datetime

def test_self_healing_complete():
    """Test completo sistema self-healing"""
    print("🚀 Test completo sistema self-healing...")
    print("=" * 60)
    
    app = create_app()
    test_results = []
    
    def log_test(test_name, success, message="", data=None):
        """Log risultato test"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        if data and not success:
            print(f"     Data: {data}")
        test_results.append({
            'test': test_name,
            'success': success,
            'message': message,
            'data': data
        })
    
    with app.app_context():
        # Setup: trova utente per test
        user = User.query.first()
        if not user:
            log_test("Setup", False, "Nessun utente trovato per i test")
            return False
        
        log_test("Setup", True, f"Utilizzando utente: {user.username}")
        
        # Test client
        with app.test_client() as client:
            # Setup session per autenticazione
            with client.session_transaction() as sess:
                sess['_user_id'] = str(user.id)
                sess['_fresh'] = True
            
            # Test 1: Submit Error (con error_type)
            try:
                error_data = {
                    "error_type": "javascript_error",
                    "message": "TypeError: Cannot read property 'length' of undefined",
                    "file": "/src/components/TestComponent.vue",
                    "stack": "TypeError: Cannot read property 'length' of undefined\\n    at TestComponent.vue:42:15",
                    "url": "http://localhost:3000/dashboard",
                    "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)",
                    "timestamp": datetime.now().isoformat(),
                    "automatic": True
                }
                
                response = client.post('/api/self-healing/submit-error', 
                                     json=error_data, 
                                     content_type='application/json')
                
                if response.status_code == 200:
                    data = json.loads(response.data)
                    if data.get('success'):
                        pattern_id = data.get('data', {}).get('pattern_id')
                        log_test("Submit Error", True, "Errore inviato con successo", 
                               f"Pattern ID: {pattern_id}")
                    else:
                        log_test("Submit Error", False, f"API error: {data.get('message')}")
                        pattern_id = None
                else:
                    log_test("Submit Error", False, f"HTTP {response.status_code}: {response.data}")
                    pattern_id = None
                    
            except Exception as e:
                log_test("Submit Error", False, f"Exception: {str(e)}")
                pattern_id = None
            
            # Test 2: Dashboard
            try:
                response = client.get('/api/self-healing/dashboard')
                
                if response.status_code == 200:
                    data = json.loads(response.data)
                    if data.get('success'):
                        dashboard_data = data.get('data', {})
                        required_keys = ['system_health', 'health_trend', 'critical_patterns', 
                                       'recent_sessions', 'quick_stats']
                        missing_keys = [key for key in required_keys if key not in dashboard_data]
                        
                        if not missing_keys:
                            log_test("Dashboard", True, "Dashboard caricato correttamente")
                        else:
                            log_test("Dashboard", False, f"Chiavi mancanti: {missing_keys}")
                    else:
                        log_test("Dashboard", False, f"API error: {data.get('message')}")
                else:
                    log_test("Dashboard", False, f"HTTP {response.status_code}: {response.data}")
                    
            except Exception as e:
                log_test("Dashboard", False, f"Exception: {str(e)}")
            
            # Test 3: Patterns List
            try:
                response = client.get('/api/self-healing/patterns?page=1&per_page=10')
                
                if response.status_code == 200:
                    data = json.loads(response.data)
                    if data.get('success'):
                        patterns_data = data.get('data', {})
                        required_keys = ['patterns', 'pagination', 'filter_options']
                        missing_keys = [key for key in required_keys if key not in patterns_data]
                        
                        if not missing_keys:
                            patterns = patterns_data.get('patterns', [])
                            log_test("Patterns List", True, f"Trovati {len(patterns)} pattern")
                        else:
                            log_test("Patterns List", False, f"Chiavi mancanti: {missing_keys}")
                    else:
                        log_test("Patterns List", False, f"API error: {data.get('message')}")
                else:
                    log_test("Patterns List", False, f"HTTP {response.status_code}: {response.data}")
                    
            except Exception as e:
                log_test("Patterns List", False, f"Exception: {str(e)}")
            
            # Test 4: Sessions List
            try:
                response = client.get('/api/self-healing/sessions?page=1&per_page=10')
                
                if response.status_code == 200:
                    data = json.loads(response.data)
                    if data.get('success'):
                        sessions_data = data.get('data', {})
                        required_keys = ['sessions', 'pagination']
                        missing_keys = [key for key in required_keys if key not in sessions_data]
                        
                        if not missing_keys:
                            sessions = sessions_data.get('sessions', [])
                            log_test("Sessions List", True, f"Trovate {len(sessions)} sessioni")
                        else:
                            log_test("Sessions List", False, f"Chiavi mancanti: {missing_keys}")
                    else:
                        log_test("Sessions List", False, f"API error: {data.get('message')}")
                else:
                    log_test("Sessions List", False, f"HTTP {response.status_code}: {response.data}")
                    
            except Exception as e:
                log_test("Sessions List", False, f"Exception: {str(e)}")
            
            # Test 5: Pattern Filters
            try:
                response = client.get('/api/self-healing/patterns?severity=critical')
                
                if response.status_code == 200:
                    data = json.loads(response.data)
                    if data.get('success'):
                        log_test("Pattern Filters", True, "Filtri pattern funzionanti")
                    else:
                        log_test("Pattern Filters", False, f"API error: {data.get('message')}")
                else:
                    log_test("Pattern Filters", False, f"HTTP {response.status_code}: {response.data}")
                    
            except Exception as e:
                log_test("Pattern Filters", False, f"Exception: {str(e)}")
            
            # Test 6: AI Analysis (se abbiamo pattern_id)
            if pattern_id:
                try:
                    response = client.post(f'/api/self-healing/patterns/{pattern_id}/analyze')
                    
                    if response.status_code == 200:
                        data = json.loads(response.data)
                        if data.get('success'):
                            log_test("AI Analysis", True, "Analisi AI completata")
                        else:
                            log_test("AI Analysis", False, f"API error: {data.get('message')}")
                    else:
                        log_test("AI Analysis", False, f"HTTP {response.status_code}: {response.data}")
                        
                except Exception as e:
                    log_test("AI Analysis", False, f"Exception: {str(e)}")
            else:
                log_test("AI Analysis", False, "Pattern ID non disponibile")
            
            # Test 7: Generate Claude Prompt (se abbiamo pattern_id)
            if pattern_id:
                try:
                    response = client.post(f'/api/self-healing/patterns/{pattern_id}/generate-prompt')
                    
                    if response.status_code == 200:
                        data = json.loads(response.data)
                        if data.get('success'):
                            log_test("Generate Prompt", True, "Prompt Claude Code generato")
                        else:
                            log_test("Generate Prompt", False, f"API error: {data.get('message')}")
                    else:
                        log_test("Generate Prompt", False, f"HTTP {response.status_code}: {response.data}")
                        
                except Exception as e:
                    log_test("Generate Prompt", False, f"Exception: {str(e)}")
            else:
                log_test("Generate Prompt", False, "Pattern ID non disponibile")
    
    # Risultati finali
    print("\n" + "=" * 60)
    print("📊 RISULTATI TEST SELF-HEALING API")
    print("=" * 60)
    
    total_tests = len(test_results)
    passed_tests = sum(1 for result in test_results if result['success'])
    failed_tests = total_tests - passed_tests
    
    print(f"Total tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {failed_tests}")
    print(f"Success rate: {(passed_tests/total_tests)*100:.1f}%")
    
    if failed_tests > 0:
        print("\n❌ TESTS FALLITI:")
        for result in test_results:
            if not result['success']:
                print(f"  • {result['test']}: {result['message']}")
    
    return {
        'total': total_tests,
        'passed': passed_tests,
        'failed': failed_tests,
        'success_rate': (passed_tests/total_tests)*100,
        'details': test_results
    }

if __name__ == "__main__":
    results = test_self_healing_complete()
    
    # Salva risultati
    with open('self_healing_api_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Risultati salvati in: self_healing_api_test_results.json")
    
    # Exit code basato sui risultati
    sys.exit(0 if results['failed'] == 0 else 1)