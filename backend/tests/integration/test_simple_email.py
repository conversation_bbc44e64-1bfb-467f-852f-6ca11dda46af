#!/usr/bin/env python3
import os
import sys
from flask import Flask
from flask_mail import Mail, Message

# Simple email test
app = Flask(__name__)
app.config['MAIL_SERVER'] = 'mail.tophost.it'
app.config['MAIL_PORT'] = 587
app.config['MAIL_USE_TLS'] = True
app.config['MAIL_USERNAME'] = 'danielesabetta.it'  # MAILBOX name
app.config['MAIL_PASSWORD'] = 'sabetta25..'
app.config['MAIL_DEFAULT_SENDER'] = '<EMAIL>'

mail = Mail(app)

with app.app_context():
    msg = Message(
        'Test Email DatPortal',
        sender='<EMAIL>',
        recipients=['<EMAIL>']
    )
    msg.body = 'Test email dal sistema recruiting DatPortal. Se ricevi questa email, tutto funziona!'
    
    try:
        mail.send(msg)
        print("✅ EMAIL INVIATA!")
    except Exception as e:
        print(f"❌ ERRORE: {e}")