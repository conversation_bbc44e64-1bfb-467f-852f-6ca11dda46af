#!/usr/bin/env python3
"""
Test script per verificare configurazione email SMTP Tophost.
Testa sia l'invio di email semplici che il sistema recruiting completo.
"""

import os
import sys
import pytest
from datetime import datetime

# Aggiungi path del backend
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from flask import Flask
from extensions import mail
from config import Config
from services.email_service import recruiting_email_service

class TestEmailTophost:
    """Test suite per configurazione email SMTP Tophost."""
    
    @pytest.fixture
    def app(self):
        """Crea app Flask per testing."""
        app = Flask(__name__)
        app.config.from_object(Config)
        
        # Override con configurazione Tophost se presente
        mail_config = {
            'MAIL_SERVER': os.getenv('MAIL_SERVER', 'mail.tophost.it'),
            'MAIL_PORT': int(os.getenv('MAIL_PORT', 587)),
            'MAIL_USE_TLS': os.getenv('MAIL_USE_TLS', 'True').lower() == 'true',
            'MAIL_USE_SSL': os.getenv('MAIL_USE_SSL', 'False').lower() == 'true',
            'MAIL_USERNAME': os.getenv('MAIL_USERNAME'),
            'MAIL_PASSWORD': os.getenv('MAIL_PASSWORD'),
            'MAIL_DEFAULT_SENDER': os.getenv('MAIL_DEFAULT_SENDER'),
            'TESTING': True
        }
        
        app.config.update(mail_config)
        
        # Inizializza estensioni
        mail.init_app(app)
        
        return app
    
    def test_email_configuration(self, app):
        """Verifica configurazione email."""
        with app.app_context():
            print("\n⚙️  Checking email configuration...")
            
            config_items = [
                ('MAIL_SERVER', app.config.get('MAIL_SERVER')),
                ('MAIL_PORT', app.config.get('MAIL_PORT')),
                ('MAIL_USE_TLS', app.config.get('MAIL_USE_TLS')),
                ('MAIL_USE_SSL', app.config.get('MAIL_USE_SSL')),
                ('MAIL_USERNAME', app.config.get('MAIL_USERNAME')),
                ('MAIL_PASSWORD', '***' if app.config.get('MAIL_PASSWORD') else None),
                ('MAIL_DEFAULT_SENDER', app.config.get('MAIL_DEFAULT_SENDER'))
            ]
            
            all_configured = True
            for name, value in config_items:
                status = "✅" if value else "❌"
                print(f"  {status} {name}: {value or 'NOT SET'}")
                if not value and name != 'MAIL_PASSWORD':  # Password può essere vuota per alcuni setup
                    all_configured = False
            
            assert all_configured, "Email configuration incomplete"
    
    def test_smtp_connection(self, app):
        """Test connessione SMTP di base."""
        with app.app_context():
            print("\n🔌 Testing SMTP connection...")
            try:
                with mail.connect() as conn:
                    print("✅ SMTP connection successful!")
                    assert True
            except Exception as e:
                print(f"❌ SMTP connection failed: {e}")
                pytest.fail(f"SMTP connection failed: {e}")
    
    def test_recruiting_email_templates(self, app):
        """Test template email recruiting."""
        with app.app_context():
            print("\n🏢 Testing recruiting email templates...")
            
            # Mock data per test
            class MockCandidate:
                def __init__(self):
                    self.full_name = "Mario Rossi"
                    self.email = "<EMAIL>"
                    self.phone = "+39 ************"
                    self.years_of_experience = 5
                    self.cv_path = "/path/to/cv.pdf"
            
            class MockJobPosting:
                def __init__(self):
                    self.title = "Senior Developer"
                    self.description = "Sviluppatore senior per progetti innovativi"
            
            class MockApplication:
                def __init__(self):
                    self.candidate = MockCandidate()
                    self.job_posting = MockJobPosting()
                    self.applied_date = datetime.now()
                    self.status = 'pending'
                    self.id = 123
            
            mock_app = MockApplication()
            
            # Test template generation (non inviamo email reale)
            try:
                # Verifica che il servizio sia inizializzato
                assert recruiting_email_service is not None
                
                # Test mock dei metodi (senza invio email reale)
                print("📧 Testing application confirmation template...")
                result = recruiting_email_service.send_application_confirmation(mock_app)
                print(f"   Application confirmation: {'✅' if result else '❌'}")
                
                print("📢 Testing HR notification template...")
                result = recruiting_email_service.send_new_application_notification(mock_app)
                print(f"   HR notification: {'✅' if result else '❌'}")
                
                print("✅ Recruiting email templates verified!")
                
            except Exception as e:
                print(f"❌ Recruiting email template test failed: {e}")
                pytest.fail(f"Template test failed: {e}")
    
    def test_manual_email_send(self, app):
        """Test invio email manuale (solo se richiesto)."""
        with app.app_context():
            # Questo test è interattivo - skip automaticamente nei test automatizzati
            test_email = os.getenv('TEST_EMAIL_RECIPIENT')
            
            if not test_email:
                print("\n📧 Skipping manual email test (set TEST_EMAIL_RECIPIENT to enable)")
                pytest.skip("Manual email test skipped - set TEST_EMAIL_RECIPIENT environment variable")
                return
            
            print(f"\n📧 Sending test email to {test_email}...")
            
            try:
                from flask_mail import Message
                
                msg = Message(
                    subject="Test DatPortal Email - Tophost SMTP",
                    recipients=[test_email],
                    body=f"""
Test email dal sistema DatPortal.

Questo è un test della configurazione SMTP Tophost.

Se ricevi questa email, la configurazione funziona correttamente!

Configurazione utilizzata:
- Server: {app.config.get('MAIL_SERVER')}
- Porta: {app.config.get('MAIL_PORT')}
- TLS: {app.config.get('MAIL_USE_TLS')}
- SSL: {app.config.get('MAIL_USE_SSL')}

Timestamp: {datetime.now()}

DatPortal Team
                    """,
                    html=f"""
                    <html>
                    <body>
                        <h2>🚀 Test DatPortal Email</h2>
                        <p>Questo è un test della configurazione <strong>SMTP Tophost</strong>.</p>
                        
                        <div style="background: #f0f9ff; padding: 15px; border-radius: 8px; margin: 20px 0;">
                            <h3>✅ Configurazione SMTP</h3>
                            <ul>
                                <li>Server: {app.config.get('MAIL_SERVER')}</li>
                                <li>Porta: {app.config.get('MAIL_PORT')}</li>
                                <li>TLS: {app.config.get('MAIL_USE_TLS')}</li>
                                <li>SSL: {app.config.get('MAIL_USE_SSL')}</li>
                            </ul>
                        </div>
                        
                        <p>Se ricevi questa email, la configurazione funziona correttamente! 🎉</p>
                        
                        <small>Timestamp: {datetime.now()}</small>
                        
                        <br><br>
                        <strong>DatPortal Team</strong>
                    </body>
                    </html>
                    """
                )
                
                mail.send(msg)
                print("✅ Test email sent successfully!")
                
            except Exception as e:
                print(f"❌ Failed to send test email: {e}")
                pytest.fail(f"Email send failed: {e}")


def test_email_system_standalone():
    """Test standalone per eseguire tutti i controlli email."""
    print("=" * 60)
    print("🚀 DatPortal Email System Test - Tophost SMTP")
    print("=" * 60)
    
    # Carica variabili ambiente da .env se esiste
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("📁 Loaded .env file")
    except ImportError:
        print("📁 python-dotenv not available, using system environment")
    
    # Crea istanza test
    test_instance = TestEmailTophost()
    
    # Crea app
    app = test_instance.app()
    
    try:
        # Esegui tutti i test
        test_instance.test_email_configuration(app)
        test_instance.test_smtp_connection(app)
        test_instance.test_recruiting_email_templates(app)
        
        # Test email manuale solo se specificato
        if os.getenv('TEST_EMAIL_RECIPIENT'):
            test_instance.test_manual_email_send(app)
        
        print("\n" + "=" * 60)
        print("🎉 All email tests passed!")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        raise


if __name__ == "__main__":
    # Permette di eseguire il test direttamente
    test_email_system_standalone()