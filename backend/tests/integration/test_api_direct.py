#!/usr/bin/env python3

import sys
import os
import json

# Add backend to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

from app import create_app

def test_api_direct():
    app = create_app()
    
    with app.test_client() as client:
        # Test 1: API without authentication (should fail)
        print("=== Test 1: API without authentication ===")
        response = client.get('/api/feature-flags')
        print(f"Status: {response.status_code}")
        print(f"Response: {response.get_json()}")
        
        # Test 2: Try with enabled_only=false
        print("\n=== Test 2: API with enabled_only=false ===")
        response = client.get('/api/feature-flags?enabled_only=false')
        print(f"Status: {response.status_code}")
        print(f"Response: {response.get_json()}")
        
        # Test 3: Try with enabled_only=true
        print("\n=== Test 3: API with enabled_only=true ===")
        response = client.get('/api/feature-flags?enabled_only=true')
        print(f"Status: {response.status_code}")
        print(f"Response: {response.get_json()}")

if __name__ == '__main__':
    test_api_direct()