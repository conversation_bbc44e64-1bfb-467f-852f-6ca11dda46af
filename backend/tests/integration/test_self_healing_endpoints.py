#!/usr/bin/env python3
"""
Test script completo per tutti gli endpoint self-healing.
Testa la funzionalità end-to-end del sistema di auto-guarigione.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import User
from extensions import db
import json
import time
from datetime import datetime

class SelfHealingTester:
    def __init__(self):
        self.app = create_app()
        self.test_results = []
        self.client = None
        self.user = None
        
    def log_test(self, test_name, success, message="", data=None):
        """Log del risultato di un test"""
        result = {
            'test': test_name,
            'success': success,
            'message': message,
            'timestamp': datetime.now().isoformat()
        }
        if data:
            result['data'] = data
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        if data and not success:
            print(f"     Data: {data}")
    
    def setup_test_client(self):
        """Setup test client con autenticazione"""
        try:
            with self.app.app_context():
                # Find a test user
                self.user = User.query.first()
                if not self.user:
                    self.log_test("Setup Test Client", False, "No users found for testing")
                    return False
                
                self.log_test("Setup Test Client", True, f"Using test user: {self.user.username}")
                
                # Create test client
                self.client = self.app.test_client()
                return True
                
        except Exception as e:
            self.log_test("Setup Test Client", False, f"Errore: {str(e)}")
            return False
    
    def test_submit_error_endpoint(self):
        """Test endpoint POST /api/self-healing/submit-error"""
        try:
            with self.app.app_context():
                # Setup session with user authentication
                with self.client.session_transaction() as sess:
                    sess['_user_id'] = str(self.user.id)
                    sess['_fresh'] = True
                
                # Test con error_type corretto
                error_data = {
                    "error_type": "javascript_error",
                    "message": "TypeError: Cannot read property 'length' of undefined",
                    "file": "/src/components/TestComponent.vue",
                    "stack": "TypeError: Cannot read property 'length' of undefined\\n    at TestComponent.vue:42:15",
                    "url": "http://localhost:3000/dashboard",
                    "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)",
                    "timestamp": datetime.now().isoformat(),
                    "automatic": True
                }
                
                response = self.client.post('/api/self-healing/submit-error', 
                                          json=error_data, 
                                          content_type='application/json')
                
                if response.status_code == 200:
                    data = json.loads(response.data)
                    if data.get('success'):
                        self.log_test("Submit Error", True, "Errore inviato con successo", data.get('data'))
                        return data.get('data', {}).get('pattern_id')
                    else:
                        self.log_test("Submit Error", False, f"Errore API: {data.get('message')}")
                        return None
                else:
                    self.log_test("Submit Error", False, f"HTTP {response.status_code}: {response.data}")
                    return None
                    
        except Exception as e:
            self.log_test("Submit Error", False, f"Errore: {str(e)}")
            return None
    
    def test_dashboard_endpoint(self):
        """Test endpoint GET /api/self-healing/dashboard"""
        try:
            with self.app.app_context():
                # Setup session with user authentication
                with self.client.session_transaction() as sess:
                    sess['_user_id'] = str(self.user.id)
                    sess['_fresh'] = True
                
                response = self.client.get('/api/self-healing/dashboard')
                
                if response.status_code == 200:
                    data = json.loads(response.data)
                    if data.get('success'):
                        dashboard_data = data.get('data', {})
                        required_keys = ['system_health', 'health_trend', 'critical_patterns', 'recent_sessions', 'quick_stats']
                        missing_keys = [key for key in required_keys if key not in dashboard_data]
                        
                        if not missing_keys:
                            self.log_test("Dashboard Data", True, "Dashboard caricato con successo")
                            return dashboard_data
                        else:
                            self.log_test("Dashboard Data", False, f"Chiavi mancanti: {missing_keys}")
                            return None
                    else:
                        self.log_test("Dashboard Data", False, f"Errore API: {data.get('message')}")
                        return None
                else:
                    self.log_test("Dashboard Data", False, f"HTTP {response.status_code}: {response.data}")
                    return None
                    
        except Exception as e:
            self.log_test("Dashboard Data", False, f"Errore: {str(e)}")
            return None
    
    def test_patterns_endpoint(self):
        """Test endpoint GET /api/self-healing/patterns"""
        try:
            response = self.session.get(f"{BASE_URL}/api/self-healing/patterns?page=1&per_page=10")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    patterns_data = data.get('data', {})
                    required_keys = ['patterns', 'pagination', 'filter_options']
                    missing_keys = [key for key in required_keys if key not in patterns_data]
                    
                    if not missing_keys:
                        patterns = patterns_data.get('patterns', [])
                        self.log_test("Patterns List", True, f"Trovati {len(patterns)} pattern")
                        return patterns_data
                    else:
                        self.log_test("Patterns List", False, f"Chiavi mancanti: {missing_keys}")
                        return None
                else:
                    self.log_test("Patterns List", False, f"Errore API: {data.get('message')}")
                    return None
            else:
                self.log_test("Patterns List", False, f"HTTP {response.status_code}: {response.text}")
                return None
                
        except Exception as e:
            self.log_test("Patterns List", False, f"Errore: {str(e)}")
            return None
    
    def test_analyze_pattern_endpoint(self, pattern_id):
        """Test endpoint POST /api/self-healing/patterns/{id}/analyze"""
        try:
            if not pattern_id:
                self.log_test("Analyze Pattern", False, "Pattern ID non disponibile")
                return None
                
            response = self.session.post(f"{BASE_URL}/api/self-healing/patterns/{pattern_id}/analyze")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    analysis_data = data.get('data', {})
                    self.log_test("Analyze Pattern", True, "Analisi AI completata", analysis_data)
                    return analysis_data
                else:
                    self.log_test("Analyze Pattern", False, f"Errore API: {data.get('message')}")
                    return None
            else:
                self.log_test("Analyze Pattern", False, f"HTTP {response.status_code}: {response.text}")
                return None
                
        except Exception as e:
            self.log_test("Analyze Pattern", False, f"Errore: {str(e)}")
            return None
    
    def test_generate_prompt_endpoint(self, pattern_id):
        """Test endpoint POST /api/self-healing/patterns/{id}/generate-prompt"""
        try:
            if not pattern_id:
                self.log_test("Generate Prompt", False, "Pattern ID non disponibile")
                return None
                
            response = self.session.post(f"{BASE_URL}/api/self-healing/patterns/{pattern_id}/generate-prompt")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    prompt_data = data.get('data', {})
                    self.log_test("Generate Prompt", True, "Prompt Claude Code generato", prompt_data)
                    return prompt_data
                else:
                    self.log_test("Generate Prompt", False, f"Errore API: {data.get('message')}")
                    return None
            else:
                self.log_test("Generate Prompt", False, f"HTTP {response.status_code}: {response.text}")
                return None
                
        except Exception as e:
            self.log_test("Generate Prompt", False, f"Errore: {str(e)}")
            return None
    
    def test_sessions_endpoint(self):
        """Test endpoint GET /api/self-healing/sessions"""
        try:
            response = self.session.get(f"{BASE_URL}/api/self-healing/sessions?page=1&per_page=10")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    sessions_data = data.get('data', {})
                    required_keys = ['sessions', 'pagination']
                    missing_keys = [key for key in required_keys if key not in sessions_data]
                    
                    if not missing_keys:
                        sessions = sessions_data.get('sessions', [])
                        self.log_test("Sessions List", True, f"Trovate {len(sessions)} sessioni")
                        return sessions_data
                    else:
                        self.log_test("Sessions List", False, f"Chiavi mancanti: {missing_keys}")
                        return None
                else:
                    self.log_test("Sessions List", False, f"Errore API: {data.get('message')}")
                    return None
            else:
                self.log_test("Sessions List", False, f"HTTP {response.status_code}: {response.text}")
                return None
                
        except Exception as e:
            self.log_test("Sessions List", False, f"Errore: {str(e)}")
            return None
    
    def test_pattern_filters(self):
        """Test filtri per patterns endpoint"""
        try:
            # Test filtro per severity
            response = self.session.get(f"{BASE_URL}/api/self-healing/patterns?severity=critical")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log_test("Pattern Filters", True, "Filtri pattern funzionanti")
                    return True
                else:
                    self.log_test("Pattern Filters", False, f"Errore API: {data.get('message')}")
                    return False
            else:
                self.log_test("Pattern Filters", False, f"HTTP {response.status_code}: {response.text}")
                return False
                
        except Exception as e:
            self.log_test("Pattern Filters", False, f"Errore: {str(e)}")
            return False
    
    def run_all_tests(self):
        """Esegue tutti i test in sequenza"""
        print("🚀 Avvio test completo sistema self-healing...")
        print("=" * 60)
        
        # 1. Setup test client
        if not self.setup_test_client():
            print("❌ Impossibile procedere senza setup test client")
            return
        
        # 2. Test submit error
        pattern_id = self.test_submit_error_endpoint()
        
        # 3. Test dashboard
        dashboard_data = self.test_dashboard_endpoint()
        
        # 4. Test patterns list
        patterns_data = self.test_patterns_endpoint()
        
        # 5. Test pattern filters
        self.test_pattern_filters()
        
        # 6. Test analyze pattern (se abbiamo pattern_id)
        analysis_data = self.test_analyze_pattern_endpoint(pattern_id)
        
        # 7. Test generate prompt (se abbiamo pattern_id)
        prompt_data = self.test_generate_prompt_endpoint(pattern_id)
        
        # 8. Test sessions
        sessions_data = self.test_sessions_endpoint()
        
        # Risultati finali
        print("\n" + "=" * 60)
        print("📊 RISULTATI TEST SELF-HEALING")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ TESTS FALLITI:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  • {result['test']}: {result['message']}")
        
        print("\n📋 DETTAGLI TEST COMPLETATI:")
        for result in self.test_results:
            status = "✅" if result['success'] else "❌"
            print(f"  {status} {result['test']}")
        
        return {
            'total': total_tests,
            'passed': passed_tests,
            'failed': failed_tests,
            'success_rate': (passed_tests/total_tests)*100,
            'details': self.test_results
        }

if __name__ == "__main__":
    tester = SelfHealingTester()
    results = tester.run_all_tests()
    
    # Salva risultati in file
    with open('self_healing_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Risultati salvati in: self_healing_test_results.json")