#!/usr/bin/env python3

import sys
import os
import json

# Add backend to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

from app import create_app
from models import User

def test_full_flow():
    app = create_app()
    
    with app.test_client() as client:
        with app.app_context():
            # Get an admin user to test with
            admin_user = User.query.filter_by(role='admin').first()
            if not admin_user:
                print("No admin user found in database")
                return
            
            print(f"Testing with admin user: {admin_user.username}")
            
            # Test 1: Login
            print("\n=== Test 1: Login ===")
            login_response = client.post('/api/auth/login', 
                json={'username': admin_user.username, 'password': 'admin123'})
            print(f"Login Status: {login_response.status_code}")
            login_data = login_response.get_json()
            print(f"Login Response: {login_data}")
            
            if login_response.status_code != 200 or not login_data.get('success'):
                print("Login failed, testing with session simulation")
                
                # Simulate logged-in session
                with client.session_transaction() as sess:
                    sess['user_id'] = admin_user.id
                    sess['_fresh'] = True
                
                print("Session simulated for user:", admin_user.username)
            
            # Test 2: Check auth
            print("\n=== Test 2: Check Auth ===")
            auth_response = client.get('/api/auth/me')
            print(f"Auth Status: {auth_response.status_code}")
            print(f"Auth Response: {auth_response.get_json()}")
            
            # Test 3: Get feature flags (all)
            print("\n=== Test 3: Get Feature Flags (all) ===")
            flags_response = client.get('/api/feature-flags?enabled_only=false')
            print(f"Flags Status: {flags_response.status_code}")
            flags_data = flags_response.get_json()
            print(f"Flags Response: {flags_data}")
            
            if flags_data and flags_data.get('success'):
                flags = flags_data.get('data', {}).get('flags', {})
                print(f"Found {len(flags)} feature flags")
                for key, value in flags.items():
                    print(f"  - {key}: {value}")
            
            # Test 4: Get feature flags (enabled only)
            print("\n=== Test 4: Get Feature Flags (enabled only) ===")
            enabled_response = client.get('/api/feature-flags?enabled_only=true')
            print(f"Enabled Status: {enabled_response.status_code}")
            enabled_data = enabled_response.get_json()
            print(f"Enabled Response: {enabled_data}")
            
            if enabled_data and enabled_data.get('success'):
                enabled_flags = enabled_data.get('data', {}).get('flags', {})
                print(f"Found {len(enabled_flags)} enabled feature flags")
                for key, value in enabled_flags.items():
                    print(f"  - {key}: {value}")

if __name__ == '__main__':
    test_full_flow()