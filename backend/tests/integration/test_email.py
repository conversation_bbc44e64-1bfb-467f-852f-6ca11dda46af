#!/usr/bin/env python3
"""
Test script per verificare l'invio email recruiting.
Invia una email di test utilizzando la configurazione Tophost.
"""

import os
import sys
from datetime import datetime

# Add backend to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from flask import Flask
from config import Config
from extensions import mail
from services.email_service import recruiting_email_service

def test_email_sending():
    """Test di invio email recruiting."""
    
    # Create Flask app context
    app = create_app()
    
    with app.app_context():
        print("🔧 Testing Email Configuration...")
        print(f"MAIL_SERVER: {app.config.get('MAIL_SERVER')}")
        print(f"MAIL_PORT: {app.config.get('MAIL_PORT')}")
        print(f"MAIL_USE_TLS: {app.config.get('MAIL_USE_TLS')}")
        print(f"MAIL_USERNAME: {app.config.get('MAIL_USERNAME')}")
        print(f"MAIL_DEFAULT_SENDER: {app.config.get('MAIL_DEFAULT_SENDER')}")
        print()
        
        # Test data for interview confirmation
        test_interview_data = {
            'id': 999,
            'scheduled_date': datetime.now().isoformat(),
            'duration_minutes': 60,
            'interview_type': 'technical',
            'location': 'Ufficio principale - Sala Riunioni A',
            'notes': 'Test email dal sistema DatPortal recruiting',
            'candidate': {
                'id': 1,
                'full_name': 'Mario Rossi Test',
                'email': '<EMAIL>'  # La tua email per test
            },
            'job_posting': {
                'id': 1,
                'title': 'Sviluppatore Full Stack (TEST)'
            },
            'interviewer': {
                'id': 1,
                'full_name': 'Daniele Sabetta',
                'email': '<EMAIL>'
            }
        }
        
        print("📧 Sending test interview confirmation email...")
        print(f"📬 To: {test_interview_data['candidate']['email']}")
        print(f"📤 From: {app.config.get('MAIL_DEFAULT_SENDER')}")
        print()
        
        try:
            # Send test email
            success = recruiting_email_service.send_interview_confirmation(test_interview_data)
            
            if success:
                print("✅ EMAIL INVIATA CON SUCCESSO!")
                print(f"✉️  Controlla la casella: {test_interview_data['candidate']['email']}")
                print("📧 Dovresti ricevere un'email di conferma colloquio con:")
                print("   - Template HTML professionale")
                print("   - Dettagli colloquio completi")
                print("   - Branding DatPortal")
                print("   - Design responsive")
            else:
                print("❌ ERRORE: Email non inviata")
                print("🔍 Controlla i log per dettagli sull'errore")
                
        except Exception as e:
            print(f"❌ ECCEZIONE: {str(e)}")
            print("🔍 Verifica configurazione .env e credenziali Tophost")
        
        print()
        print("🧪 Test completato!")

if __name__ == "__main__":
    test_email_sending()