#!/usr/bin/env python3
"""
Test script per verificare il funzionamento delle API Help dopo il refactoring.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import HelpCategory, Help<PERSON>ontent, User
from extensions import db
from flask_login import login_user
import requests
import json

def test_help_models():
    """Test dei modelli Help per verificare che funzionino correttamente"""
    app = create_app()
    
    with app.app_context():
        print("🧪 Testing Help Models...")
        
        # Test 1: Query categorie esistenti
        try:
            categories = HelpCategory.query.all()
            print(f"✅ Found {len(categories)} help categories")
            
            for cat in categories[:3]:
                print(f"  - {cat.name}: {cat.active_content_count} active content items")
                
                # Test to_dict method
                cat_dict = cat.to_dict()
                assert 'id' in cat_dict
                assert 'name' in cat_dict
                assert 'active_content_count' in cat_dict
                print(f"  ✅ to_dict() works: {list(cat_dict.keys())}")
                
        except Exception as e:
            print(f"❌ Error testing categories: {e}")
            return False
        
        # Test 2: Query contenuti esistenti
        try:
            content_items = HelpContent.query.all()
            print(f"✅ Found {len(content_items)} help content items")
            
            if content_items:
                content = content_items[0]
                print(f"  - {content.title}")
                
                # Test to_dict method
                content_dict = content.to_dict()
                assert 'id' in content_dict
                assert 'title' in content_dict
                assert 'helpfulness_ratio' in content_dict
                print(f"  ✅ to_dict() works: {list(content_dict.keys())}")
                
        except Exception as e:
            print(f"❌ Error testing content: {e}")
            return False
        
        print("🎉 All model tests passed!")
        return True

def test_help_api_with_auth():
    """Test delle API Help con autenticazione"""
    app = create_app()
    
    with app.app_context():
        print("\n🧪 Testing Help API with Authentication...")
        
        # Find a test user
        user = User.query.first()
        if not user:
            print("❌ No users found for testing")
            return False
            
        print(f"📋 Using test user: {user.username}")
        
        # Create test client with app context
        with app.test_client() as client:
            # Simulate login
            with client.session_transaction() as sess:
                sess['_user_id'] = str(user.id)
                sess['_fresh'] = True
            
            # Test categories endpoint
            try:
                response = client.get('/api/help/categories')
                print(f"Categories API Status: {response.status_code}")
                
                if response.status_code == 200:
                    data = json.loads(response.data)
                    print(f"✅ Categories API: {data.get('success', False)}")
                    if data.get('success'):
                        categories = data.get('data', {}).get('categories', [])
                        print(f"  Returned {len(categories)} categories")
                        
                        # Show first category structure
                        if categories:
                            print(f"  Sample category: {list(categories[0].keys())}")
                else:
                    print(f"❌ Categories API failed: {response.data}")
                    
            except Exception as e:
                print(f"❌ Error testing categories API: {e}")
                return False
            
            # Test content endpoint
            try:
                response = client.get('/api/help/content')
                print(f"Content API Status: {response.status_code}")
                
                if response.status_code == 200:
                    data = json.loads(response.data)
                    print(f"✅ Content API: {data.get('success', False)}")
                    if data.get('success'):
                        content = data.get('data', {}).get('content', [])
                        pagination = data.get('data', {}).get('pagination', {})
                        print(f"  Returned {len(content)} content items")
                        print(f"  Pagination: page {pagination.get('page', 1)} of {pagination.get('pages', 1)}")
                else:
                    print(f"❌ Content API failed: {response.data}")
                    
            except Exception as e:
                print(f"❌ Error testing content API: {e}")
                return False
        
        print("🎉 All API tests passed!")
        return True

if __name__ == '__main__':
    print("🚀 Starting Help System Tests...")
    
    models_ok = test_help_models()
    api_ok = test_help_api_with_auth()
    
    if models_ok and api_ok:
        print("\n✅ ALL TESTS PASSED! Help system is working correctly.")
        sys.exit(0)
    else:
        print("\n❌ SOME TESTS FAILED! Check the output above.")
        sys.exit(1)