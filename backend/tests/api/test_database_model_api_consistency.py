"""
Test completo per verificare coerenza tra Database PostgreSQL reale, Modelli SQLAlchemy e API.
Questo test scova bug di mismatch tra i tre livelli.
"""

import pytest
import sqlalchemy as sa
from flask import Flask
from models import TimeOffRequest, User, Project, Task, Client, Contract, Proposal, TimesheetEntry
from extensions import db


class TestDatabaseModelAPIConsistency:
    """Test per verificare coerenza Database ↔ Modello ↔ API"""
    
    def test_time_off_requests_full_consistency(self, app):
        """Test completo TimeOffRequest: DB ↔ Model ↔ API"""
        with app.app_context():
            # 1. Struttura database reale (da PostgreSQL)
            db_columns = self._get_db_columns('time_off_requests')
            
            # 2. Campi del modello SQLAlchemy
            model_fields = self._get_model_fields(TimeOffRequest)
            
            # 3. Campi che l'API usa (da analisi codebase)
            api_expected_fields = [
                'id', 'user_id', 'type', 'start_date', 'end_date', 
                'reason', 'status', 'approved_by', 'approved_at', 
                'notes', 'created_at', 'updated_at', 'submission_date'
            ]
            
            # 4. Relationships che l'API usa
            api_expected_relationships = ['user', 'approver']
            
            print(f"\n=== TimeOffRequest Consistency Check ===")
            print(f"DB columns: {sorted(db_columns)}")
            print(f"Model fields: {sorted(model_fields)}")
            print(f"API expects: {sorted(api_expected_fields)}")
            
            # Test 1: Verifica che tutti i campi DB abbiano corrispondenza nel modello
            missing_in_model = []
            for db_col in db_columns:
                if not hasattr(TimeOffRequest, db_col):
                    missing_in_model.append(db_col)
            
            # Test 2: Verifica che tutti i campi API abbiano corrispondenza nel modello
            missing_api_fields = []
            temp_instance = TimeOffRequest()
            for api_field in api_expected_fields:
                if not hasattr(temp_instance, api_field):
                    missing_api_fields.append(api_field)
            
            # Test 3: Verifica relationships
            missing_relationships = []
            for rel in api_expected_relationships:
                if not hasattr(temp_instance, rel):
                    missing_relationships.append(rel)
            
            # Report risultati
            issues = []
            if missing_in_model:
                issues.append(f"Campi DB mancanti nel modello: {missing_in_model}")
            if missing_api_fields:
                issues.append(f"Campi API mancanti nel modello: {missing_api_fields}")
            if missing_relationships:
                issues.append(f"Relationships API mancanti: {missing_relationships}")
            
            if issues:
                print(f"🐛 ISSUES FOUND:")
                for issue in issues:
                    print(f"   - {issue}")
                assert False, f"TimeOffRequest consistency issues: {'; '.join(issues)}"
            
            print("✅ TimeOffRequest: DB ↔ Model ↔ API consistency OK")
    
    def test_critical_models_consistency(self, app):
        """Test consistency per tutti i modelli critici"""
        with app.app_context():
            critical_models = [
                ('TimeOffRequest', TimeOffRequest, 'time_off_requests'),
                ('User', User, 'users'),
                ('Project', Project, 'projects'),
                ('Task', Task, 'tasks'),
                ('TimesheetEntry', TimesheetEntry, 'timesheet_entries'),
                ('Client', Client, 'clients'),
                ('Contract', Contract, 'contracts'),
                ('Proposal', Proposal, 'proposals')
            ]
            
            all_issues = []
            
            for model_name, model_class, table_name in critical_models:
                try:
                    # Get DB columns
                    db_columns = self._get_db_columns(table_name)
                    
                    # Get model fields
                    model_fields = self._get_model_fields(model_class)
                    
                    # Check for major mismatches
                    missing_in_model = []
                    for db_col in db_columns:
                        if not hasattr(model_class, db_col):
                            missing_in_model.append(db_col)
                    
                    if missing_in_model:
                        all_issues.append(f"{model_name}: DB fields missing in model: {missing_in_model}")
                    
                    print(f"✅ {model_name}: Basic consistency OK")
                    
                except Exception as e:
                    all_issues.append(f"{model_name}: Error checking consistency: {str(e)}")
            
            if all_issues:
                print(f"\n🐛 CRITICAL MODEL ISSUES:")
                for issue in all_issues:
                    print(f"   - {issue}")
                assert False, f"Critical model consistency issues found: {len(all_issues)} problems"
            
            print(f"\n✅ All {len(critical_models)} critical models passed consistency check")
    
    def test_api_field_mapping_bugs(self, app):
        """Test specifico per bug di mapping campi API"""
        with app.app_context():
            # Test TimeOffRequest field mappings che hanno causato bug
            tor = TimeOffRequest()
            
            # Test 1: type property (maps to request_type)
            assert hasattr(tor, 'type'), "Missing 'type' property for API compatibility"
            assert hasattr(tor, 'request_type'), "Missing 'request_type' DB field"
            
            # Test 2: reason property (maps to notes)
            assert hasattr(tor, 'reason'), "Missing 'reason' property for API compatibility"
            assert hasattr(tor, 'notes'), "Missing 'notes' DB field"
            
            # Test 3: approved_at property (maps to approval_date)
            assert hasattr(tor, 'approved_at'), "Missing 'approved_at' property for API compatibility"
            assert hasattr(tor, 'approval_date'), "Missing 'approval_date' DB field"
            
            # Test 4: Relationships
            assert hasattr(tor, 'user'), "Missing 'user' relationship"
            assert hasattr(tor, 'approver'), "Missing 'approver' relationship"
            
            print("✅ All API field mappings are correctly implemented")
    
    def _get_db_columns(self, table_name):
        """Get actual database columns for a table"""
        try:
            inspector = sa.inspect(db.engine)
            columns = inspector.get_columns(table_name)
            return [col['name'] for col in columns]
        except Exception as e:
            print(f"Warning: Could not inspect table {table_name}: {e}")
            return []
    
    def _get_model_fields(self, model_class):
        """Get SQLAlchemy model fields"""
        try:
            # Get column attributes
            columns = []
            for attr_name in dir(model_class):
                attr = getattr(model_class, attr_name)
                if hasattr(attr, 'property') and hasattr(attr.property, 'columns'):
                    columns.append(attr_name)
            return columns
        except Exception as e:
            print(f"Warning: Could not inspect model {model_class.__name__}: {e}")
            return []


class TestBusinessLogicBugs:
    """Test per scovare bug di logica business"""
    
    def test_time_off_request_validation_bugs(self, app, db_session):
        """Test per scovare bug di validazione TimeOffRequest"""
        with app.app_context():
            from datetime import date, timedelta
            
            # Create test user
            user = User(username='test_user', email='<EMAIL>', first_name='Test', last_name='User')
            user.set_password('password')
            db_session.add(user)
            db_session.commit()
            
            # Test 1: Date validation bugs
            today = date.today()
            
            # Bug: End date before start date
            with pytest.raises(Exception):
                invalid_request = TimeOffRequest(
                    user_id=user.id,
                    type='vacation',
                    start_date=today + timedelta(days=10),
                    end_date=today + timedelta(days=5),  # Before start_date!
                    status='pending'
                )
                db_session.add(invalid_request)
                db_session.commit()
            
            # Test 2: Duration calculation bugs
            valid_request = TimeOffRequest(
                user_id=user.id,
                type='vacation',
                start_date=today,
                end_date=today + timedelta(days=4),  # 5 days total
                status='pending'
            )
            
            expected_duration = 5
            actual_duration = valid_request.duration_days
            assert actual_duration == expected_duration, f"Duration bug: expected {expected_duration}, got {actual_duration}"
            
            print("✅ TimeOffRequest validation logic working correctly")
