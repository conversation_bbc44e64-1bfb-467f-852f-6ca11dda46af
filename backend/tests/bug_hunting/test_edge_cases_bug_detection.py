"""
Test specifici per scovare bug con edge cases e situazioni limite.
Questi test cercano di rompere il sistema con dati strani o situazioni inaspettate.
"""

import pytest
from datetime import datetime, date, timedelta
from decimal import Decimal
from models import (
    TimeOffRequest, Project, Task, User, Client, Contract, 
    TimesheetEntry, ProjectResource, BIReport, CaseStudy
)
from extensions import db


class TestEdgeCasesBugDetection:
    """Test per scovare bug con edge cases"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user
    
    def test_timeoff_request_edge_cases(self):
        """Test TimeOffRequest con dati limite per scovare bug"""
        with self.app.app_context():
            # Test 1: Date nel passato molto lontano
            ancient_request = TimeOffRequest(
                user_id=self.user.id,
                request_type='vacation',
                start_date=date(1900, 1, 1),  # Data molto vecchia
                end_date=date(1900, 1, 2),
                status='pending'
            )
            db.session.add(ancient_request)
            
            # Test 2: Date nel futuro molto lontano
            future_request = TimeOffRequest(
                user_id=self.user.id,
                request_type='vacation',
                start_date=date(2099, 12, 30),  # Data molto futura
                end_date=date(2099, 12, 31),
                status='pending'
            )
            db.session.add(future_request)
            
            # Test 3: Richiesta di 1 solo giorno
            single_day = TimeOffRequest(
                user_id=self.user.id,
                request_type='sick',
                start_date=date.today(),
                end_date=date.today(),  # Stesso giorno
                status='pending'
            )
            db.session.add(single_day)
            
            # Test 4: Richiesta molto lunga (1 anno)
            long_request = TimeOffRequest(
                user_id=self.user.id,
                request_type='leave',
                start_date=date.today(),
                end_date=date.today() + timedelta(days=365),  # 1 anno
                status='pending'
            )
            db.session.add(long_request)
            
            db.session.commit()
            
            # Verifica che tutti siano stati salvati
            assert ancient_request.id is not None
            assert future_request.id is not None
            assert single_day.id is not None
            assert long_request.id is not None
            
            print("✅ TimeOffRequest edge cases: OK")
    
    def test_project_budget_edge_cases(self):
        """Test Project con budget estremi per scovare bug"""
        with self.app.app_context():
            # Crea un client per i progetti
            client = Client(name='Test Client', industry='Technology')
            db.session.add(client)
            db.session.commit()
            
            # Test 1: Budget zero
            zero_budget = Project(
                name='Zero Budget Project',
                client_id=client.id,
                budget=Decimal('0.00'),
                expenses=Decimal('0.00')
            )
            db.session.add(zero_budget)
            
            # Test 2: Budget negativo (dovrebbe essere impossibile?)
            try:
                negative_budget = Project(
                    name='Negative Budget Project',
                    client_id=client.id,
                    budget=Decimal('-1000.00'),
                    expenses=Decimal('0.00')
                )
                db.session.add(negative_budget)
                db.session.commit()
                print("⚠️  POSSIBILE BUG: Budget negativo accettato!")
            except Exception as e:
                print("✅ Budget negativo correttamente rifiutato")
            
            # Test 3: Budget molto grande
            huge_budget = Project(
                name='Huge Budget Project',
                client_id=client.id,
                budget=Decimal('999999999999.99'),  # Quasi 1 trilione
                expenses=Decimal('0.00')
            )
            db.session.add(huge_budget)
            
            # Test 4: Spese maggiori del budget
            overspent = Project(
                name='Overspent Project',
                client_id=client.id,
                budget=Decimal('1000.00'),
                expenses=Decimal('2000.00')  # Spese > Budget
            )
            db.session.add(overspent)
            
            db.session.commit()
            
            # Verifica calcoli
            assert zero_budget.budget == Decimal('0.00')
            assert huge_budget.budget == Decimal('999999999999.99')
            assert overspent.expenses > overspent.budget  # Questo dovrebbe essere un warning
            
            print("✅ Project budget edge cases: OK")
    
    def test_string_length_limits(self):
        """Test limiti lunghezza stringhe per scovare overflow"""
        with self.app.app_context():
            # Test 1: Nome progetto molto lungo
            very_long_name = "A" * 1000  # 1000 caratteri
            
            try:
                long_name_project = Project(
                    name=very_long_name,
                    description="Test project with very long name"
                )
                db.session.add(long_name_project)
                db.session.commit()
                print("⚠️  POSSIBILE BUG: Nome progetto troppo lungo accettato!")
            except Exception as e:
                print("✅ Nome progetto troppo lungo correttamente rifiutato")
            
            # Test 2: Email molto lunga
            very_long_email = "a" * 200 + "@example.com"
            
            try:
                long_email_client = Client(
                    name="Test Client",
                    email=very_long_email
                )
                db.session.add(long_email_client)
                db.session.commit()
                print("⚠️  POSSIBILE BUG: Email troppo lunga accettata!")
            except Exception as e:
                print("✅ Email troppo lunga correttamente rifiutata")
    
    def test_null_and_empty_values(self):
        """Test valori null e vuoti per scovare bug di validazione"""
        with self.app.app_context():
            # Test 1: BIReport con campi vuoti
            empty_report = BIReport(
                name="",  # Nome vuoto
                description=None,  # Descrizione null
                created_by=self.user.id
            )
            
            try:
                db.session.add(empty_report)
                db.session.commit()
                print("⚠️  POSSIBILE BUG: BIReport con nome vuoto accettato!")
            except Exception as e:
                print("✅ BIReport con nome vuoto correttamente rifiutato")
    
    def test_concurrent_operations_simulation(self):
        """Simula operazioni concorrenti per scovare race conditions"""
        with self.app.app_context():
            # Crea un progetto base
            project = Project(
                name="Concurrent Test Project",
                budget=Decimal('1000.00'),
                expenses=Decimal('0.00')
            )
            db.session.add(project)
            db.session.commit()
            
            # Simula due operazioni che modificano le spese contemporaneamente
            # (In un vero test di concorrenza useresti threading)
            
            # Operazione 1: Aggiungi spesa di 500
            project.expenses += Decimal('500.00')
            
            # Operazione 2: Aggiungi spesa di 300 (simula concorrenza)
            project.expenses += Decimal('300.00')
            
            db.session.commit()
            
            # Verifica risultato
            assert project.expenses == Decimal('800.00')
            print("✅ Operazioni concorrenti simulate: OK")
    
    def test_json_field_corruption(self):
        """Test corruzione campi JSON per scovare bug di serializzazione"""
        with self.app.app_context():
            # Test 1: JSON molto annidato
            deep_json = {
                'level1': {
                    'level2': {
                        'level3': {
                            'level4': {
                                'level5': {
                                    'data': 'very deep'
                                }
                            }
                        }
                    }
                }
            }
            
            case_study = CaseStudy(
                title="Deep JSON Test",
                overview="Testing deep JSON structures",
                case_type="test",
                technologies=deep_json,  # JSON molto annidato
                created_by=self.user.id
            )
            db.session.add(case_study)
            db.session.commit()
            
            # Verifica che il JSON sia stato salvato correttamente
            retrieved = CaseStudy.query.get(case_study.id)
            assert retrieved.technologies['level1']['level2']['level3']['level4']['level5']['data'] == 'very deep'
            
            print("✅ JSON field corruption test: OK")


class TestAPIConsistencyBugs:
    """Test per scovare inconsistenze tra API e modelli"""
    
    def test_to_dict_completeness(self):
        """Verifica che to_dict() includa tutti i campi importanti"""
        with pytest.app.app_context():
            # Test TimeOffRequest.to_dict()
            tor = TimeOffRequest(
                user_id=1,
                request_type='vacation',
                start_date=date.today(),
                end_date=date.today() + timedelta(days=1),
                status='pending',
                notes='Test notes'
            )
            
            dict_data = tor.to_dict()
            
            # Verifica che tutti i campi importanti siano presenti
            required_fields = [
                'id', 'user_id', 'request_type', 'start_date', 
                'end_date', 'status', 'notes'
            ]
            
            for field in required_fields:
                assert field in dict_data, f"Campo {field} mancante in to_dict()"
            
            print("✅ to_dict() completeness test: OK")
