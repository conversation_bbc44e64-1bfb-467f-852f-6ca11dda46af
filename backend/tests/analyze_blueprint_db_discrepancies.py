#!/usr/bin/env python3
"""
Script per analizzare le discrepanze tra i campi utilizzati nei blueprint 
e quelli effettivamente presenti nel database PostgreSQL.
"""

import os
import re
import json
from pathlib import Path

# Mappa dei campi presenti nel database (da database/database_detailed.txt)
DATABASE_FIELDS = {
    'users': [
        'id', 'username', 'email', 'password_hash', 'first_name', 'last_name',
        'role', 'department', 'department_id', 'position', 'hire_date', 'phone',
        'profile_image', 'bio', 'is_active', 'dark_mode', 'created_at', 'last_login',
        'reset_token', 'reset_token_expiry'
    ],
    'contracts': [
        'id', 'client_id', 'contract_number', 'title', 'description', 'contract_type',
        'hourly_rate', 'budget_hours', 'budget_amount', 'start_date', 'end_date',
        'status', 'created_at', 'updated_at', 'milestone_amount', 'milestone_count',
        'subscription_frequency', 'subscription_amount', 'retainer_amount', 'retainer_frequency'
    ],
    'time_off_requests': [
        'id', 'user_id', 'request_type', 'start_date', 'end_date', 'status',
        'notes', 'submission_date', 'approval_date', 'approved_by', 'rejection_reason',
        'created_at', 'updated_at'
    ],
    'clients': [
        'id', 'name', 'industry', 'description', 'website', 'address', 'created_at',
        'updated_at', 'status', 'email', 'phone', 'vat_number', 'fiscal_code'
    ],
    'projects': [
        'id', 'name', 'description', 'client_id', 'start_date', 'end_date',
        'status', 'budget', 'expenses', 'created_at', 'updated_at', 'is_billable',
        'client_daily_rate', 'markup_percentage', 'project_type', 'contract_id',
        'funding_source', 'funding_application_id'
    ],
    'timesheet_entries': [
        'id', 'user_id', 'project_id', 'task_id', 'date', 'hours', 'description',
        'status', 'created_at', 'monthly_timesheet_id', 'billable', 'billing_rate',
        'contract_id', 'invoice_line_id', 'billing_status'
    ],
    'performance_reviews': [
        'id', 'employee_id', 'reviewer_id', 'review_year', 'review_period_start',
        'review_period_end', 'status', 'due_date', 'overall_rating', 'technical_skills_rating',
        'soft_skills_rating', 'goals_achievement_rating', 'leadership_rating',
        'teamwork_rating', 'communication_rating', 'initiative_rating', 'strengths',
        'areas_for_improvement', 'manager_comments', 'employee_comments', 'hr_comments',
        'promotion_recommendation', 'salary_increase_recommendation', 'bonus_recommendation',
        'training_recommendations', 'employee_signed_at', 'manager_signed_at',
        'hr_signed_at', 'created_at', 'updated_at', 'created_by', 'template_id',
        'comments', 'submitted_date', 'completed_date', 'approved_date', 'approved_by',
        'achievements', 'areas_improvement', 'development_goals', 'reviewer_comments'
    ],
    'performance_goals': [
        'id', 'employee_id', 'review_id', 'title', 'description', 'category',
        'target_year', 'start_date', 'target_date', 'status', 'progress_percentage',
        'completion_date', 'success_criteria', 'measurable_outcomes', 'weight',
        'priority', 'achievement_rating', 'manager_assessment', 'employee_self_assessment',
        'created_at', 'updated_at', 'created_by', 'set_by_id', 'quarter', 'year',
        'notes', 'completion_notes', 'progress', 'is_template', 'template_id',
        'assigned_by_id', 'visibility'
    ],
    'engagement_user_profiles': [
        'id', 'user_id', 'total_points', 'total_points_spent', 'available_points',
        'current_level_id', 'next_level_id', 'total_logins', 'total_actions',
        'streak_days', 'last_activity_date', 'created_at', 'updated_at',
        'activities_completed', 'achievements_count', 'current_streak_days'
    ],
    'polls': [
        'id', 'title', 'description', 'author_id', 'is_anonymous', 'multiple_choice',
        'expires_at', 'is_active', 'total_votes', 'created_at', 'updated_at'
    ]
}

# Proprietà calcolate/virtuali conosciute nei modelli
VIRTUAL_PROPERTIES = {
    'users': ['full_name'],
    'time_off_requests': ['duration_days'],
    'contracts': ['total_budget'],  # Alias per budget_amount
    'projects': ['remaining_budget'],
    'polls': ['is_expired', 'unique_voters']
}

def analyze_blueprint_file(filepath):
    """Analizza un singolo file blueprint per estrarre i campi utilizzati"""
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Pattern per trovare accessi ai campi dei modelli
    patterns = [
        r'(\w+)\.(\w+)(?:\s*[=!<>]|\s+(?:in|not in|like|ilike)|\s*\.)',  # Model.field
        r'(\w+)\.filter\([^)]*(\w+)\.(\w+)',  # query.filter(Model.field)
        r'(\w+)\.filter_by\([^)]*(\w+)=',  # query.filter_by(field=value)
        r'(\w+)\.order_by\([^)]*(\w+)\.(\w+)',  # query.order_by(Model.field)
        r'(\w+)\.(\w+)\s*=\s*data\.get\([\'"](\w+)[\'"]',  # model.field = data.get('field')
        r'[\'"](\w+)[\'"]\s*:\s*\w+\.(\w+)',  # 'field': model.field in dict
    ]
    
    used_fields = {}
    
    for pattern in patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        for match in matches:
            if len(match) >= 2:
                if len(match) == 3:  # Model.field pattern
                    model, field = match[0], match[1]
                    if field == 'query':  # Skip .query
                        continue
                else:  # filter_by pattern
                    field = match[1] if len(match) > 1 else match[0]
                    model = 'unknown'
                
                # Normalizza il nome del modello
                model_name = model.lower()
                if model_name.endswith('request'):
                    model_name = 'time_off_requests'
                elif model_name == 'contract':
                    model_name = 'contracts'
                elif model_name == 'user':
                    model_name = 'users'
                elif model_name == 'client':
                    model_name = 'clients'
                elif model_name == 'project':
                    model_name = 'projects'
                elif model_name == 'timesheetentry':
                    model_name = 'timesheet_entries'
                elif model_name == 'performancereview':
                    model_name = 'performance_reviews'
                elif model_name == 'performancegoal':
                    model_name = 'performance_goals'
                elif model_name == 'poll':
                    model_name = 'polls'
                
                if model_name not in used_fields:
                    used_fields[model_name] = set()
                used_fields[model_name].add(field)
    
    return used_fields

def check_field_discrepancies():
    """Controlla le discrepanze tra campi utilizzati nei blueprint e database"""
    backend_dir = Path('backend')
    blueprints_dir = backend_dir / 'blueprints' / 'api'
    
    if not blueprints_dir.exists():
        print(f"Directory {blueprints_dir} non trovata")
        return
    
    all_used_fields = {}
    discrepancies = []
    
    # Analizza tutti i file blueprint
    for blueprint_file in blueprints_dir.glob('*.py'):
        if blueprint_file.name.startswith('__'):
            continue
            
        print(f"\n🔍 Analizzando {blueprint_file.name}...")
        used_fields = analyze_blueprint_file(blueprint_file)
        
        # Merge con i campi già trovati
        for model, fields in used_fields.items():
            if model not in all_used_fields:
                all_used_fields[model] = set()
            all_used_fields[model].update(fields)
    
    # Controlla discrepanze
    print("\n" + "="*60)
    print("📊 ANALISI DISCREPANZE BLUEPRINT vs DATABASE")
    print("="*60)
    
    for model, used_fields in all_used_fields.items():
        if model == 'unknown':
            continue
            
        print(f"\n📋 Modello: {model}")
        print("-" * 40)
        
        db_fields = set(DATABASE_FIELDS.get(model, []))
        virtual_fields = set(VIRTUAL_PROPERTIES.get(model, []))
        
        # Campi utilizzati ma non presenti nel DB
        missing_in_db = used_fields - db_fields - virtual_fields
        if missing_in_db:
            print(f"❌ Campi utilizzati ma NON presenti nel DB: {sorted(missing_in_db)}")
            for field in missing_in_db:
                discrepancies.append({
                    'type': 'missing_in_db',
                    'model': model,
                    'field': field,
                    'severity': 'high'
                })
        
        # Campi virtuali utilizzati (OK)
        used_virtual = used_fields & virtual_fields
        if used_virtual:
            print(f"✅ Proprietà virtuali utilizzate: {sorted(used_virtual)}")
        
        # Campi DB utilizzati (OK)
        used_db_fields = used_fields & db_fields
        if used_db_fields:
            print(f"✅ Campi DB utilizzati: {sorted(used_db_fields)}")
        
        # Campi DB non utilizzati (info)
        unused_db_fields = db_fields - used_fields
        if unused_db_fields:
            print(f"ℹ️  Campi DB non utilizzati: {sorted(unused_db_fields)}")
    
    # Riepilogo finale
    print("\n" + "="*60)
    print("🎯 RIEPILOGO PROBLEMI TROVATI")
    print("="*60)
    
    if not discrepancies:
        print("✅ Nessuna discrepanza critica trovata!")
        return
    
    high_severity = [d for d in discrepancies if d['severity'] == 'high']
    
    if high_severity:
        print(f"\n❌ {len(high_severity)} problemi ad alta priorità:")
        for issue in high_severity:
            print(f"   - {issue['model']}.{issue['field']} (campo mancante nel DB)")
    
    # Suggerimenti per la risoluzione
    print("\n💡 SUGGERIMENTI PER LA RISOLUZIONE:")
    print("-" * 40)
    
    for issue in high_severity:
        model = issue['model']
        field = issue['field']
        
        print(f"\n🔧 {model}.{field}:")
        print(f"   1. Verificare se il campo esiste con nome diverso nel DB")
        print(f"   2. Aggiungere il campo al modello SQLAlchemy se mancante")
        print(f"   3. Creare una migrazione database se necessario")
        print(f"   4. Oppure implementare come proprietà virtuale nel modello")

if __name__ == "__main__":
    check_field_discrepancies() 