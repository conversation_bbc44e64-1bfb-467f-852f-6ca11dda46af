"""Unit tests for BIReport model."""
import pytest
from models import <PERSON><PERSON>eport, User
from extensions import db

class TestBIReportModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user

    def test_bireport_creation_basic(self):
        report = BIReport(
            name='Test BI Report',
            description='This is a test BI report',
            report_type='dashboard',
            created_by=self.user.id
        )
        db.session.add(report)
        db.session.commit()

        assert report.id is not None
        assert report.name == 'Test BI Report'
        assert report.report_type == 'dashboard'

    def test_bireport_deletion(self):
        report = BIReport(name='To Delete', description='Delete me', created_by=self.user.id)
        db.session.add(report)
        db.session.commit()
        report_id = report.id

        db.session.delete(report)
        db.session.commit()

        deleted = BIReport.query.get(report_id)
        assert deleted is None
