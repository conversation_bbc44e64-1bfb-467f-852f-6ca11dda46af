"""Unit tests for BusinessProcess model."""
import pytest
from models import BusinessProcess, User
from extensions import db

class TestBusinessProcessModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user

    def test_businessprocess_creation_basic(self):
        process = BusinessProcess(
            name='Employee Onboarding',
            description='Process for onboarding new employees'
        )
        db.session.add(process)
        db.session.commit()
        
        assert process.id is not None
        assert process.name == 'Employee Onboarding'
        assert process.description == 'Process for onboarding new employees'

    def test_businessprocess_creation_complete(self):
        process = BusinessProcess(
            name='Invoice Processing',
            description='Complete invoice processing workflow',
            owner_id=self.user.id
        )
        db.session.add(process)
        db.session.commit()

        assert process.owner_id == self.user.id
        assert process.name == 'Invoice Processing'

    def test_businessprocess_multiple_creation(self):
        names = ['Process A', 'Process B', 'Process C', 'Process D']
        processes = []

        for i, name in enumerate(names):
            process = BusinessProcess(
                name=name,
                description=f'Description for {name}'
            )
            processes.append(process)

        db.session.add_all(processes)
        db.session.commit()

        for process, expected_name in zip(processes, names):
            assert process.name == expected_name

    def test_businessprocess_update(self):
        process = BusinessProcess(
            name='Original Process',
            description='Original description'
        )
        db.session.add(process)
        db.session.commit()

        process.name = 'Updated Process'
        process.description = 'Updated description'
        db.session.commit()

        updated = BusinessProcess.query.get(process.id)
        assert updated.name == 'Updated Process'
        assert updated.description == 'Updated description'

    def test_businessprocess_deletion(self):
        process = BusinessProcess(
            name='To Be Deleted',
            description='This will be deleted'
        )
        db.session.add(process)
        db.session.commit()
        process_id = process.id
        
        db.session.delete(process)
        db.session.commit()
        
        deleted = BusinessProcess.query.get(process_id)
        assert deleted is None
