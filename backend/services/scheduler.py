import os
import time
import threading
from datetime import datetime, timed<PERSON>ta
from flask import current_app
from flask_mail import Message
from extensions import mail
from models import BIReport, db
import json
import logging

logger = logging.getLogger(__name__)

class ReportScheduler:
    """Gestisce la generazione on-demand dei report BI"""
    
    def __init__(self):
        self.enabled = os.getenv('ENABLE_REPORT_SCHEDULER', 'false').lower() == 'true'
        self.from_email = None
        self.company_name = None
        logger.info(f"Report Scheduler configurato: {'ABILITATO' if self.enabled else 'DISABILITATO'}")
    
    def _ensure_initialized(self):
        """Lazy initialization of email service properties."""
        if self.from_email is None:
            self.from_email = current_app.config.get('MAIL_DEFAULT_SENDER')
            self.company_name = current_app.config.get('COMPANY_NAME', 'DatPortal')
    
    def is_enabled(self):
        """Controlla se lo scheduler è abilitato"""
        return self.enabled
    
    def start(self):
        """Placeholder per compatibilità - non fa nulla"""
        if self.enabled:
            logger.info("Report Scheduler è abilitato ma funziona on-demand")
        else:
            logger.info("Report Scheduler è disabilitato")
    
    def stop(self):
        """Placeholder per compatibilità - non fa nulla"""
        logger.info("Report Scheduler fermato")
    
    def check_and_generate_reports(self):
        """Controlla e genera report schedulati - restituisce il numero di report generati"""
        try:
            with current_app.app_context():
                now = datetime.utcnow()
                
                # Trova report che devono essere generati
                overdue_reports = BIReport.query.filter(
                    BIReport.is_scheduled == True,
                    BIReport.status == 'active',
                    BIReport.next_generation <= now
                ).all()
                
                generated_count = 0
                for report in overdue_reports:
                    try:
                        self.generate_scheduled_report(report)
                        generated_count += 1
                    except Exception as e:
                        logger.error(f"Errore generazione report {report.id}: {e}")
                
                logger.info(f"Controllati {len(overdue_reports)} report schedulati, {generated_count} generati con successo")
                return generated_count
                
        except Exception as e:
            logger.error(f"Errore controllo report schedulati: {e}")
            return 0
    
    def generate_scheduled_report(self, report):
        """Genera un singolo report schedulato"""
        try:
            # Genera il report
            report_data = self.generate_report_data(report)
            
            # Aggiorna timestamp
            report.last_generated = datetime.utcnow()
            report.next_generation = self.calculate_next_generation(
                report.schedule_frequency, 
                report.last_generated
            )
            
            # Salva nel database
            db.session.commit()
            
            # Invia via email se configurato
            if report.schedule_recipients:
                self.send_report_email(report, report_data)
            
            logger.info(f"Report {report.id} '{report.name}' generato con successo")
            
        except Exception as e:
            logger.error(f"Errore generazione report {report.id}: {e}")
            # Segna come in errore ma schedula per il prossimo tentativo
            report.next_generation = datetime.utcnow() + timedelta(hours=1)
            db.session.commit()
    
    def generate_report_data(self, report):
        """Genera i dati del report"""
        if report.report_type == 'skills_analysis':
            return self.generate_skills_analysis_report(report.filters or {})
        elif report.report_type == 'market_analysis':
            return self.generate_market_analysis_report(report.filters or {})
        elif report.report_type == 'performance_metrics':
            return self.generate_performance_metrics_report(report.filters or {})
        elif report.report_type == 'financial_overview':
            return self.generate_financial_overview_report(report.filters or {})
        else:
            return {'message': f'Tipo report {report.report_type} non supportato'}
    
    def generate_skills_analysis_report(self, filters):
        """Genera report analisi competenze"""
        from models import User, Skill, UserSkill, Department
        
        # Query base competenze
        skills_data = db.session.query(
            Skill.name,
            Skill.category,
            db.func.count(UserSkill.id).label('users_count'),
            db.func.avg(UserSkill.proficiency_level).label('avg_proficiency')
        ).join(UserSkill).group_by(Skill.id, Skill.name, Skill.category).all()
        
        return {
            'report_type': 'skills_analysis',
            'generated_at': datetime.utcnow().isoformat(),
            'data': {
                'skills_overview': [
                    {
                        'skill': skill.name,
                        'category': skill.category,
                        'users_count': skill.users_count,
                        'avg_proficiency': round(float(skill.avg_proficiency), 2)
                    }
                    for skill in skills_data
                ],
                'summary': {
                    'total_skills': len(skills_data),
                    'total_skill_assignments': sum(s.users_count for s in skills_data)
                }
            }
        }
    
    def generate_market_analysis_report(self, filters):
        """Genera report analisi mercato"""
        from models import MarketProspect
        
        prospects = MarketProspect.query.all()
        
        return {
            'report_type': 'market_analysis',
            'generated_at': datetime.utcnow().isoformat(),
            'data': {
                'prospects_overview': [
                    {
                        'company_name': p.company_name,
                        'sector': p.sector,
                        'fit_score': p.fit_score,
                        'estimated_budget': (p.estimated_budget_min + p.estimated_budget_max) / 2
                    }
                    for p in prospects
                ],
                'summary': {
                    'total_prospects': len(prospects),
                    'avg_fit_score': sum(p.fit_score for p in prospects) / len(prospects) if prospects else 0,
                    'high_quality_prospects': len([p for p in prospects if p.fit_score >= 8.0])
                }
            }
        }
    
    def generate_performance_metrics_report(self, filters):
        """Genera report metriche performance"""
        from models import Project, TimesheetEntry
        
        # Progetti completati ultimo mese
        last_month = datetime.utcnow() - timedelta(days=30)
        completed_projects = Project.query.filter(
            Project.status == 'completed',
            Project.updated_at >= last_month
        ).count()
        
        # Ore lavorate ultimo mese
        total_hours = db.session.query(db.func.sum(TimesheetEntry.hours)).filter(
            TimesheetEntry.date >= last_month.date()
        ).scalar() or 0
        
        return {
            'report_type': 'performance_metrics',
            'generated_at': datetime.utcnow().isoformat(),
            'data': {
                'period': 'last_30_days',
                'completed_projects': completed_projects,
                'total_hours_worked': float(total_hours),
                'avg_hours_per_project': float(total_hours) / completed_projects if completed_projects > 0 else 0
            }
        }
    
    def generate_financial_overview_report(self, filters):
        """Genera report overview finanziario"""
        from models import Contract, Invoice
        
        # Contratti attivi
        active_contracts = Contract.query.filter_by(status='active').count()
        
        # Fatturato ultimo trimestre
        last_quarter = datetime.utcnow() - timedelta(days=90)
        revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter(
            Invoice.status == 'paid',
            Invoice.created_at >= last_quarter
        ).scalar() or 0
        
        return {
            'report_type': 'financial_overview',
            'generated_at': datetime.utcnow().isoformat(),
            'data': {
                'period': 'last_90_days',
                'active_contracts': active_contracts,
                'total_revenue': float(revenue),
                'avg_monthly_revenue': float(revenue) / 3
            }
        }
    
    def send_report_email(self, report, report_data):
        """Invia report via email"""
        self._ensure_initialized()
        
        try:
            # Parse recipients
            recipients = report.schedule_recipients.split(',') if report.schedule_recipients else []
            recipients = [email.strip() for email in recipients if email.strip()]
            
            if not recipients:
                logger.warning(f"Nessun destinatario specificato per il report {report.id}")
                return False
            
            subject = f"Report Automatico: {report.name}"
            
            # Generate HTML and text templates
            html_body = self._get_report_email_template(report, report_data)
            text_body = self._get_report_email_text(report, report_data)
            
            # Create and send email message
            msg = Message(
                subject=subject,
                sender=self.from_email,
                recipients=recipients,
                body=text_body,
                html=html_body
            )
            
            mail.send(msg)
            logger.info(f"Report email inviato con successo - Report {report.id} a {len(recipients)} destinatari")
            return True
            
        except Exception as e:
            logger.error(f"Errore invio email report {report.id}: {e}")
            return False
    
    def calculate_next_generation(self, frequency, last_generated):
        """Calcola la prossima data di generazione"""
        if frequency == 'daily':
            return last_generated + timedelta(days=1)
        elif frequency == 'weekly':
            return last_generated + timedelta(weeks=1)
        elif frequency == 'monthly':
            return last_generated + timedelta(days=30)
        elif frequency == 'quarterly':
            return last_generated + timedelta(days=90)
        else:
            return last_generated + timedelta(days=1)  # Default giornaliero
    
    def cleanup_old_reports(self):
        """Pulisce report vecchi (oltre 6 mesi)"""
        try:
            with current_app.app_context():
                cutoff_date = datetime.utcnow() - timedelta(days=180)
                
                # Trova report vecchi non schedulati
                old_reports = BIReport.query.filter(
                    BIReport.is_scheduled == False,
                    BIReport.created_at < cutoff_date,
                    BIReport.status != 'archived'
                ).all()
                
                for report in old_reports:
                    report.status = 'archived'
                
                db.session.commit()
                logger.info(f"Archiviati {len(old_reports)} report vecchi")
                
        except Exception as e:
            logger.error(f"Errore pulizia report vecchi: {e}")
    
    def _get_report_email_template(self, report, report_data):
        """Template HTML per email report"""
        generated_at = report_data.get('generated_at', datetime.utcnow().isoformat())
        report_type_label = self._get_report_type_label(report.report_type)
        
        # Format data for display
        data_content = self._format_report_data_for_email(report_data)
        
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .header {{ background: #2563eb; color: white; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; }}
                .report-info {{ background: #f8fafc; padding: 15px; border-radius: 8px; margin: 20px 0; }}
                .data-section {{ background: #f0fdf4; padding: 15px; border-radius: 8px; margin: 20px 0; }}
                .footer {{ background: #f1f5f9; padding: 15px; text-align: center; font-size: 12px; }}
                .summary {{ background: #eff6ff; padding: 15px; border-radius: 8px; margin: 20px 0; }}
                table {{ width: 100%; border-collapse: collapse; margin: 10px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>{self.company_name}</h1>
                <h2>Report Automatico</h2>
            </div>
            
            <div class="content">
                <h2>Report: {report.name}</h2>
                
                <div class="report-info">
                    <h3>Informazioni Report</h3>
                    <p><strong>Tipo:</strong> {report_type_label}</p>
                    <p><strong>Generato il:</strong> {datetime.fromisoformat(generated_at.replace('Z', '+00:00')).strftime('%d/%m/%Y %H:%M')}</p>
                    <p><strong>Frequenza:</strong> {self._get_frequency_label(report.schedule_frequency)}</p>
                    <p><strong>Prossima generazione:</strong> {report.next_generation.strftime('%d/%m/%Y %H:%M') if report.next_generation else 'N/A'}</p>
                </div>
                
                <div class="data-section">
                    <h3>Dati Report</h3>
                    {data_content}
                </div>
                
                <p>Questo report è stato generato automaticamente dal sistema {self.company_name}.</p>
                
                <p>Per ulteriori informazioni, accedi al sistema di business intelligence.</p>
            </div>
            
            <div class="footer">
                <p>{self.company_name} - Sistema di Reporting Automatico</p>
                <p>Questa email è stata generata automaticamente. Non rispondere a questa email.</p>
            </div>
        </body>
        </html>
        """
    
    def _get_report_email_text(self, report, report_data):
        """Template testo per email report"""
        generated_at = report_data.get('generated_at', datetime.utcnow().isoformat())
        report_type_label = self._get_report_type_label(report.report_type)
        
        # Format data for display
        data_content = self._format_report_data_for_text(report_data)
        
        return f"""
{self.company_name} - REPORT AUTOMATICO

Report: {report.name}

INFORMAZIONI REPORT:
Tipo: {report_type_label}
Generato il: {datetime.fromisoformat(generated_at.replace('Z', '+00:00')).strftime('%d/%m/%Y %H:%M')}
Frequenza: {self._get_frequency_label(report.schedule_frequency)}
Prossima generazione: {report.next_generation.strftime('%d/%m/%Y %H:%M') if report.next_generation else 'N/A'}

DATI REPORT:
{data_content}

Questo report è stato generato automaticamente dal sistema {self.company_name}.
Per ulteriori informazioni, accedi al sistema di business intelligence.

{self.company_name} - Sistema di Reporting Automatico
Questa email è stata generata automaticamente. Non rispondere a questa email.
        """
    
    def _get_report_type_label(self, report_type):
        """Converte il tipo report in label italiana"""
        labels = {
            'skills_analysis': 'Analisi Competenze',
            'market_analysis': 'Analisi Mercato',
            'performance_metrics': 'Metriche Performance',
            'financial_overview': 'Panoramica Finanziaria'
        }
        return labels.get(report_type, report_type)
    
    def _get_frequency_label(self, frequency):
        """Converte la frequenza in label italiana"""
        labels = {
            'daily': 'Giornaliero',
            'weekly': 'Settimanale',
            'monthly': 'Mensile',
            'quarterly': 'Trimestrale'
        }
        return labels.get(frequency, frequency)
    
    def _format_report_data_for_email(self, report_data):
        """Formatta i dati del report per l'email HTML"""
        data = report_data.get('data', {})
        
        if report_data.get('report_type') == 'skills_analysis':
            skills = data.get('skills_overview', [])
            summary = data.get('summary', {})
            
            content = f"""
            <div class="summary">
                <h4>Riepilogo</h4>
                <p><strong>Competenze totali:</strong> {summary.get('total_skills', 0)}</p>
                <p><strong>Assegnazioni competenze:</strong> {summary.get('total_skill_assignments', 0)}</p>
            </div>
            """
            
            if skills:
                content += """
                <h4>Top Competenze</h4>
                <table>
                    <tr>
                        <th>Competenza</th>
                        <th>Categoria</th>
                        <th>Utenti</th>
                        <th>Livello Medio</th>
                    </tr>
                """
                
                for skill in skills[:10]:  # Top 10
                    content += f"""
                    <tr>
                        <td>{skill.get('skill', 'N/A')}</td>
                        <td>{skill.get('category', 'N/A')}</td>
                        <td>{skill.get('users_count', 0)}</td>
                        <td>{skill.get('avg_proficiency', 0):.1f}</td>
                    </tr>
                    """
                
                content += "</table>"
            
            return content
        
        elif report_data.get('report_type') == 'performance_metrics':
            content = f"""
            <div class="summary">
                <h4>Metriche Performance (Ultimi 30 giorni)</h4>
                <p><strong>Progetti completati:</strong> {data.get('completed_projects', 0)}</p>
                <p><strong>Ore totali lavorate:</strong> {data.get('total_hours_worked', 0):.1f}</p>
                <p><strong>Ore medie per progetto:</strong> {data.get('avg_hours_per_project', 0):.1f}</p>
            </div>
            """
            return content
        
        elif report_data.get('report_type') == 'financial_overview':
            content = f"""
            <div class="summary">
                <h4>Panoramica Finanziaria (Ultimi 90 giorni)</h4>
                <p><strong>Contratti attivi:</strong> {data.get('active_contracts', 0)}</p>
                <p><strong>Fatturato totale:</strong> €{data.get('total_revenue', 0):.2f}</p>
                <p><strong>Fatturato medio mensile:</strong> €{data.get('avg_monthly_revenue', 0):.2f}</p>
            </div>
            """
            return content
        
        else:
            return f"<p>Dati report: {json.dumps(data, indent=2)}</p>"
    
    def _format_report_data_for_text(self, report_data):
        """Formatta i dati del report per l'email testo"""
        data = report_data.get('data', {})
        
        if report_data.get('report_type') == 'skills_analysis':
            skills = data.get('skills_overview', [])
            summary = data.get('summary', {})
            
            content = f"""
RIEPILOGO:
Competenze totali: {summary.get('total_skills', 0)}
Assegnazioni competenze: {summary.get('total_skill_assignments', 0)}
"""
            
            if skills:
                content += "\\nTOP COMPETENZE:\\n"
                for skill in skills[:10]:  # Top 10
                    content += f"- {skill.get('skill', 'N/A')} ({skill.get('category', 'N/A')}) - {skill.get('users_count', 0)} utenti - Livello {skill.get('avg_proficiency', 0):.1f}\\n"
            
            return content
        
        elif report_data.get('report_type') == 'performance_metrics':
            return f"""
METRICHE PERFORMANCE (Ultimi 30 giorni):
Progetti completati: {data.get('completed_projects', 0)}
Ore totali lavorate: {data.get('total_hours_worked', 0):.1f}
Ore medie per progetto: {data.get('avg_hours_per_project', 0):.1f}
"""
        
        elif report_data.get('report_type') == 'financial_overview':
            return f"""
PANORAMICA FINANZIARIA (Ultimi 90 giorni):
Contratti attivi: {data.get('active_contracts', 0)}
Fatturato totale: €{data.get('total_revenue', 0):.2f}
Fatturato medio mensile: €{data.get('avg_monthly_revenue', 0):.2f}
"""
        
        else:
            return f"Dati report: {json.dumps(data, indent=2)}"

# Istanza globale dello scheduler
report_scheduler = ReportScheduler() 