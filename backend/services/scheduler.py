import os
import time
import threading
from datetime import datetime, timedelta
from flask import current_app
from models import BIReport, db
import json
import logging

logger = logging.getLogger(__name__)

class ReportScheduler:
    """Gestisce la generazione on-demand dei report BI"""
    
    def __init__(self):
        self.enabled = os.getenv('ENABLE_REPORT_SCHEDULER', 'false').lower() == 'true'
        logger.info(f"Report Scheduler configurato: {'ABILITATO' if self.enabled else 'DISABILITATO'}")
    
    def is_enabled(self):
        """Controlla se lo scheduler è abilitato"""
        return self.enabled
    
    def start(self):
        """Placeholder per compatibilità - non fa nulla"""
        if self.enabled:
            logger.info("Report Scheduler è abilitato ma funziona on-demand")
        else:
            logger.info("Report Scheduler è disabilitato")
    
    def stop(self):
        """Placeholder per compatibilità - non fa nulla"""
        logger.info("Report Scheduler fermato")
    
    def check_and_generate_reports(self):
        """Controlla e genera report schedulati - restituisce il numero di report generati"""
        try:
            with current_app.app_context():
                now = datetime.utcnow()
                
                # Trova report che devono essere generati
                overdue_reports = BIReport.query.filter(
                    BIReport.is_scheduled == True,
                    BIReport.status == 'active',
                    BIReport.next_generation <= now
                ).all()
                
                generated_count = 0
                for report in overdue_reports:
                    try:
                        self.generate_scheduled_report(report)
                        generated_count += 1
                    except Exception as e:
                        logger.error(f"Errore generazione report {report.id}: {e}")
                
                logger.info(f"Controllati {len(overdue_reports)} report schedulati, {generated_count} generati con successo")
                return generated_count
                
        except Exception as e:
            logger.error(f"Errore controllo report schedulati: {e}")
            return 0
    
    def generate_scheduled_report(self, report):
        """Genera un singolo report schedulato"""
        try:
            # Genera il report
            report_data = self.generate_report_data(report)
            
            # Aggiorna timestamp
            report.last_generated = datetime.utcnow()
            report.next_generation = self.calculate_next_generation(
                report.schedule_frequency, 
                report.last_generated
            )
            
            # Salva nel database
            db.session.commit()
            
            # Invia via email se configurato
            if report.schedule_recipients:
                self.send_report_email(report, report_data)
            
            logger.info(f"Report {report.id} '{report.name}' generato con successo")
            
        except Exception as e:
            logger.error(f"Errore generazione report {report.id}: {e}")
            # Segna come in errore ma schedula per il prossimo tentativo
            report.next_generation = datetime.utcnow() + timedelta(hours=1)
            db.session.commit()
    
    def generate_report_data(self, report):
        """Genera i dati del report"""
        if report.report_type == 'skills_analysis':
            return self.generate_skills_analysis_report(report.filters or {})
        elif report.report_type == 'market_analysis':
            return self.generate_market_analysis_report(report.filters or {})
        elif report.report_type == 'performance_metrics':
            return self.generate_performance_metrics_report(report.filters or {})
        elif report.report_type == 'financial_overview':
            return self.generate_financial_overview_report(report.filters or {})
        else:
            return {'message': f'Tipo report {report.report_type} non supportato'}
    
    def generate_skills_analysis_report(self, filters):
        """Genera report analisi competenze"""
        from models import User, Skill, UserSkill, Department
        
        # Query base competenze
        skills_data = db.session.query(
            Skill.name,
            Skill.category,
            db.func.count(UserSkill.id).label('users_count'),
            db.func.avg(UserSkill.proficiency_level).label('avg_proficiency')
        ).join(UserSkill).group_by(Skill.id, Skill.name, Skill.category).all()
        
        return {
            'report_type': 'skills_analysis',
            'generated_at': datetime.utcnow().isoformat(),
            'data': {
                'skills_overview': [
                    {
                        'skill': skill.name,
                        'category': skill.category,
                        'users_count': skill.users_count,
                        'avg_proficiency': round(float(skill.avg_proficiency), 2)
                    }
                    for skill in skills_data
                ],
                'summary': {
                    'total_skills': len(skills_data),
                    'total_skill_assignments': sum(s.users_count for s in skills_data)
                }
            }
        }
    
    def generate_market_analysis_report(self, filters):
        """Genera report analisi mercato"""
        from models import MarketProspect
        
        prospects = MarketProspect.query.all()
        
        return {
            'report_type': 'market_analysis',
            'generated_at': datetime.utcnow().isoformat(),
            'data': {
                'prospects_overview': [
                    {
                        'company_name': p.company_name,
                        'sector': p.sector,
                        'fit_score': p.fit_score,
                        'estimated_budget': (p.estimated_budget_min + p.estimated_budget_max) / 2
                    }
                    for p in prospects
                ],
                'summary': {
                    'total_prospects': len(prospects),
                    'avg_fit_score': sum(p.fit_score for p in prospects) / len(prospects) if prospects else 0,
                    'high_quality_prospects': len([p for p in prospects if p.fit_score >= 8.0])
                }
            }
        }
    
    def generate_performance_metrics_report(self, filters):
        """Genera report metriche performance"""
        from models import Project, TimesheetEntry
        
        # Progetti completati ultimo mese
        last_month = datetime.utcnow() - timedelta(days=30)
        completed_projects = Project.query.filter(
            Project.status == 'completed',
            Project.updated_at >= last_month
        ).count()
        
        # Ore lavorate ultimo mese
        total_hours = db.session.query(db.func.sum(TimesheetEntry.hours)).filter(
            TimesheetEntry.date >= last_month.date()
        ).scalar() or 0
        
        return {
            'report_type': 'performance_metrics',
            'generated_at': datetime.utcnow().isoformat(),
            'data': {
                'period': 'last_30_days',
                'completed_projects': completed_projects,
                'total_hours_worked': float(total_hours),
                'avg_hours_per_project': float(total_hours) / completed_projects if completed_projects > 0 else 0
            }
        }
    
    def generate_financial_overview_report(self, filters):
        """Genera report overview finanziario"""
        from models import Contract, Invoice
        
        # Contratti attivi
        active_contracts = Contract.query.filter_by(status='active').count()
        
        # Fatturato ultimo trimestre
        last_quarter = datetime.utcnow() - timedelta(days=90)
        revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter(
            Invoice.status == 'paid',
            Invoice.created_at >= last_quarter
        ).scalar() or 0
        
        return {
            'report_type': 'financial_overview',
            'generated_at': datetime.utcnow().isoformat(),
            'data': {
                'period': 'last_90_days',
                'active_contracts': active_contracts,
                'total_revenue': float(revenue),
                'avg_monthly_revenue': float(revenue) / 3
            }
        }
    
    def send_report_email(self, report, report_data):
        """Invia report via email"""
        try:
            # Email service non implementato - placeholder
            recipients = report.schedule_recipients.split(',') if report.schedule_recipients else []
            recipients = [email.strip() for email in recipients if email.strip()]
            
            subject = f"Report Automatico: {report.name}"
            
            # Simula invio email (da implementare servizio email reale)
            logger.info(f"Simulazione invio email - Report {report.id} a {len(recipients)} destinatari")
            logger.info(f"Subject: {subject}")
            logger.info(f"Recipients: {', '.join(recipients)}")
            
        except Exception as e:
            logger.error(f"Errore invio email report {report.id}: {e}")
    
    def calculate_next_generation(self, frequency, last_generated):
        """Calcola la prossima data di generazione"""
        if frequency == 'daily':
            return last_generated + timedelta(days=1)
        elif frequency == 'weekly':
            return last_generated + timedelta(weeks=1)
        elif frequency == 'monthly':
            return last_generated + timedelta(days=30)
        elif frequency == 'quarterly':
            return last_generated + timedelta(days=90)
        else:
            return last_generated + timedelta(days=1)  # Default giornaliero
    
    def cleanup_old_reports(self):
        """Pulisce report vecchi (oltre 6 mesi)"""
        try:
            with current_app.app_context():
                cutoff_date = datetime.utcnow() - timedelta(days=180)
                
                # Trova report vecchi non schedulati
                old_reports = BIReport.query.filter(
                    BIReport.is_scheduled == False,
                    BIReport.created_at < cutoff_date,
                    BIReport.status != 'archived'
                ).all()
                
                for report in old_reports:
                    report.status = 'archived'
                
                db.session.commit()
                logger.info(f"Archiviati {len(old_reports)} report vecchi")
                
        except Exception as e:
            logger.error(f"Errore pulizia report vecchi: {e}")

# Istanza globale dello scheduler
report_scheduler = ReportScheduler() 