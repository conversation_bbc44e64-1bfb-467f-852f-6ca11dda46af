"""
OAuth Service for Google and Microsoft Authentication
Handles OAuth flows for enterprise authentication with user linking.
NO auto-registration - only links to pre-existing users.
"""

import requests
import secrets
from datetime import datetime
from urllib.parse import urlencode
from flask import current_app, session
from models import User, OAuthAccount
from extensions import db


class OAuthService:
    """
    Service for handling OAuth authentication flows.
    Supports Google and Microsoft OAuth providers.
    """
    
    @staticmethod
    def get_auth_url(provider):
        """
        Generate OAuth authorization URL for the specified provider.
        
        Args:
            provider (str): OAuth provider ('google' or 'microsoft')
            
        Returns:
            str: OAuth authorization URL
            
        Raises:
            ValueError: If provider is not supported
        """
        if provider == 'google':
            return OAuthService._get_google_auth_url()
        elif provider == 'microsoft':
            return OAuthService._get_microsoft_auth_url()
        else:
            raise ValueError(f'Provider {provider} non supportato')
    
    @staticmethod
    def _get_google_auth_url():
        """Generate Google OAuth authorization URL"""
        state = secrets.token_urlsafe(32)
        session['oauth_state'] = state
        session['oauth_provider'] = 'google'
        
        params = {
            'client_id': current_app.config['GOOGLE_CLIENT_ID'],
            'redirect_uri': current_app.config['OAUTH_REDIRECT_URI'],
            'scope': 'openid email profile',
            'response_type': 'code',
            'state': state,
            'access_type': 'offline',
            'prompt': 'consent'
        }
        
        base_url = 'https://accounts.google.com/o/oauth2/v2/auth'
        return f"{base_url}?{urlencode(params)}"
    
    @staticmethod
    def _get_microsoft_auth_url():
        """Generate Microsoft OAuth authorization URL"""
        state = secrets.token_urlsafe(32)
        session['oauth_state'] = state
        session['oauth_provider'] = 'microsoft'
        
        params = {
            'client_id': current_app.config['MICROSOFT_CLIENT_ID'],
            'redirect_uri': current_app.config['OAUTH_REDIRECT_URI'],
            'scope': 'openid email profile',
            'response_type': 'code',
            'state': state
        }
        
        base_url = 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize'
        return f"{base_url}?{urlencode(params)}"
    
    @staticmethod
    def handle_callback(code, state):
        """
        Handle OAuth callback for both providers.
        
        Args:
            code (str): Authorization code from OAuth provider
            state (str): State parameter for CSRF protection
            
        Returns:
            User: The authenticated user object
            
        Raises:
            ValueError: If state is invalid or user not found
        """
        # Validate state for CSRF protection
        if state != session.get('oauth_state'):
            raise ValueError('Invalid state parameter - possible CSRF attack')
        
        provider = session.get('oauth_provider')
        if not provider:
            raise ValueError('No OAuth provider in session')
        
        # Get user info from provider
        if provider == 'google':
            user_info = OAuthService._get_google_user_info(code)
        elif provider == 'microsoft':
            user_info = OAuthService._get_microsoft_user_info(code)
        else:
            raise ValueError(f'Provider {provider} non supportato')
        
        # Link to existing user (NO auto-registration)
        return OAuthService._link_to_existing_user(provider, user_info)
    
    @staticmethod
    def _get_google_user_info(code):
        """
        Exchange Google authorization code for user info.
        
        Args:
            code (str): Authorization code from Google
            
        Returns:
            dict: User information from Google
            
        Raises:
            ValueError: If token exchange fails
        """
        # Exchange code for access token
        token_url = 'https://oauth2.googleapis.com/token'
        token_data = {
            'client_id': current_app.config['GOOGLE_CLIENT_ID'],
            'client_secret': current_app.config['GOOGLE_CLIENT_SECRET'],
            'code': code,
            'grant_type': 'authorization_code',
            'redirect_uri': current_app.config['OAUTH_REDIRECT_URI']
        }
        
        try:
            token_response = requests.post(token_url, data=token_data, timeout=10)
            token_response.raise_for_status()
            token_info = token_response.json()
        except requests.RequestException as e:
            current_app.logger.error(f"Google token exchange failed: {str(e)}")
            raise ValueError('Impossibile ottenere token da Google')
        
        if 'access_token' not in token_info:
            current_app.logger.error(f"Google token response missing access_token: {token_info}")
            raise ValueError('Token di accesso non ricevuto da Google')
        
        # Get user information
        user_info_url = f"https://www.googleapis.com/oauth2/v2/userinfo?access_token={token_info['access_token']}"
        
        try:
            user_response = requests.get(user_info_url, timeout=10)
            user_response.raise_for_status()
            return user_response.json()
        except requests.RequestException as e:
            current_app.logger.error(f"Google user info request failed: {str(e)}")
            raise ValueError('Impossibile ottenere informazioni utente da Google')
    
    @staticmethod
    def _get_microsoft_user_info(code):
        """
        Exchange Microsoft authorization code for user info.
        
        Args:
            code (str): Authorization code from Microsoft
            
        Returns:
            dict: User information from Microsoft
            
        Raises:
            ValueError: If token exchange fails
        """
        # Exchange code for access token
        token_url = 'https://login.microsoftonline.com/common/oauth2/v2.0/token'
        token_data = {
            'client_id': current_app.config['MICROSOFT_CLIENT_ID'],
            'client_secret': current_app.config['MICROSOFT_CLIENT_SECRET'],
            'code': code,
            'grant_type': 'authorization_code',
            'redirect_uri': current_app.config['OAUTH_REDIRECT_URI']
        }
        
        try:
            token_response = requests.post(token_url, data=token_data, timeout=10)
            token_response.raise_for_status()
            token_info = token_response.json()
        except requests.RequestException as e:
            current_app.logger.error(f"Microsoft token exchange failed: {str(e)}")
            raise ValueError('Impossibile ottenere token da Microsoft')
        
        if 'access_token' not in token_info:
            current_app.logger.error(f"Microsoft token response missing access_token: {token_info}")
            raise ValueError('Token di accesso non ricevuto da Microsoft')
        
        # Get user information
        user_info_url = 'https://graph.microsoft.com/v1.0/me'
        headers = {'Authorization': f"Bearer {token_info['access_token']}"}
        
        try:
            user_response = requests.get(user_info_url, headers=headers, timeout=10)
            user_response.raise_for_status()
            return user_response.json()
        except requests.RequestException as e:
            current_app.logger.error(f"Microsoft user info request failed: {str(e)}")
            raise ValueError('Impossibile ottenere informazioni utente da Microsoft')
    
    @staticmethod
    def _link_to_existing_user(provider, user_info):
        """
        Link OAuth account to existing user ONLY.
        NO auto-registration allowed - security requirement for enterprise.
        
        Args:
            provider (str): OAuth provider name
            user_info (dict): User information from OAuth provider
            
        Returns:
            User: The linked user object
            
        Raises:
            ValueError: If user not found or linking fails
        """
        # Extract email and user ID based on provider
        if provider == 'google':
            email = user_info.get('email')
            provider_user_id = user_info.get('id')
            display_name = user_info.get('name', '')
            avatar_url = user_info.get('picture')
        elif provider == 'microsoft':
            # Microsoft can return email in different fields
            email = user_info.get('mail') or user_info.get('userPrincipalName')
            provider_user_id = user_info.get('id')
            display_name = user_info.get('displayName', '')
            avatar_url = None  # Microsoft Graph API requires separate call for photo
        else:
            raise ValueError(f'Provider {provider} non gestito')
        
        if not email or not provider_user_id:
            current_app.logger.error(f"Missing email or user ID from {provider}: {user_info}")
            raise ValueError('Email e ID utente richiesti dal provider OAuth')
        
        # Check if OAuth account already exists
        oauth_account = OAuthAccount.query.filter_by(
            provider=provider,
            provider_user_id=provider_user_id
        ).first()
        
        if oauth_account:
            # OAuth account already linked - update last login
            oauth_account.last_login = datetime.utcnow()
            db.session.commit()
            current_app.logger.info(f"OAuth login: {provider}:{email} -> User:{oauth_account.user.username}")
            return oauth_account.user
        
        # Look for existing user by email (case insensitive)
        user = User.query.filter(
            db.func.lower(User.email) == db.func.lower(email),
            User.is_active == True
        ).first()
        
        if not user:
            # USER NOT FOUND - NO AUTO-REGISTRATION ALLOWED
            current_app.logger.warning(f"OAuth login attempt for non-existent user: {provider}:{email}")
            raise ValueError(
                f'Nessun utente trovato con email {email}. '
                'Contatta l\'amministratore per essere censito nel sistema.'
            )
        
        # Create OAuth account link to existing user
        oauth_account = OAuthAccount(
            user_id=user.id,
            provider=provider,
            provider_user_id=provider_user_id,
            email=email,
            display_name=display_name,
            avatar_url=avatar_url,
            last_login=datetime.utcnow()
        )
        
        try:
            db.session.add(oauth_account)
            db.session.commit()
            current_app.logger.info(f"OAuth account linked: {provider}:{email} -> User:{user.username}")
            return user
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Failed to link OAuth account: {str(e)}")
            raise ValueError('Errore nel collegamento dell\'account OAuth')
    
    @staticmethod
    def get_user_oauth_accounts(user_id):
        """
        Get all OAuth accounts linked to a user.
        
        Args:
            user_id (int): User ID
            
        Returns:
            list: List of OAuthAccount objects
        """
        return OAuthAccount.query.filter_by(user_id=user_id).all()
    
    @staticmethod
    def unlink_oauth_account(account_id, user_id=None):
        """
        Unlink an OAuth account.
        
        Args:
            account_id (int): OAuth account ID
            user_id (int, optional): User ID for authorization check
            
        Returns:
            bool: True if successfully unlinked
            
        Raises:
            ValueError: If account not found or unauthorized
        """
        oauth_account = OAuthAccount.query.get(account_id)
        if not oauth_account:
            raise ValueError('Account OAuth non trovato')
        
        # Authorization check if user_id provided
        if user_id and oauth_account.user_id != user_id:
            raise ValueError('Non autorizzato a scollegare questo account')
        
        try:
            provider = oauth_account.provider
            email = oauth_account.email
            username = oauth_account.user.username
            
            db.session.delete(oauth_account)
            db.session.commit()
            
            current_app.logger.info(f"OAuth account unlinked: {provider}:{email} from User:{username}")
            return True
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Failed to unlink OAuth account: {str(e)}")
            raise ValueError('Errore nello scollegamento dell\'account OAuth')