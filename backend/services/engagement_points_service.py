"""
Service per generazione automatica punti engagement da audit logs
Trasforma attività di compliance in punti gamification con regole configurabili
"""

from datetime import datetime, timedelta
from sqlalchemy import func, and_, or_
from extensions import db
from models import (
    ComplianceAuditLog, EngagementPoint, EngagementUserProfile, 
    EngagementCampaign, EngagementLevel, User
)

class EngagementPointsService:
    """Service per generazione e gestione punti engagement"""
    
    # Configurazione base punti per azione
    BASE_POINTS_CONFIG = {
        'login': 5,
        'create': 10,
        'update': 8,
        'delete': 5,
        'view': 2,
        'approve': 15,
        'review': 12,
        'submit': 10,
        'complete': 20
    }
    
    # Moltiplicatori per risorsa
    RESOURCE_MULTIPLIERS = {
        'project': 1.5,
        'contract': 1.3,
        'proposal': 1.4,
        'timesheet': 1.0,
        'certification': 2.0,
        'client': 1.2,
        'task': 1.1,
        'default': 1.0
    }
    
    def __init__(self):
        self.processed_logs = set()
    
    def process_audit_logs_for_points(self, hours_back=24):
        """
        Processa gli audit logs delle ultime N ore per generare punti
        
        Args:
            hours_back (int): Ore indietro da processare (default 24)
            
        Returns:
            dict: Statistiche del processamento
        """
        stats = {
            'logs_processed': 0,
            'points_generated': 0,
            'users_affected': 0,
            'new_points_total': 0,
            'errors': []
        }
        
        try:
            # Calcola timestamp di inizio
            cutoff_time = datetime.utcnow() - timedelta(hours=hours_back)
            
            # Query audit logs non ancora processati
            unprocessed_logs = ComplianceAuditLog.query.filter(
                and_(
                    ComplianceAuditLog.timestamp >= cutoff_time,
                    ~ComplianceAuditLog.id.in_(
                        db.session.query(EngagementPoint.source_id).filter(
                            EngagementPoint.source_type == 'audit_log'
                        )
                    )
                )
            ).order_by(ComplianceAuditLog.timestamp.asc()).all()
            
            stats['logs_processed'] = len(unprocessed_logs)
            users_with_points = set()
            
            for log in unprocessed_logs:
                try:
                    points_awarded = self._generate_points_from_log(log)
                    if points_awarded > 0:
                        stats['points_generated'] += 1
                        stats['new_points_total'] += points_awarded
                        users_with_points.add(log.user_id)
                        
                except Exception as e:
                    stats['errors'].append(f"Error processing log {log.id}: {str(e)}")
            
            stats['users_affected'] = len(users_with_points)
            
            # Aggiorna livelli per utenti che hanno ricevuto punti
            for user_id in users_with_points:
                self._update_user_level(user_id)
            
            db.session.commit()
            
        except Exception as e:
            db.session.rollback()
            stats['errors'].append(f"General error: {str(e)}")
        
        return stats
    
    def _generate_points_from_log(self, audit_log):
        """
        Genera punti da un singolo audit log
        
        Args:
            audit_log (ComplianceAuditLog): Log da processare
            
        Returns:
            int: Punti assegnati
        """
        # Calcola punti base dall'azione
        base_points = self.BASE_POINTS_CONFIG.get(audit_log.action_type, 5)
        
        # Applica moltiplicatore per risorsa
        resource_multiplier = self.RESOURCE_MULTIPLIERS.get(
            audit_log.resource_type, 
            self.RESOURCE_MULTIPLIERS['default']
        )
        
        # Calcola punti finali
        final_points = int(base_points * resource_multiplier)
        
        # Controlla campagne attive per moltiplicatori extra
        campaign_multiplier = self._get_active_campaign_multiplier(audit_log.timestamp)
        final_points = int(final_points * campaign_multiplier)
        
        # Bonus streak per attività consecutive
        streak_bonus = self._calculate_streak_bonus(audit_log.user_id, audit_log.timestamp)
        final_points += streak_bonus
        
        # Crea record del punto
        engagement_point = EngagementPoint(
            user_id=audit_log.user_id,
            points_earned=final_points,
            action_type=audit_log.action_type,
            resource_type=audit_log.resource_type,
            resource_id=audit_log.resource_id,
            source_type='audit_log',
            source_id=audit_log.id,
            description=f"{audit_log.action_type.title()} {audit_log.resource_type}",
            earned_at=audit_log.timestamp,
            campaign_id=self._get_active_campaign_id(audit_log.timestamp),
            multiplier_applied=campaign_multiplier
        )
        
        db.session.add(engagement_point)
        
        # Aggiorna profilo utente
        self._update_user_profile_points(audit_log.user_id, final_points)
        
        return final_points
    
    def _get_active_campaign_multiplier(self, timestamp):
        """Ottieni moltiplicatore da campagne attive al momento dell'azione"""
        active_campaign = EngagementCampaign.query.filter(
            and_(
                EngagementCampaign.status == 'active',
                EngagementCampaign.start_date <= timestamp.date(),
                EngagementCampaign.end_date >= timestamp.date()
            )
        ).first()
        
        return active_campaign.points_multiplier if active_campaign else 1.0
    
    def _get_active_campaign_id(self, timestamp):
        """Ottieni ID campagna attiva al momento dell'azione"""
        active_campaign = EngagementCampaign.query.filter(
            and_(
                EngagementCampaign.status == 'active',
                EngagementCampaign.start_date <= timestamp.date(),
                EngagementCampaign.end_date >= timestamp.date()
            )
        ).first()
        
        return active_campaign.id if active_campaign else None
    
    def _calculate_streak_bonus(self, user_id, current_timestamp):
        """Calcola bonus per streak di attività consecutive"""
        # Controlla se l'utente ha avuto attività nel giorno precedente
        yesterday = current_timestamp.date() - timedelta(days=1)
        
        has_yesterday_activity = EngagementPoint.query.filter(
            and_(
                EngagementPoint.user_id == user_id,
                func.date(EngagementPoint.earned_at) == yesterday
            )
        ).first() is not None
        
        if has_yesterday_activity:
            # Calcola lunghezza streak
            streak_days = self._calculate_user_streak(user_id, current_timestamp.date())
            
            # Bonus escalante: 1 punto ogni 3 giorni di streak
            return max(0, (streak_days // 3) * 1)
        
        return 0
    
    def _calculate_user_streak(self, user_id, current_date):
        """Calcola la lunghezza dello streak corrente"""
        streak_days = 0
        check_date = current_date
        
        # Controlla all'indietro per giorni consecutivi con attività
        while streak_days < 30:  # Limite massimo controllo 30 giorni
            has_activity = EngagementPoint.query.filter(
                and_(
                    EngagementPoint.user_id == user_id,
                    func.date(EngagementPoint.earned_at) == check_date
                )
            ).first() is not None
            
            if has_activity:
                streak_days += 1
                check_date -= timedelta(days=1)
            else:
                break
        
        return streak_days
    
    def _update_user_profile_points(self, user_id, points_to_add):
        """Aggiorna il profilo utente con i nuovi punti"""
        user_profile = EngagementUserProfile.query.filter_by(user_id=user_id).first()
        
        if not user_profile:
            user_profile = self._create_user_profile(user_id)
        
        user_profile.total_points += points_to_add
        user_profile.available_points += points_to_add
        user_profile.last_activity_date = datetime.utcnow().date()
        
        # Aggiorna streak
        user_profile.streak_days = self._calculate_user_streak(user_id, datetime.utcnow().date())
    
    def _update_user_level(self, user_id):
        """Aggiorna il livello dell'utente basandosi sui punti totali"""
        user_profile = EngagementUserProfile.query.filter_by(user_id=user_id).first()
        if not user_profile:
            return
        
        # Trova il livello appropriato per i punti correnti
        appropriate_level = EngagementLevel.query.filter(
            EngagementLevel.points_required <= user_profile.total_points
        ).order_by(EngagementLevel.points_required.desc()).first()
        
        if appropriate_level and appropriate_level.id != user_profile.current_level_id:
            old_level_id = user_profile.current_level_id
            user_profile.current_level_id = appropriate_level.id
            
            # Trova prossimo livello
            next_level = EngagementLevel.query.filter(
                EngagementLevel.points_required > user_profile.total_points
            ).order_by(EngagementLevel.points_required.asc()).first()
            
            user_profile.next_level_id = next_level.id if next_level else None
            
            # Log level up (se non è il primo livello)
            if old_level_id:
                self._create_level_up_notification(user_id, appropriate_level)
    
    def _create_user_profile(self, user_id):
        """Crea un nuovo profilo engagement per l'utente"""
        first_level = EngagementLevel.query.filter_by(level_order=1).first()
        next_level = EngagementLevel.query.filter_by(level_order=2).first()
        
        user_profile = EngagementUserProfile(
            user_id=user_id,
            total_points=0,
            available_points=0,
            current_level_id=first_level.id if first_level else None,
            next_level_id=next_level.id if next_level else None,
            last_activity_date=datetime.utcnow().date(),
            streak_days=0
        )
        
        db.session.add(user_profile)
        return user_profile
    
    def _create_level_up_notification(self, user_id, new_level):
        """Crea notifica per level up (da implementare con sistema notifiche)"""
        # TODO: Integrare con sistema notifiche quando disponibile
        pass
    
    def recalculate_all_points(self, user_id=None):
        """
        Ricalcola tutti i punti da zero (operazione pesante)
        
        Args:
            user_id (int, optional): Se specificato, ricalcola solo per questo utente
        """
        try:
            # Se user_id specificato, pulisci solo per quell'utente
            if user_id:
                EngagementPoint.query.filter_by(
                    user_id=user_id, 
                    source_type='audit_log'
                ).delete()
                
                user_profile = EngagementUserProfile.query.filter_by(user_id=user_id).first()
                if user_profile:
                    user_profile.total_points = 0
                    user_profile.available_points = 0
            else:
                # Pulisci tutti i punti da audit logs
                EngagementPoint.query.filter_by(source_type='audit_log').delete()
                
                # Reset profili utente
                EngagementUserProfile.query.update({
                    EngagementUserProfile.total_points: 0,
                    EngagementUserProfile.available_points: 0
                })
            
            db.session.commit()
            
            # Riprocessa tutti gli audit logs
            if user_id:
                logs_to_process = ComplianceAuditLog.query.filter_by(user_id=user_id).all()
            else:
                logs_to_process = ComplianceAuditLog.query.all()
            
            total_points = 0
            for log in logs_to_process:
                points = self._generate_points_from_log(log)
                total_points += points
            
            # Aggiorna livelli
            if user_id:
                self._update_user_level(user_id)
            else:
                user_profiles = EngagementUserProfile.query.all()
                for profile in user_profiles:
                    self._update_user_level(profile.user_id)
            
            db.session.commit()
            
            return {
                'success': True,
                'logs_processed': len(logs_to_process),
                'total_points_awarded': total_points
            }
            
        except Exception as e:
            db.session.rollback()
            return {
                'success': False,
                'error': str(e)
            }