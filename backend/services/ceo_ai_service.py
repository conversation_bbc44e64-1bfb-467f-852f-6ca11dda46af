"""
Human CEO AI Service - Strategic intelligence e business analysis
Integra OpenAI e Perplexity per fornire insights strategici reali
"""

import os
import json
import logging
import asyncio
import httpx
from datetime import datetime
from typing import Dict, List, Optional, Union
from flask import current_app

logger = logging.getLogger(__name__)

class CEOAIService:
    """
    Servizio AI per Human CEO module.
    Gestisce sia Sonar Pro che Sonar Deep research modes.
    """
    
    def __init__(self):
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        self.perplexity_api_key = os.getenv('PERPLEXITY_API_KEY')
        self.openai_base_url = "https://api.openai.com/v1"
        self.perplexity_base_url = "https://api.perplexity.ai"
        
    async def generate_ceo_response(self, query: str, context: Dict, mode: str = 'pro') -> Dict:
        """
        Genera response strategica basata su query e contesto aziendale.
        
        Args:
            query: <PERSON><PERSON> del CEO
            context: Contesto aziendale e dati business
            mode: 'pro' per quick insights, 'deep' per comprehensive analysis
            
        Returns:
            Dict con response, confidence, sources, action_items
        """
        try:
            if mode == 'pro':
                return await self._generate_pro_response(query, context)
            else:
                return await self._generate_deep_response(query, context)
                
        except Exception as e:
            logger.error(f"❌ CEO AI SERVICE ERROR: {str(e)}")
            logger.error(f"❌ Unable to generate CEO insights - API configuration required")
            raise Exception(f"CEO AI service failed: {str(e)}")
    
    async def _generate_pro_response(self, query: str, context: Dict) -> Dict:
        """Genera quick strategic insights (Sonar Pro mode)."""
        try:
            start_time = datetime.utcnow()
            
            logger.info(f"🚀 SONAR PRO MODE: Quick strategic analysis for '{query}'")
            
            # Costruisci prompt ottimizzato per response veloci
            system_prompt = self._build_pro_system_prompt(context)
            user_prompt = f"""
            CEO Strategic Query: {query}
            
            Provide a concise, executive-ready response in markdown format with:
            1. Key Strategic Insights (2-3 critical points)
            2. Immediate Actions (concrete steps for next 30 days)
            3. Strategic Focus (recommended strategic direction)
            
            Use emojis for visual hierarchy and keep response under 500 words.
            Format as: ⚡ **SONAR PRO - [ANALYSIS TYPE]**
            """
            
            # Use configured model for Pro mode
            ceo_pro_model = current_app.config.get('OPENAI_CEO_PRO_MODEL', 'gpt-4o-mini')
            response = await self._call_openai(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                max_tokens=800,
                temperature=0.7,
                model=ceo_pro_model
            )
            
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            
            logger.info(f"✅ SONAR PRO COMPLETED: {processing_time:.1f}s processing time")
            
            return {
                'content': response,
                'confidence': 0.92,
                'sources': [f'OpenAI {ceo_pro_model}', 'DatPortal Business Intelligence'],
                'action_items': self._extract_action_items(response),
                'processing_time': round(processing_time, 1),
                'mode': 'pro'
            }
            
        except Exception as e:
            logger.error(f"❌ SONAR PRO ERROR: {str(e)}")
            raise Exception(f"Sonar Pro analysis failed: {str(e)}")
    
    async def _generate_deep_response(self, query: str, context: Dict) -> Dict:
        """Genera comprehensive analysis (Sonar Deep mode)."""
        try:
            start_time = datetime.utcnow()
            
            logger.info(f"🔬 SONAR DEEP MODE: Comprehensive strategic analysis for '{query}'")
            
            # Step 1: Market research con Perplexity Sonar Deep
            logger.info("🔍 STEP 1: Market intelligence gathering via Perplexity Sonar Deep")
            market_research = await self._perplexity_market_research(query, context, mode='deep')
            
            # Step 2: Strategic analysis con OpenAI (usando modello più avanzato per deep analysis)
            logger.info("🧠 STEP 2: Strategic analysis with OpenAI")
            strategic_analysis = await self._openai_strategic_analysis(query, context, market_research)
            
            # Step 3: Combina insights per final response usando o3-mini se disponibile
            logger.info("🎯 STEP 3: Synthesis and final strategic briefing")
            final_response = await self._synthesize_deep_response(query, context, market_research, strategic_analysis)
            
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            
            logger.info(f"✅ SONAR DEEP COMPLETED: {processing_time:.1f}s total processing time")
            
            return {
                'content': final_response,
                'confidence': 0.96,
                'sources': ['Perplexity Sonar Intelligence', 'OpenAI Strategic Analysis', 'DatPortal Business Data'],
                'action_items': self._extract_action_items(final_response),
                'processing_time': round(processing_time, 1),
                'mode': 'deep',
                'market_research': market_research,
                'strategic_analysis': strategic_analysis
            }
            
        except Exception as e:
            logger.error(f"❌ SONAR DEEP ERROR: {str(e)}")
            raise Exception(f"Sonar Deep analysis failed: {str(e)}")
    
    async def _call_openai(self, system_prompt: str, user_prompt: str, max_tokens: int = 1500, temperature: float = 0.7, model: str = None) -> str:
        """Effettua chiamata OpenAI API con supporto per modelli avanzati."""
        if not self.openai_api_key:
            logger.error("⚠️ OPENAI API KEY NOT CONFIGURED")
            raise ValueError("OpenAI API key not configured. CEO insights require OpenAI API access.")
        
        # Seleziona modello in base al tipo di task
        if model is None:
            model = "gpt-4o-mini"  # Default for quick tasks
        
        logger.info(f"🤖 OPENAI CALL: Using model {model} for strategic analysis")
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.openai_base_url}/chat/completions",
                    headers={
                        "Authorization": f"Bearer {self.openai_api_key}",
                        "Content-Type": "application/json"
                    },
                    json={
                        "model": model,
                        "messages": [
                            {"role": "system", "content": system_prompt},
                            {"role": "user", "content": user_prompt}
                        ],
                        "max_tokens": max_tokens,
                        "temperature": temperature
                    },
                    timeout=60.0  # Increased timeout for o3-mini
                )
                
                if response.status_code != 200:
                    logger.error(f"❌ OPENAI API ERROR: {response.status_code} - {response.text}")
                    raise Exception(f"OpenAI API error: {response.status_code} - {response.text}")
                
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                logger.info(f"✅ OPENAI RESPONSE: {len(content)} characters generated with {model}")
                return content
                
        except Exception as e:
            logger.error(f"❌ OPENAI CALL FAILED: {str(e)}")
            raise Exception(f"OpenAI strategic analysis failed: {str(e)}")
    
    async def _perplexity_market_research(self, query: str, context: Dict, mode: str = 'pro') -> str:
        """Effettua market research con Perplexity Sonar."""
        if not self.perplexity_api_key:
            logger.error("⚠️ PERPLEXITY API KEY NOT CONFIGURED")
            logger.error("⚠️ CEO insights require Perplexity API key for real-time market intelligence")
            raise ValueError("Perplexity API key not configured. CEO insights require real-time market intelligence.")
        
        try:
            company_industry = context.get('company_profile', {}).get('industry', 'Technology')
            company_name = context.get('company_profile', {}).get('name', 'Company')
            
            # Seleziona modello in base alla modalità
            if mode == 'deep':
                sonar_model = current_app.config.get('PERPLEXITY_SONAR_DEEP_MODEL', 'sonar-deep-research')
                max_tokens = 4000
                logger.info(f"🔬 SONAR DEEP RESEARCH: Using {sonar_model} for comprehensive market intelligence")
            else:
                sonar_model = current_app.config.get('PERPLEXITY_SONAR_MODEL', 'sonar-pro')
                max_tokens = 2000
                logger.info(f"⚡ SONAR PRO RESEARCH: Using {sonar_model} for quick market intelligence")
            
            logger.info(f"🔍 SONAR RESEARCH: Starting market intelligence for {company_industry} industry")
            
            if mode == 'deep':
                research_query = f"""
                Deep market intelligence analysis for {company_industry} company ({company_name}) regarding: {query}
                
                Comprehensive research areas:
                - Current market trends and dynamics (2024-2025) with specific data
                - Competitive landscape analysis with company names, market shares, and financial performance
                - Growth opportunities and market threats with quantified impact
                - Industry best practices and benchmarks with comparative metrics
                - Financial KPIs and performance metrics with industry averages
                - Regulatory changes and market disruptions with timeline and impact assessment
                - Investment patterns and M&A activity with deal values and frequencies
                - Technology adoption trends with adoption rates and timeline
                - Market sizing and segmentation with TAM/SAM/SOM analysis
                - Customer behavior and preference shifts with supporting data
                
                Provide comprehensive data-driven insights with specific metrics, percentages, financial figures, and sources.
                Include publication dates and credibility assessment for all claims.
                Focus on actionable intelligence for strategic decision-making.
                """
            else:
                research_query = f"""
                Quick market intelligence analysis for {company_industry} company ({company_name}) regarding: {query}
                
                Key research areas:
                - Current market trends and dynamics (2024-2025)
                - Competitive landscape analysis with key players
                - Growth opportunities and immediate threats
                - Industry best practices and benchmarks
                - Financial KPIs and performance indicators
                - Recent regulatory changes and market shifts
                - Investment trends and funding activity
                - Technology adoption patterns
                
                Provide concise data-driven insights with key metrics, percentages, and financial figures.
                Include sources and focus on immediate strategic implications.
                """
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.perplexity_base_url}/chat/completions",
                    headers={
                        "Authorization": f"Bearer {self.perplexity_api_key}",
                        "Content-Type": "application/json"
                    },
                    json={
                        "model": sonar_model,
                        "messages": [
                            {"role": "user", "content": research_query}
                        ],
                        "max_tokens": max_tokens,
                        "temperature": 0.3
                    },
                    timeout=60.0  # Increased timeout for deep research
                )
                
                if response.status_code != 200:
                    logger.error(f"❌ PERPLEXITY API ERROR: {response.status_code} - {response.text}")
                    raise Exception(f"Perplexity API error: {response.status_code}")
                
                result = response.json()
                research_content = result['choices'][0]['message']['content']
                
                logger.info(f"✅ SONAR RESEARCH COMPLETED: {len(research_content)} characters of market intelligence using {sonar_model}")
                return research_content
                
        except Exception as e:
            logger.error(f"❌ PERPLEXITY RESEARCH FAILED: {str(e)}")
            raise Exception(f"Market research failed: {str(e)}")
    
    async def _openai_strategic_analysis(self, query: str, context: Dict, market_research: str) -> str:
        """Genera strategic analysis con OpenAI basato su market research."""
        system_prompt = f"""
        You are a strategic business consultant analyzing data for a CEO.
        
        Company Context:
        - Industry: {context.get('company_profile', {}).get('industry', 'Technology')}
        - Mission: {context.get('company_profile', {}).get('mission', 'Not specified')}
        - Business Metrics: {json.dumps(context.get('business_metrics', {}), indent=2)}
        
        Your role is to provide strategic recommendations based on:
        1. The company's current performance data
        2. Market research insights
        3. Industry best practices
        4. Risk assessment and mitigation strategies
        """
        
        user_prompt = f"""
        CEO Question: {query}
        
        Market Research Insights:
        {market_research}
        
        Based on the market research and company data, provide a comprehensive strategic analysis with:
        
        1. Strategic Context Assessment
        2. Market Position Analysis  
        3. Strategic Options Matrix (with risk/return analysis)
        4. Recommended Strategic Path (with timeline and phases)
        5. Success Metrics & KPIs
        6. Risk Assessment & Mitigation
        7. Decision Framework
        
        Format in clear markdown with specific, actionable recommendations.
        """
        
        # Use configured model for strategic analysis
        analysis_model = current_app.config.get('OPENAI_CEO_PRO_MODEL', 'gpt-4o-mini')
        return await self._call_openai(
            system_prompt, 
            user_prompt, 
            max_tokens=2500, 
            temperature=0.6, 
            model=analysis_model
        )
    
    async def _synthesize_deep_response(self, query: str, context: Dict, market_research: str, strategic_analysis: str) -> str:
        """Sintetizza market research e strategic analysis in una response finale usando o3-mini se disponibile."""
        synthesis_prompt = f"""
        Synthesize the following research and analysis into a comprehensive CEO strategic briefing:
        
        Original CEO Question: {query}
        
        Market Intelligence (Perplexity Sonar):
        {market_research}
        
        Strategic Analysis (OpenAI):
        {strategic_analysis}
        
        Create a final executive briefing in markdown format with:
        
        # 🔬 SONAR DEEP - COMPREHENSIVE STRATEGIC ANALYSIS
        
        ## Executive Summary
        ## Market Intelligence  
        ## Strategic Recommendations
        ## Implementation Roadmap
        ## Risk Assessment
        ## Success Metrics
        
        Include specific timelines, investment estimates, and measurable outcomes.
        Use professional formatting with appropriate emoji for visual hierarchy.
        This is the final synthesis for a CEO - make it actionable and data-driven.
        """
        
        # Try configured deep model for synthesis
        deep_model = current_app.config.get('OPENAI_CEO_DEEP_MODEL', 'o3-mini')
        fallback_model = current_app.config.get('OPENAI_CEO_PRO_MODEL', 'gpt-4o-mini')
        
        try:
            logger.info(f"🧠 SYNTHESIS: Using {deep_model} for final strategic synthesis")
            return await self._call_openai(
                system_prompt="You are an executive briefing specialist. Create polished, executive-ready strategic briefings with deep analytical insights.",
                user_prompt=synthesis_prompt,
                max_tokens=3000,
                temperature=0.5,
                model=deep_model
            )
        except Exception as e:
            logger.warning(f"⚠️ {deep_model} not available, using {fallback_model} for synthesis: {str(e)}")
            return await self._call_openai(
                system_prompt="You are an executive briefing specialist. Create polished, executive-ready strategic briefings.",
                user_prompt=synthesis_prompt,
                max_tokens=3000,
                temperature=0.5,
                model=fallback_model
            )
    
    def _build_pro_system_prompt(self, context: Dict) -> str:
        """Costruisce system prompt ottimizzato per Sonar Pro mode."""
        return f"""
        You are a strategic AI assistant for a CEO providing quick, actionable business insights.
        
        Company Profile:
        - Industry: {context.get('company_profile', {}).get('industry', 'Technology')}
        - Mission: {context.get('company_profile', {}).get('mission', 'Innovation-focused growth')}
        
        Current Business Metrics:
        {json.dumps(context.get('business_metrics', {}), indent=2)}
        
        Your responses should be:
        - Concise and actionable (under 500 words)
        - Formatted in markdown with emoji
        - Focused on immediate next steps
        - Based on current business data
        - Optimized for busy executives
        
        Use this format:
        ⚡ **SONAR PRO - [ANALYSIS TYPE]**
        
        *Quick strategic overview*
        
        **🎯 Key Insights:**
        **⚡ Immediate Actions:**
        **💡 Strategic Focus:**
        
        *Processing time: Xm Xs | Confidence: X%*
        """
    
    # Removed _simulate_market_research - CEO insights must use real APIs
    
    def _extract_action_items(self, response: str) -> List[str]:
        """Estrae action items dalla response."""
        action_items = []
        lines = response.split('\n')
        
        in_actions_section = False
        for line in lines:
            line = line.strip()
            
            # Identifica sezioni di azioni
            if any(keyword in line.lower() for keyword in ['immediate actions', 'next steps', 'action plan', 'recommendations']):
                in_actions_section = True
                continue
                
            # Estrai azioni numerati o con bullet points
            if in_actions_section and line:
                if line.startswith(('1.', '2.', '3.', '•', '-', '*')):
                    # Pulisci il testo dell'azione
                    action = line
                    for prefix in ['1.', '2.', '3.', '4.', '5.', '•', '-', '*']:
                        if action.startswith(prefix):
                            action = action[len(prefix):].strip()
                            break
                    
                    if action and len(action) > 10:  # Filtro azioni troppo corte
                        action_items.append(action)
                
                # Stop dopo una nuova sezione
                elif line.startswith('#') or line.startswith('**'):
                    in_actions_section = False
        
        return action_items[:5]  # Limita a 5 action items max
    
    # Removed _get_fallback_response - CEO insights must use real APIs


# Utility functions per integration
def get_ceo_ai_service() -> CEOAIService:
    """Factory function per CEO AI Service."""
    return CEOAIService()

async def process_ceo_query(query: str, context: Dict, mode: str = 'pro') -> Dict:
    """
    Processa query CEO in modo asincrono.
    Wrapper principale per l'integrazione con Flask routes.
    """
    service = get_ceo_ai_service()
    return await service.generate_ceo_response(query, context, mode)