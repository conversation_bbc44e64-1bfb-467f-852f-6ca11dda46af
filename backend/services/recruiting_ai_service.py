"""
Recruiting AI Service - AI-powered recruiting functionality.
Provides intelligent assistance for job posting creation, candidate evaluation,
resume enhancement, and interview question generation.
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any

from services.ai import ai_service
from models import JobPosting, Candidate, Application, User, Department, Project

logger = logging.getLogger(__name__)


class RecruitingAIService:
    """AI service for recruiting-specific functionality."""
    
    def __init__(self):
        self.ai_service = ai_service
    
    def generate_job_posting(self, prompt_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate job posting content using AI based on user input.
        
        Args:
            prompt_data: Dict containing:
                - role_type: str (Technical, Management, Sales, etc.)
                - experience_level: str (Junior, Mid, Senior, Executive) 
                - company_context: str (optional)
                - custom_prompt: str (optional)
                - department_id: int (optional)
                - reference_job_id: int (optional for template)
        
        Returns:
            Dict with generated content and metadata
        """
        try:
            # Build context from existing data
            context_parts = []
            
            # Add company context if available
            if prompt_data.get('company_context'):
                context_parts.append(f"Contesto aziendale: {prompt_data['company_context']}")
            
            # Add department context
            if prompt_data.get('department_id'):
                from models import Department
                dept = Department.query.get(prompt_data['department_id'])
                if dept:
                    context_parts.append(f"Dipartimento: {dept.name}")
            
            # Add reference job template
            reference_context = ""
            if prompt_data.get('reference_job_id'):
                ref_job = JobPosting.query.get(prompt_data['reference_job_id'])
                if ref_job:
                    reference_context = f"""
ESEMPIO POSIZIONE ESISTENTE (usa come riferimento per struttura e stile):
Titolo: {ref_job.title}
Descrizione: {ref_job.description[:500] if ref_job.description else 'N/A'}
Requisiti: {ref_job.requirements[:300] if ref_job.requirements else 'N/A'}
"""
            
            # Build the main prompt
            role_type = prompt_data.get('role_type', 'Generico')
            experience_level = prompt_data.get('experience_level', 'Mid')
            custom_prompt = prompt_data.get('custom_prompt', '')
            
            context = "\n".join(context_parts) if context_parts else ""
            
            system_prompt = f"""Sei un esperto HR specializzato nella creazione di job posting professionali e accattivanti per il mercato del lavoro italiano.

PARAMETRI RICHIESTI:
- Tipo di ruolo: {role_type}
- Livello esperienza: {experience_level}
- Richiesta specifica: {custom_prompt}

{context}

{reference_context}

COMPITO:
Genera un job posting completo e professionale in italiano che includa:

1. TITOLO: Breve e specifico (max 60 caratteri)
2. DESCRIZIONE: Panoramica coinvolgente dell'opportunità (150-200 parole)
3. REQUISITI: Lista dettagliata dei requisiti tecnici e soft skills
4. RESPONSABILITÀ: Compiti principali e obiettivi del ruolo
5. BENEFIT SUGGERITI: Vantaggi tipici per questo tipo di posizione

LINEE GUIDA:
- Usa un tono professionale ma accogliente
- Includi competenze tecniche specifiche per il ruolo
- Adatta il linguaggio al livello di esperienza richiesto
- Menziona crescita professionale e opportunità di sviluppo
- Considera il mercato del lavoro italiano e le aspettative locali

Rispondi SOLO con un JSON valido con questa struttura:
{{
    "title": "Titolo della posizione",
    "description": "Descrizione completa della posizione e dell'azienda",
    "requirements": "Requisiti tecnici e competenze richieste",
    "responsibilities": "Responsabilità principali e obiettivi del ruolo",
    "suggested_benefits": "Benefit e vantaggi consigliati per attrarre candidati",
    "ai_metadata": {{
        "generated_at": "{datetime.utcnow().isoformat()}",
        "role_type": "{role_type}",
        "experience_level": "{experience_level}",
        "confidence_score": 0.95
    }}
}}"""

            # Call AI service
            ai_response = self.ai_service.query_openai(
                system_prompt,
                model="gpt-4o-mini",
                max_tokens=1500,
                temperature=0.7
            )
            
            if not ai_response.get('success'):
                return {
                    'success': False,
                    'error': ai_response.get('error', 'AI service error'),
                    'generated_content': None
                }
            
            # Parse AI response
            ai_content = ai_response.get('response', '')
            try:
                # Extract JSON from response
                if '```json' in ai_content:
                    ai_content = ai_content.split('```json')[1].split('```')[0].strip()
                elif '{' in ai_content:
                    start = ai_content.find('{')
                    end = ai_content.rfind('}') + 1
                    ai_content = ai_content[start:end]
                
                generated_content = json.loads(ai_content)
                
                return {
                    'success': True,
                    'generated_content': generated_content,
                    'prompt_data': prompt_data,
                    'ai_model': ai_response.get('model'),
                    'generated_at': datetime.utcnow().isoformat()
                }
                
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse AI response as JSON: {e}")
                return {
                    'success': False,
                    'error': f'Errore nel parsing della risposta AI: {str(e)}',
                    'raw_response': ai_content
                }
            
        except Exception as e:
            logger.error(f"Error in generate_job_posting: {str(e)}")
            return {
                'success': False,
                'error': f'Errore nella generazione: {str(e)}'
            }
    
    def evaluate_candidate_match(self, application_id: int, force_refresh: bool = False) -> Dict[str, Any]:
        """
        Evaluate how well a candidate matches a job posting using AI.
        
        Args:
            application_id: ID of the application to evaluate
            force_refresh: Whether to force a new evaluation even if one exists
            
        Returns:
            Dict with evaluation scores and reasoning
        """
        try:
            application = Application.query.get(application_id)
            if not application:
                return {
                    'success': False,
                    'error': 'Candidatura non trovata'
                }
            
            job_posting = application.job_posting
            candidate = application.candidate
            
            # Build candidate profile summary
            candidate_info = f"""
CANDIDATO: {candidate.full_name}
Email: {candidate.email}
Telefono: {candidate.phone or 'N/A'}
Località: {candidate.location or 'N/A'}
Fonte: {candidate.source or 'N/A'}
Lettera di motivazione: {application.cover_letter or 'N/A'}
"""
            
            # Add candidate skills if available
            skills_summary = "Competenze non specificate"
            if candidate.skills:
                skills_list = [skill.skill_name for skill in candidate.skills]
                skills_summary = ", ".join(skills_list) if skills_list else "Competenze non specificate"
            
            candidate_info += f"Competenze identificate: {skills_summary}\n"
            
            # Build job requirements
            job_info = f"""
POSIZIONE: {job_posting.title}
Descrizione: {job_posting.description or 'N/A'}
Requisiti: {job_posting.requirements or 'N/A'}
Responsabilità: {job_posting.responsibilities or 'N/A'}
Tipo di impiego: {job_posting.employment_type or 'N/A'}
Località: {job_posting.location or 'N/A'}
Remote consentito: {'Sì' if job_posting.remote_allowed else 'No'}
"""
            
            evaluation_prompt = f"""Sei un esperto HR specializzato nella valutazione di candidati. Analizza la compatibilità tra questo candidato e la posizione lavorativa.

{candidate_info}

{job_info}

COMPITO:
Valuta la compatibilità del candidato con la posizione e fornisci una valutazione strutturata.

CRITERI DI VALUTAZIONE:
1. COMPETENZE TECNICHE (30%): Allineamento tra skills richieste e possedute
2. ESPERIENZA (25%): Livello e rilevanza dell'esperienza professionale
3. MOTIVAZIONE (20%): Qualità della lettera di motivazione e interesse dimostrato
4. CULTURAL FIT (15%): Compatibilità con l'ambiente di lavoro e valori aziendali
5. CRESCITA POTENZIALE (10%): Capacità di apprendimento e sviluppo

Rispondi SOLO con un JSON valido:
{{
    "overall_score": 85,
    "scores": {{
        "technical_skills": 80,
        "experience": 75,
        "motivation": 90,
        "cultural_fit": 85,
        "growth_potential": 90
    }},
    "strengths": [
        "Lista dei punti di forza del candidato"
    ],
    "concerns": [
        "Lista delle possibili criticità o mancanze"
    ],
    "reasoning": "Spiegazione dettagliata del punteggio e valutazione complessiva",
    "recommendation": "hire|interview|reject",
    "confidence": 0.85,
    "evaluation_date": "{datetime.utcnow().isoformat()}"
}}"""

            ai_response = self.ai_service.query_openai(
                evaluation_prompt,
                model="gpt-4o-mini",
                max_tokens=1000,
                temperature=0.3  # Lower temperature for more consistent scoring
            )
            
            if not ai_response.get('success'):
                return {
                    'success': False,
                    'error': ai_response.get('error', 'AI evaluation failed')
                }
            
            # Parse evaluation response
            ai_content = ai_response.get('response', '')
            try:
                if '```json' in ai_content:
                    ai_content = ai_content.split('```json')[1].split('```')[0].strip()
                elif '{' in ai_content:
                    start = ai_content.find('{')
                    end = ai_content.rfind('}') + 1
                    ai_content = ai_content[start:end]
                
                evaluation_result = json.loads(ai_content)
                
                # Store evaluation in application notes for future reference
                evaluation_summary = f"AI Evaluation Score: {evaluation_result.get('overall_score', 0)}/100\n"
                evaluation_summary += f"Recommendation: {evaluation_result.get('recommendation', 'N/A')}\n"
                evaluation_summary += f"Generated: {datetime.utcnow().strftime('%Y-%m-%d %H:%M')}"
                
                return {
                    'success': True,
                    'evaluation': evaluation_result,
                    'application_id': application_id,
                    'generated_at': datetime.utcnow().isoformat()
                }
                
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse AI evaluation response: {e}")
                return {
                    'success': False,
                    'error': f'Errore nel parsing della valutazione AI: {str(e)}',
                    'raw_response': ai_content
                }
                
        except Exception as e:
            logger.error(f"Error in evaluate_candidate_match: {str(e)}")
            return {
                'success': False,
                'error': f'Errore nella valutazione: {str(e)}'
            }
    
    def enhance_resume(self, candidate_id: int, enhancement_prompt: str) -> Dict[str, Any]:
        """
        Generate resume enhancement suggestions for a candidate.
        
        Args:
            candidate_id: ID of the candidate
            enhancement_prompt: Specific enhancement goals
            
        Returns:
            Dict with enhancement suggestions
        """
        try:
            candidate = Candidate.query.get(candidate_id)
            if not candidate:
                return {
                    'success': False,
                    'error': 'Candidato non trovato'
                }
            
            # Build current candidate profile
            candidate_profile = f"""
PROFILO ATTUALE:
Nome: {candidate.full_name}
Email: {candidate.email}
Telefono: {candidate.phone or 'N/A'}
Località: {candidate.location or 'N/A'}
LinkedIn: {candidate.linkedin_url or 'N/A'}
"""
            
            # Add skills if available
            if candidate.skills:
                skills_list = [f"{skill.skill_name} (Livello: {skill.skill_level or 'N/A'})" for skill in candidate.skills]
                candidate_profile += f"Competenze: {', '.join(skills_list)}\n"
            
            # Add CV analysis if available
            if candidate.cv_analysis_data:
                try:
                    cv_data = json.loads(candidate.cv_analysis_data)
                    candidate_profile += f"Analisi CV esistente: {json.dumps(cv_data, indent=2)}\n"
                except:
                    pass
            
            enhancement_prompt_text = f"""Sei un esperto career coach specializzato nell'ottimizzazione di CV e profili professionali per il mercato del lavoro italiano.

{candidate_profile}

OBIETTIVO MIGLIORAMENTO:
{enhancement_prompt}

COMPITO:
Genera suggerimenti specifici e attuabili per migliorare il profilo del candidato e aumentare la sua attrattiva per i recruiter.

AREE DI FOCUS:
1. Presentazione professionale e personal branding
2. Valorizzazione delle competenze e esperienze
3. Ottimizzazione per ATS (Applicant Tracking Systems)
4. Miglioramenti nella struttura e formattazione
5. Suggerimenti per competenze aggiuntive da sviluppare

Rispondi SOLO con un JSON valido:
{{
    "summary": "Breve riassunto dei miglioramenti suggeriti",
    "improvements": {{
        "professional_summary": "Suggerimento per summary/presentazione professionale migliorata",
        "skills_optimization": [
            "Lista di suggerimenti per migliorare la presentazione delle competenze"
        ],
        "experience_enhancement": [
            "Suggerimenti per valorizzare meglio l'esperienza lavorativa"
        ],
        "formatting_tips": [
            "Consigli per la struttura e formattazione del CV"
        ],
        "missing_elements": [
            "Elementi mancanti che dovrebbero essere aggiunti"
        ]
    }},
    "recommended_skills": [
        "Competenze aggiuntive da sviluppare per il mercato attuale"
    ],
    "ats_optimization": [
        "Suggerimenti specifici per ottimizzazione ATS"
    ],
    "next_steps": [
        "Azioni concrete che il candidato può intraprendere"
    ],
    "generated_at": "{datetime.utcnow().isoformat()}"
}}"""

            ai_response = self.ai_service.query_openai(
                enhancement_prompt_text,
                model="gpt-4o-mini",
                max_tokens=1200,
                temperature=0.6
            )
            
            if not ai_response.get('success'):
                return {
                    'success': False,
                    'error': ai_response.get('error', 'AI enhancement failed')
                }
            
            # Parse enhancement response
            ai_content = ai_response.get('response', '')
            try:
                if '```json' in ai_content:
                    ai_content = ai_content.split('```json')[1].split('```')[0].strip()
                elif '{' in ai_content:
                    start = ai_content.find('{')
                    end = ai_content.rfind('}') + 1
                    ai_content = ai_content[start:end]
                
                enhancement_result = json.loads(ai_content)
                
                return {
                    'success': True,
                    'enhancements': enhancement_result,
                    'candidate_id': candidate_id,
                    'prompt': enhancement_prompt,
                    'generated_at': datetime.utcnow().isoformat()
                }
                
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse AI enhancement response: {e}")
                return {
                    'success': False,
                    'error': f'Errore nel parsing dei suggerimenti AI: {str(e)}',
                    'raw_response': ai_content
                }
                
        except Exception as e:
            logger.error(f"Error in enhance_resume: {str(e)}")
            return {
                'success': False,
                'error': f'Errore nella generazione suggerimenti: {str(e)}'
            }
    
    def generate_interview_questions(self, interview_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate interview questions based on job posting and candidate profile.
        
        Args:
            interview_data: Dict containing:
                - interview_type: str (phone_screening, technical, behavioral, final)
                - job_posting_id: int
                - candidate_id: int (optional)
                - focus_areas: List[str] (optional)
                - custom_prompt: str (optional)
                
        Returns:
            Dict with generated questions organized by category
        """
        try:
            job_posting_id = interview_data.get('job_posting_id')
            candidate_id = interview_data.get('candidate_id')
            interview_type = interview_data.get('interview_type', 'behavioral')
            focus_areas = interview_data.get('focus_areas', [])
            custom_prompt = interview_data.get('custom_prompt', '')
            
            # Get job posting
            job_posting = JobPosting.query.get(job_posting_id)
            if not job_posting:
                return {
                    'success': False,
                    'error': 'Posizione lavorativa non trovata'
                }
            
            # Build context
            context = f"""
POSIZIONE: {job_posting.title}
Descrizione: {job_posting.description or 'N/A'}
Requisiti: {job_posting.requirements or 'N/A'}
Responsabilità: {job_posting.responsibilities or 'N/A'}
Dipartimento: {job_posting.department.name if job_posting.department else 'N/A'}
"""
            
            # Add candidate context if provided
            candidate_context = ""
            if candidate_id:
                candidate = Candidate.query.get(candidate_id)
                if candidate:
                    candidate_context = f"""
CANDIDATO: {candidate.full_name}
Background: {candidate.location or 'N/A'}
Competenze note: {', '.join([skill.skill_name for skill in candidate.skills]) if candidate.skills else 'N/A'}
"""
            
            # Customize prompt based on interview type
            interview_type_prompts = {
                'phone_screening': 'Colloquio telefonico di screening iniziale (15-20 minuti)',
                'technical': 'Colloquio tecnico approfondito per valutare competenze specifiche',
                'behavioral': 'Colloquio comportamentale per valutare soft skills e cultural fit',
                'final': 'Colloquio finale con management per decisione di assunzione'
            }
            
            interview_context = interview_type_prompts.get(interview_type, 'Colloquio generico')
            focus_areas_text = ', '.join(focus_areas) if focus_areas else 'Generale'
            
            questions_prompt = f"""Sei un esperto HR specializzato nella progettazione di colloqui strutturati ed efficaci.

CONTESTO COLLOQUIO:
Tipo: {interview_context}
Aree di focus: {focus_areas_text}
Richiesta specifica: {custom_prompt}

{context}
{candidate_context}

COMPITO:
Genera un set completo di domande per questo colloquio, organizzate per categoria e difficoltà.

LINEE GUIDA:
- Domande specifiche per il ruolo e il livello richiesto
- Mix di domande aperte e situazionali
- Includi domande di follow-up suggerite
- Considera il tempo disponibile per il tipo di colloquio
- Domande adatte al mercato del lavoro italiano

Rispondi SOLO con un JSON valido:
{{
    "interview_overview": {{
        "type": "{interview_type}",
        "estimated_duration": "30-45 minuti",
        "objectives": ["Obiettivi principali del colloquio"]
    }},
    "question_categories": {{
        "opening": {{
            "title": "Domande di apertura",
            "questions": [
                {{
                    "question": "Domanda di apertura",
                    "purpose": "Scopo della domanda",
                    "follow_up": ["Possibili domande di approfondimento"]
                }}
            ]
        }},
        "experience": {{
            "title": "Esperienza e background",
            "questions": [
                {{
                    "question": "Domanda sull'esperienza",
                    "purpose": "Scopo della domanda",
                    "follow_up": ["Domande di approfondimento"]
                }}
            ]
        }},
        "technical": {{
            "title": "Competenze tecniche",
            "questions": [
                {{
                    "question": "Domanda tecnica",
                    "purpose": "Scopo della domanda",
                    "difficulty": "Junior|Mid|Senior",
                    "follow_up": ["Approfondimenti tecnici"]
                }}
            ]
        }},
        "behavioral": {{
            "title": "Comportamentali",
            "questions": [
                {{
                    "question": "Domanda comportamentale (metodo STAR)",
                    "purpose": "Competenza da valutare",
                    "evaluation_criteria": ["Criteri di valutazione della risposta"]
                }}
            ]
        }},
        "motivation": {{
            "title": "Motivazione e obiettivi",
            "questions": [
                {{
                    "question": "Domanda su motivazione",
                    "purpose": "Scopo della domanda"
                }}
            ]
        }},
        "closing": {{
            "title": "Chiusura",
            "questions": [
                {{
                    "question": "Domanda di chiusura",
                    "purpose": "Finalizzare positivamente il colloquio"
                }}
            ]
        }}
    }},
    "evaluation_tips": [
        "Suggerimenti per valutare le risposte"
    ],
    "red_flags": [
        "Segnali di attenzione nelle risposte"
    ],
    "generated_at": "{datetime.utcnow().isoformat()}"
}}"""

            ai_response = self.ai_service.query_openai(
                questions_prompt,
                model="gpt-4o-mini",
                max_tokens=2000,
                temperature=0.7
            )
            
            if not ai_response.get('success'):
                return {
                    'success': False,
                    'error': ai_response.get('error', 'AI questions generation failed')
                }
            
            # Parse questions response
            ai_content = ai_response.get('response', '')
            try:
                if '```json' in ai_content:
                    ai_content = ai_content.split('```json')[1].split('```')[0].strip()
                elif '{' in ai_content:
                    start = ai_content.find('{')
                    end = ai_content.rfind('}') + 1
                    ai_content = ai_content[start:end]
                
                questions_result = json.loads(ai_content)
                
                return {
                    'success': True,
                    'questions': questions_result,
                    'interview_data': interview_data,
                    'generated_at': datetime.utcnow().isoformat()
                }
                
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse AI questions response: {e}")
                return {
                    'success': False,
                    'error': f'Errore nel parsing delle domande AI: {str(e)}',
                    'raw_response': ai_content
                }
                
        except Exception as e:
            logger.error(f"Error in generate_interview_questions: {str(e)}")
            return {
                'success': False,
                'error': f'Errore nella generazione domande: {str(e)}'
            }


# Global service instance
recruiting_ai_service = RecruitingAIService()