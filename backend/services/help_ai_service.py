"""
Help AI Service - Assistente AI per documentazione e supporto utenti
Integra OpenAI per supporto conversazionale intelligente e context-aware
"""

import os
import json
import logging
import asyncio
import httpx
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Union
from flask import current_app
from sqlalchemy import or_, desc, and_

from extensions import db
from models import (
    HelpCategory, HelpContent, HelpConversation, HelpFeedback,
    User, Project, Department
)

logger = logging.getLogger(__name__)

class HelpAIService:
    """
    Servizio AI per Help System module.
    Gestisce assistente conversazionale e ricerca intelligente.
    """
    
    def __init__(self):
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        self.openai_base_url = "https://api.openai.com/v1"
        
        # Moduli supportati dal sistema help
        self.supported_modules = {
            'dashboard': 'Dashboard e Overview',
            'personnel': 'Gestione Personale e HR',
            'projects': 'Gestione Progetti',
            'timesheets': 'Registrazione Tempi',
            'crm': 'CRM e Gestione Clienti',
            'recruiting': 'Recruiting e Selezione',
            'certifications': 'Certificazioni e Compliance',
            'ceo': 'Human CEO e Intelligence',
            'funding': 'Bandi e Finanziamenti',
            'engagement': 'Engagement e Gamification',
            'communication': 'Comunicazione Aziendale',
            'invoicing': 'Fatturazione',
            'admin': 'Amministrazione Sistema'
        }
        
        # Tipi di supporto disponibili
        self.support_types = {
            'how_to': 'Come fare qualcosa',
            'troubleshooting': 'Risoluzione problemi',
            'feature_info': 'Informazioni su funzionalità',
            'best_practices': 'Best practices e suggerimenti',
            'navigation': 'Navigazione nell\'applicazione',
            'permissions': 'Permessi e accesso',
            'integrations': 'Integrazioni esterne',
            'reporting': 'Report e analytics'
        }

    async def process_help_query(
        self, 
        user_message: str, 
        user_id: int, 
        session_id: str = None,
        current_module: str = None,
        user_context: Dict = None
    ) -> Dict:
        """
        Processa query help utente e genera risposta assistente AI.
        
        Args:
            user_message: Domanda dell'utente
            user_id: ID utente che fa la domanda
            session_id: ID sessione per tracking conversazione
            current_module: Modulo attuale dell'utente per contesto
            user_context: Contesto aggiuntivo (ruolo, permessi, etc.)
            
        Returns:
            Dict con risposta, categoria, confidence, contenuti utilizzati
        """
        try:
            start_time = datetime.utcnow()
            
            # Step 1: Detect categoria e tipo di supporto
            query_analysis = await self._analyze_help_query(user_message, current_module)
            
            # Step 2: Search help content per contenuti rilevanti
            relevant_content = await self._search_help_content(
                user_message, 
                query_analysis.get('category'),
                current_module
            )
            
            # Step 3: Get user context per personalizzazione
            user_info = await self._get_user_context(user_id) if not user_context else user_context
            
            # Step 4: Generate risposta assistente
            ai_response = await self._generate_help_response(
                user_message=user_message,
                query_analysis=query_analysis,
                relevant_content=relevant_content,
                user_context=user_info,
                current_module=current_module
            )
            
            processing_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            
            # Return structured response
            return {
                'content': ai_response.get('content'),
                'category': query_analysis.get('category'),
                'support_type': query_analysis.get('support_type'),
                'confidence': ai_response.get('confidence'),
                'relevant_content': relevant_content,
                'suggested_actions': ai_response.get('suggested_actions', []),
                'escalation_recommended': ai_response.get('escalation_recommended', False),
                'processing_time_ms': int(processing_time),
                'session_id': session_id or str(uuid.uuid4())
            }
            
        except Exception as e:
            logger.error(f"Error processing help query: {str(e)}")
            return self._get_fallback_response(user_message, user_id)

    async def _analyze_help_query(self, user_message: str, current_module: str = None) -> Dict:
        """Analizza la query per determinare categoria e tipo di supporto."""
        try:
            # Use simple keyword matching for now, can be enhanced with AI later
            message_lower = user_message.lower()
            
            # Detect module
            detected_module = current_module
            if not detected_module:
                for module, description in self.supported_modules.items():
                    if module in message_lower or any(word in message_lower for word in description.lower().split()):
                        detected_module = module
                        break
            
            # Detect support type
            support_type = 'feature_info'  # default
            if any(word in message_lower for word in ['come', 'how', 'fare', 'do']):
                support_type = 'how_to'
            elif any(word in message_lower for word in ['errore', 'error', 'problema', 'non funziona', 'issue']):
                support_type = 'troubleshooting'
            elif any(word in message_lower for word in ['dove', 'where', 'trovare', 'find', 'navigare']):
                support_type = 'navigation'
            elif any(word in message_lower for word in ['permesso', 'permission', 'accesso', 'access']):
                support_type = 'permissions'
            elif any(word in message_lower for word in ['best practice', 'suggerimento', 'consiglio', 'tip']):
                support_type = 'best_practices'
            
            return {
                'category': detected_module or 'general',
                'support_type': support_type,
                'keywords': self._extract_keywords(user_message)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing help query: {str(e)}")
            return {'category': 'general', 'support_type': 'feature_info', 'keywords': []}

    async def _search_help_content(
        self, 
        user_message: str, 
        category: str = None, 
        current_module: str = None
    ) -> List[Dict]:
        """Cerca contenuti help rilevanti nella knowledge base."""
        try:
            # Build search query
            search_query = HelpContent.query.filter(HelpContent.is_published == True)
            
            # Add text search
            message_words = user_message.lower().split()
            text_filters = []
            for word in message_words:
                if len(word) > 2:  # Skip very short words
                    text_filters.append(
                        or_(
                            HelpContent.title.ilike(f'%{word}%'),
                            HelpContent.content.ilike(f'%{word}%'),
                            HelpContent.keywords.ilike(f'%{word}%'),
                            HelpContent.excerpt.ilike(f'%{word}%')
                        )
                    )
            
            if text_filters:
                search_query = search_query.filter(or_(*text_filters))
            
            # Add category filter
            if category and category != 'general':
                # Search by related modules
                search_query = search_query.filter(
                    HelpContent.related_modules.like(f'%"{category}"%')
                )
            
            # Order by relevance (featured, view count, creation date)
            search_query = search_query.order_by(
                desc(HelpContent.featured),
                desc(HelpContent.view_count),
                desc(HelpContent.created_at)
            ).limit(5)
            
            results = search_query.all()
            
            return [
                {
                    'id': content.id,
                    'title': content.title,
                    'excerpt': content.excerpt,
                    'content': content.content[:500] + '...' if len(content.content) > 500 else content.content,
                    'url': f'/app/help/content/{content.id}',
                    'relevance_score': self._calculate_relevance_score(content, user_message)
                }
                for content in results
            ]
            
        except Exception as e:
            logger.error(f"Error searching help content: {str(e)}")
            return []

    async def _get_user_context(self, user_id: int) -> Dict:
        """Ottiene contesto utente per personalizzazione risposte."""
        try:
            user = User.query.get(user_id)
            if not user:
                return {}
            
            return {
                'role': user.role,
                'department': user.department_obj.name if user.department_obj else None,
                'experience_level': self._determine_experience_level(user),
                'permissions': self._get_user_permissions(user),
                'preferred_language': 'it'  # Default Italian
            }
            
        except Exception as e:
            logger.error(f"Error getting user context: {str(e)}")
            return {}

    async def _generate_help_response(
        self,
        user_message: str,
        query_analysis: Dict,
        relevant_content: List[Dict],
        user_context: Dict,
        current_module: str = None
    ) -> Dict:
        """Genera risposta AI personalizzata basata su contesto e contenuti."""
        try:
            # Build context-aware prompt
            system_prompt = self._build_help_system_prompt(user_context, current_module)
            
            # Prepare relevant content for AI
            content_context = ""
            if relevant_content:
                content_context = "\n\nContenuti rilevanti dalla knowledge base:\n"
                for content in relevant_content[:3]:  # Use top 3 most relevant
                    content_context += f"- {content['title']}: {content['excerpt']}\n"
            
            user_prompt = f"""
            Domanda utente: {user_message}
            
            Categoria rilevata: {query_analysis.get('category', 'generale')}
            Tipo supporto: {query_analysis.get('support_type', 'informazioni')}
            Modulo corrente: {current_module or 'non specificato'}
            {content_context}
            
            Fornisci una risposta utile e personalizzata in italiano che:
            1. Risponda direttamente alla domanda
            2. Includa passi specifici se è una richiesta "come fare"
            3. Suggerisca contenuti della knowledge base se rilevanti
            4. Indichi se servono permessi specifici
            5. Offra azioni concrete che l'utente può intraprendere
            
            Mantieni un tono professionale ma amichevole. Usa markdown per formattazione.
            """
            
            # Call OpenAI (simplified for now - can be enhanced with actual API call)
            ai_content = await self._call_openai_help(system_prompt, user_prompt)
            
            # Determine confidence based on content match and query clarity
            confidence = self._calculate_response_confidence(
                user_message, query_analysis, relevant_content
            )
            
            # Generate suggested actions
            suggested_actions = self._generate_suggested_actions(
                query_analysis, relevant_content, current_module
            )
            
            # Determine if escalation is recommended
            escalation_recommended = confidence < 0.7 or any(
                word in user_message.lower() 
                for word in ['bug', 'errore critico', 'non riesco', 'urgent', 'urgente']
            )
            
            return {
                'content': ai_content,
                'confidence': confidence,
                'suggested_actions': suggested_actions,
                'escalation_recommended': escalation_recommended
            }
            
        except Exception as e:
            logger.error(f"Error generating help response: {str(e)}")
            return {
                'content': "Mi dispiace, ho riscontrato un problema nel generare la risposta. Prova a riformulare la domanda o contatta il supporto.",
                'confidence': 0.1,
                'suggested_actions': [{'type': 'contact_support', 'label': 'Contatta Supporto'}],
                'escalation_recommended': True
            }

    async def _call_openai_help(self, system_prompt: str, user_prompt: str) -> str:
        """Effettua chiamata OpenAI per generazione risposta help."""
        try:
            # For now, return a structured placeholder response
            # In production, this would make actual OpenAI API call
            return f"""
Grazie per la tua domanda! 

## Risposta
Basandomi sulla tua richiesta, ecco cosa posso aiutarti a fare:

La funzionalità che stai cercando si trova nel modulo appropriato dell'applicazione. 

## Passi suggeriti
1. **Verifica i permessi**: Assicurati di avere i permessi necessari per accedere alla funzionalità
2. **Consulta la documentazione**: Controlla i contenuti help disponibili per dettagli specifici
3. **Prova l'operazione**: Segui i passi indicati nella documentazione

## Risorse utili
- Consulta la sezione help per guide dettagliate
- Verifica le FAQ per domande comuni
- Contatta il supporto se hai bisogno di assistenza aggiuntiva

*Questa risposta è generata dall'assistente AI e sarà migliorata con l'integrazione OpenAI completa.*
            """
            
        except Exception as e:
            logger.error(f"Error calling OpenAI: {str(e)}")
            return "Mi dispiace, non riesco a generare una risposta al momento. Prova di nuovo più tardi."

    def _build_help_system_prompt(self, user_context: Dict, current_module: str = None) -> str:
        """Costruisce prompt di sistema per assistente help."""
        role_context = f"L'utente ha ruolo {user_context.get('role', 'employee')}"
        module_context = f" e sta utilizzando il modulo {current_module}" if current_module else ""
        
        return f"""
        Sei l'assistente AI del sistema DatPortal, specializzato in supporto e documentazione.
        
        Contesto utente: {role_context}{module_context}.
        
        Le tue responsabilità:
        - Fornire supporto accurato e personalizzato basato sul ruolo utente
        - Spiegare funzionalità dell'applicazione in modo chiaro
        - Guidare gli utenti attraverso procedure step-by-step
        - Suggerire best practices e soluzioni ottimali
        - Identificare quando è necessario escalare al supporto umano
        
        Stile di comunicazione:
        - Professionale ma amichevole
        - Chiaro e conciso
        - Orientato alla soluzione
        - Usa esempi pratici quando possibile
        
        Rispondi sempre in italiano.
        """

    def _calculate_relevance_score(self, content: 'HelpContent', user_message: str) -> float:
        """Calcola score di rilevanza per contenuto help."""
        try:
            score = 0.0
            message_words = set(user_message.lower().split())
            
            # Check title match
            title_words = set(content.title.lower().split())
            title_matches = len(message_words.intersection(title_words))
            score += title_matches * 2
            
            # Check keyword match
            if content.keywords:
                keyword_words = set(content.keywords.lower().split())
                keyword_matches = len(message_words.intersection(keyword_words))
                score += keyword_matches * 1.5
            
            # Boost score for featured content
            if content.featured:
                score += 1
            
            # Normalize score
            return min(score / len(message_words), 1.0) if message_words else 0.0
            
        except:
            return 0.0

    def _calculate_response_confidence(
        self, 
        user_message: str, 
        query_analysis: Dict, 
        relevant_content: List[Dict]
    ) -> float:
        """Calcola confidence score per la risposta generata."""
        try:
            confidence = 0.5  # Base confidence
            
            # Boost if we found relevant content
            if relevant_content:
                confidence += 0.2 * min(len(relevant_content), 3) / 3
            
            # Boost if query is clear and specific
            if len(user_message.split()) > 3:
                confidence += 0.1
            
            # Boost if we detected a known category
            if query_analysis.get('category') != 'general':
                confidence += 0.1
            
            # Reduce if query contains uncertainty indicators
            uncertainty_words = ['forse', 'maybe', 'non so', 'penso', 'credo']
            if any(word in user_message.lower() for word in uncertainty_words):
                confidence -= 0.1
            
            return max(0.1, min(confidence, 0.95))
            
        except:
            return 0.5

    def _generate_suggested_actions(
        self, 
        query_analysis: Dict, 
        relevant_content: List[Dict], 
        current_module: str = None
    ) -> List[Dict]:
        """Genera azioni suggerite basate su query e contenuti."""
        actions = []
        
        try:
            # Add relevant content links
            for content in relevant_content[:2]:
                actions.append({
                    'type': 'view_content',
                    'label': f"Leggi: {content['title']}",
                    'url': content['url']
                })
            
            # Add module-specific actions
            if current_module:
                actions.append({
                    'type': 'module_help',
                    'label': f"Guida {self.supported_modules.get(current_module, current_module)}",
                    'url': f'/app/help/module/{current_module}'
                })
            
            # Add general help actions
            if query_analysis.get('support_type') == 'troubleshooting':
                actions.append({
                    'type': 'contact_support',
                    'label': 'Contatta Supporto Tecnico',
                    'url': '/app/help/contact'
                })
            
            actions.append({
                'type': 'search_help',
                'label': 'Cerca altri contenuti',
                'url': '/app/help/search'
            })
            
            return actions[:4]  # Limit to 4 actions
            
        except:
            return []

    def _extract_keywords(self, text: str) -> List[str]:
        """Estrae parole chiave dal testo."""
        try:
            # Simple keyword extraction
            words = text.lower().split()
            # Filter out common words and short words
            stop_words = {'il', 'la', 'di', 'che', 'e', 'a', 'un', 'in', 'con', 'per', 'su', 'da', 'come', 'del', 'della'}
            keywords = [word for word in words if len(word) > 2 and word not in stop_words]
            return keywords[:10]  # Return top 10 keywords
        except:
            return []

    def _determine_experience_level(self, user: 'User') -> str:
        """Determina livello di esperienza utente."""
        try:
            # Simple heuristic based on account age and activity
            if user.created_at:
                days_since_creation = (datetime.utcnow() - user.created_at).days
                if days_since_creation > 365:
                    return 'experienced'
                elif days_since_creation > 90:
                    return 'intermediate'
            return 'beginner'
        except:
            return 'intermediate'

    def _get_user_permissions(self, user: 'User') -> List[str]:
        """Ottiene lista permessi utente."""
        try:
            # This would integrate with the actual permission system
            # For now, return basic permissions based on role
            role_permissions = {
                'admin': ['all'],
                'manager': ['view_all', 'manage_team'],
                'employee': ['view_own', 'submit_data']
            }
            return role_permissions.get(user.role, ['view_own'])
        except:
            return []

    def _get_fallback_response(self, user_message: str, user_id: int) -> Dict:
        """Risposta di fallback in caso di errore."""
        return {
            'content': """
Mi dispiace, al momento non riesco a elaborare la tua richiesta. 

## Cosa puoi fare:
- Prova a riformulare la domanda in modo più specifico
- Consulta la sezione [Help Center](/app/help) per guide dettagliate
- Contatta il [supporto tecnico](/app/help/contact) per assistenza diretta

## Problemi comuni:
- Verifica di essere nella sezione corretta dell'applicazione
- Controlla di avere i permessi necessari per l'operazione
- Prova ad aggiornare la pagina se hai problemi tecnici
            """,
            'category': 'general',
            'support_type': 'feature_info',
            'confidence': 0.1,
            'relevant_content': [],
            'suggested_actions': [
                {
                    'type': 'view_help_center',
                    'label': 'Vai al Help Center',
                    'url': '/app/help'
                },
                {
                    'type': 'contact_support',
                    'label': 'Contatta Supporto',
                    'url': '/app/help/contact'
                }
            ],
            'escalation_recommended': True,
            'processing_time_ms': 100,
            'session_id': str(uuid.uuid4())
        }