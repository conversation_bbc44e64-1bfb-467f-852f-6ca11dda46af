"""
Servizio per l'integrazione con FattureInCloud API usando SDK ufficiale
Gestisce l'invio di pre-fatture e la sincronizzazione con il sistema esterno
"""

import os
import json
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any
import logging

# SDK ufficiale FattureInCloud
import fattureincloud_python_sdk
from fattureincloud_python_sdk import (
    CompaniesApi, IssuedDocumentsApi, ClientsApi,
    IssuedDocument, Client as FicClient, Entity,
    IssuedDocumentItemsListItem, VatType, IssuedDocumentPaymentsListItem,
    ApiException
)

from extensions import db
from models import IntegrationSettings, PreInvoice, CompanyInvoicingSettings
from utils.encryption import decrypt_data

logger = logging.getLogger(__name__)

class FattureInCloudService:
    """
    Servizio per l'integrazione con FattureInCloud usando SDK ufficiale
    """
    
    def __init__(self):
        self.integration = None
        self.access_token = None
        self.company_id = None
        self.api_client = None
        self._load_settings()
    
    def _load_settings(self):
        """Carica le impostazioni di integrazione dal database e variabili d'ambiente"""
        try:
            # Carica da database
            self.integration = IntegrationSettings.query.filter_by(
                provider='fattureincloud',
                is_active=True
            ).first()
            
            if self.integration:
                if self.integration.api_key_encrypted:
                    self.access_token = decrypt_data(self.integration.api_key_encrypted)
                # Converti company_id in int se è stringa
                if self.integration.company_id:
                    self.company_id = int(self.integration.company_id) if isinstance(self.integration.company_id, str) else self.integration.company_id
            
            # Carica credenziali da variabili d'ambiente (autenticazione manuale)
            if not self.access_token:
                self.access_token = os.getenv('FIC_ACCESS_TOKEN')
            if not self.company_id:
                company_id_str = os.getenv('FIC_APP_ID')
                self.company_id = int(company_id_str) if company_id_str else None
            
            # Inizializza API client se abbiamo il token
            if self.access_token:
                self._init_api_client()
                
        except Exception as e:
            logger.error(f"Errore nel caricamento delle impostazioni FattureInCloud: {e}")
            self.integration = None
            self.access_token = None
            self.company_id = None
    
    def _init_api_client(self):
        """Inizializza il client API con il token di accesso"""
        try:
            configuration = fattureincloud_python_sdk.Configuration()
            configuration.access_token = self.access_token
            self.api_client = fattureincloud_python_sdk.ApiClient(configuration)
        except Exception as e:
            logger.error(f"Errore nell'inizializzazione del client API: {e}")
            self.api_client = None
    
    
    def test_connection(self) -> Dict:
        """
        Testa la connessione con FattureInCloud usando SDK
        """
        if not self.api_client or not self.company_id:
            return {
                'success': False,
                'error': 'Client API non inizializzato o Company ID mancante'
            }
        
        try:
            # Test con API companies
            companies_api_instance = CompaniesApi(self.api_client)
            company_info = companies_api_instance.get_company_info(self.company_id)
            
            if company_info and company_info.data:
                return {
                    'success': True,
                    'message': 'Connessione riuscita',
                    'data': {
                        'company_name': company_info.data.name,
                        'vat_number': company_info.data.vat_number,
                        'country': company_info.data.country,
                        'plan_name': getattr(company_info.data, 'plan_name', 'N/A')
                    }
                }
            else:
                return {
                    'success': False,
                    'error': 'Risposta vuota dal server'
                }
                
        except ApiException as e:
            logger.error(f"Errore API FattureInCloud: {e}")
            return {
                'success': False,
                'error': f'Errore API: {e.reason}'
            }
        except Exception as e:
            logger.error(f"Errore nel test connessione: {e}")
            return {
                'success': False,
                'error': f'Errore interno: {str(e)}'
            }
    
    @staticmethod
    def test_connection_with_credentials(access_token: str, company_id: str) -> Dict:
        """
        Testa la connessione con credenziali specifiche
        """
        try:
            configuration = fattureincloud_python_sdk.Configuration()
            configuration.access_token = access_token
            
            with fattureincloud_python_sdk.ApiClient(configuration) as api_client:
                companies_api_instance = CompaniesApi(api_client)
                company_info = companies_api_instance.get_company_info(int(company_id))
                
                if company_info and company_info.data:
                    return {
                        'success': True,
                        'message': 'Connessione riuscita',
                        'data': {
                            'company_name': company_info.data.name,
                            'vat_number': company_info.data.vat_number,
                            'country': company_info.data.country,
                            'plan_name': getattr(company_info.data, 'plan_name', 'N/A')
                        }
                    }
                    
        except ApiException as e:
            logger.error(f"Errore API nel test connessione: {e}")
            if e.status == 401:
                return {
                    'success': False,
                    'error': 'Access token non valido o scaduto'
                }
            elif e.status == 403:
                return {
                    'success': False,
                    'error': 'Accesso negato. Verificare i permessi'
                }
            elif e.status == 404:
                return {
                    'success': False,
                    'error': 'Company ID non trovato'
                }
            else:
                return {
                    'success': False,
                    'error': f'Errore API {e.status}: {e.reason}'
                }
        except Exception as e:
            logger.error(f"Errore nel test connessione: {e}")
            return {
                'success': False,
                'error': f'Errore interno: {str(e)}'
            }
    
    def list_clients(self, limit: int = 10) -> Dict:
        """
        Ottiene la lista dei clienti da FattureInCloud per test
        """
        if not self.api_client or not self.company_id:
            return {
                'success': False,
                'error': 'Client API non inizializzato'
            }
        
        try:
            clients_api_instance = ClientsApi(self.api_client)
            response = clients_api_instance.list_clients(self.company_id, q='', page=1, per_page=limit)
            
            if response and response.data:
                clients_list = []
                for client in response.data:
                    clients_list.append({
                        'id': client.id,
                        'name': client.name,
                        'vat_number': client.vat_number,
                        'country': client.country
                    })
                
                return {
                    'success': True,
                    'data': {
                        'clients': clients_list,
                        'total': len(clients_list)
                    }
                }
            else:
                return {
                    'success': True,
                    'data': {
                        'clients': [],
                        'total': 0
                    }
                }
                
        except ApiException as e:
            logger.error(f"Errore API list_clients: {e}")
            return {
                'success': False,
                'error': f'Errore API: {e.reason}'
            }
        except Exception as e:
            logger.error(f"Errore list_clients: {e}")
            return {
                'success': False,
                'error': f'Errore interno: {str(e)}'
            }
    
    def get_company_info(self) -> Dict:
        """
        Ottiene le informazioni dell'azienda da FattureInCloud
        """
        if not self.api_client or not self.company_id:
            return {
                'success': False,
                'error': 'Client API non inizializzato'
            }
        
        try:
            companies_api_instance = CompaniesApi(self.api_client)
            response = companies_api_instance.get_company_info(self.company_id)
            
            return {
                'success': True,
                'data': response.data
            }
            
        except ApiException as e:
            logger.error(f"Errore API get_company_info: {e}")
            return {
                'success': False,
                'error': f'Errore API: {e.reason}'
            }
        except Exception as e:
            logger.error(f"Errore get_company_info: {e}")
            return {
                'success': False,
                'error': f'Errore interno: {str(e)}'
            }
    
    def create_client(self, client_data: Dict) -> Dict:
        """
        Crea un cliente su FattureInCloud usando SDK
        """
        if not self.api_client or not self.company_id:
            return {
                'success': False,
                'error': 'Client API non inizializzato'
            }
        
        try:
            clients_api_instance = ClientsApi(self.api_client)
            
            # Crea oggetto Client secondo SDK
            fic_client = FicClient(
                name=client_data.get('name', ''),
                type='company',  # o 'person'
                vat_number=client_data.get('vat_number', ''),
                tax_code=client_data.get('fiscal_code', ''),
                address_street=client_data.get('address', ''),
                address_city=client_data.get('city', ''),
                address_postal_code=client_data.get('postal_code', ''),
                address_province=client_data.get('province', ''),
                country='Italia',
                email=client_data.get('email', ''),
                phone=client_data.get('phone', ''),
                certified_email=client_data.get('pec', ''),
                ei_code=client_data.get('ei_code', '0000000')
            )
            
            response = clients_api_instance.create_client(self.company_id, fic_client)
            
            return {
                'success': True,
                'data': response.data
            }
            
        except ApiException as e:
            logger.error(f"Errore API create_client: {e}")
            return {
                'success': False,
                'error': f'Errore API: {e.reason}'
            }
        except Exception as e:
            logger.error(f"Errore create_client: {e}")
            return {
                'success': False,
                'error': f'Errore interno: {str(e)}'
            }
    
    def send_pre_invoice(self, pre_invoice_id: int) -> Dict:
        """
        Invia una pre-fattura a FattureInCloud come fattura usando SDK
        """
        try:
            # Carica la pre-fattura dal database
            pre_invoice = PreInvoice.query.get(pre_invoice_id)
            if not pre_invoice:
                return {
                    'success': False,
                    'error': 'Pre-fattura non trovata'
                }
            
            # Verifica che la pre-fattura sia pronta per l'invio
            if not pre_invoice.can_send_external:
                return {
                    'success': False,
                    'error': f'Pre-fattura non può essere inviata. Stato attuale: {pre_invoice.status}'
                }
            
            if not self.api_client or not self.company_id:
                return {
                    'success': False,
                    'error': 'Client API non inizializzato'
                }
            
            # Carica le impostazioni aziendali
            company_settings = CompanyInvoicingSettings.query.filter_by(is_active=True).first()
            if not company_settings:
                return {
                    'success': False,
                    'error': 'Impostazioni aziendali non configurate'
                }
            
            # Prepara la fattura usando SDK
            invoice_data = self._prepare_invoice_data_sdk(pre_invoice, company_settings)
            
            # Invia la fattura
            issued_docs_api = IssuedDocumentsApi(self.api_client)
            response = issued_docs_api.create_issued_document(self.company_id, invoice_data)
            
            if response and response.data:
                # Aggiorna la pre-fattura con i dati esterni
                pre_invoice.external_invoice_id = str(response.data.id)
                pre_invoice.external_status = getattr(response.data, 'status', 'sent')
                pre_invoice.external_sent_date = date.today()
                pre_invoice.status = 'sent_external'
                
                # Aggiorna l'integrazione
                if self.integration:
                    self.integration.last_sync_date = datetime.utcnow()
                    self.integration.last_error = None
                
                db.session.commit()
                
                return {
                    'success': True,
                    'message': 'Pre-fattura inviata con successo a FattureInCloud',
                    'data': {
                        'external_id': pre_invoice.external_invoice_id,
                        'external_status': pre_invoice.external_status,
                        'sent_date': pre_invoice.external_sent_date.isoformat()
                    }
                }
            else:
                return {
                    'success': False,
                    'error': 'Risposta vuota dal server'
                }
            
        except ApiException as e:
            logger.error(f"Errore API nell'invio della pre-fattura {pre_invoice_id}: {e}")
            return {
                'success': False,
                'error': f'Errore API: {e.reason}'
            }
        except Exception as e:
            logger.error(f"Errore nell'invio della pre-fattura {pre_invoice_id}: {e}")
            return {
                'success': False,
                'error': f'Errore interno: {str(e)}'
            }
    
    def _prepare_invoice_data_sdk(self, pre_invoice: PreInvoice, company_settings: CompanyInvoicingSettings) -> IssuedDocument:
        """
        Prepara i dati della fattura nel formato SDK secondo mapping documentazione
        """
        # Calcola la data di scadenza
        due_date = date.today()
        if company_settings.default_payment_terms:
            due_date = date.today() + timedelta(days=company_settings.default_payment_terms)
        
        # Prepara le righe della fattura
        items_list = []
        for line in pre_invoice.lines:
            # Determina aliquota IVA
            vat_value = float(pre_invoice.vat_rate)
            
            # Crea oggetto VatType
            vat = VatType(value=vat_value)
            
            # Crea item secondo mapping documentazione
            item = IssuedDocumentItemsListItem(
                name=line.description or f"Servizio {line.project.name if line.project else 'Consulenza'}",
                description=line.description or "Servizi di consulenza tecnica",
                qty=1,  # Sempre 1 secondo mapping
                net_price=float(line.total_amount),  # Importo totale della riga
                vat=vat
            )
            items_list.append(item)
        
        # Dati del cliente (Entity)
        entity = Entity(
            name=pre_invoice.client.name,
            vat_number=pre_invoice.client.vat_number or '',
            tax_code=pre_invoice.client.fiscal_code or pre_invoice.client.vat_number or '',
            address_street=getattr(pre_invoice.client, 'address', '') or '',
            country='Italia'
        )
        
        # Gestione Split Payment per Enti Pubblici
        use_split_payment = 'pubblic' in pre_invoice.client.name.lower() or 'comune' in pre_invoice.client.name.lower()
        
        # Lista pagamenti
        payments_list = [
            IssuedDocumentPaymentsListItem(
                amount=float(pre_invoice.total_amount),
                due_date=due_date
            )
        ]
        
        # Crea la fattura secondo il mapping semplificato
        invoice = IssuedDocument(
            type='invoice',
            entity=entity,
            date=pre_invoice.generated_date,
            subject=f"[{company_settings.invoice_prefix or 'Consulenza'}] - [{pre_invoice.pre_invoice_number}]",
            numeration=company_settings.invoice_prefix or 'Principale',
            items_list=items_list,
            payments_list=payments_list,
            use_split_payment=use_split_payment,
            notes=pre_invoice.notes or '',
            # Ritenuta d'acconto se presente
            withholding_tax=float(pre_invoice.retention_rate) if pre_invoice.retention_rate else None,
            # Cassa previdenziale (4% per ingegneria)
            cassa=4.0 if 'ingegn' in (pre_invoice.notes or '').lower() else None
        )
        
        return invoice
    
    def sync_invoice_status(self, pre_invoice_id: int) -> Dict:
        """
        Sincronizza lo stato di una fattura da FattureInCloud usando SDK
        """
        try:
            pre_invoice = PreInvoice.query.get(pre_invoice_id)
            if not pre_invoice or not pre_invoice.external_invoice_id:
                return {
                    'success': False,
                    'error': 'Pre-fattura non trovata o non inviata esternamente'
                }
            
            if not self.api_client or not self.company_id:
                return {
                    'success': False,
                    'error': 'Client API non inizializzato'
                }
            
            # Ottieni lo stato della fattura da FattureInCloud
            issued_docs_api = IssuedDocumentsApi(self.api_client)
            response = issued_docs_api.get_issued_document(self.company_id, int(pre_invoice.external_invoice_id))
            
            if response and response.data:
                old_status = pre_invoice.external_status
                new_status = getattr(response.data, 'status', 'unknown')
                
                # Aggiorna lo stato se è cambiato
                if old_status != new_status:
                    pre_invoice.external_status = new_status
                    db.session.commit()
                    
                    return {
                        'success': True,
                        'message': f'Stato aggiornato da {old_status} a {new_status}',
                        'data': {
                            'old_status': old_status,
                            'new_status': new_status,
                            'external_data': response.data
                        }
                    }
                else:
                    return {
                        'success': True,
                        'message': 'Stato invariato',
                        'data': {
                            'status': new_status,
                            'external_data': response.data
                        }
                    }
            else:
                return {
                    'success': False,
                    'error': 'Risposta vuota dal server'
                }
            
        except ApiException as e:
            logger.error(f"Errore API nella sincronizzazione dello stato per pre-fattura {pre_invoice_id}: {e}")
            return {
                'success': False,
                'error': f'Errore API: {e.reason}'
            }
        except Exception as e:
            logger.error(f"Errore nella sincronizzazione dello stato per pre-fattura {pre_invoice_id}: {e}")
            return {
                'success': False,
                'error': f'Errore interno: {str(e)}'
            }
    
    def download_invoice_pdf(self, pre_invoice_id: int) -> Dict:
        """
        Scarica il PDF di una fattura da FattureInCloud usando SDK
        """
        try:
            pre_invoice = PreInvoice.query.get(pre_invoice_id)
            if not pre_invoice or not pre_invoice.external_invoice_id:
                return {
                    'success': False,
                    'error': 'Pre-fattura non trovata o non inviata esternamente'
                }
            
            if not self.api_client or not self.company_id:
                return {
                    'success': False,
                    'error': 'Client API non inizializzato'
                }
            
            # Richiedi il PDF della fattura
            issued_docs_api = IssuedDocumentsApi(self.api_client)
            response = issued_docs_api.get_issued_document_pdf(self.company_id, int(pre_invoice.external_invoice_id))
            
            if response:
                return {
                    'success': True,
                    'message': 'PDF scaricato con successo',
                    'data': {
                        'pdf_content': response,
                        'filename': f"fattura_{pre_invoice.pre_invoice_number}.pdf"
                    }
                }
            else:
                return {
                    'success': False,
                    'error': 'PDF non disponibile'
                }
            
        except ApiException as e:
            logger.error(f"Errore API nel download PDF per pre-fattura {pre_invoice_id}: {e}")
            return {
                'success': False,
                'error': f'Errore API: {e.reason}'
            }
        except Exception as e:
            logger.error(f"Errore nel download PDF per pre-fattura {pre_invoice_id}: {e}")
            return {
                'success': False,
                'error': f'Errore interno: {str(e)}'
            }