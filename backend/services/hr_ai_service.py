"""
HR Assistant AI Service - Knowledge management e chatbot support
Integra OpenAI e Perplexity per assistenza dipendenti e content creation
"""

import os
import json
import logging
import asyncio
import httpx
from datetime import datetime
from typing import Dict, List, Optional, Union
from flask import current_app
from sqlalchemy import or_, desc

from extensions import db
from models import HRKnowledgeBase, HRChatConversation, HRContentTemplate

logger = logging.getLogger(__name__)

class HRAIService:
    """
    Servizio AI per HR Assistant module.
    Gestisce chatbot support e content creation con AI.
    """
    
    def __init__(self):
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        self.perplexity_api_key = os.getenv('PERPLEXITY_API_KEY')
        self.openai_base_url = "https://api.openai.com/v1"
        self.perplexity_base_url = "https://api.perplexity.ai"
        
        # Categorie HR supportate
        self.hr_categories = {
            'contracts': 'Informazioni Contrattuali',
            'onboarding': 'Procedure di Onboarding',
            'offboarding': 'Procedure di Offboarding', 
            'leave': 'Gestione Ferie',
            'permits': 'Permessi e Congedi',
            'travel': 'Trasferte e Rimborsi',
            'benefits': 'Benefit e Welfare',
            'tools': 'Strumenti Aziendali',
            'purchases': 'Richiesta Acquisti',
            'training': 'Formazione e Certificazioni'
        }

    async def process_hr_query(self, user_message: str, user_id: int, session_id: str = None) -> Dict:
        """
        Processa query HR employee e genera risposta chatbot.
        
        Args:
            user_message: Domanda del dipendente
            user_id: ID utente che fa la domanda
            session_id: ID sessione per tracking conversazione
            
        Returns:
            Dict con risposta, categoria, confidence, KB entries usate
        """
        try:
            start_time = datetime.utcnow()
            
            # Step 1: Detect categoria dalla domanda
            category = await self._detect_hr_category(user_message)
            
            # Step 2: Search knowledge base per contenuti rilevanti
            relevant_kb = await self._search_knowledge_base(user_message, category)
            
            # Step 3: Generate risposta chatbot
            bot_response = await self._generate_hr_response(
                user_message, category, relevant_kb
            )
            
            # Step 4: Store conversazione per analytics
            processing_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            
            conversation = HRChatConversation(
                user_id=user_id,
                session_id=session_id or f"session_{user_id}_{int(datetime.utcnow().timestamp())}",
                user_message=user_message,
                bot_response=bot_response.get('content', ''),
                category_detected=category,
                confidence_score=bot_response.get('confidence', 'medium'),
                kb_entries_used=json.dumps([kb['id'] for kb in relevant_kb]),
                response_time_ms=int(processing_time)
            )
            
            db.session.add(conversation)
            db.session.commit()
            
            return {
                'response': bot_response.get('content'),
                'category': category,
                'confidence': bot_response.get('confidence'),
                'kb_entries_used': relevant_kb,
                'response_time_ms': int(processing_time),
                'conversation_id': conversation.id,
                'suggested_actions': bot_response.get('suggested_actions', [])
            }
            
        except Exception as e:
            logger.error(f"Error processing HR query: {str(e)}")
            return self._get_fallback_hr_response(user_message, user_id)

    async def _detect_hr_category(self, user_message: str) -> str:
        """Detect categoria HR dalla domanda usando AI."""
        try:
            categories_text = "\n".join([
                f"- {key}: {value}" for key, value in self.hr_categories.items()
            ])
            
            prompt = f"""
            Analizza la seguente domanda HR e identifica la categoria più appropriata.
            
            Domanda: "{user_message}"
            
            Categorie disponibili:
            {categories_text}
            
            Rispondi SOLO con la chiave della categoria (es: 'contracts', 'leave', etc.).
            Se non sei sicuro, usa 'general'.
            """
            
            response = await self._call_openai_simple(prompt, max_tokens=50)
            category = response.strip().lower()
            
            # Validate categoria
            if category in self.hr_categories:
                return category
            else:
                return 'general'
                
        except Exception as e:
            logger.warning(f"Category detection failed: {str(e)}")
            return 'general'

    async def _search_knowledge_base(self, query: str, category: str = None) -> List[Dict]:
        """Search knowledge base per contenuti rilevanti."""
        try:
            import re
            
            # Base query
            kb_query = HRKnowledgeBase.query.filter(HRKnowledgeBase.is_active == True)
            
            # Filter by categoria se specificata
            if category and category != 'general':
                kb_query = kb_query.filter(HRKnowledgeBase.category == category)
            
            # Estrai parole chiave significative dalla query
            # Rimuovi solo le parole comuni più basilari
            stop_words = {'su', 'di', 'da', 'in', 'con', 'per', 'tra', 'fra', 'il', 'lo', 'la', 'i', 'gli', 'le', 
                         'un', 'uno', 'una', 'e', 'o', 'ma', 'se', 'che', 'chi', 'cui', 'non', 'più', 'è', 'c\'è',
                         'sono', 'ho', 'hai', 'ha', 'abbiamo', 'avete', 'hanno'}
            
            # Estrai keywords significative (parole > 3 caratteri, non stop words)
            words = re.findall(r'\b\w+\b', query.lower())
            keywords = [word for word in words if len(word) > 3 and word not in stop_words]
            
            # Log per debug
            logger.info(f"HR KB Search - Query originale: '{query}'")
            logger.info(f"HR KB Search - Keywords estratte: {keywords}")
            
            # Se non ci sono keywords, usa la query originale
            if not keywords:
                search_term = f"%{query}%"
                kb_query = kb_query.filter(
                    or_(
                        HRKnowledgeBase.title.ilike(search_term),
                        HRKnowledgeBase.content.ilike(search_term),
                        HRKnowledgeBase.tags.ilike(search_term)
                    )
                )
            else:
                # Crea condizioni OR per ogni keyword
                search_conditions = []
                for keyword in keywords:
                    search_term = f"%{keyword}%"
                    search_conditions.extend([
                        HRKnowledgeBase.title.ilike(search_term),
                        HRKnowledgeBase.content.ilike(search_term),
                        HRKnowledgeBase.tags.ilike(search_term)
                    ])
                
                # Applica tutte le condizioni con OR
                if search_conditions:
                    kb_query = kb_query.filter(or_(*search_conditions))
            
            # Ordina per rilevanza (per ora by data, in futuro similarity scoring)
            kb_entries = kb_query.order_by(desc(HRKnowledgeBase.created_at)).limit(5).all()
            
            # Calcola un punteggio di rilevanza basato sul numero di keyword trovate
            results = []
            for entry in kb_entries:
                score = 0
                content_lower = (entry.title + ' ' + entry.content + ' ' + (entry.tags or '')).lower()
                for keyword in keywords:
                    if keyword in content_lower:
                        score += content_lower.count(keyword)
                
                results.append({
                    'id': entry.id,
                    'title': entry.title,
                    'content': entry.content,
                    'category': entry.category,
                    'relevance_score': min(1.0, score / 10.0) if keywords else 0.8
                })
            
            # Ordina per relevance score
            results.sort(key=lambda x: x['relevance_score'], reverse=True)
            
            return results[:5]  # Ritorna top 5
            
        except Exception as e:
            logger.error(f"Knowledge base search failed: {str(e)}")
            return []

    async def _generate_hr_response(self, user_message: str, category: str, kb_entries: List[Dict]) -> Dict:
        """Genera risposta HR chatbot usando KB e AI."""
        try:
            # Costruisci context dalla knowledge base
            if kb_entries:
                kb_context = "\n\n".join([
                    f"**{entry['title']}**\n{entry['content']}" 
                    for entry in kb_entries
                ])
            else:
                kb_context = "❌ **NESSUNA LINEA GUIDA AZIENDALE SPECIFICA TROVATA**\n\nNon sono presenti informazioni specifiche aziendali per questa richiesta nella knowledge base. Dovrai fare riferimento a informazioni generali e suggerire di contattare HR per linee guida aziendali specifiche."
            
            system_prompt = f"""
            Sei un assistente HR specializzato per aziende italiane PMI e startup. 
            Il tuo ruolo è fornire supporto ai dipendenti su tematiche HR aziendali.
            
            Categoria domanda: {self.hr_categories.get(category, 'Generale')}
            
            REGOLE IMPORTANTI:
            - Rispondi SOLO a domande inerenti l'ambito HR aziendale e lavorativo
            - Per domande NON aziendali (sport, cucina, argomenti generali): "Mi dispiace, posso assistere solo su tematiche HR e lavorative aziendali"  
            - Usa SEMPRE informazioni dalla knowledge base aziendale quando disponibili
            - Se non hai linee guida aziendali specifiche, dillo chiaramente e suggerisci di contattare HR
            - Includi riferimenti normativi italiani (CCNL, Decreto Legislativo) quando rilevanti
            - Mantieni tono professionale ma cordiale
            - Suggerisci sempre azioni concrete e pratiche
            - Rispondi in italiano
            
            STRUTTURA RISPOSTA:
            1. Risposta diretta alla domanda
            2. Informazioni dalla knowledge base aziendale (se disponibili)
            3. Riferimenti normativi (se rilevanti)  
            4. Azioni concrete suggerite
            5. Quando contattare HR per approfondimenti
            """
            
            user_prompt = f"""
            Domanda dipendente: {user_message}
            
            Informazioni dalla Knowledge Base Aziendale:
            {kb_context}
            
            VERIFICA: Questa domanda riguarda l'ambito HR aziendale o lavorativo?
            
            Se SÌ, fornisci una risposta strutturata che:
            1. Risponda direttamente alla domanda
            2. Specifichi se sono disponibili linee guida aziendali o meno
            3. Includa informazioni dalla knowledge base se disponibili
            4. Aggiunga riferimenti normativi italiani pertinenti
            5. Suggerisca azioni concrete da intraprendere
            6. Indichi quando contattare HR per approfondimenti specifici
            
            Se NO (domanda non inerente HR/lavoro), rispondi semplicemente:
            "Mi dispiace, posso assistere solo su tematiche HR e lavorative aziendali. Per questa richiesta ti suggerisco di rivolgerti altrove."
            
            Formato risposta sempre in markdown con sezioni chiare.
            """
            
            response_content = await self._call_openai(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                max_tokens=1000,
                temperature=0.3
            )
            
            # Extract suggested actions
            suggested_actions = self._extract_suggested_actions(response_content)
            
            # Determine confidence based on KB matches
            confidence = 'high' if len(kb_entries) >= 2 else 'medium' if len(kb_entries) == 1 else 'low'
            
            return {
                'content': response_content,
                'confidence': confidence,
                'suggested_actions': suggested_actions
            }
            
        except Exception as e:
            logger.error(f"HR response generation failed: {str(e)}")
            return self._get_simple_fallback_response(user_message, category)

    async def generate_hr_content(self, category: str, topic: str, requirements: str = '', template_id: int = None) -> Dict:
        """
        Genera contenuto HR assistito da AI per amministratori.
        Usa Perplexity per research + OpenAI per content creation.
        """
        try:
            start_time = datetime.utcnow()
            
            # Step 1: Load template se specificato
            template_data = None
            if template_id:
                template = HRContentTemplate.query.get(template_id)
                if template:
                    template_data = {
                        'prompt_template': template.prompt_template,
                        'required_fields': json.loads(template.required_fields or '[]'),
                        'output_format': template.output_format
                    }
            
            # Step 2: Research web con Perplexity per context aggiornato
            web_research = await self._perplexity_hr_research(category, topic, requirements)
            
            # Step 3: Generate content con OpenAI
            content_result = await self._generate_ai_hr_content(
                category, topic, requirements, web_research, template_data
            )
            
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            
            return {
                'content': content_result.get('content'),
                'sources': web_research.get('sources', []),
                'confidence': content_result.get('confidence', 'medium'),
                'processing_time': round(processing_time, 1),
                'template_used': template_id is not None,
                'suggestions': content_result.get('suggestions', [])
            }
            
        except Exception as e:
            logger.error(f"HR content generation failed: {str(e)}")
            return self._get_content_generation_fallback(category, topic)

    async def _perplexity_hr_research(self, category: str, topic: str, requirements: str) -> Dict:
        """Ricerca web con Perplexity per context HR aggiornato."""
        if not self.perplexity_api_key:
            logger.warning("Perplexity API key not configured, using simulated research")
            return self._simulate_hr_research(category, topic)
        
        try:
            research_query = f"""
            Ricerca informazioni aggiornate per contenuto HR italiano su: {topic}
            
            Categoria: {self.hr_categories.get(category, category)}
            Requisiti specifici: {requirements}
            
            Focus su:
            - Normative italiane vigenti (Codice del Lavoro, CCNL)
            - Best practices PMI italiane
            - Procedure standard e template
            - Aspetti GDPR e privacy
            - Aggiornamenti recenti normativa lavoro
            
            Fornisci informazioni accurate e fonti verificabili.
            """
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.perplexity_base_url}/chat/completions",
                    headers={
                        "Authorization": f"Bearer {self.perplexity_api_key}",
                        "Content-Type": "application/json"
                    },
                    json={
                        "model": "llama-3.1-sonar-large-128k-online",
                        "messages": [
                            {"role": "user", "content": research_query}
                        ],
                        "max_tokens": 2000,
                        "temperature": 0.2
                    },
                    timeout=45.0
                )
                
                if response.status_code != 200:
                    logger.warning(f"Perplexity API error: {response.status_code}")
                    return self._simulate_hr_research(category, topic)
                
                result = response.json()
                research_content = result['choices'][0]['message']['content']
                
                # Extract sources (basic parsing)
                sources = self._extract_sources_from_research(research_content)
                
                return {
                    'content': research_content,
                    'sources': sources,
                    'research_time': datetime.utcnow().isoformat()
                }
                
        except Exception as e:
            logger.warning(f"Perplexity research failed: {str(e)}")
            return self._simulate_hr_research(category, topic)

    async def _generate_ai_hr_content(self, category: str, topic: str, requirements: str, 
                                    web_research: Dict, template_data: Dict = None) -> Dict:
        """Genera contenuto HR finale con OpenAI."""
        try:
            # Template prompt o standard prompt
            if template_data:
                base_prompt = template_data['prompt_template']
                system_prompt = f"""
                Sei un esperto HR per PMI italiane. Crea contenuti professionali seguendo il template fornito.
                Categoria: {self.hr_categories.get(category, category)}
                
                Template requirements: {json.dumps(template_data.get('required_fields', []))}
                Output format: {template_data.get('output_format', 'Markdown strutturato')}
                """
            else:
                system_prompt = f"""
                Sei un esperto HR per PMI italiane specializzato in {self.hr_categories.get(category, category)}.
                
                Crea contenuti HR professionali, accurati e pratici che includano:
                - Informazioni normative italiane aggiornate
                - Procedure step-by-step chiare
                - Template e esempi pratici quando appropriato
                - Riferimenti GDPR e privacy
                - Linguaggio professionale ma accessibile
                
                Output in markdown ben strutturato con sezioni logiche.
                """
                base_prompt = """
                Crea contenuto completo su: {topic}
                
                Requisiti specifici: {requirements}
                
                Research context aggiornato:
                {research_content}
                
                Struttura consigliata:
                # {topic}
                
                ## Panoramica
                ## Normativa di Riferimento  
                ## Procedure e Processi
                ## Template/Esempi
                ## FAQ Comuni
                ## Contatti e Risorse
                """
            
            user_prompt = base_prompt.format(
                topic=topic,
                requirements=requirements,
                research_content=web_research.get('content', '')
            )
            
            content = await self._call_openai(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                max_tokens=2500,
                temperature=0.4
            )
            
            # Extract suggestions for improvement
            suggestions = self._extract_content_suggestions(content, web_research)
            
            # Determine confidence based on research quality
            confidence = 'high' if web_research.get('sources') else 'medium'
            
            return {
                'content': content,
                'confidence': confidence,
                'suggestions': suggestions
            }
            
        except Exception as e:
            logger.error(f"AI content generation failed: {str(e)}")
            raise

    # Utility methods following CEO AI service patterns
    async def _call_openai(self, system_prompt: str, user_prompt: str, max_tokens: int = 1500, temperature: float = 0.7) -> str:
        """Effettua chiamata OpenAI API."""
        if not self.openai_api_key:
            raise ValueError("OpenAI API key not configured")
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.openai_base_url}/chat/completions",
                headers={
                    "Authorization": f"Bearer {self.openai_api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": "gpt-4o-mini",
                    "messages": [
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_prompt}
                    ],
                    "max_tokens": max_tokens,
                    "temperature": temperature
                },
                timeout=30.0
            )
            
            if response.status_code != 200:
                raise Exception(f"OpenAI API error: {response.status_code} - {response.text}")
            
            result = response.json()
            return result['choices'][0]['message']['content']

    async def _call_openai_simple(self, prompt: str, max_tokens: int = 100) -> str:
        """Chiamata OpenAI semplificata per task rapidi."""
        return await self._call_openai(
            system_prompt="Sei un assistente AI preciso e conciso.",
            user_prompt=prompt,
            max_tokens=max_tokens,
            temperature=0.1
        )

    def _extract_suggested_actions(self, response_content: str) -> List[str]:
        """Estrae azioni suggerite dalla risposta."""
        actions = []
        lines = response_content.split('\n')
        
        in_actions_section = False
        for line in lines:
            line = line.strip()
            
            if any(keyword in line.lower() for keyword in ['azioni', 'passaggi', 'next steps', 'cosa fare']):
                in_actions_section = True
                continue
                
            if in_actions_section and line:
                if line.startswith(('1.', '2.', '3.', '•', '-', '*')):
                    action = line
                    for prefix in ['1.', '2.', '3.', '4.', '5.', '•', '-', '*']:
                        if action.startswith(prefix):
                            action = action[len(prefix):].strip()
                            break
                    
                    if action and len(action) > 10:
                        actions.append(action)
                
                elif line.startswith('#') or line.startswith('**'):
                    in_actions_section = False
        
        return actions[:3]  # Limit to 3 actions

    def _extract_sources_from_research(self, research_content: str) -> List[str]:
        """Estrae fonti dal contenuto di ricerca."""
        sources = []
        lines = research_content.split('\n')
        
        for line in lines:
            # Look for URLs or source citations
            if 'http' in line or 'www.' in line:
                sources.append(line.strip())
            elif any(keyword in line.lower() for keyword in ['fonte:', 'source:', 'riferimento:']):
                sources.append(line.strip())
        
        return sources[:5]  # Limit sources

    def _extract_content_suggestions(self, content: str, web_research: Dict) -> List[str]:
        """Genera suggerimenti per miglioramento contenuto."""
        suggestions = []
        
        # Check content completeness
        if len(content) < 500:
            suggestions.append("Considera di espandere il contenuto con più dettagli ed esempi")
        
        # Check for normative references
        if not any(keyword in content.lower() for keyword in ['decreto', 'legge', 'ccnl', 'codice civile']):
            suggestions.append("Aggiungi riferimenti normativi specifici per maggiore autorevolezza")
        
        # Check for practical examples
        if not any(keyword in content.lower() for keyword in ['esempio', 'template', 'modello']):
            suggestions.append("Includi esempi pratici o template per facilitare l'applicazione")
        
        return suggestions

    def _simulate_hr_research(self, category: str, topic: str) -> Dict:
        """Simula ricerca HR quando Perplexity non è disponibile."""
        simulated_content = f"""
        Ricerca simulata per {self.hr_categories.get(category, category)} - {topic}:
        
        Riferimenti Normativi Italiani:
        - Decreto Legislativo 81/2008 (Sicurezza sul lavoro)
        - Codice Civile - Libro V (Rapporti di lavoro)
        - CCNL di riferimento settoriale
        - Decreto Legislativo 196/2003 (Privacy)
        
        Best Practices PMI:
        - Procedure standardizzate per efficienza operativa
        - Documentazione conforme normativa italiana
        - Template personalizzabili per diverse esigenze
        - Integrazione con sistemi HR digitali
        
        Nota: Questa è ricerca simulata. Per informazioni real-time,
        configurare Perplexity API key nelle variabili ambiente.
        """
        
        return {
            'content': simulated_content,
            'sources': [
                'Decreto Legislativo 81/2008',
                'Codice Civile Italiano',
                'CCNL Settoriale',
                'GDPR - Regolamento UE 2016/679'
            ],
            'research_time': datetime.utcnow().isoformat()
        }

    def _get_fallback_hr_response(self, user_message: str, user_id: int) -> Dict:
        """Risposta di fallback per errori HR chatbot."""
        return {
            'response': f"""🤖 **Assistente HR - Servizio Temporaneamente Non Disponibile**

Mi dispiace, sto riscontrando difficoltà tecniche nel elaborare la tua richiesta: "{user_message}"

**Cosa puoi fare ora:**
• Riprova tra qualche minuto
• Contatta direttamente l'ufficio HR per assistenza urgente
• Controlla la knowledge base aziendale per info rapide

**Contatti HR:**
📧 <EMAIL>
📞 +39 XXX XXXXXXX

Ti assicuro che risolveremo al più presto!""",
            'category': 'general',
            'confidence': 'low',
            'kb_entries_used': [],
            'response_time_ms': 100,
            'conversation_id': None,
            'suggested_actions': [
                'Contatta direttamente HR',
                'Riprova la richiesta più tardi',
                'Consulta knowledge base aziendale'
            ]
        }

    def _get_simple_fallback_response(self, user_message: str, category: str) -> Dict:
        """Risposta fallback semplice per errori generazione."""
        category_name = self.hr_categories.get(category, 'Generale')
        
        return {
            'content': f"""**Informazioni {category_name}**

Ho ricevuto la tua domanda su: "{user_message}"

Per questa tipologia di richiesta ({category_name}), ti consiglio di:

1. **Consultare** la documentazione aziendale disponibile
2. **Contattare** direttamente l'ufficio HR per informazioni specifiche
3. **Verificare** eventuali comunicazioni recenti sull'argomento

📞 **Contatto HR diretto:** <EMAIL>

Sarò in grado di fornirti risposte più dettagliate non appena il servizio tornerà completamente operativo.""",
            'confidence': 'low',
            'suggested_actions': [
                'Contatta HR per informazioni specifiche',
                'Consulta documentazione aziendale',
                'Riprova più tardi per assistenza completa'
            ]
        }

    def _get_content_generation_fallback(self, category: str, topic: str) -> Dict:
        """Fallback per content generation quando AI non è disponibile."""
        category_name = self.hr_categories.get(category, category)
        
        return {
            'content': f"""# {topic}

## Struttura Base per {category_name}

Questo template di base può essere utilizzato come punto di partenza per sviluppare contenuti su **{topic}**.

### Sezioni Consigliate:

1. **Panoramica**
   - Introduzione all'argomento
   - Applicabilità nel contesto aziendale

2. **Normativa di Riferimento**
   - Decreto Legislativo applicabile
   - CCNL settoriale
   - Disposizioni aziendali

3. **Procedure Operative**
   - Passaggi step-by-step
   - Responsabilità e ruoli
   - Tempistiche

4. **Documenti e Template**
   - Modulistica necessaria
   - Template di comunicazione
   - Checklist operative

5. **FAQ e Casi Comuni**
   - Domande frequenti
   - Situazioni tipiche
   - Soluzioni pratiche

### Nota Importante

Per contenuti più dettagliati e aggiornati, si consiglia di:
- Configurare i servizi AI (OpenAI + Perplexity)
- Consultare fonti normative ufficiali
- Coinvolgere esperti HR nella revisione

---
*Template generato automaticamente - Personalizzare secondo esigenze specifiche*""",
            'sources': [],
            'confidence': 'low',
            'processing_time': 0.1,
            'template_used': False,
            'suggestions': [
                'Configura servizi AI per content generation avanzata',
                'Consulta fonti normative ufficiali aggiornate', 
                'Fai revisionare da esperti HR interni'
            ]
        }


# Factory functions per integration
def get_hr_ai_service() -> HRAIService:
    """Factory function per HR AI Service."""
    return HRAIService()

async def process_hr_query(user_message: str, user_id: int, session_id: str = None) -> Dict:
    """
    Processa query HR in modo asincrono.
    Wrapper principale per l'integrazione con Flask routes.
    """
    service = get_hr_ai_service()
    return await service.process_hr_query(user_message, user_id, session_id)

async def generate_hr_content(category: str, topic: str, requirements: str = '', template_id: int = None) -> Dict:
    """
    Genera contenuto HR assistito da AI.
    Wrapper per content creation da parte degli amministratori.
    """
    service = get_hr_ai_service()
    return await service.generate_hr_content(category, topic, requirements, template_id)