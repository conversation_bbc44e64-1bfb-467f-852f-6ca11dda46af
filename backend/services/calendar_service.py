"""
Servizio calendario per export .ics di interviews recruiting.
Genera file calendario universali compatibili con Google Calendar, Outlook, Apple Calendar.
"""

from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from icalendar import Calendar, Event, vCalAddress, vText
from flask import current_app


class RecruitingCalendarService:
    """Servizio per generazione file .ics per interviews recruiting."""
    
    def __init__(self):
        self.company_name = current_app.config.get('COMPANY_NAME', 'DatPortal')
        self.calendar_name = f"{self.company_name} - Recruiting Calendar"
    
    def generate_interview_ics(self, interview_data: Dict[str, Any]) -> bytes:
        """
        Genera file .ics per singolo interview.
        
        Args:
            interview_data: <PERSON>ti completi interview con candidate, job_posting, etc.
            
        Returns:
            bytes: Contenuto file .ics
        """
        cal = Calendar()
        cal.add('prodid', f'-//{self.company_name}//Recruiting Calendar//IT')
        cal.add('version', '2.0')
        cal.add('calscale', 'GREGORIAN')
        cal.add('method', 'PUBLISH')
        cal.add('x-wr-calname', self.calendar_name)
        cal.add('x-wr-timezone', 'Europe/Rome')
        
        # Crea evento interview
        event = self._create_interview_event(interview_data)
        cal.add_component(event)
        
        return cal.to_ical()
    
    def generate_interviews_batch_ics(self, interviews_data: List[Dict[str, Any]], title: Optional[str] = None) -> bytes:
        """
        Genera file .ics per multipli interviews.
        
        Args:
            interviews_data: Lista dati interviews
            title: Titolo personalizzato calendario
            
        Returns:
            bytes: Contenuto file .ics
        """
        cal = Calendar()
        cal.add('prodid', f'-//{self.company_name}//Recruiting Calendar//IT')
        cal.add('version', '2.0')
        cal.add('calscale', 'GREGORIAN')
        cal.add('method', 'PUBLISH')
        
        calendar_title = title or f"{self.calendar_name} - {len(interviews_data)} Colloqui"
        cal.add('x-wr-calname', calendar_title)
        cal.add('x-wr-timezone', 'Europe/Rome')
        
        # Aggiungi eventi per ogni interview
        for interview_data in interviews_data:
            try:
                event = self._create_interview_event(interview_data)
                cal.add_component(event)
            except Exception as e:
                current_app.logger.warning(f"Skipping interview {interview_data.get('id')} due to error: {str(e)}")
                continue
        
        return cal.to_ical()
    
    def generate_monthly_calendar_ics(self, interviews_data: List[Dict[str, Any]], year: int, month: int) -> bytes:
        """
        Genera calendario mensile con tutti gli interviews del mese.
        
        Args:
            interviews_data: Lista interviews del mese
            year: Anno
            month: Mese (1-12)
            
        Returns:
            bytes: Contenuto file .ics
        """
        month_name = datetime(year, month, 1).strftime('%B %Y')
        title = f"{self.company_name} - Colloqui {month_name}"
        
        return self.generate_interviews_batch_ics(interviews_data, title)
    
    def _create_interview_event(self, interview_data: Dict[str, Any]) -> Event:
        """
        Crea evento calendario per singolo interview.
        
        Args:
            interview_data: Dati interview
            
        Returns:
            Event: Evento calendario iCal
        """
        event = Event()
        
        # Parse datetime
        start_time = datetime.fromisoformat(interview_data['scheduled_date'].replace('Z', '+00:00'))
        end_time = start_time + timedelta(minutes=interview_data.get('duration_minutes', 60))
        
        # Basic event info
        event.add('uid', f"interview-{interview_data['id']}@{self.company_name.lower()}.com")
        event.add('dtstart', start_time)
        event.add('dtend', end_time)
        event.add('dtstamp', datetime.now())
        event.add('created', datetime.now())
        event.add('last-modified', datetime.now())
        
        # Titolo evento
        candidate_name = interview_data['candidate']['full_name']
        job_title = interview_data['job_posting']['title']
        interview_type = self._get_interview_type_label(interview_data['interview_type'])
        
        summary = f"Colloquio {interview_type}: {candidate_name} - {job_title}"
        event.add('summary', summary)
        
        # Descrizione dettagliata
        description = self._build_interview_description(interview_data)
        event.add('description', description)
        
        # Location
        if interview_data.get('location'):
            event.add('location', interview_data['location'])
        
        # Partecipanti
        if interview_data.get('interviewer'):
            # Organizzatore (interviewer)
            organizer = vCalAddress(f"MAILTO:{interview_data['interviewer'].get('email', 'recruiting@' + self.company_name.lower() + '.com')}")
            organizer.params['cn'] = vText(interview_data['interviewer']['full_name'])
            organizer.params['role'] = vText('CHAIR')
            event.add('organizer', organizer)
            
            # Attendee (interviewer)
            interviewer_attendee = vCalAddress(f"MAILTO:{interview_data['interviewer'].get('email', 'recruiting@' + self.company_name.lower() + '.com')}")
            interviewer_attendee.params['cn'] = vText(interview_data['interviewer']['full_name'])
            interviewer_attendee.params['role'] = vText('REQ-PARTICIPANT')
            interviewer_attendee.params['partstat'] = vText('ACCEPTED')
            event.add('attendee', interviewer_attendee)
        
        # Attendee (candidato)
        candidate_attendee = vCalAddress(f"MAILTO:{interview_data['candidate']['email']}")
        candidate_attendee.params['cn'] = vText(interview_data['candidate']['full_name'])
        candidate_attendee.params['role'] = vText('REQ-PARTICIPANT')
        candidate_attendee.params['partstat'] = vText('NEEDS-ACTION')
        event.add('attendee', candidate_attendee)
        
        # Categoria
        event.add('categories', 'RECRUITING,INTERVIEW')
        
        # Status
        status_map = {
            'scheduled': 'CONFIRMED',
            'completed': 'CONFIRMED', 
            'cancelled': 'CANCELLED',
            'rescheduled': 'TENTATIVE'
        }
        event.add('status', status_map.get(interview_data.get('status', 'scheduled'), 'CONFIRMED'))
        
        # Priorità in base al tipo
        priority_map = {
            'final': '1',  # Alta
            'technical': '3',  # Media
            'behavioral': '5',  # Normal
            'phone': '7',  # Bassa
            'video': '5'  # Normal
        }
        event.add('priority', priority_map.get(interview_data['interview_type'], '5'))
        
        # Alarm (reminder 15 minuti prima)
        self._add_interview_reminder(event)
        
        return event
    
    def _build_interview_description(self, interview_data: Dict[str, Any]) -> str:
        """
        Costruisce descrizione dettagliata per evento interview.
        
        Args:
            interview_data: Dati interview
            
        Returns:
            str: Descrizione formattata
        """
        lines = []
        
        # Header
        lines.append(f"🎯 COLLOQUIO DI SELEZIONE - {self.company_name}")
        lines.append("=" * 50)
        lines.append("")
        
        # Dettagli candidato
        lines.append("👤 CANDIDATO:")
        lines.append(f"   Nome: {interview_data['candidate']['full_name']}")
        lines.append(f"   Email: {interview_data['candidate']['email']}")
        if interview_data['candidate'].get('phone'):
            lines.append(f"   Telefono: {interview_data['candidate']['phone']}")
        lines.append("")
        
        # Dettagli posizione
        lines.append("💼 POSIZIONE:")
        lines.append(f"   Titolo: {interview_data['job_posting']['title']}")
        if interview_data['job_posting'].get('department'):
            lines.append(f"   Dipartimento: {interview_data['job_posting']['department']['name']}")
        lines.append("")
        
        # Dettagli colloquio
        lines.append("📋 DETTAGLI COLLOQUIO:")
        lines.append(f"   Tipo: {self._get_interview_type_label(interview_data['interview_type'])}")
        lines.append(f"   Durata: {interview_data['duration_minutes']} minuti")
        
        if interview_data.get('interviewer'):
            lines.append(f"   Intervistatore: {interview_data['interviewer']['full_name']}")
        
        if interview_data.get('location'):
            lines.append(f"   Luogo/Link: {interview_data['location']}")
        
        lines.append("")
        
        # Note
        if interview_data.get('notes'):
            lines.append("📝 NOTE:")
            lines.append(f"   {interview_data['notes']}")
            lines.append("")
        
        # Footer
        lines.append("=" * 50)
        lines.append(f"Generato da {self.company_name} Recruiting System")
        
        return "\\n".join(lines)
    
    def _add_interview_reminder(self, event: Event) -> None:
        """
        Aggiunge reminder all'evento interview.
        
        Args:
            event: Evento calendario
        """
        from icalendar import Alarm
        
        # Reminder 15 minuti prima
        alarm = Alarm()
        alarm.add('action', 'DISPLAY')
        alarm.add('description', 'Reminder: Colloquio tra 15 minuti')
        alarm.add('trigger', timedelta(minutes=-15))
        
        event.add_component(alarm)
        
        # Reminder aggiuntivo 1 giorno prima (solo per colloqui importanti)
        if event.get('priority') and int(str(event.get('priority'))) <= 3:
            daily_alarm = Alarm()
            daily_alarm.add('action', 'DISPLAY')
            daily_alarm.add('description', 'Reminder: Colloquio domani')
            daily_alarm.add('trigger', timedelta(days=-1))
            
            event.add_component(daily_alarm)
    
    def _get_interview_type_label(self, interview_type: str) -> str:
        """
        Converte tipo interview in label italiana.
        
        Args:
            interview_type: Tipo interview
            
        Returns:
            str: Label italiana
        """
        labels = {
            'phone': 'Telefonico',
            'video': 'Video Call',
            'in_person': 'Di Persona',
            'technical': 'Tecnico',
            'behavioral': 'Comportamentale',
            'final': 'Finale'
        }
        return labels.get(interview_type, interview_type.title())


# Singleton instance
recruiting_calendar_service = RecruitingCalendarService()