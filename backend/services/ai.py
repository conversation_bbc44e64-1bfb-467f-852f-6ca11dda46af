import os
import json
import requests
import logging
from openai import OpenAI
from flask import current_app

logger = logging.getLogger(__name__)

# API Keys
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
PERPLEXITY_API_KEY = os.environ.get("PERPLEXITY_API_KEY")

# OpenAI client will be initialized lazily
openai_client = None


class AIService:
    """Unified AI service for OpenAI and Perplexity interactions"""
    
    def __init__(self):
        self.openai_client = None
        
    def get_openai_client(self):
        """Get or create OpenAI client instance."""
        if self.openai_client is None:
            if not OPENAI_API_KEY:
                raise ValueError("OPENAI_API_KEY environment variable not set")
            self.openai_client = OpenAI(api_key=OPENAI_API_KEY)
        return self.openai_client
    
    def query_openai(self, prompt, model=None, max_tokens=500, temperature=0.7):
        """Query OpenAI with a prompt using configured model"""
        if model is None:
            model = current_app.config.get('OPENAI_DEFAULT_MODEL', 'gpt-4o-mini')
        """Query OpenAI with a prompt"""
        try:
            client = self.get_openai_client()
            response = client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=max_tokens,
                temperature=temperature,
            )
            
            return {
                "success": True,
                "response": response.choices[0].message.content,
                "model": model
            }
        except Exception as e:
            logger.error(f"OpenAI API error: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "response": None
            }


# Global instance
ai_service = AIService()

def get_openai_client():
    """Get or create OpenAI client instance."""
    global openai_client
    if openai_client is None:
        if not OPENAI_API_KEY:
            raise ValueError("OPENAI_API_KEY environment variable not set")
        openai_client = OpenAI(api_key=OPENAI_API_KEY)
    return openai_client

def get_configured_model(model_type='default'):
    """Get configured OpenAI model for specific use case."""
    config_keys = {
        'default': 'OPENAI_DEFAULT_MODEL',
        'analysis': 'OPENAI_ANALYSIS_MODEL', 
        'chat': 'OPENAI_CHAT_MODEL'
    }
    
    config_key = config_keys.get(model_type, 'OPENAI_DEFAULT_MODEL')
    return current_app.config.get(config_key, 'gpt-4o-mini')

def analyze_text_with_openai(text, prompt="", model=None):
    """
    Analyze text using OpenAI GPT models
    """
    try:
        # Get configured model or use default
        if model is None:
            model = get_configured_model('analysis')
            
        if not prompt:
            prompt = "Analyze the following text and provide insights:"

        complete_prompt = f"{prompt}\n\n{text}"

        client = get_openai_client()
        response = client.chat.completions.create(
            model=model,
            messages=[{"role": "user", "content": complete_prompt}],
            temperature=0.2,
        )

        return response.choices[0].message.content
    except Exception as e:
        logger.error(f"OpenAI API error: {str(e)}")
        return f"Error analyzing text: {str(e)}"

def generate_summary_with_openai(text, max_length=200):
    """
    Generate a concise summary of the provided text
    """
    try:
        prompt = f"Summarize the following text in about {max_length} words or less:"

        client = get_openai_client()
        response = client.chat.completions.create(
            model=get_configured_model('analysis'),
            messages=[{"role": "user", "content": f"{prompt}\n\n{text}"}],
            temperature=0.3,
        )

        return response.choices[0].message.content
    except Exception as e:
        logger.error(f"OpenAI API error: {str(e)}")
        return f"Error generating summary: {str(e)}"

def extract_insights_with_openai(text, context="business"):
    """
    Extract key insights from text with a specific business context
    """
    try:
        prompt = f"Extract key {context} insights and actionable points from the following text:"

        client = get_openai_client()
        response = client.chat.completions.create(
            model=get_configured_model('analysis'),
            messages=[{"role": "user", "content": f"{prompt}\n\n{text}"}],
            temperature=0.2,
            response_format={"type": "json_object"},
        )

        return json.loads(response.choices[0].message.content)
    except Exception as e:
        logger.error(f"OpenAI API error: {str(e)}")
        return {"error": str(e), "insights": []}


def generate_detailed_funding_analysis(company_profile, funding_opportunity):
    """
    Generate comprehensive AI analysis for funding opportunity matching
    considering technical compatibility, financial feasibility, and success probability
    """
    try:
        # Prepare comprehensive analysis prompt
        analysis_prompt = f"""
Analizza in dettaglio la compatibilità tra questa azienda e questa opportunità di finanziamento.

PROFILO AZIENDA:
- Settore: {company_profile.get('sector', 'Non specificato')}
- Dimensione: {company_profile.get('size', 'Non specificata')}
- Competenze: {', '.join(company_profile.get('skills', []))}
- Progetti precedenti: {', '.join(company_profile.get('projects', []))}
- Certificazioni: {', '.join(company_profile.get('certifications', []))}
- Localizzazione: {company_profile.get('location', 'Non specificata')}
- Budget disponibile: {company_profile.get('budget', 'Non specificato')}

OPPORTUNITÀ DI FINANZIAMENTO:
- Titolo: {funding_opportunity.get('title', '')}
- Obiettivi: {funding_opportunity.get('objectives', '')}
- Settori target: {', '.join(funding_opportunity.get('target_sectors', []))}
- Requisiti tecnici: {funding_opportunity.get('technical_requirements', '')}
- Budget: {funding_opportunity.get('budget_range', '')}
- Criteri di valutazione: {funding_opportunity.get('evaluation_criteria', '')}
- Scadenza: {funding_opportunity.get('deadline', '')}
- Ambito geografico: {funding_opportunity.get('geographic_scope', '')}

Fornisci un'analisi strutturata in formato JSON con:
1. match_score (0-100): Punteggio di compatibilità complessivo
2. technical_compatibility (0-100): Compatibilità tecnica e settoriale
3. financial_feasibility (0-100): Fattibilità finanziaria e di budget
4. success_probability (0-100): Probabilità di successo della candidatura
5. recommendation: Raccomandazione ("Altamente raccomandato", "Raccomandato", "Da valutare", "Non raccomandato")
6. insights: Array di insight chiave (massimo 4)
7. strengths: Array di punti di forza dell'azienda per questa opportunità
8. weaknesses: Array di punti deboli o aree di miglioramento
9. next_steps: Array di prossimi passi consigliati
10. detailed_analysis: Analisi dettagliata testuale

Rispondi SOLO con JSON valido.
"""

        client = get_openai_client()
        response = client.chat.completions.create(
            model=get_configured_model('analysis'),
            messages=[{"role": "user", "content": analysis_prompt}],
            temperature=0.3,
            response_format={"type": "json_object"}
        )

        analysis_result = json.loads(response.choices[0].message.content)
        
        # Ensure all required fields are present with defaults
        default_analysis = {
            "match_score": 50,
            "technical_compatibility": 50,
            "financial_feasibility": 50,
            "success_probability": 50,
            "recommendation": "Da valutare",
            "insights": ["Analisi in corso"],
            "strengths": ["Da determinare"],
            "weaknesses": ["Da valutare"],
            "next_steps": ["Revisione dettagliata necessaria"],
            "detailed_analysis": "Analisi dettagliata in elaborazione"
        }
        
        # Merge with defaults
        for key, default_value in default_analysis.items():
            if key not in analysis_result:
                analysis_result[key] = default_value
                
        return analysis_result
        
    except Exception as e:
        logger.error(f"Error in detailed funding analysis: {str(e)}")
        return {
            "match_score": 0,
            "technical_compatibility": 0,
            "financial_feasibility": 0,
            "success_probability": 0,
            "recommendation": "Errore nell'analisi",
            "insights": [f"Errore durante l'analisi: {str(e)}"],
            "strengths": [],
            "weaknesses": [],
            "next_steps": ["Riprovare l'analisi"],
            "detailed_analysis": f"Si è verificato un errore durante l'analisi: {str(e)}"
        }

def analyze_with_perplexity(text, question="", model="sonar"):
    """
    Analyze text using Perplexity API
    """
    if not PERPLEXITY_API_KEY:
        logger.error("Perplexity API key not found")
        return "Error: Perplexity API key not configured"

    try:
        if not question:
            question = "Analyze this text and provide insights:"

        headers = {
            "Authorization": f"Bearer {PERPLEXITY_API_KEY}",
            "Content-Type": "application/json"
        }

        data = {
            "model": model,
            "messages": [
                {
                    "role": "system",
                    "content": "You are an AI assistant for business analytics. Be precise and concise."
                },
                {
                    "role": "user",
                    "content": f"{question}\n\n{text}"
                }
            ],
            "temperature": 0.2,
            "max_tokens": 500,
            "stream": False
        }

        response = requests.post(
            "https://api.perplexity.ai/chat/completions",
            headers=headers,
            json=data
        )

        if response.status_code == 200:
            response_data = response.json()
            return {
                "content": response_data["choices"][0]["message"]["content"],
                "citations": response_data.get("citations", [])
            }
        else:
            logger.error(f"Perplexity API error: {response.status_code} - {response.text}")
            return {"error": f"API Error: {response.status_code}", "content": ""}
    except Exception as e:
        logger.error(f"Perplexity API call error: {str(e)}")
        return {"error": str(e), "content": ""}

def analyze_project_requirements(requirements_text):
    """
    Analyze project requirements and extract key components
    """
    try:
        prompt = """
        Analyze these project requirements and extract the following information in JSON format:
        1. Key deliverables
        2. Potential risks
        3. Resources needed
        4. Estimated timeline
        5. Success criteria
        """

        client = get_openai_client()
        response = client.chat.completions.create(
            model=get_configured_model('analysis'),
            messages=[{"role": "user", "content": f"{prompt}\n\n{requirements_text}"}],
            temperature=0.2,
            response_format={"type": "json_object"},
        )

        return json.loads(response.choices[0].message.content)
    except Exception as e:
        logger.error(f"Project requirements analysis error: {str(e)}")
        return {
            "error": str(e),
            "key_deliverables": [],
            "potential_risks": [],
            "resources_needed": [],
            "estimated_timeline": "Unknown",
            "success_criteria": []
        }

def generate_funding_recommendations(company_profile):
    """
    Generate funding recommendations based on company profile
    """
    try:
        prompt = """
        Based on this company profile, provide recommendations for:
        1. Suitable funding opportunities
        2. Grant programs to consider
        3. Application strategy suggestions
        4. Key points to highlight

        Respond in JSON format.
        """

        client = get_openai_client()
        response = client.chat.completions.create(
            model=get_configured_model('analysis'),
            messages=[{"role": "user", "content": f"{prompt}\n\n{company_profile}"}],
            temperature=0.3,
            response_format={"type": "json_object"},
        )

        return json.loads(response.choices[0].message.content)
    except Exception as e:
        logger.error(f"Funding recommendations error: {str(e)}")
        return {
            "error": str(e),
            "funding_opportunities": [],
            "grant_programs": [],
            "application_strategy": [],
            "key_highlights": []
        }

def extract_skills_from_cv(cv_text):
    """
    Estrae competenze da un CV usando OpenAI
    """
    try:
        prompt = """
        Analizza questo CV e estrai tutte le competenze tecniche e professionali.
        Categorizza le competenze in:
        - Linguaggi di programmazione
        - Framework e librerie
        - Database
        - Cloud e DevOps
        - Soft skills
        - Certificazioni
        - Strumenti e software
        - Metodologie

        Restituisci il risultato in formato JSON con questa struttura:
        {
            "skills": [
                {
                    "name": "nome competenza",
                    "category": "categoria",
                    "level": "beginner|intermediate|advanced|expert"
                }
            ],
            "summary": "breve riassunto del profilo professionale",
            "experience_years": numero_totale_anni_esperienza
        }
        """

        client = get_openai_client()
        response = client.chat.completions.create(
            model=get_configured_model('analysis'),
            messages=[{"role": "user", "content": f"{prompt}\n\nCV:\n{cv_text}"}],
            temperature=0.2,
            response_format={"type": "json_object"},
        )

        return json.loads(response.choices[0].message.content)
    except Exception as e:
        logger.error(f"OpenAI CV analysis error: {str(e)}")
        return {"error": str(e), "skills": [], "summary": "", "experience_years": 0}

def generate_cv_html(user_data, skills_data):
    """
    Genera HTML per CV usando OpenAI
    """
    try:
        prompt = f"""
        Genera un CV professionale in HTML per questo profilo:

        Dati utente: {json.dumps(user_data, indent=2)}
        Competenze: {json.dumps(skills_data, indent=2)}

        Crea un CV completo con:
        - Intestazione con dati personali
        - Profilo professionale (summary)
        - Competenze tecniche organizzate per categoria
        - Layout professionale con CSS inline
        - Stile moderno e pulito

        Restituisci solo l'HTML completo del CV.
        """

        client = get_openai_client()
        response = client.chat.completions.create(
            model=get_configured_model('analysis'),
            messages=[{"role": "user", "content": prompt}],
            temperature=0.3,
        )

        return response.choices[0].message.content
    except Exception as e:
        logger.error(f"OpenAI CV generation error: {str(e)}")
        return f"<p>Errore nella generazione del CV: {str(e)}</p>"

# ============================================================================
# RESOURCE ALLOCATION AI SERVICES
# ============================================================================

def analyze_resource_allocation(project_data, available_resources, current_allocations=None):
    """
    Analizza l'allocazione delle risorse per un progetto e fornisce suggerimenti AI
    """
    try:
        # Prepara i dati per l'analisi
        context = {
            "project": {
                "name": project_data.get("name", ""),
                "description": project_data.get("description", ""),
                "project_type": project_data.get("project_type", ""),
                "start_date": project_data.get("start_date", ""),
                "end_date": project_data.get("end_date", ""),
                "budget": project_data.get("budget", 0),
                "estimated_hours": project_data.get("estimated_hours", 0),
                "required_skills": project_data.get("required_skills", [])
            },
            "available_resources": available_resources,
            "current_allocations": current_allocations or []
        }

        prompt = """
        Analizza questa situazione di allocazione risorse e fornisci suggerimenti intelligenti.

        IMPORTANTE: Usa SOLO i nomi reali degli utenti forniti nei dati available_resources.
        NON usare placeholder generici come "employee" o "admin".

        Considera:
        1. Competenze richieste vs competenze disponibili
        2. Carico di lavoro attuale delle risorse
        3. Disponibilità temporale
        4. Costi e budget
        5. Efficienza del team

        Fornisci suggerimenti in formato JSON con:
        - recommended_allocations: array di allocazioni suggerite con user_id, user_name (nome reale), role, allocation (percentuale numerica)
        - optimization_insights: insights per ottimizzazione usando i nomi reali
        - potential_conflicts: conflitti potenziali identificati
        - efficiency_score: punteggio efficienza (0-100)
        - cost_analysis: analisi costi

        Esempio formato recommended_allocations:
        [
          {
            "user_id": 1,
            "user_name": "Mario Rossi",
            "role": "Senior Developer",
            "allocation": 80
          }
        ]
        """

        client = get_openai_client()
        response = client.chat.completions.create(
            model=get_configured_model('analysis'),
            messages=[
                {"role": "system", "content": "Sei un esperto AI in resource management e project planning. Analizza i dati e fornisci suggerimenti pratici e attuabili."},
                {"role": "user", "content": f"{prompt}\n\nDati:\n{json.dumps(context, indent=2)}"}
            ],
            temperature=0.2,
            response_format={"type": "json_object"},
        )

        return json.loads(response.choices[0].message.content)
    except Exception as e:
        logger.error(f"Resource allocation analysis error: {str(e)}")
        return {
            "error": str(e),
            "recommended_allocations": [],
            "optimization_insights": [],
            "potential_conflicts": [],
            "efficiency_score": 0,
            "cost_analysis": {}
        }

def predict_resource_conflicts(allocations_data, timeline_data):
    """
    Predice conflitti di risorse basandosi su dati storici e allocazioni attuali
    """
    try:
        prompt = """
        Analizza queste allocazioni di risorse e predici potenziali conflitti.

        Identifica:
        1. Sovrallocazioni (>100% capacità)
        2. Conflitti temporali
        3. Competenze mancanti
        4. Rischi di burnout
        5. Dipendenze critiche

        Fornisci risultato in JSON con:
        - conflicts: array di conflitti identificati
        - risk_level: livello rischio (low/medium/high)
        - recommendations: raccomandazioni per risoluzione
        - timeline_impact: impatto su timeline progetto
        """

        context = {
            "allocations": allocations_data,
            "timeline": timeline_data
        }

        client = get_openai_client()
        response = client.chat.completions.create(
            model=get_configured_model('analysis'),
            messages=[
                {"role": "system", "content": "Sei un AI specialist in project risk management. Identifica proattivamente i rischi nelle allocazioni di risorse."},
                {"role": "user", "content": f"{prompt}\n\nDati:\n{json.dumps(context, indent=2)}"}
            ],
            temperature=0.1,
            response_format={"type": "json_object"},
        )

        return json.loads(response.choices[0].message.content)
    except Exception as e:
        logger.error(f"Resource conflict prediction error: {str(e)}")
        return {
            "error": str(e),
            "conflicts": [],
            "risk_level": "unknown",
            "recommendations": [],
            "timeline_impact": "unknown"
        }

def analyze_platform_compliance(standard_code, company_features_usage=None):
    """
    Analizza la compliance di un'azienda per una certificazione specifica
    basandosi sull'utilizzo delle funzionalità DatPortal con dati reali aggregati
    """
    try:
        import os
        import json
        from flask import current_app
        from .data_aggregation import collect_platform_usage_data
        
        current_app.logger.info(f"🎯 [AI Service] Starting platform compliance analysis for {standard_code}")
        
        # Se non sono forniti dati di utilizzo, raccoglie dati reali aggregati
        if company_features_usage is None:
            current_app.logger.info(f"📊 [AI Service] No usage data provided, collecting real aggregated data for {standard_code}")
            company_features_usage = collect_platform_usage_data(standard_code)
        else:
            current_app.logger.info(f"📈 [AI Service] Using provided usage data for {standard_code}")
        
        # Carica il mapping features -> compliance
        features_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'features.json')
        with open(features_path, 'r', encoding='utf-8') as f:
            features_data = json.load(f)
        
        datportal_features = features_data.get('datportal_features', {})
        calculator_config = features_data.get('certification_readiness_calculator', {})
        
        current_app.logger.info(f"🔧 [AI Service] Loaded features configuration with {len(datportal_features)} platform features")
        
        # Calcola compliance score basato su utilizzo features con ENHANCED MODULE WEIGHTING
        platform_score = 0
        total_possible_score = 0
        compliance_details = {}
        
        current_app.logger.info(f"🔍 [AI Service] Analyzing ENHANCED features compliance for {standard_code}")
        
        # Lista per raccogliere i log di calcolo
        calculation_logs = []
        calculation_logs.append(f"🎯 Analisi ENHANCED compliance per {standard_code}")
        calculation_logs.append(f"📊 Dati utilizzo raccolti: {len(company_features_usage)} features")
        
        # Enhanced weighting for new modules
        enhanced_module_weights = {
            'governance_module': 1.5,      # Governance ha peso maggiore per compliance
            'ceo_strategic_module': 1.2,   # CEO strategic importance
            'engagement_module': 1.1,      # Engagement per training compliance
            'recruiting_module': 1.1,      # Recruiting per competency management
            # Moduli esistenti mantengono peso standard
            'project_management': 1.0,
            'personnel_management': 1.0,
            'time_tracking': 1.0,
            'document_management': 1.0,
            'crm_system': 1.0,
            'financial_management': 1.0,
            'communication_platform': 1.0,
            'analytics_dashboard': 1.0,
            'ai_integration': 1.0,
            'access_control': 1.0
        }
        
        calculation_logs.append("🆕 ENHANCED WEIGHTING APPLICATO:")
        calculation_logs.append("   → Governance Module: +50% peso")
        calculation_logs.append("   → CEO Strategic: +20% peso")
        calculation_logs.append("   → Engagement: +10% peso")
        calculation_logs.append("   → Recruiting: +10% peso")
        
        for feature_key, feature_data in datportal_features.items():
            if standard_code in feature_data.get('compliance_mapping', {}):
                mapping = feature_data['compliance_mapping'][standard_code]
                base_compliance_score = mapping.get('compliance_score', 0)
                
                # Applica enhanced weighting per nuovi moduli
                module_weight = enhanced_module_weights.get(feature_key, 1.0)
                feature_compliance_score = base_compliance_score * module_weight
                
                # Controlla se l'azienda utilizza questa feature
                feature_usage = company_features_usage.get(feature_key, {})
                usage_score = feature_usage.get('adoption_score', 0)  # 0-100
                
                # Calcola score ponderato
                weighted_score = (feature_compliance_score * usage_score) / 100
                platform_score += weighted_score
                total_possible_score += feature_compliance_score
                
                compliance_details[feature_key] = {
                    'name': feature_data.get('name'),
                    'base_compliance_score': base_compliance_score,
                    'enhanced_weight': module_weight,
                    'compliance_score': feature_compliance_score,
                    'usage_score': usage_score,
                    'weighted_score': weighted_score,
                    'requirements_covered': mapping.get('requirements', []),
                    'notes': mapping.get('notes', ''),
                    'is_enhanced_module': module_weight > 1.0
                }
                
                # Log dettagliato delle motivazioni per ogni feature
                status = "✅ STRONG" if usage_score >= 80 else "⚠️ MODERATE" if usage_score >= 60 else "❌ WEAK"
                enhanced_indicator = " 🆕 ENHANCED" if module_weight > 1.0 else ""
                log_entry = f"📋 {feature_data.get('name')}: {usage_score}% utilizzo × {feature_compliance_score:.1f}% peso = {weighted_score:.1f} punti {status}{enhanced_indicator}"
                current_app.logger.info(f"   {log_entry}")
                calculation_logs.append(log_entry)
                
                if module_weight > 1.0:
                    weight_detail = f"   ⚡ Enhanced: {base_compliance_score}% base × {module_weight} weight = {feature_compliance_score:.1f}%"
                    current_app.logger.info(f"      {weight_detail}")
                    calculation_logs.append(weight_detail)
                
                requirements_log = f"   → Copre requisiti: {', '.join(mapping.get('requirements', [])[:2])}"
                current_app.logger.info(f"      {requirements_log}")
                calculation_logs.append(requirements_log)
        
        # Normalizza il punteggio
        if total_possible_score > 0:
            normalized_platform_score = (platform_score / total_possible_score) * 100
        else:
            normalized_platform_score = 0
            
        # Log finali e calcolo complessivo con breakdown enhanced modules
        enhanced_modules_count = sum(1 for details in compliance_details.values() if details.get('is_enhanced_module', False))
        enhanced_score_contribution = sum(details['weighted_score'] for details in compliance_details.values() if details.get('is_enhanced_module', False))
        
        final_calculation = f"🎯 Score finale ENHANCED: {platform_score:.1f} / {total_possible_score:.1f} = {normalized_platform_score:.1f}%"
        features_summary = f"📈 Features analizzate: {len(compliance_details)} su {len(datportal_features)} totali"
        enhanced_summary = f"🆕 Moduli Enhanced: {enhanced_modules_count} (contributo: {enhanced_score_contribution:.1f} punti)"
        
        current_app.logger.info(f"🎯 [AI Service] Final ENHANCED platform compliance score: {normalized_platform_score:.1f}%")
        current_app.logger.info(f"📊 [AI Service] Score calculation: {platform_score:.1f} / {total_possible_score:.1f} = {normalized_platform_score:.1f}%")
        current_app.logger.info(f"📈 [AI Service] Features analyzed: {len(compliance_details)} out of {len(datportal_features)} total")
        current_app.logger.info(f"🆕 [AI Service] Enhanced modules contribution: {enhanced_modules_count} modules = {enhanced_score_contribution:.1f} points")
        
        calculation_logs.append(final_calculation)
        calculation_logs.append(features_summary)
        calculation_logs.append(enhanced_summary)
        
        # Breakdown per categoria di moduli
        governance_details = {k: v for k, v in compliance_details.items() if 'governance' in k}
        engagement_details = {k: v for k, v in compliance_details.items() if 'engagement' in k}
        ceo_details = {k: v for k, v in compliance_details.items() if 'ceo' in k}
        recruiting_details = {k: v for k, v in compliance_details.items() if 'recruiting' in k}
        
        return {
            "success": True,
            "standard_code": standard_code,
            "platform_compliance_score": round(normalized_platform_score, 1),
            "compliance_details": compliance_details,
            "total_features_analyzed": len(compliance_details),
            "enhanced_modules_analyzed": enhanced_modules_count,
            "enhanced_score_contribution": round(enhanced_score_contribution, 1),
            "module_breakdown": {
                "governance": governance_details,
                "engagement": engagement_details,
                "ceo_strategic": ceo_details,
                "recruiting": recruiting_details
            },
            "real_data_used": company_features_usage is None,  # Indica se sono stati usati dati reali
            "recommendations": _generate_platform_recommendations(standard_code, compliance_details, features_data),
            "calculation_logs": calculation_logs  # Aggiungi i log di calcolo per il frontend
        }
        
    except Exception as e:
        logger.error(f"💥 [AI Service] Error in platform compliance analysis: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "platform_compliance_score": 0,
            "compliance_details": {},
            "recommendations": []
        }

def _generate_platform_recommendations(standard_code, compliance_details, features_data):
    """
    Genera raccomandazioni specifiche per migliorare la compliance
    """
    recommendations = []
    
    # Raccomandazioni per features con basso utilizzo
    for feature_key, details in compliance_details.items():
        if details['usage_score'] < 70:  # Soglia di utilizzo bassa
            recommendations.append({
                "type": "feature_adoption",
                "feature": details['name'],
                "current_usage": details['usage_score'],
                "potential_impact": details['compliance_score'],
                "suggestion": f"Aumentare l'utilizzo di {details['name']} per migliorare compliance del {details['compliance_score']}%"
            })
    
    # Aggiungi raccomandazioni pre-configurate dal features.json
    recommendations_engine = features_data.get('recommendations_engine', {})
    standard_suggestions = recommendations_engine.get('low_compliance_suggestions', {}).get(standard_code, [])
    
    for suggestion in standard_suggestions:
        recommendations.append({
            "type": "configuration",
            "suggestion": suggestion
        })
    
    return recommendations

def generate_certification_insights(standard_code, company_profile, platform_usage):
    """
    Genera insights AI per la creazione di una nuova certificazione
    """
    try:
        # Analizza platform compliance
        platform_analysis = analyze_platform_compliance(standard_code, platform_usage)
        
        # Prepara prompt per AI
        analysis_prompt = f"""
Analizza il profilo aziendale e l'utilizzo della piattaforma DatPortal per fornire insights sulla certificazione {standard_code}.

PROFILO AZIENDALE:
- Settore: {company_profile.get('industry', 'N/A')}
- Dimensioni: {company_profile.get('employees_count', 'N/A')} dipendenti
- Fatturato: {company_profile.get('revenue', 'N/A')}
- Principali servizi: {', '.join(company_profile.get('main_services', []))}

UTILIZZO PIATTAFORMA DATPORTAL:
- Score compliance piattaforma: {platform_analysis.get('platform_compliance_score', 0)}%
- Features attive: {platform_analysis.get('total_features_analyzed', 0)}
- Principali gap: {', '.join([r['suggestion'] for r in platform_analysis.get('recommendations', [])[:3]])}

IMPORTANTE per la stima costi (COERENZA MATEMATICA OBBLIGATORIA):
- Per GDPR: Certificazione ente €5.000-€25.000, Preparazione interna 140-280 ore (€50/ora), Audit annuali €2.000-€8.000
- Per ISO 9001: Certificazione ente €3.000-€15.000, Preparazione 240-480 ore (€50/ora), Sorveglianza annuale €2.000-€5.000
- Per ISO 27001: Certificazione ente €15.000-€35.000, Implementazione 480-960 ore (€50/ora), Sorveglianza annuale €5.000-€12.000
- NON includere costi di "sviluppo software" per certificazioni non IT
- Categorie di costo valide: "Ente certificatore", "Preparazione interna", "Consulenza esterna", "Audit e sorveglianza", "Formazione personale"
- VERIFICA SEMPRE: la somma delle voci del breakdown DEVE essere coerente con il total_range (min/max)
- Il total_range.min deve essere >= somma minima breakdown, total_range.max deve essere >= somma massima breakdown

Fornisci un'analisi JSON con questi campi esatti:
{{
  "readiness_assessment": {{
    "overall_score": <0-100>,
    "strengths": ["<strength1>", "<strength2>"],
    "gaps": ["<gap1>", "<gap2>"],
    "readiness_level": "<Pronto|In preparazione|Richiede lavoro>"
  }},
  "timeline_prediction": {{
    "estimated_months": <numero>,
    "phases": [
      {{"name": "<fase>", "duration_weeks": <numero>, "description": "<descrizione>"}}
    ]
  }},
  "cost_estimation": {{
    "total_range": {{"min": <euro>, "max": <euro>}},
    "breakdown": [
      {{"category": "<categoria>", "amount": <euro>, "description": "<descrizione>"}}
    ],
    "verification": {{
      "breakdown_sum": <somma_breakdown>,
      "is_coherent": <true/false>,
      "notes": "<note_coerenza>"
    }}
  }},
  "team_recommendations": {{
    "required_roles": ["<ruolo1>", "<ruolo2>"],
    "estimated_effort_hours": <ore_totali>,
    "key_competencies": ["<competenza1>", "<competenza2>"]
  }},
  "platform_leverage": {{
    "current_advantages": ["<vantaggio1>", "<vantaggio2>"],
    "missing_configurations": ["<config1>", "<config2>"],
    "suggested_optimizations": ["<ottimizzazione1>", "<ottimizzazione2>"]
  }}
}}

ESEMPIO DI COST_ESTIMATION COERENTE:
{{
  "cost_estimation": {{
    "total_range": {{"min": 14000, "max": 34000}},
    "breakdown": [
      {{"category": "Ente certificatore", "amount": 8000, "description": "Costo medio certificazione ISO 9001"}},
      {{"category": "Preparazione interna", "amount": 12000, "description": "360 ore @ €50/ora per preparazione documentazione"}},
      {{"category": "Audit e sorveglianza", "amount": 3500, "description": "Audit iniziale e prima sorveglianza"}},
      {{"category": "Formazione personale", "amount": 2000, "description": "Training team su standard ISO"}}
    ],
    "verification": {{
      "breakdown_sum": 25500,
      "is_coherent": true,
      "notes": "La somma del breakdown (€25.500) rientra nel range totale €14.000-€34.000"
    }}
  }}
}}
"""

        client = get_openai_client()
        response = client.chat.completions.create(
            model=get_configured_model('analysis'),
            messages=[{"role": "user", "content": analysis_prompt}],
            temperature=0.3,
            response_format={"type": "json_object"}
        )

        ai_insights = json.loads(response.choices[0].message.content)
        
        # Combina con l'analisi della piattaforma
        return {
            "success": True,
            "platform_analysis": platform_analysis,
            "ai_insights": ai_insights,
            "combined_score": round((
                platform_analysis.get('platform_compliance_score', 0) * 0.4 +
                ai_insights.get('readiness_assessment', {}).get('overall_score', 0) * 0.6
            ), 1)
        }
        
    except Exception as e:
        logger.error(f"Error generating certification insights: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "platform_analysis": platform_analysis if 'platform_analysis' in locals() else {},
            "ai_insights": {},
            "combined_score": 0
        }

def optimize_team_composition(project_requirements, candidate_resources):
    """
    Ottimizza la composizione del team per un progetto specifico
    """
    try:
        prompt = """
        Ottimizza la composizione del team per questo progetto.

        Considera:
        1. Skill match con requisiti progetto
        2. Sinergie tra membri del team
        3. Bilanciamento senior/junior
        4. Copertura competenze critiche
        5. Dinamiche di team

        Fornisci in JSON:
        - optimal_team: composizione team ottimale
        - skill_coverage: copertura competenze (%)
        - team_synergy_score: punteggio sinergia (0-100)
        - alternative_options: opzioni alternative
        - training_needs: necessità formazione
        """

        context = {
            "project_requirements": project_requirements,
            "candidate_resources": candidate_resources
        }

        client = get_openai_client()
        response = client.chat.completions.create(
            model=get_configured_model('analysis'),
            messages=[
                {"role": "system", "content": "Sei un AI expert in team building e resource optimization. Crea team ad alta performance."},
                {"role": "user", "content": f"{prompt}\n\nDati:\n{json.dumps(context, indent=2)}"}
            ],
            temperature=0.3,
            response_format={"type": "json_object"},
        )

        return json.loads(response.choices[0].message.content)
    except Exception as e:
        logger.error(f"Team composition optimization error: {str(e)}")
        return {
            "error": str(e),
            "optimal_team": [],
            "skill_coverage": 0,
            "team_synergy_score": 0,
            "alternative_options": [],
            "training_needs": []
        }

def generate_case_study_with_ai(prompt, case_type="use-case", target_audience="internal", project_context=None, company_context=None):
    """
    Genera un case study utilizzando AI basato su dati reali di progetto.
    """
    try:
        # Usa info azienda reale se disponibile
        company_name = company_context.get('name', 'La nostra azienda') if company_context else 'La nostra azienda'
        company_expertise = ', '.join(company_context.get('expertise', [])) if company_context else 'consulenza tecnologica'
        
        system_prompt = f"""
        Sei un AI expert in business communication che scrive case studies per {company_name}, un'azienda italiana specializzata in {company_expertise}.
        
        IMPORTANTE: Stai generando un case study di un PROGETTO REALE di {company_name} che usa DatPortal come piattaforma interna.
        
        Linee guida:
        1. Scrivi sempre in PRIMA PERSONA come {company_name} ("Abbiamo implementato", "Il nostro team di {company_name}", "Per il nostro cliente")
        2. Usa dati reali del progetto quando forniti nel contesto
        3. Enfatizza le competenze specifiche di {company_name}: {company_expertise}
        4. Mostra risultati concreti e misurabili ottenuti da {company_name}
        5. Evidenzia metodologie e best practices utilizzate dal team di {company_name}
        6. Scrivi per il target audience specificato (interno/prospect/partner)
        
        Struttura richiesta:
        - title: Titolo che identifica il progetto specifico di {company_name}
        - overview: Come {company_name} ha aiutato il cliente (prima persona)
        - challenge: La sfida che il cliente ha presentato a {company_name}
        - solution: La soluzione che {company_name} ha progettato e implementato
        - implementation: Il processo di implementazione di {company_name}
        - results: I risultati che {company_name} ha ottenuto per il cliente
        - technologies: Stack tecnologico utilizzato dal team di {company_name}
        - business_kpis: Metriche di successo misurabili ottenute da {company_name}
        """

        # Aggiunge contesto progetto se disponibile
        full_prompt = prompt
        if project_context:
            full_prompt += f"\n\nCONTESTO PROGETTO REALE:\n{json.dumps(project_context, indent=2)}"

        full_prompt += f"""
        
        ESEMPIO DI STRUTTURA PROFESSIONALE:
        - TITOLO: Specifico e orientato ai risultati (es: "Digitalizzazione Completa per [Cliente]: +40% Efficienza e -30% Costi Operativi")
        - OVERVIEW: Il problema del cliente, la nostra soluzione, i risultati chiave (2-3 frasi)
        - CHALLENGE: Contesto dettagliato, problemi specifici, vincoli tecnici/business
        - SOLUTION: Approccio metodologico, architettura tecnica, innovazioni implementate
        - IMPLEMENTATION: Fasi del progetto, milestone, gestione rischi, metodologie agili
        - RESULTS: Metriche specifiche, benefici tangibili, feedback del cliente, ROI calcolato
        
        Genera il contenuto in formato JSON con questa struttura:
        {{
            "title": "Titolo orientato ai risultati con metriche concrete",
            "overview": "Sintesi di valore: problema risolto, approccio unico, benefici ottenuti",
            "challenge": "Contesto aziendale del cliente, problemi specifici, vincoli e complessità affrontate",
            "solution": "Metodologia {company_name}, architettura tecnica, innovazioni e best practices implementate",
            "implementation": "Piano di progetto strutturato: fasi, milestone, gestione rischi, metodologie agili utilizzate",
            "results": "Risultati misurabili con metriche specifiche, feedback del cliente, impatto sul business",
            "technologies": ["Stack", "tecnologico", "specifico"],
            "business_kpis": {{
                "time_reduction": 40,
                "cost_reduction": 30,
                "roi": 180,
                "efficiency_gain": 45,
                "user_satisfaction": 95
            }},
            "primary_sector": "Settore principale del cliente",
            "secondary_sectors": ["Settori", "dove", "applicabile"],
            "implementation_duration": 90,
            "team_size": 5,
            "key_takeaways": ["Insight", "metodologico", "riutilizzabile"],
            "target_audience": "{target_audience}",
            "case_type": "{case_type}"
        }}
        
        IMPORTANTISSIMO: 
        - Usa terminologia business precisa e professionale
        - Includi numeri e percentuali realistiche ma impressionanti
        - Mostra competenze tecniche avanzate di {company_name}
        - Evidenzia metodologie proprietarie e best practices
        - Scrivi sempre in prima persona come {company_name}
        """

        client = get_openai_client()
        response = client.chat.completions.create(
            model=get_configured_model('analysis'),
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": full_prompt}
            ],
            temperature=0.4,
            response_format={"type": "json_object"},
        )

        result = json.loads(response.choices[0].message.content)
        
        # Assicura che i campi obbligatori siano presenti
        required_fields = {
            "title": "Case Study Generato con AI",
            "overview": "Panoramica del case study generata automaticamente.",
            "challenge": "Sfida identificata dal sistema AI.",
            "solution": "Soluzione proposta dal sistema AI.",
            "implementation": "Piano di implementazione generato.",
            "results": "Risultati attesi o ottenuti.",
            "technologies": [],
            "business_kpis": {},
            "primary_sector": "Generale",
            "secondary_sectors": [],
            "implementation_duration": 60,
            "team_size": 3,
            "key_takeaways": [],
            "target_audience": target_audience,
            "case_type": case_type
        }
        
        for field, default_value in required_fields.items():
            if field not in result:
                result[field] = default_value

        logger.info(f"Case study generated successfully for type: {case_type}, audience: {target_audience}")
        return result

    except Exception as e:
        logger.error(f"Case study generation error: {str(e)}")
        # Fallback con contenuto base
        return {
            "error": str(e),
            "title": "Case Study - Errore Generazione",
            "overview": "Si è verificato un errore durante la generazione del case study con AI.",
            "challenge": "Errore nel sistema di generazione AI.",
            "solution": "Riprovare la generazione o creare manualmente.",
            "implementation": "Non disponibile a causa dell'errore.",
            "results": "Generazione fallita.",
            "technologies": [],
            "business_kpis": {},
            "primary_sector": "Generale",
            "secondary_sectors": [],
            "implementation_duration": 0,
            "team_size": 0,
            "key_takeaways": ["Errore nella generazione AI"],
            "target_audience": target_audience,
            "case_type": case_type
        }

def search_funding_opportunities(company_profile, search_criteria=None, model="sonar-pro"):
    """
    Ricerca opportunità di bandi e finanziamenti usando Perplexity AI
    
    Args:
        company_profile: Profilo aziendale con settore, dimensioni, localizzazione
        search_criteria: Criteri di ricerca specifici (importo, scadenze, etc.)
        model: Modello Perplexity da utilizzare (default: sonar-pro)
    
    Returns:
        Lista di opportunità trovate con score di compatibilità
    """
    if not PERPLEXITY_API_KEY:
        logger.error("Perplexity API key not found")
        return {
            "error": "API key not configured",
            "opportunities": [],
            "search_performed": False
        }

    try:
        # Costruisci il prompt per la ricerca strutturata
        # Aggiungi keywords specifiche se presenti
        keywords = search_criteria.get('keywords', '') if search_criteria else ''
        
        base_query = f"""
        Cerca bandi e opportunità di finanziamento pubblici italiani ed europei per:
        
        AZIENDA:
        - Settore: {company_profile.get('sector', 'Non specificato')}
        - Dimensione: {company_profile.get('size', 'PMI')}
        - Localizzazione: {company_profile.get('location', 'Italia')}
        - Attività principali: {company_profile.get('activities', 'Digitalizzazione e innovazione')}
        
        PAROLE CHIAVE SPECIFICHE: {keywords}
        """
        
        if search_criteria:
            criteria_text = f"""
        
        CRITERI SPECIFICI:
        - Importo massimo: {search_criteria.get('max_amount', 'Qualsiasi')}
        - Ambito geografico: {search_criteria.get('geographic_scope', 'Nazionale/Regionale')}
        - Settori target: {search_criteria.get('target_sectors', [])}
        - Scadenza entro: {search_criteria.get('deadline_limit', '6 mesi')}
            """
            base_query += criteria_text

        query = base_query + """
        
        ISTRUZIONI SPECIFICHE:
        1. Se sono specificate PAROLE CHIAVE, cerca prioritariamente bandi che le contengono nel titolo o descrizione
        2. Concentrati su bandi attivi nel 2025 e ancora aperti per candidature
        3. Includi solo bandi realmente esistenti con informazioni verificabili
        
        FONTI DA CONSULTARE:
        - Regioni italiane (focus su Piemonte se richiesto)
        - Ministeri (MISE, MIUR, etc.)
        - Unione Europea (Horizon Europe, Digital Europe, etc.)
        - Enti nazionali (Invitalia, Simest, etc.)
        
        FORMATO RISPOSTA RICHIESTO - RISPONDI SOLO IN QUESTO FORMATO JSON:
        {
          "bandi_trovati": [
            {
              "titolo": "Nome esatto del bando",
              "ente_erogatore": "Nome ente",
              "importo_massimo": "Cifra in euro o descrizione",
              "percentuale_copertura": "Percentuale finanziamento",
              "scadenza": "Data scadenza YYYY-MM-DD o descrizione",
              "settori_target": ["settore1", "settore2"],
              "requisiti_principali": "Requisiti chiave",
              "link_ufficiale": "URL completo se disponibile",
              "motivo_compatibilita": "Perché è adatto all'azienda",
              "descrizione_breve": "Breve descrizione del bando"
            }
          ]
        }
        
        IMPORTANTE: Rispondi SOLO con il JSON, senza testo aggiuntivo prima o dopo.
        """

        headers = {
            "Authorization": f"Bearer {PERPLEXITY_API_KEY}",
            "Content-Type": "application/json"
        }

        # Usa il modello specificato (default sonar-pro per ricerche approfondite)
        data = {
            "model": model,
            "messages": [
                {
                    "role": "system",
                    "content": "Sei un esperto consulente di bandi e finanziamenti pubblici italiani ed europei. Fornisci informazioni accurate e aggiornate su opportunità reali di finanziamento per PMI e startup."
                },
                {
                    "role": "user",
                    "content": query
                }
            ],
            "temperature": 0.1,  # Bassa per risultati più precisi
            "max_tokens": 3000,  # Aumentato per JSON strutturato
            "stream": False
        }

        response = requests.post(
            "https://api.perplexity.ai/chat/completions",
            headers=headers,
            json=data,
            timeout=30
        )

        if response.status_code == 200:
            response_data = response.json()
            content = response_data["choices"][0]["message"]["content"]
            citations = response_data.get("citations", [])
            
            # Parse delle opportunità dal contenuto
            opportunities = parse_funding_opportunities_from_ai_response(content)
            
            return {
                "content": content,
                "citations": citations,
                "opportunities": opportunities,
                "search_performed": True,
                "model_used": model,
                "company_profile": company_profile
            }
        else:
            logger.error(f"Perplexity API error: {response.status_code} - {response.text}")
            return {
                "error": f"API Error: {response.status_code}",
                "opportunities": [],
                "search_performed": False
            }
            
    except Exception as e:
        logger.error(f"Funding search error: {str(e)}")
        return {
            "error": str(e),
            "opportunities": [],
            "search_performed": False
        }

def parse_funding_opportunities_from_ai_response(ai_content):
    """
    Estrae opportunità strutturate dalla risposta AI di Sonar-Pro
    Supporta parsing JSON strutturato e fallback al parsing testo
    """
    opportunities = []
    
    try:
        # Prima prova a parsare come JSON strutturato
        import json
        
        # Pulisci la risposta da eventuali caratteri extra
        cleaned_content = ai_content.strip()
        
        # Rimuovi eventuali markdown code blocks
        if cleaned_content.startswith('```json'):
            cleaned_content = cleaned_content[7:]
        if cleaned_content.endswith('```'):
            cleaned_content = cleaned_content[:-3]
        cleaned_content = cleaned_content.strip()
        
        # Prova parsing JSON
        try:
            json_data = json.loads(cleaned_content)
            
            # Gestisci formato nuovo strutturato
            if 'bandi_trovati' in json_data:
                for bando in json_data['bandi_trovati']:
                    opportunity = {
                        'title': bando.get('titolo', 'Titolo non disponibile'),
                        'source_entity': bando.get('ente_erogatore', 'Ente non specificato'),
                        'max_amount_text': bando.get('importo_massimo', 'Non specificato'),
                        'coverage_percentage': bando.get('percentuale_copertura', 'Non specificato'),
                        'deadline': bando.get('scadenza', 'Non specificata'),
                        'target_sectors': bando.get('settori_target', []),
                        'requirements': bando.get('requisiti_principali', 'Non specificati'),
                        'official_link': bando.get('link_ufficiale', ''),
                        'compatibility_reason': bando.get('motivo_compatibilita', ''),
                        'description': bando.get('descrizione_breve', 'Descrizione non disponibile'),
                        'match_score': 85,  # Score alto per risultati strutturati
                        'parsed_from_json': True
                    }
                    opportunities.append(opportunity)
                    
                logger.info(f"Parsed {len(opportunities)} opportunities from structured JSON")
                return opportunities
                
        except json.JSONDecodeError:
            logger.warning("JSON parsing failed, fallback to text parsing")
        
        # Fallback: parsing testo tradizionale migliorato
        lines = ai_content.split('\n')
        current_opportunity = {}
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Identifica nuovo bando con pattern migliorati
            if any(keyword in line.lower() for keyword in ['bando', 'programma', 'finanziamento', 'horizon', 'pnrr', 'switch', 'digital']):
                if current_opportunity and 'title' in current_opportunity:
                    opportunities.append(current_opportunity)
                    current_opportunity = {}
                current_opportunity['title'] = line
                current_opportunity['match_score'] = 70  # Score medio per parsing testo
                current_opportunity['parsed_from_json'] = False
                
            # Estrai informazioni specifiche con pattern migliorati
            elif any(keyword in line.lower() for keyword in ['ente', 'erogatore', 'organizzatore']):
                current_opportunity['source_entity'] = line.split(':')[-1].strip()
            elif any(keyword in line.lower() for keyword in ['importo', 'finanziamento', 'budget']):
                current_opportunity['max_amount_text'] = line.split(':')[-1].strip()
            elif any(keyword in line.lower() for keyword in ['scadenza', 'deadline', 'entro']):
                current_opportunity['deadline'] = line.split(':')[-1].strip()
            elif any(keyword in line.lower() for keyword in ['settore', 'target', 'destinatari']):
                current_opportunity['target_sectors_text'] = line.split(':')[-1].strip()
            elif any(keyword in line.lower() for keyword in ['link', 'url', 'sito', 'http']):
                current_opportunity['official_link'] = line.split(':')[-1].strip()
            elif any(keyword in line.lower() for keyword in ['descrizione', 'obiettiv', 'finalità']):
                current_opportunity['description'] = line.split(':')[-1].strip()
                
        # Aggiungi l'ultima opportunità se presente
        if current_opportunity and 'title' in current_opportunity:
            opportunities.append(current_opportunity)
            
        logger.info(f"Parsed {len(opportunities)} opportunities from text fallback")
        
    except Exception as e:
        logger.error(f"Error parsing AI response: {str(e)}")
        # Fallback: crea almeno un'opportunità generica se c'è contenuto
        if ai_content and len(ai_content.strip()) > 50:
            opportunities.append({
                'title': 'Risultato ricerca AI',
                'description': ai_content[:300] + '...' if len(ai_content) > 300 else ai_content,
                'match_score': 40,
                'source_entity': 'Ricerca AI',
                'max_amount_text': 'Vedere contenuto completo',
                'parsed_from_json': False,
                'raw_content': ai_content  # Mantieni contenuto originale per debug
            })
    
    return opportunities

def calculate_funding_match_score(opportunity, company_profile):
    """
    Calcola score di compatibilità tra opportunità e profilo aziendale
    """
    try:
        score = 50  # Base score
        
        # Boost per settore compatibile
        company_sector = company_profile.get('sector', '').lower()
        if opportunity.get('target_sectors_text', '').lower():
            if company_sector in opportunity['target_sectors_text'].lower():
                score += 25
                
        # Boost per dimensione aziendale
        company_size = company_profile.get('size', '').lower()
        if 'pmi' in company_size or 'startup' in company_size:
            score += 15
            
        # Penalità se molto generico
        if len(opportunity.get('title', '')) < 20:
            score -= 10
            
        return min(max(score, 0), 100)
        
    except Exception as e:
        logger.error(f"Error calculating match score: {str(e)}")
        return 50

def get_funding_suggestions_with_ai(company_profile):
    """
    Ottieni suggerimenti personalizzati per bandi usando AI
    """
    try:
        # Prima cerca con Sonar-Pro
        search_result = search_funding_opportunities(company_profile)
        
        if search_result.get('search_performed'):
            # Calcola match scores
            for opp in search_result['opportunities']:
                opp['match_score'] = calculate_funding_match_score(opp, company_profile)
            
            # Ordina per score
            search_result['opportunities'].sort(key=lambda x: x.get('match_score', 0), reverse=True)
            
        return search_result
        
    except Exception as e:
        logger.error(f"AI funding suggestions error: {str(e)}")
        return {
            "error": str(e),
            "opportunities": [],
            "search_performed": False
        }