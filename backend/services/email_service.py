"""
Servizio email per recruiting con template professionale.
Gestisce notifiche automatiche per colloqui, candidature e comunicazioni recruiting.
"""

import os
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from flask import current_app, render_template_string
from flask_mail import Message
from extensions import mail


class RecruitingEmailService:
    """Servizio per l'invio di email recruiting professionali."""
    
    def __init__(self):
        self.from_email = None
        self.company_name = None
    
    def _ensure_initialized(self):
        """Lazy initialization of service properties."""
        if self.from_email is None:
            self.from_email = current_app.config.get('MAIL_DEFAULT_SENDER')
            self.company_name = current_app.config.get('COMPANY_NAME', 'DatPortal')
    
    def send_interview_confirmation(self, interview_data: Dict[str, Any]) -> bool:
        """
        Invia email di conferma colloquio al candidato.
        
        Args:
            interview_data: <PERSON><PERSON><PERSON><PERSON> con dati interview (candidate, scheduled_date, location, etc.)
            
        Returns:
            bool: True se email inviata con successo
        """
        self._ensure_initialized()
        
        try:
            candidate = interview_data['candidate']
            interview_date = datetime.fromisoformat(interview_data['scheduled_date'].replace('Z', '+00:00'))
            
            subject = f"Conferma Colloquio - {interview_data['job_posting']['title']}"
            
            # Template HTML professionale
            html_body = self._get_interview_confirmation_template(interview_data, interview_date)
            
            # Template testo semplice
            text_body = self._get_interview_confirmation_text(interview_data, interview_date)
            
            msg = Message(
                subject=subject,
                sender=self.from_email,
                recipients=[candidate['email']],
                body=text_body,
                html=html_body
            )
            
            mail.send(msg)
            current_app.logger.info(f"Interview confirmation sent to {candidate['email']} for interview {interview_data['id']}")
            return True
            
        except Exception as e:
            current_app.logger.error(f"Failed to send interview confirmation: {str(e)}")
            return False
    
    def send_interview_reminder(self, interview_data: Dict[str, Any]) -> bool:
        """
        Invia reminder colloquio 24h prima.
        
        Args:
            interview_data: Dizionario con dati interview
            
        Returns:
            bool: True se email inviata con successo
        """
        self._ensure_initialized()
        
        try:
            candidate = interview_data['candidate']
            interview_date = datetime.fromisoformat(interview_data['scheduled_date'].replace('Z', '+00:00'))
            
            subject = f"Reminder: Colloquio domani - {interview_data['job_posting']['title']}"
            
            html_body = self._get_interview_reminder_template(interview_data, interview_date)
            text_body = self._get_interview_reminder_text(interview_data, interview_date)
            
            msg = Message(
                subject=subject,
                sender=self.from_email,
                recipients=[candidate['email']],
                body=text_body,
                html=html_body
            )
            
            mail.send(msg)
            current_app.logger.info(f"Interview reminder sent to {candidate['email']} for interview {interview_data['id']}")
            return True
            
        except Exception as e:
            current_app.logger.error(f"Failed to send interview reminder: {str(e)}")
            return False
    
    def send_application_status_update(self, application_data: Dict[str, Any], old_status: str, new_status: str) -> bool:
        """
        Invia notifica cambio status candidatura.
        
        Args:
            application_data: Dati candidatura
            old_status: Status precedente
            new_status: Nuovo status
            
        Returns:
            bool: True se email inviata con successo
        """
        self._ensure_initialized()
        
        try:
            candidate = application_data['candidate']
            
            subject = f"Aggiornamento Candidatura - {application_data['job_posting']['title']}"
            
            html_body = self._get_status_update_template(application_data, old_status, new_status)
            text_body = self._get_status_update_text(application_data, old_status, new_status)
            
            msg = Message(
                subject=subject,
                sender=self.from_email,
                recipients=[candidate['email']],
                body=text_body,
                html=html_body
            )
            
            mail.send(msg)
            current_app.logger.info(f"Status update sent to {candidate['email']} for application {application_data['id']}")
            return True
            
        except Exception as e:
            current_app.logger.error(f"Failed to send status update: {str(e)}")
            return False
    
    def send_welcome_candidate(self, candidate_data: Dict[str, Any]) -> bool:
        """
        Invia email di benvenuto a nuovo candidato.
        
        Args:
            candidate_data: Dati candidato
            
        Returns:
            bool: True se email inviata con successo
        """
        self._ensure_initialized()
        
        try:
            subject = f"Benvenuto in {self.company_name} - La tua candidatura è stata ricevuta"
            
            html_body = self._get_welcome_template(candidate_data)
            text_body = self._get_welcome_text(candidate_data)
            
            msg = Message(
                subject=subject,
                sender=self.from_email,
                recipients=[candidate_data['email']],
                body=text_body,
                html=html_body
            )
            
            mail.send(msg)
            current_app.logger.info(f"Welcome email sent to {candidate_data['email']}")
            return True
            
        except Exception as e:
            current_app.logger.error(f"Failed to send welcome email: {str(e)}")
            return False

    # Template Methods
    def _get_interview_confirmation_template(self, interview_data: Dict[str, Any], interview_date: datetime) -> str:
        """Template HTML per conferma colloquio."""
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .header {{ background: #2563eb; color: white; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; }}
                .details {{ background: #f8fafc; padding: 15px; border-radius: 8px; margin: 20px 0; }}
                .footer {{ background: #f1f5f9; padding: 15px; text-align: center; font-size: 12px; }}
                .button {{ display: inline-block; background: #2563eb; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>{self.company_name}</h1>
                <h2>Conferma Colloquio</h2>
            </div>
            
            <div class="content">
                <p>Gentile <strong>{interview_data['candidate']['full_name']}</strong>,</p>
                
                <p>Siamo lieti di confermare il colloquio per la posizione di <strong>{interview_data['job_posting']['title']}</strong>.</p>
                
                <div class="details">
                    <h3>Dettagli Colloquio</h3>
                    <p><strong>Data:</strong> {interview_date.strftime('%d/%m/%Y')}</p>
                    <p><strong>Ora:</strong> {interview_date.strftime('%H:%M')}</p>
                    <p><strong>Durata:</strong> {interview_data['duration_minutes']} minuti</p>
                    <p><strong>Tipo:</strong> {self._get_interview_type_label(interview_data['interview_type'])}</p>
                    {f"<p><strong>Luogo/Link:</strong> {interview_data['location']}</p>" if interview_data.get('location') else ''}
                    {f"<p><strong>Intervistatore:</strong> {interview_data['interviewer']['full_name']}</p>" if interview_data.get('interviewer') else ''}
                </div>
                
                {f"<div class='details'><h4>Note:</h4><p>{interview_data['notes']}</p></div>" if interview_data.get('notes') else ''}
                
                <p>Ti preghiamo di confermare la tua presenza rispondendo a questa email.</p>
                
                <p>Non vediamo l'ora di conoscerti!</p>
                
                <p>Cordiali saluti,<br>
                Team Recruiting {self.company_name}</p>
            </div>
            
            <div class="footer">
                <p>{self.company_name} - Sistema di Recruiting</p>
            </div>
        </body>
        </html>
        """
    
    def _get_interview_confirmation_text(self, interview_data: Dict[str, Any], interview_date: datetime) -> str:
        """Template testo per conferma colloquio."""
        location_text = f"\\nLuogo/Link: {interview_data['location']}" if interview_data.get('location') else ''
        interviewer_text = f"\\nIntervistatore: {interview_data['interviewer']['full_name']}" if interview_data.get('interviewer') else ''
        notes_text = f"\\n\\nNote:\\n{interview_data['notes']}" if interview_data.get('notes') else ''
        
        return f"""
Gentile {interview_data['candidate']['full_name']},

Siamo lieti di confermare il colloquio per la posizione di {interview_data['job_posting']['title']}.

DETTAGLI COLLOQUIO:
Data: {interview_date.strftime('%d/%m/%Y')}
Ora: {interview_date.strftime('%H:%M')}
Durata: {interview_data['duration_minutes']} minuti
Tipo: {self._get_interview_type_label(interview_data['interview_type'])}{location_text}{interviewer_text}{notes_text}

Ti preghiamo di confermare la tua presenza rispondendo a questa email.

Non vediamo l'ora di conoscerti!

Cordiali saluti,
Team Recruiting {self.company_name}
        """
    
    def _get_interview_reminder_template(self, interview_data: Dict[str, Any], interview_date: datetime) -> str:
        """Template HTML per reminder colloquio."""
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .header {{ background: #f59e0b; color: white; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; }}
                .details {{ background: #fef3c7; padding: 15px; border-radius: 8px; margin: 20px 0; }}
                .footer {{ background: #f1f5f9; padding: 15px; text-align: center; font-size: 12px; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>{self.company_name}</h1>
                <h2>Reminder Colloquio Domani</h2>
            </div>
            
            <div class="content">
                <p>Gentile <strong>{interview_data['candidate']['full_name']}</strong>,</p>
                
                <p>Ti ricordiamo che domani hai il colloquio per la posizione di <strong>{interview_data['job_posting']['title']}</strong>.</p>
                
                <div class="details">
                    <h3>Dettagli Colloquio</h3>
                    <p><strong>Data:</strong> {interview_date.strftime('%d/%m/%Y')}</p>
                    <p><strong>Ora:</strong> {interview_date.strftime('%H:%M')}</p>
                    <p><strong>Durata:</strong> {interview_data['duration_minutes']} minuti</p>
                    {f"<p><strong>Luogo/Link:</strong> {interview_data['location']}</p>" if interview_data.get('location') else ''}
                </div>
                
                <p>Assicurati di essere puntuale e di avere tutto il necessario per il colloquio.</p>
                
                <p>In bocca al lupo!</p>
                
                <p>Cordiali saluti,<br>
                Team Recruiting {self.company_name}</p>
            </div>
            
            <div class="footer">
                <p>{self.company_name} - Sistema di Recruiting</p>
            </div>
        </body>
        </html>
        """
    
    def _get_interview_reminder_text(self, interview_data: Dict[str, Any], interview_date: datetime) -> str:
        """Template testo per reminder colloquio."""
        location_text = f"\\nLuogo/Link: {interview_data['location']}" if interview_data.get('location') else ''
        
        return f"""
Gentile {interview_data['candidate']['full_name']},

Ti ricordiamo che domani hai il colloquio per la posizione di {interview_data['job_posting']['title']}.

DETTAGLI COLLOQUIO:
Data: {interview_date.strftime('%d/%m/%Y')}
Ora: {interview_date.strftime('%H:%M')}
Durata: {interview_data['duration_minutes']} minuti{location_text}

Assicurati di essere puntuale e di avere tutto il necessario per il colloquio.

In bocca al lupo!

Cordiali saluti,
Team Recruiting {self.company_name}
        """
    
    def _get_status_update_template(self, application_data: Dict[str, Any], old_status: str, new_status: str) -> str:
        """Template HTML per aggiornamento status."""
        status_labels = {
            'pending': 'In Attesa',
            'in_progress': 'In Corso',
            'completed': 'Completata',
            'rejected': 'Non Selezionata'
        }
        
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .header {{ background: #2563eb; color: white; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; }}
                .status {{ background: #f0fdf4; padding: 15px; border-radius: 8px; margin: 20px 0; }}
                .footer {{ background: #f1f5f9; padding: 15px; text-align: center; font-size: 12px; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>{self.company_name}</h1>
                <h2>Aggiornamento Candidatura</h2>
            </div>
            
            <div class="content">
                <p>Gentile <strong>{application_data['candidate']['full_name']}</strong>,</p>
                
                <p>La tua candidatura per la posizione di <strong>{application_data['job_posting']['title']}</strong> è stata aggiornata.</p>
                
                <div class="status">
                    <h3>Nuovo Status</h3>
                    <p><strong>{status_labels.get(new_status, new_status)}</strong></p>
                </div>
                
                <p>Ti terremo aggiornato sui prossimi passi del processo di selezione.</p>
                
                <p>Cordiali saluti,<br>
                Team Recruiting {self.company_name}</p>
            </div>
            
            <div class="footer">
                <p>{self.company_name} - Sistema di Recruiting</p>
            </div>
        </body>
        </html>
        """
    
    def _get_status_update_text(self, application_data: Dict[str, Any], old_status: str, new_status: str) -> str:
        """Template testo per aggiornamento status."""
        status_labels = {
            'pending': 'In Attesa',
            'in_progress': 'In Corso', 
            'completed': 'Completata',
            'rejected': 'Non Selezionata'
        }
        
        return f"""
Gentile {application_data['candidate']['full_name']},

La tua candidatura per la posizione di {application_data['job_posting']['title']} è stata aggiornata.

NUOVO STATUS: {status_labels.get(new_status, new_status)}

Ti terremo aggiornato sui prossimi passi del processo di selezione.

Cordiali saluti,
Team Recruiting {self.company_name}
        """
    
    def _get_welcome_template(self, candidate_data: Dict[str, Any]) -> str:
        """Template HTML per email di benvenuto."""
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .header {{ background: #10b981; color: white; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; }}
                .welcome {{ background: #f0fdf4; padding: 15px; border-radius: 8px; margin: 20px 0; }}
                .footer {{ background: #f1f5f9; padding: 15px; text-align: center; font-size: 12px; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Benvenuto in {self.company_name}</h1>
            </div>
            
            <div class="content">
                <p>Gentile <strong>{candidate_data['full_name']}</strong>,</p>
                
                <p>Grazie per il tuo interesse verso {self.company_name}!</p>
                
                <div class="welcome">
                    <h3>La tua candidatura è stata ricevuta</h3>
                    <p>Il nostro team recruiting esaminerà attentamente il tuo profilo e ti contatteremo presto per aggiornarti sui prossimi passi.</p>
                </div>
                
                <p>Nel frattempo, ti invitiamo a seguire le nostre novità e opportunità di carriera.</p>
                
                <p>Cordiali saluti,<br>
                Team Recruiting {self.company_name}</p>
            </div>
            
            <div class="footer">
                <p>{self.company_name} - Siamo felici di conoscerti!</p>
            </div>
        </body>
        </html>
        """
    
    def _get_welcome_text(self, candidate_data: Dict[str, Any]) -> str:
        """Template testo per email di benvenuto."""
        return f"""
Gentile {candidate_data['full_name']},

Grazie per il tuo interesse verso {self.company_name}!

La tua candidatura è stata ricevuta con successo. Il nostro team recruiting esaminerà attentamente il tuo profilo e ti contatteremo presto per aggiornarti sui prossimi passi.

Nel frattempo, ti invitiamo a seguire le nostre novità e opportunità di carriera.

Cordiali saluti,
Team Recruiting {self.company_name}

Siamo felici di conoscerti!
        """
    
    def _get_interview_type_label(self, interview_type: str) -> str:
        """Converte il tipo interview in label italiana."""
        labels = {
            'phone': 'Telefonico',
            'video': 'Video Call',
            'in_person': 'Di Persona',
            'technical': 'Tecnico',
            'behavioral': 'Comportamentale',
            'final': 'Finale'
        }
        return labels.get(interview_type, interview_type)
    
    def send_application_confirmation(self, application) -> bool:
        """
        Invia email di conferma candidatura al candidato.
        
        Args:
            application: Oggetto Application con candidate e job_posting
            
        Returns:
            bool: True se email inviata con successo
        """
        self._ensure_initialized()
        
        try:
            candidate = application.candidate
            job_posting = application.job_posting
            
            subject = f"Conferma Candidatura - {job_posting.title if job_posting else 'Posizione'}"
            
            # Template HTML
            html_body = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .header {{ background: #2563eb; color: white; padding: 20px; text-align: center; }}
                    .content {{ padding: 20px; }}
                    .confirmation {{ background: #f0fdf4; padding: 15px; border-radius: 8px; margin: 20px 0; }}
                    .footer {{ background: #f1f5f9; padding: 15px; text-align: center; font-size: 12px; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>{self.company_name} - Recruiting</h1>
                </div>
                <div class="content">
                    <h2>Gentile {candidate.full_name},</h2>
                    <p>Ti confermiamo di aver ricevuto la tua candidatura per la posizione di <strong>{job_posting.title if job_posting else 'Posizione'}</strong>.</p>
                    
                    <div class="confirmation">
                        <h3>✅ Candidatura Ricevuta</h3>
                        <p><strong>Data invio:</strong> {application.applied_date.strftime('%d/%m/%Y') if application.applied_date else 'N/A'}</p>
                        <p><strong>Posizione:</strong> {job_posting.title if job_posting else 'N/A'}</p>
                        <p><strong>Stato:</strong> In valutazione</p>
                    </div>
                    
                    <p>Il nostro team HR esaminerà attentamente il tuo profilo. Ti contatteremo presto per aggiornarti sull'esito della selezione.</p>
                    
                    <p>Grazie per l'interesse mostrato verso {self.company_name}!</p>
                    
                    <p>Cordiali saluti,<br>
                    Team Recruiting {self.company_name}</p>
                </div>
                <div class="footer">
                    <p>Questa è una email automatica generata da {self.company_name}.</p>
                </div>
            </body>
            </html>
            """
            
            # Template testo semplice
            text_body = f"""
Gentile {candidate.full_name},

Ti confermiamo di aver ricevuto la tua candidatura per la posizione di {job_posting.title if job_posting else 'Posizione'}.

DETTAGLI CANDIDATURA:
Data invio: {application.applied_date.strftime('%d/%m/%Y') if application.applied_date else 'N/A'}
Posizione: {job_posting.title if job_posting else 'N/A'}
Stato: In valutazione

Il nostro team HR esaminerà attentamente il tuo profilo. Ti contatteremo presto per aggiornarti sull'esito della selezione.

Grazie per l'interesse mostrato verso {self.company_name}!

Cordiali saluti,
Team Recruiting {self.company_name}
            """
            
            msg = Message(
                subject=subject,
                sender=self.from_email,
                recipients=[candidate.email],
                body=text_body,
                html=html_body
            )
            
            mail.send(msg)
            current_app.logger.info(f"Email conferma candidatura inviata a {candidate.email}")
            return True
            
        except Exception as e:
            current_app.logger.error(f"Errore invio email conferma candidatura: {str(e)}")
            return False
    
    def send_new_application_notification(self, application) -> bool:
        """
        Invia notifica al team HR per nuova candidatura.
        
        Args:
            application: Oggetto Application con candidate e job_posting
            
        Returns:
            bool: True se email inviata con successo
        """
        self._ensure_initialized()
        
        try:
            candidate = application.candidate
            job_posting = application.job_posting
            
            # Email HR (configurabile)
            hr_emails = current_app.config.get('HR_NOTIFICATION_EMAILS', [self.from_email])
            if isinstance(hr_emails, str):
                hr_emails = [hr_emails]
            
            subject = f"Nuova Candidatura - {job_posting.title if job_posting else 'Posizione'}"
            
            # Template HTML
            html_body = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .header {{ background: #dc2626; color: white; padding: 20px; text-align: center; }}
                    .content {{ padding: 20px; }}
                    .candidate-info {{ background: #fef2f2; padding: 15px; border-radius: 8px; margin: 20px 0; }}
                    .action {{ background: #eff6ff; padding: 15px; border-radius: 8px; margin: 20px 0; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>{self.company_name} - Notifica HR</h1>
                </div>
                <div class="content">
                    <h2>🚨 Nuova Candidatura Ricevuta</h2>
                    
                    <div class="candidate-info">
                        <h3>Informazioni Candidato</h3>
                        <p><strong>Nome:</strong> {candidate.full_name}</p>
                        <p><strong>Email:</strong> {candidate.email}</p>
                        <p><strong>Telefono:</strong> {candidate.phone or 'Non fornito'}</p>
                        <p><strong>Esperienza:</strong> {candidate.years_of_experience or 0} anni</p>
                        <p><strong>CV:</strong> {'Presente' if candidate.cv_path else 'Non presente'}</p>
                    </div>
                    
                    <div class="candidate-info">
                        <h3>Dettagli Posizione</h3>
                        <p><strong>Posizione:</strong> {job_posting.title if job_posting else 'N/A'}</p>
                        <p><strong>Data candidatura:</strong> {application.applied_date.strftime('%d/%m/%Y %H:%M') if application.applied_date else 'N/A'}</p>
                        <p><strong>Stato:</strong> {application.status}</p>
                    </div>
                    
                    <div class="action">
                        <h3>📋 Azioni Necessarie</h3>
                        <p>• Rivedi il profilo del candidato nel sistema</p>
                        <p>• Valuta la candidatura entro 3 giorni lavorativi</p>
                        <p>• Pianifica eventuale colloquio se il profilo è interessante</p>
                    </div>
                    
                    <p>Accedi al sistema per gestire questa candidatura.</p>
                </div>
            </body>
            </html>
            """
            
            # Template testo semplice
            text_body = f"""
NUOVA CANDIDATURA RICEVUTA

CANDIDATO:
Nome: {candidate.full_name}
Email: {candidate.email}
Telefono: {candidate.phone or 'Non fornito'}
Esperienza: {candidate.years_of_experience or 0} anni
CV: {'Presente' if candidate.cv_path else 'Non presente'}

POSIZIONE:
Posizione: {job_posting.title if job_posting else 'N/A'}
Data candidatura: {application.applied_date.strftime('%d/%m/%Y %H:%M') if application.applied_date else 'N/A'}
Stato: {application.status}

AZIONI NECESSARIE:
• Rivedi il profilo del candidato nel sistema
• Valuta la candidatura entro 3 giorni lavorativi
• Pianifica eventuale colloquio se il profilo è interessante

Accedi al sistema per gestire questa candidatura.

Team {self.company_name}
            """
            
            msg = Message(
                subject=subject,
                sender=self.from_email,
                recipients=hr_emails,
                body=text_body,
                html=html_body
            )
            
            mail.send(msg)
            current_app.logger.info(f"Notifica HR inviata per candidatura {application.id}")
            return True
            
        except Exception as e:
            current_app.logger.error(f"Errore invio notifica HR: {str(e)}")
            return False


# Singleton instance
recruiting_email_service = RecruitingEmailService()