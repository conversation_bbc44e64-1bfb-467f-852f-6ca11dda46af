"""
Self-Healing AI Service - Analisi automatica errori e generazione soluzioni
Integra OpenAI per diagnosi errori e generazione prompt Claude Code
"""

import os
import json
import logging
import hashlib
import asyncio
import httpx
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union, Tuple
from flask import current_app

from models_split.system import ErrorPattern, HealingSession, SystemHealth
from extensions import db

logger = logging.getLogger(__name__)

class HealingAIService:
    """
    Servizio AI per analisi errori e self-healing.
    Gestisce pattern recognition, AI analysis, e generazione prompt Claude Code.
    """
    
    def __init__(self):
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        self.openai_base_url = "https://api.openai.com/v1"
        self.max_retries = 3
        
        # Pattern di errori comuni con soluzioni note
        self.known_patterns = {
            'frontend_network_error': {
                'description': 'Errori di rete frontend (timeout, 5xx)',
                'auto_healable': True,
                'common_fixes': ['Retry logic', 'Better error handling', 'Timeout adjustment']
            },
            'vue_component_error': {
                'description': 'Errori componenti Vue.js',
                'auto_healable': False,
                'common_fixes': ['Props validation', 'Lifecycle hooks', 'Reactive data']
            },
            'api_validation_error': {
                'description': 'Errori validazione API backend',
                'auto_healable': True,
                'common_fixes': ['Input validation', 'Schema updates', 'Error messages']
            }
        }
    
    def create_error_pattern(self, error_data: Dict) -> ErrorPattern:
        """
        Crea o aggiorna un ErrorPattern dal raw error data.
        
        Args:
            error_data: Dict con error info (type, message, file, stack, etc.)
            
        Returns:
            ErrorPattern: Pattern creato o aggiornato
        """
        try:
            # Genera hash unico per il pattern
            pattern_string = f"{error_data.get('type', '')}-{error_data.get('message', '')[:100]}-{error_data.get('file', '')}"
            pattern_hash = hashlib.sha256(pattern_string.encode()).hexdigest()[:16]
            
            # Cerca pattern esistente
            existing_pattern = ErrorPattern.query.filter_by(pattern_hash=pattern_hash).first()
            
            if existing_pattern:
                # Aggiorna contatori
                existing_pattern.update_occurrence()
                db.session.commit()
                return existing_pattern
            
            # Crea nuovo pattern
            new_pattern = ErrorPattern(
                pattern_hash=pattern_hash,
                error_type=error_data.get('type', 'unknown'),
                message_pattern=error_data.get('message', '')[:500],  # Limita lunghezza
                file_pattern=error_data.get('file', ''),
                severity=self._classify_severity(error_data)
            )
            
            db.session.add(new_pattern)
            db.session.commit()
            
            logger.info(f"Created new error pattern: {pattern_hash}")
            return new_pattern
            
        except Exception as e:
            logger.error(f"Error creating error pattern: {str(e)}")
            db.session.rollback()
            raise
    
    def _classify_severity(self, error_data: Dict) -> str:
        """Classifica automaticamente la gravità dell'errore."""
        message = error_data.get('message', '').lower()
        error_type = error_data.get('type', '').lower()
        
        # Critico: errori che bloccano funzionalità core
        critical_patterns = [
            'network error', '500', 'internal server error',
            'database connection', 'authentication failed',
            'typeerror.*undefined', 'referenceerror'
        ]
        
        # Alto: errori che impattano UX
        high_patterns = [
            '400', '401', '403', '404', 'failed to fetch',
            'timeout', 'permission denied'
        ]
        
        # Controllo pattern critici
        for pattern in critical_patterns:
            if pattern in message or pattern in error_type:
                return 'critical'
        
        # Controllo pattern alta priorità
        for pattern in high_patterns:
            if pattern in message or pattern in error_type:
                return 'high'
        
        # Default: medium
        return 'medium'
    
    async def analyze_error_pattern(self, pattern: ErrorPattern) -> Dict:
        """
        Analizza un error pattern con AI per determinare causa e soluzioni.
        
        Args:
            pattern: ErrorPattern da analizzare
            
        Returns:
            Dict con analisi AI completa
        """
        try:
            # Costruisci contesto per AI
            context = self._build_error_context(pattern)
            
            # Prompt per analisi AI
            analysis_prompt = f"""
Analizza questo pattern di errore di DatPortal (sistema intranet aziendale Vue.js + Flask):

PATTERN ERRORE:
- Tipo: {pattern.error_type}
- Messaggio: {pattern.message_pattern}
- File: {pattern.file_pattern or 'Non specificato'}
- Occorrenze: {pattern.occurrence_count}
- Prima volta: {pattern.first_seen}
- Ultima volta: {pattern.last_seen}
- Gravità attuale: {pattern.severity}

CONTESTO SISTEMA:
{context}

Fornisci analisi in JSON:
{{
  "root_cause": "Causa principale dell'errore",
  "business_impact": "Impatto su utenti e operazioni business",
  "severity_assessment": "critical|high|medium|low",
  "recurrence_risk": "Probabilità che si ripeta (0-1)",
  "auto_healable": "Se può essere risolto automaticamente (true/false)",
  "estimated_fix_time": "Tempo stimato per risoluzione",
  "suggested_actions": ["azione1", "azione2", "azione3"],
  "prevention_strategies": ["strategia1", "strategia2"],
  "code_patterns_to_review": ["pattern1", "pattern2"],
  "monitoring_recommendations": ["metrica1", "metrica2"]
}}

Considera che DatPortal è un sistema mission-critical per gestione HR/CRM aziendale.
"""

            # Chiamata OpenAI
            analysis = await self._call_openai_analysis(analysis_prompt)
            
            # Salva analisi nel pattern
            pattern.set_ai_analysis(analysis)
            
            # Aggiorna auto-healable flag se suggerito
            if 'auto_healable' in analysis:
                pattern.is_auto_healable = analysis['auto_healable']
            
            db.session.commit()
            
            logger.info(f"AI analysis completed for pattern {pattern.pattern_hash}")
            return analysis
            
        except Exception as e:
            logger.error(f"Error in AI analysis: {str(e)}")
            return self._get_fallback_analysis(pattern)
    
    async def generate_claude_prompt(self, pattern: ErrorPattern, analysis: Dict = None) -> str:
        """
        Genera prompt specifico per Claude Code basato su pattern e analisi.
        
        Args:
            pattern: ErrorPattern da fixare
            analysis: Analisi AI (opzionale, se None viene richiamata)
            
        Returns:
            str: Prompt pronto per Claude Code
        """
        try:
            # Ottieni analisi se non fornita
            if not analysis:
                analysis = pattern.get_ai_analysis()
                if not analysis:
                    analysis = await self.analyze_error_pattern(pattern)
            
            # Determina framework e contesto
            framework = "Vue.js frontend" if "frontend/" in (pattern.file_pattern or "") else "Flask backend"
            module = self._extract_module_name(pattern.file_pattern or "")
            
            # Timestamp per branch naming
            timestamp = datetime.now().strftime('%Y%m%d-%H%M%S')
            
            # Genera prompt strutturato
            prompt = f"""🔧 DATPORTAL SELF-HEALING REQUEST

**🚨 Errore Rilevato:**
- **Pattern ID**: {pattern.pattern_hash}
- **Tipo**: {pattern.error_type}
- **File**: {pattern.file_pattern or 'Da identificare'}
- **Messaggio**: {pattern.message_pattern}
- **Occorrenze**: {pattern.occurrence_count} volte (dal {pattern.first_seen.strftime('%d/%m/%Y')})
- **Gravità**: {analysis.get('severity_assessment', pattern.severity).upper()}

**🧠 Analisi AI:**
- **Causa principale**: {analysis.get('root_cause', 'Da determinare')}
- **Impatto business**: {analysis.get('business_impact', 'Da valutare')}
- **Rischio ricorrenza**: {int(analysis.get('recurrence_risk', 0.5) * 100)}%
- **Auto-risolvibile**: {'✅ Sì' if analysis.get('auto_healable') else '❌ No'}

**🛠️ Contesto Tecnico:**
- **Framework**: {framework}
- **Modulo**: {module}
- **Branch suggerito**: `fix/self-heal-{pattern.pattern_hash}-{timestamp}`
- **Tempo stimato**: {analysis.get('estimated_fix_time', 'Non determinato')}

**📋 Azioni Richieste:**
1. Analizza il pattern di errore nel contesto DatPortal
2. Identifica la causa root seguendo i pattern architetturali esistenti
3. Implementa fix seguendo le convenzioni del progetto (vedi CLAUDE.md)
4. Testa la soluzione contro il pattern ricorrente
5. Verifica che non introduca regressioni nei moduli correlati

**💡 Suggerimenti AI:**
{self._format_suggestions(analysis.get('suggested_actions', []))}

**🔍 Pattern di Codice da Rivedere:**
{self._format_suggestions(analysis.get('code_patterns_to_review', []))}

**🛡️ Strategie di Prevenzione:**
{self._format_suggestions(analysis.get('prevention_strategies', []))}

**⚠️ Note Importanti:**
- DatPortal è un sistema aziendale critico - prioritizza stabilità
- Segui i pattern esistenti per: API responses, error handling, UI components
- Testa approfonditamente prima di committare
- Mantieni backward compatibility con tutti i moduli
- Documenta i cambiamenti per il team

**📊 Monitoraggio Post-Fix:**
{self._format_suggestions(analysis.get('monitoring_recommendations', []))}

**🎯 Obiettivo**: Risolvere il pattern ricorrente mantenendo l'integrità del sistema e prevenendo future occorrenze.

Procedi con l'analisi e implementazione del fix. Grazie per il contributo al self-healing di DatPortal! 🚀
"""
            
            return prompt
            
        except Exception as e:
            logger.error(f"Error generating Claude prompt: {str(e)}")
            return self._get_fallback_prompt(pattern)
    
    def _build_error_context(self, pattern: ErrorPattern) -> str:
        """Costruisce contesto del sistema per AI analysis."""
        try:
            # Info di sistema basic
            recent_health = SystemHealth.query.order_by(SystemHealth.timestamp.desc()).first()
            
            context_parts = [
                "=== CONTESTO DATPORTAL ===",
                f"Architettura: Vue.js 3 + Flask + PostgreSQL",
                f"Moduli: Personnel, Projects, CRM, Timesheets, Recruiting, CEO AI",
                f"Pattern Framework: Pinia stores, Blueprint APIs, Component-based UI"
            ]
            
            if recent_health:
                context_parts.extend([
                    f"Health Score: {recent_health.health_score}/100",
                    f"Errori 24h: {recent_health.error_count_24h}",
                    f"Errori critici: {recent_health.critical_errors}"
                ])
            
            # Pattern simili
            similar_patterns = ErrorPattern.query.filter(
                ErrorPattern.error_type == pattern.error_type,
                ErrorPattern.id != pattern.id
            ).limit(3).all()
            
            if similar_patterns:
                context_parts.append("\n=== PATTERN SIMILI ===")
                for p in similar_patterns:
                    context_parts.append(f"- {p.message_pattern[:100]} ({p.occurrence_count} volte)")
            
            return "\n".join(context_parts)
            
        except Exception as e:
            logger.error(f"Error building context: {str(e)}")
            return "Contesto non disponibile"
    
    def _extract_module_name(self, file_path: str) -> str:
        """Estrae il nome del modulo dal file path."""
        if not file_path:
            return "Core System"
        
        if 'frontend/src/' in file_path:
            parts = file_path.split('frontend/src/')[-1].split('/')
            if len(parts) > 1:
                return f"{parts[0].title()} (Frontend)"
        elif 'backend/' in file_path:
            parts = file_path.split('backend/')[-1].split('/')
            if len(parts) > 1:
                return f"{parts[0].title()} (Backend)"
        
        return "Sistema"
    
    def _format_suggestions(self, suggestions: List[str]) -> str:
        """Formatta lista di suggerimenti per prompt."""
        if not suggestions:
            return "- Nessun suggerimento specifico"
        return "\n".join([f"- {suggestion}" for suggestion in suggestions])
    
    async def _call_openai_analysis(self, prompt: str) -> Dict:
        """Chiama OpenAI per analisi errore."""
        try:
            async with httpx.AsyncClient(timeout=180.0) as client:
                response = await client.post(
                    f"{self.openai_base_url}/chat/completions",
                    headers={
                        "Authorization": f"Bearer {self.openai_api_key}",
                        "Content-Type": "application/json"
                    },
                    json={
                        "model": "gpt-4o-mini",
                        "messages": [
                            {
                                "role": "system",
                                "content": "Sei un esperto system administrator e sviluppatore senior specializzato in debugging e risoluzione errori per applicazioni web enterprise. Fornisci analisi tecniche precise e soluzioni actionable."
                            },
                            {
                                "role": "user", 
                                "content": prompt
                            }
                        ],
                        "max_tokens": 1500,
                        "temperature": 0.1  # Bassa per analisi precise
                    }
                )
                
                if response.status_code == 200:
                    result = response.json()
                    content = result['choices'][0]['message']['content']
                    
                    # Prova a parsare JSON
                    try:
                        return json.loads(content)
                    except json.JSONDecodeError:
                        # Se non è JSON valido, wrap in struttura
                        return {
                            "root_cause": "Analisi testuale disponibile",
                            "raw_analysis": content,
                            "parsing_error": True
                        }
                else:
                    logger.error(f"OpenAI API error: {response.status_code}")
                    raise Exception(f"OpenAI API error: {response.status_code}")
                    
        except Exception as e:
            logger.error(f"Error calling OpenAI: {str(e)}")
            raise
    
    def _get_fallback_analysis(self, pattern: ErrorPattern) -> Dict:
        """Analisi fallback quando AI non disponibile."""
        return {
            "root_cause": f"Pattern {pattern.error_type} ricorrente rilevato automaticamente",
            "business_impact": "Impatto da determinare manualmente",
            "severity_assessment": pattern.severity,
            "recurrence_risk": 0.7 if pattern.occurrence_count > 5 else 0.3,
            "auto_healable": False,
            "estimated_fix_time": "30-60 minuti",
            "suggested_actions": [
                "Analizzare stack trace completo",
                "Verificare pattern nel codice correlato", 
                "Implementare error handling robusto"
            ],
            "prevention_strategies": [
                "Implementare logging più dettagliato",
                "Aggiungere test per questo scenario"
            ],
            "fallback_mode": True
        }
    
    def _get_fallback_prompt(self, pattern: ErrorPattern) -> str:
        """Prompt fallback quando AI non disponibile."""
        return f"""🔧 DATPORTAL SELF-HEALING REQUEST (Modalità Fallback)

**🚨 Errore Pattern Rilevato:**
- Pattern: {pattern.pattern_hash}
- Tipo: {pattern.error_type}
- Messaggio: {pattern.message_pattern}
- Occorrenze: {pattern.occurrence_count}
- File: {pattern.file_pattern or 'Da identificare'}

**📋 Azioni Base Richieste:**
1. Analizza questo pattern di errore ricorrente in DatPortal
2. Identifica la causa nel contesto del framework Vue.js/Flask
3. Implementa fix seguendo i pattern esistenti
4. Testa la soluzione
5. Documenta il fix per prevenire ricorrenze future

**⚠️ Nota**: Analisi AI non disponibile - procedi con analisi manuale approfondita.

Segui le best practices DatPortal definite in CLAUDE.md.
"""

    def update_system_health(self):
        """Aggiorna metriche generali di system health."""
        try:
            # Calcola metriche ultime 24h
            yesterday = datetime.utcnow() - timedelta(days=1)
            
            error_count_24h = ErrorPattern.query.filter(
                ErrorPattern.last_seen >= yesterday
            ).count()
            
            critical_errors = ErrorPattern.query.filter(
                ErrorPattern.severity == 'critical',
                ErrorPattern.last_seen >= yesterday
            ).count()
            
            # Sessioni healing completate
            completed_healing = HealingSession.query.filter(
                HealingSession.completed_at >= yesterday,
                HealingSession.success == True
            ).count()
            
            # Issues pending
            pending_issues = ErrorPattern.query.filter(
                ErrorPattern.severity.in_(['critical', 'high']),
                ErrorPattern.last_seen >= yesterday
            ).count()
            
            # Calcola health score (0-100)
            base_score = 100
            base_score -= min(error_count_24h * 2, 40)  # Max -40 per errori
            base_score -= min(critical_errors * 10, 30)  # Max -30 per critici
            base_score -= min(pending_issues * 5, 20)   # Max -20 per pending
            base_score += min(completed_healing * 5, 10)  # Max +10 per healing
            
            health_score = max(0, min(100, base_score))
            
            # Crea record SystemHealth
            health_record = SystemHealth(
                health_score=health_score,
                error_count_24h=error_count_24h,
                critical_errors=critical_errors,
                auto_healed_count=completed_healing,
                pending_issues=pending_issues
            )
            
            db.session.add(health_record)
            db.session.commit()
            
            logger.info(f"System health updated: {health_score}/100")
            return health_record
            
        except Exception as e:
            logger.error(f"Error updating system health: {str(e)}")
            db.session.rollback()
            return None