{"platform_info": {"name": "DatPortal", "version": "1.0", "description": "Comprehensive Italian corporate intranet platform for SMEs, innovative companies and startups", "last_updated": "2025-01-24"}, "datportal_features": {"project_management": {"name": "Gestione Progetti", "description": "Sistema completo per la gestione di progetti, task, timeline e risorse", "icon": "briefcase", "modules": ["projects", "tasks", "kpis", "resource_allocation"], "compliance_mapping": {"ISO_9001_2015": {"requirements": ["Definizione del sistema di gestione qualità e dei suoi processi", "Identificazione dei processi e delle loro interazioni", "Definizione di obiettivi misurabili per la qualità", "Gestione sistematica delle non conformità"], "compliance_score": 85, "notes": "Project management con KPI tracking soddisfa maggior parte requisiti qualità"}, "ISO_27001_2022": {"requirements": ["Gestione degli accessi e delle identità", "Change management formalizzato", "Controllo e monitoraggio dei fornitori e terze parti"], "compliance_score": 60, "notes": "Sistema progetti supporta change management, ma serve rafforzare controlli accesso"}}}, "personnel_management": {"name": "Gestione HR", "description": "Sistema completo per gestione personale, competenze, organigramma e performance", "icon": "users", "modules": ["personnel", "skills_matrix", "org_chart", "performance", "timeoff"], "compliance_mapping": {"ISO_9001_2015": {"requirements": ["Formazione del team qualità", "Definizione di obiettivi misurabili per la qualità", "Matrice delle responsabilità"], "compliance_score": 90, "notes": "Skills matrix e org chart coprono completamente requisiti competenze e responsabilità"}, "ISO_27001_2022": {"requirements": ["Programma di training di awareness sulla sicurezza", "Gestione degli accessi e delle identità", "Controllo e monitoraggio dei fornitori e terze parti"], "compliance_score": 75, "notes": "Sistema HR gestisce training e accessi, ma serve modulo specifico sicurezza"}, "GDPR_COMPLIANCE": {"requirements": ["Training GDPR personale", "Gestione consensi e diritti interessati", "Registri di formazione sicurezza"], "compliance_score": 80, "notes": "Sistema training e gestione personale supporta compliance GDPR"}}}, "time_tracking": {"name": "Time Tracking", "description": "Sistema di tracciamento ore, timesheets e gestione presenze", "icon": "clock", "modules": ["timesheets", "timeoff_requests", "monthly_reports"], "compliance_mapping": {"ISO_9001_2015": {"requirements": ["Monitoraggio della soddisfazione del cliente", "Definizione di obiettivi misurabili per la qualità", "Registrazioni e documentazione processi"], "compliance_score": 70, "notes": "Timesheet fornisce dati per misurazione performance e allocazione risorse"}, "ISO_14001_2015": {"requirements": ["Monitoraggio continuo delle prestazioni ambientali", "Registri di monitoraggio"], "compliance_score": 65, "notes": "Sistema ore può trackare attività ambientali se configurato appropriatamente"}}}, "document_management": {"name": "Gestione Documentale", "description": "Sistema per archiviazione, versioning e controllo documenti", "icon": "document-text", "modules": ["documents", "templates", "approval_workflows"], "compliance_mapping": {"ISO_9001_2015": {"requirements": ["Implementazione sistema documentale", "Procedure operative standard e istruzioni di lavoro dettagliate", "Manuale della Qualità", "Moduli e registrazioni qualità"], "compliance_score": 95, "notes": "Sistema documentale con workflow approvazione copre tutti requisiti documentali"}, "ISO_27001_2022": {"requirements": ["Politiche di sicurezza IT documentate e comunicate", "Statement of Applicability (SoA)", "Documentazione controlli implementati"], "compliance_score": 85, "notes": "Sistema documenti gestisce politiche sicurezza e documentazione controlli"}, "GDPR_COMPLIANCE": {"requirements": ["Privacy policy aggiornate", "Registro dei trattamenti", "Procedure diritti interessati", "Contratti DPA fornitori"], "compliance_score": 90, "notes": "Gestione documentale supporta tutti i documenti GDPR richiesti"}}}, "crm_system": {"name": "Sistema CRM", "description": "Gestione clienti, contatti, proposte e contratti", "icon": "user-group", "modules": ["clients", "contacts", "proposals", "contracts"], "compliance_mapping": {"ISO_9001_2015": {"requirements": ["Monitoraggio della soddisfazione del cliente", "Gestione sistematica delle non conformità", "Implementazione di azioni correttive e preventive"], "compliance_score": 80, "notes": "CRM traccia soddisfazione clienti e gestisce feedback per miglioramento continuo"}, "GDPR_COMPLIANCE": {"requirements": ["Registro dettagliato dei trattamenti implementato e mantenuto", "Procedure operative per garantire i diritti degli interessati", "Misure di sicurezza tecniche e organizzative appropriate"], "compliance_score": 85, "notes": "CRM gestisce dati personali con controlli accesso e tracciabilità trattamenti"}}}, "financial_management": {"name": "Gestione Finanziaria", "description": "Sistema per fatturazione, spese, budget e reporting finanziario", "icon": "banknotes", "modules": ["invoices", "expenses", "pre_invoices", "budget_tracking"], "compliance_mapping": {"ISO_9001_2015": {"requirements": ["Definizione di obiettivi misurabili per la qualità", "Monitoraggio delle performance", "Riesame della direzione periodico"], "compliance_score": 75, "notes": "Sistema finanziario fornisce KPI economici per riesame direzione"}, "SOC2_TYPE2": {"requirements": ["Controlli di sicurezza implementati e operativi", "Monitoring continuo dei sistemi", "Change management formalizzato"], "compliance_score": 70, "notes": "Sistema finanziario ha controlli accesso e audit trail transazioni"}}}, "communication_platform": {"name": "Piattaforma Comunicazione", "description": "Sistema per messaggi interni, forum, eventi e sondaggi", "icon": "chat-bubble-left-right", "modules": ["messages", "forum", "events", "polls", "announcements"], "compliance_mapping": {"ISO_27001_2022": {"requirements": ["Programma di training di awareness sulla sicurezza", "Politiche di sicurezza IT documentate e comunicate", "Piano di incident response strutturato"], "compliance_score": 65, "notes": "Piattaforma comunicazione supporta diffusione politiche e training sicurezza"}, "GDPR_COMPLIANCE": {"requirements": ["Privacy policy aggiornata e completa per tutti i trattamenti", "Procedure operative per garantire i diritti degli interessati"], "compliance_score": 70, "notes": "Sistema comunicazione gestisce consensi e diritti privacy degli utenti"}}}, "analytics_dashboard": {"name": "Analytics e Reporting", "description": "Dashboard avanzate, KPI monitoring e business intelligence", "icon": "chart-bar", "modules": ["dashboard", "kpis", "reports", "business_intelligence"], "compliance_mapping": {"ISO_9001_2015": {"requirements": ["Definizione KPI e indicatori qualità", "Monitoraggio della soddisfazione del cliente", "Riesame della direzione periodico", "Conduzione di audit interni regolari"], "compliance_score": 90, "notes": "Sistema analytics fornisce tutti i KPI e reporting necessari per ISO 9001"}, "ISO_14001_2015": {"requirements": ["Monitoraggio continuo delle prestazioni ambientali", "Definizione di obiettivi ambientali misurabili e target", "Registri di monitoraggio"], "compliance_score": 80, "notes": "Dashboard KPI può essere configurato per monitoraggio ambientale"}, "ISO_27001_2022": {"requirements": ["Monitoring continuo dei sistemi", "Risk assessment completo sulla sicurezza delle informazioni"], "compliance_score": 75, "notes": "Analytics supporta monitoring sicurezza se integrato con controlli appropriati"}}}, "ai_integration": {"name": "Integrazione AI", "description": "Servizi di intelligenza artificiale per automazione e analisi", "icon": "cpu-chip", "modules": ["ai_resources", "cv_parsing", "resource_recommendations"], "compliance_mapping": {"ISO_9001_2015": {"requirements": ["Implementazione di azioni correttive e preventive", "Definizione KPI e indicatori qualità", "Monitoraggio della soddisfazione del cliente"], "compliance_score": 70, "notes": "AI fornisce insights predittivi per miglioramento continuo qualità"}, "ISO_27001_2022": {"requirements": ["Risk assessment completo sulla sicurezza delle informazioni", "Piano di incident response strutturato"], "compliance_score": 65, "notes": "AI può supportare risk assessment e detection anomalie sicurezza"}}}, "governance_module": {"name": "Governance e Compliance", "description": "Sistema completo di audit, compliance, gestione rischi e policy", "icon": "shield-exclamation", "modules": ["compliance_audit", "risk_management", "policy_management", "incident_tracking"], "compliance_mapping": {"ISO_9001_2015": {"requirements": ["Audit interni regolari", "Gestione non conformità", "Azioni correttive e preventive", "Riesame della direzione"], "compliance_score": 95, "notes": "Modulo governance copre completamente requisiti audit e gestione qualità"}, "ISO_27001_2022": {"requirements": ["Risk assessment completo", "Gestione incidenti sicurezza", "Audit trail completo", "Policy di sicurezza documentate"], "compliance_score": 90, "notes": "Sistema governance fornisce controlli sicurezza e audit trail richiesti"}, "GDPR_COMPLIANCE": {"requirements": ["Audit trail accessi dati", "Gestione incident data breach", "Policy privacy documentate", "Risk assessment privacy"], "compliance_score": 85, "notes": "Governance supporta compliance GDPR con audit e gestione incident"}, "SOC2_TYPE2": {"requirements": ["Controlli operativi documentati", "Monitoring continuo", "Gestione incident", "Audit trail attività"], "compliance_score": 90, "notes": "Modulo governance soddisfa requisiti SOC2 per controlli operativi"}}}, "engagement_module": {"name": "Employee Engagement", "description": "Sistema gamificazione, campagne engagement e tracking partecipazione", "icon": "trophy", "modules": ["engagement_campaigns", "points_system", "training_tracking", "leaderboards"], "compliance_mapping": {"ISO_9001_2015": {"requirements": ["Formazione del personale", "Coinvolgimento dei dipendenti", "Miglioramento continuo", "Competenze e awareness"], "compliance_score": 80, "notes": "Sistema engagement traccia formazione e coinvolgimento dipendenti"}, "ISO_27001_2022": {"requirements": ["Programma training sicurezza", "Awareness sui rischi", "Coinvolgimento nelle politiche", "Reporting comportamenti sospetti"], "compliance_score": 75, "notes": "Engagement supporta programmi training sicurezza e awareness"}, "ISO_45001_2018": {"requirements": ["Partecipazione e consultazione lavoratori", "Training sicurezza sul lavoro", "Comunicazione politiche sicurezza", "Coinvolgimento miglioramento continuo"], "compliance_score": 85, "notes": "Sistema engagement ottimale per coinvolgimento sicurezza sul lavoro"}}}, "ceo_strategic_module": {"name": "Human CEO Intelligence", "description": "AI strategico per CEO, insights business e decision support", "icon": "academic-cap", "modules": ["strategic_insights", "ai_assistant", "management_reviews", "business_intelligence"], "compliance_mapping": {"ISO_9001_2015": {"requirements": ["Riesame della direzione", "Leadership e commitment", "Obiettivi qualità strategici", "Miglioramento continuo sistematico"], "compliance_score": 85, "notes": "CEO module supporta riesame direzione e leadership commitment"}, "ISO_27001_2022": {"requirements": ["Leadership e commitment sicurezza", "Risk assessment strategico", "Riesame direzione sicurezza", "Strategic planning sicurezza"], "compliance_score": 80, "notes": "AI strategico supporta decision making sicurezza e risk assessment"}, "SOC2_TYPE2": {"requirements": ["Governance e oversight", "Management review processes", "Strategic risk management", "Organizational controls"], "compliance_score": 75, "notes": "CEO module fornisce governance e strategic oversight richiesti"}}}, "recruiting_module": {"name": "Recruiting e Selezione", "description": "Sistema completo recruiting, valutazione candidati e gestione competenze", "icon": "user-plus", "modules": ["job_postings", "candidate_management", "interview_process", "skills_assessment"], "compliance_mapping": {"ISO_9001_2015": {"requirements": ["Competenze e formazione", "Gestione risorse umane", "Valutazione competenze", "Identificazione bisogni formativi"], "compliance_score": 85, "notes": "Sistema recruiting supporta gestione competenze per qualità"}, "GDPR_COMPLIANCE": {"requirements": ["<PERSON><PERSON><PERSON><PERSON><PERSON> dati candidati", "Consenso processing CV", "<PERSON><PERSON><PERSON> interessati recruiting", "Data retention recruiting"], "compliance_score": 90, "notes": "Recruiting gestisce dati personali con controlli GDPR appropriati"}, "ISO_45001_2018": {"requirements": ["Competenze sicurezza sul lavoro", "Training needs assessment", "Selezione personale competente", "Awareness requisiti sicurezza"], "compliance_score": 80, "notes": "Sistema recruiting valuta competenze sicurezza richieste"}}}, "access_control": {"name": "Controllo Accessi", "description": "Sistema RBAC (Role-Based Access Control) con permessi granulari", "icon": "shield-check", "modules": ["authentication", "authorization", "session_management", "audit_logs"], "compliance_mapping": {"ISO_27001_2022": {"requirements": ["Gestione degli accessi e delle identità", "Controllo operativo dei processi con impatto sicurezza", "Implementazione controlli tecnici (firewall, antivirus, backup, crittografia)"], "compliance_score": 90, "notes": "Sistema RBAC soddisfa completamente requisiti controllo accessi ISO 27001"}, "SOC2_TYPE2": {"requirements": ["Controlli di sicurezza implementati e operativi", "Politiche di accesso fisico e logico", "Change management formalizzato"], "compliance_score": 85, "notes": "Controlli accesso e audit logs coprono requisiti SOC2 per sicurezza"}, "GDPR_COMPLIANCE": {"requirements": ["Misure di sicurezza tecniche e organizzative appropriate", "Controllo accessi ai dati personali", "Audit trail delle operazioni sui dati"], "compliance_score": 85, "notes": "Sistema accessi garantisce protezione dati personali secondo GDPR"}}}}, "certification_readiness_calculator": {"scoring_weights": {"platform_features_score": 0.4, "standard_requirements_score": 0.6}, "readiness_thresholds": {"excellent": 85, "good": 70, "fair": 55, "poor": 40}, "platform_bonus_factors": {"ai_integration_active": 5, "full_modules_adoption": 10, "advanced_analytics_usage": 5, "document_workflows_configured": 8, "rbac_properly_configured": 7}}, "recommendations_engine": {"low_compliance_suggestions": {"ISO_9001_2015": ["Configurare workflow approvazione documenti per procedure qualità", "Attivare modulo KPI per monitoraggio obiettivi qualità", "Utilizzare sistema HR per definire matrice responsabilità", "Implementare dashboard analytics per riesame direzione"], "ISO_27001_2022": ["Rafforzare configurazione RBAC per controllo accessi", "Attivare audit logs per monitoraggio <PERSON>zza", "Utilizzare sistema comunicazione per diffondere politiche sicurezza", "Configurare dashboard per monitoring continuo sistemi"], "GDPR_COMPLIANCE": ["Configurare registri trattamento nel sistema CRM", "Implementare workflow consensi in piattaforma comunicazione", "Attivare audit trail completo per tracciabilità dati", "Utilizzare sistema documenti per gestire privacy policy"]}}, "certification_data_queries": {"ISO_9001_2015": {"description": "Gestione Qualità - Focus su processi, miglioramento continuo e soddisfazione cliente", "required_features": ["project_management", "personnel_management", "time_tracking", "document_management", "analytics_dashboard"], "aggregation_functions": {"project_success_rate": {"description": "Percentuale progetti completati con successo", "weight": 25, "target_threshold": 80, "model": "Project", "calculation": "completed_projects_last_year / total_projects_last_year * 100"}, "task_efficiency": {"description": "Efficienza task (ore stimate vs effettive)", "weight": 20, "target_threshold": 85, "model": "Task", "calculation": "avg(estimated_hours / actual_hours) * 100"}, "timesheet_compliance": {"description": "Compliance approvazione timesheet", "weight": 15, "target_threshold": 90, "model": "MonthlyTimesheet", "calculation": "approved_timesheets / total_timesheets * 100"}, "performance_reviews_completion": {"description": "Completamento performance review", "weight": 20, "target_threshold": 95, "model": "PerformanceReview", "calculation": "completed_reviews / total_reviews * 100"}, "client_satisfaction_proxy": {"description": "Proxy soddisfazione cliente (progetti ripetuti)", "weight": 20, "target_threshold": 70, "model": "Project", "calculation": "clients_with_multiple_projects / total_clients * 100"}}}, "ISO_27001_2022": {"description": "Sicurezza Informazioni - Focus su controlli accesso, risk management e incident response", "required_features": ["access_control", "document_management", "personnel_management", "analytics_dashboard"], "aggregation_functions": {"access_control_compliance": {"description": "Utilizzo controllo accessi RBAC", "weight": 30, "target_threshold": 95, "model": "User", "calculation": "users_with_defined_roles / total_users * 100"}, "security_training_completion": {"description": "Completamento training sicurezza", "weight": 25, "target_threshold": 100, "model": "PerformanceReview", "calculation": "reviews_with_security_training / total_reviews * 100"}, "incident_response_time": {"description": "Tempo medio risposta incident (task urgenti)", "weight": 20, "target_threshold": 24, "model": "Task", "calculation": "avg_hours_urgent_tasks_completion"}, "policy_documentation_coverage": {"description": "Copertura documentazione politiche", "weight": 25, "target_threshold": 90, "model": "Document", "calculation": "security_documents / total_policy_documents * 100"}}}, "GDPR_COMPLIANCE": {"description": "Privacy e Protezione Dati - Focus su consensi, diritti interessati e audit trail", "required_features": ["crm_system", "document_management", "personnel_management", "access_control"], "aggregation_functions": {"data_subject_rights_response": {"description": "Tempo medio risposta richieste GDPR", "weight": 30, "target_threshold": 72, "model": "Task", "calculation": "avg_hours_gdpr_requests_completion"}, "consent_management_coverage": {"description": "Copertura gestione consensi clienti", "weight": 25, "target_threshold": 100, "model": "Client", "calculation": "clients_with_consent_records / total_clients * 100"}, "privacy_training_completion": {"description": "Completamento training privacy", "weight": 25, "target_threshold": 100, "model": "PerformanceReview", "calculation": "reviews_with_privacy_training / total_reviews * 100"}, "data_breach_response_time": {"description": "Tempo medio gestione data breach", "weight": 20, "target_threshold": 24, "model": "Task", "calculation": "avg_hours_security_incidents_completion"}}}, "ISO_9001_2015_ENHANCED": {"description": "Gestione Qualità ENHANCED - Include governance, engagement e strategic oversight", "required_features": ["project_management", "personnel_management", "time_tracking", "document_management", "analytics_dashboard", "governance_module", "engagement_module", "ceo_strategic_module"], "aggregation_functions": {"project_success_rate": {"description": "Percentuale progetti completati con successo", "weight": 20, "target_threshold": 80, "model": "Project", "calculation": "completed_projects_last_year / total_projects_last_year * 100"}, "task_efficiency": {"description": "Efficienza task (ore stimate vs effettive)", "weight": 15, "target_threshold": 85, "model": "Task", "calculation": "avg(estimated_hours / actual_hours) * 100"}, "timesheet_compliance": {"description": "Compliance approvazione timesheet", "weight": 10, "target_threshold": 90, "model": "MonthlyTimesheet", "calculation": "approved_timesheets / total_timesheets * 100"}, "performance_reviews_completion": {"description": "Completamento performance review", "weight": 15, "target_threshold": 95, "model": "PerformanceReview", "calculation": "completed_reviews / total_reviews * 100"}, "client_satisfaction_proxy": {"description": "Proxy soddisfazione cliente (progetti ripetuti)", "weight": 10, "target_threshold": 70, "model": "Project", "calculation": "clients_with_multiple_projects / total_clients * 100"}, "governance_audit_coverage": {"description": "Copertura audit trail e azioni ad alto rischio", "weight": 15, "target_threshold": 80, "model": "ComplianceAuditLog", "calculation": "high_risk_actions_tracked / total_actions * 100"}, "training_participation_rate": {"description": "Partecipazione dipendenti a training qualità", "weight": 10, "target_threshold": 90, "model": "EngagementPoint", "calculation": "users_with_training_points / active_users * 100"}, "management_review_frequency": {"description": "Frequenza revisioni strategiche direzione", "weight": 5, "target_threshold": 12, "model": "AIInteraction", "calculation": "strategic_compliance_interactions_per_year"}}}, "ISO_27001_2022_ENHANCED": {"description": "Sicurezza Informazioni ENHANCED - Include governance, risk management e strategic security", "required_features": ["access_control", "document_management", "personnel_management", "analytics_dashboard", "governance_module", "ceo_strategic_module"], "aggregation_functions": {"access_control_compliance": {"description": "Utilizzo controllo accessi RBAC", "weight": 20, "target_threshold": 95, "model": "User", "calculation": "users_with_defined_roles / total_users * 100"}, "governance_audit_coverage": {"description": "Copertura audit trail sicurezza", "weight": 25, "target_threshold": 90, "model": "ComplianceAuditLog", "calculation": "security_actions_tracked / total_security_actions * 100"}, "risk_management_effectiveness": {"description": "Efficacia gestione rischi sicurezza", "weight": 20, "target_threshold": 85, "model": "Risk", "calculation": "mitigated_risks / total_identified_risks * 100"}, "policy_management_maturity": {"description": "Maturità gestione policy sicurezza", "weight": 15, "target_threshold": 90, "model": "CompliancePolicy", "calculation": "reviewed_policies / active_policies * 100"}, "incident_response_effectiveness": {"description": "Efficacia risposta incident sicurezza", "weight": 15, "target_threshold": 95, "model": "ComplianceEvent", "calculation": "resolved_security_events / total_security_events * 100"}, "strategic_insights_adoption": {"description": "Adozione insights AI per sicurezza strategica", "weight": 5, "target_threshold": 70, "model": "StrategicInsight", "calculation": "implemented_security_insights / total_security_insights * 100"}}}, "GDPR_COMPLIANCE_ENHANCED": {"description": "Privacy e Protezione Dati ENHANCED - Include governance privacy e recruiting compliance", "required_features": ["crm_system", "document_management", "personnel_management", "access_control", "governance_module", "recruiting_module"], "aggregation_functions": {"governance_audit_coverage": {"description": "Audit trail accessi dati personali", "weight": 30, "target_threshold": 95, "model": "ComplianceAuditLog", "calculation": "pii_access_actions_tracked / total_pii_actions * 100"}, "policy_management_maturity": {"description": "Gestione policy privacy aggiornate", "weight": 20, "target_threshold": 100, "model": "CompliancePolicy", "calculation": "privacy_policies_reviewed / total_privacy_policies * 100"}, "incident_response_effectiveness": {"description": "Gestione incident data breach", "weight": 25, "target_threshold": 100, "model": "ComplianceEvent", "calculation": "resolved_privacy_incidents / total_privacy_incidents * 100"}, "training_participation_rate": {"description": "Partecipazione training GDPR", "weight": 15, "target_threshold": 100, "model": "EngagementPoint", "calculation": "users_with_gdpr_training / active_users * 100"}, "competency_assessment_coverage": {"description": "Gestione competente dati recruiting", "weight": 10, "target_threshold": 100, "model": "CandidateSkill", "calculation": "candidates_with_gdpr_skills / total_candidates * 100"}}}, "SOC2_TYPE2_ENHANCED": {"description": "SOC 2 Type II ENHANCED - Include governance operativa e controlli strategici", "required_features": ["access_control", "analytics_dashboard", "governance_module", "ceo_strategic_module"], "aggregation_functions": {"access_control_compliance": {"description": "Controlli accesso operativi", "weight": 25, "target_threshold": 98, "model": "User", "calculation": "users_with_proper_access_controls / total_users * 100"}, "governance_audit_coverage": {"description": "Audit trail controlli operativi", "weight": 30, "target_threshold": 95, "model": "ComplianceAuditLog", "calculation": "operational_controls_tracked / total_operational_actions * 100"}, "incident_response_effectiveness": {"description": "Gestione incident operativi", "weight": 20, "target_threshold": 100, "model": "ComplianceEvent", "calculation": "resolved_operational_incidents / total_operational_incidents * 100"}, "management_review_frequency": {"description": "Review controlli da parte direzione", "weight": 15, "target_threshold": 12, "model": "AIInteraction", "calculation": "operational_review_interactions_per_year"}, "policy_management_maturity": {"description": "Policy operative documentate e aggiornate", "weight": 10, "target_threshold": 100, "model": "CompliancePolicy", "calculation": "operational_policies_reviewed / total_operational_policies * 100"}}}}}