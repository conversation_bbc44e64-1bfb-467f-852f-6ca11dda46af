import os
from datetime import timedelta

class Config:
    # Flask config
    DEBUG = os.environ.get('FLASK_DEBUG', 'True') == 'True'
    TESTING = False

    # Database config
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        "pool_recycle": 300,
        "pool_pre_ping": True,
    }

    # API keys
    OPENAI_API_KEY = os.environ.get('OPENAI_API_KEY')
    PERPLEXITY_API_KEY = os.environ.get('PERPLEXITY_API_KEY')
    
    # AI Model Configuration
    OPENAI_DEFAULT_MODEL = os.environ.get('OPENAI_DEFAULT_MODEL', 'gpt-4o-mini')
    OPENAI_ANALYSIS_MODEL = os.environ.get('OPENAI_ANALYSIS_MODEL', 'gpt-4o-mini')  
    OPENAI_CHAT_MODEL = os.environ.get('OPENAI_CHAT_MODEL', 'gpt-4o-mini')
    OPENAI_CEO_PRO_MODEL = os.environ.get('OPENAI_CEO_PRO_MODEL', 'gpt-4o-mini')
    OPENAI_CEO_DEEP_MODEL = os.environ.get('OPENAI_CEO_DEEP_MODEL', 'o3-mini')
    # Perplexity Model Configuration for CEO Insights
    PERPLEXITY_DEFAULT_MODEL = os.environ.get('PERPLEXITY_DEFAULT_MODEL', 'sonar-pro')
    PERPLEXITY_SONAR_MODEL = os.environ.get('PERPLEXITY_SONAR_MODEL', 'sonar-pro')  # Pro mode: 200k context
    PERPLEXITY_SONAR_DEEP_MODEL = os.environ.get('PERPLEXITY_SONAR_DEEP_MODEL', 'sonar-deep-research')  # Deep mode: 128k context
    PERPLEXITY_REASONING_MODEL = os.environ.get('PERPLEXITY_REASONING_MODEL', 'sonar-reasoning-pro')  # Advanced reasoning: 128k context

    # Application config
    COMPANY_NAME = "Portale"
    APPLICATION_NAME = "PlaceholderPortal"
    UPLOAD_FOLDER = os.path.join('static', 'uploads')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max upload size
    PASSWORD_RESET_TOKEN_EXPIRATION_SECONDS = 3600 * 24  # 24 ore

    # Email config
    MAIL_SERVER = os.environ.get('MAIL_SERVER', 'smtp.gmail.com')
    MAIL_PORT = int(os.environ.get('MAIL_PORT', 587))
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'True') == 'True'
    MAIL_USE_SSL = os.environ.get('MAIL_USE_SSL', 'False') == 'True'
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    MAIL_DEFAULT_SENDER = os.environ.get('MAIL_DEFAULT_SENDER', f'noreply@{COMPANY_NAME.lower()}.com')

    # Session management
    SESSION_PERMANENT = True
    PERMANENT_SESSION_LIFETIME = timedelta(minutes=30)  # Idle timeout
    ABSOLUTE_SESSION_LIFETIME = 3600  # Absolute timeout in seconds
    REMEMBER_COOKIE_DURATION = timedelta(days=7)

    # Cookie configuration for CORS/SPA
    SESSION_COOKIE_SECURE = False  # Set to True in production with HTTPS
    SESSION_COOKIE_HTTPONLY = True  # Prevent XSS attacks
    SESSION_COOKIE_SAMESITE = 'Lax'  # Allow cross-origin requests
    SESSION_COOKIE_NAME = 'session'

    # OAuth Configuration - Google
    GOOGLE_OAUTH_ENABLED = bool(
        os.environ.get('GOOGLE_CLIENT_ID') and 
        os.environ.get('GOOGLE_CLIENT_SECRET')
    )
    GOOGLE_CLIENT_ID = os.environ.get('GOOGLE_CLIENT_ID')
    GOOGLE_CLIENT_SECRET = os.environ.get('GOOGLE_CLIENT_SECRET')

    # OAuth Configuration - Microsoft
    MICROSOFT_OAUTH_ENABLED = bool(
        os.environ.get('MICROSOFT_CLIENT_ID') and 
        os.environ.get('MICROSOFT_CLIENT_SECRET')
    )
    MICROSOFT_CLIENT_ID = os.environ.get('MICROSOFT_CLIENT_ID')
    MICROSOFT_CLIENT_SECRET = os.environ.get('MICROSOFT_CLIENT_SECRET')

    # OAuth General Settings
    OAUTH_REDIRECT_URI = os.environ.get('OAUTH_REDIRECT_URI', 'http://localhost:5000/api/auth/oauth/callback')
    OAUTH_REQUIRE_EXISTING_USER = os.environ.get('OAUTH_REQUIRE_EXISTING_USER', 'true').lower() == 'true'

    # Security Configuration
    # File upload security
    CV_UPLOAD_DIRECTORIES = [
        os.path.join(os.path.dirname(__file__), 'uploads', 'cvs'),
        os.path.join('static', 'uploads', 'cvs')
    ]
    
    # Export security limits
    EXPORT_MAX_RECORDS = int(os.environ.get('EXPORT_MAX_RECORDS', 10000))
    EXPORT_TIMEOUT_SECONDS = int(os.environ.get('EXPORT_TIMEOUT_SECONDS', 300))  # 5 minutes
    EXPORT_MAX_FILE_SIZE_MB = int(os.environ.get('EXPORT_MAX_FILE_SIZE_MB', 100))
    
    # Rate limiting for exports
    EXPORT_RATE_LIMIT_PER_HOUR = int(os.environ.get('EXPORT_RATE_LIMIT_PER_HOUR', 20))
    EXPORT_RATE_LIMIT_PER_DAY = int(os.environ.get('EXPORT_RATE_LIMIT_PER_DAY', 100))
    
    # File security
    ALLOWED_UPLOAD_EXTENSIONS = {
        'cv': {'.pdf', '.doc', '.docx', '.txt', '.rtf'},
        'image': {'.jpg', '.jpeg', '.png', '.gif', '.webp'},
        'document': {'.pdf', '.doc', '.docx', '.xls', '.xlsx', '.csv', '.txt'}
    }
    
    MAX_FILENAME_LENGTH = 200
    
    # Audit logging configuration
    AUDIT_LOG_EXPORTS = True
    AUDIT_LOG_FILE_DOWNLOADS = True
    AUDIT_LOG_RETENTION_DAYS = int(os.environ.get('AUDIT_LOG_RETENTION_DAYS', 365))
    
    # Compliance settings
    GDPR_COMPLIANCE_MODE = os.environ.get('GDPR_COMPLIANCE_MODE', 'true').lower() == 'true'
    ISO27001_COMPLIANCE_MODE = os.environ.get('ISO27001_COMPLIANCE_MODE', 'true').lower() == 'true'
