"""
Compliance Tracker Middleware
Non-invasive audit trail system per compliance (ISO 27001, GDPR, SOC 2)
"""
import os
import json
import hashlib
import time
from datetime import datetime
from flask import request, g, session
from flask_login import current_user
from urllib.parse import urlparse, parse_qs
import logging

logger = logging.getLogger(__name__)

class ComplianceTracker:
    """
    Middleware per tracking compliance senza modificare flussi esistenti
    Traccia tutte le requests per audit trail completo
    """
    
    def __init__(self, app=None):
        self.app = app
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """Inizializza il middleware con l'app Flask"""
        app.before_request(self.before_request)
        app.after_request(self.after_request)
        
        # Configuration
        self.enabled = os.getenv('ENABLE_COMPLIANCE_AUDIT', 'false').lower() == 'true'
        self.log_level = os.getenv('COMPLIANCE_LOG_LEVEL', 'info').lower()
        self.mask_sensitive = os.getenv('COMPLIANCE_SENSITIVE_DATA_MASK', 'true').lower() == 'true'
        self.async_logging = os.getenv('COMPLIANCE_ASYNC_LOGGING', 'true').lower() == 'true'
        
        logger.info(f"ComplianceTracker initialized: {'ENABLED' if self.enabled else 'DISABLED'}")
    
    def should_track_request(self):
        """Determina se la request deve essere tracciata"""
        if not self.enabled:
            return False
        
        # Skip static files e health checks
        if request.endpoint in ['static', 'health', 'ping']:
            return False
        
        # Skip OPTIONS requests (CORS preflight)
        if request.method == 'OPTIONS':
            return False
        
        # Track solo API e protected routes
        path = request.path
        track_paths = ['/api/', '/app/', '/admin/']
        return any(path.startswith(p) for p in track_paths)
    
    def before_request(self):
        """Hook before request - prepara context audit"""
        if not self.should_track_request():
            return
        
        try:
            # Store request context per after_request
            g.audit_context = {
                'start_time': time.time(),
                'timestamp': datetime.utcnow(),
                'user_id': current_user.id if current_user.is_authenticated else None,
                'session_id': session.get('_id', 'anonymous'),
                'endpoint': request.endpoint,
                'method': request.method,
                'url': request.url,
                'path': request.path,
                'ip': self._get_client_ip(),
                'user_agent': request.headers.get('User-Agent', ''),
                'referer': request.headers.get('Referer'),
                'content_type': request.headers.get('Content-Type'),
                'request_size': request.content_length or 0
            }
            
            # Estrai info request
            g.audit_context.update(self._extract_request_info())
            
        except Exception as e:
            logger.error(f"Error in compliance before_request: {e}")
    
    def after_request(self, response):
        """Hook after request - log audit"""
        if not hasattr(g, 'audit_context'):
            return response
        
        try:
            # Calcola processing time
            processing_time = int((time.time() - g.audit_context['start_time']) * 1000)
            
            # Prepara audit log data
            audit_data = {
                **g.audit_context,
                'response_status': response.status_code,
                'response_size': len(response.get_data()) if response.get_data() else 0,
                'processing_time_ms': processing_time,
                'risk_level': self._assess_risk_level(g.audit_context, response),
                'data_classification': self._classify_data(g.audit_context),
                'is_sensitive': self._is_sensitive_operation(g.audit_context),
                'compliance_context': self._build_compliance_context(g.audit_context, response)
            }
            
            # Log async per non bloccare response
            if self.async_logging:
                self._async_log_audit(audit_data)
            else:
                self._log_audit_sync(audit_data)
                
            # Check per eventi compliance critici
            self._check_compliance_events(audit_data)
            
        except Exception as e:
            logger.error(f"Error in compliance after_request: {e}")
        
        return response
    
    def _get_client_ip(self):
        """Estrae IP cliente considerando proxy"""
        # Check X-Forwarded-For per proxy/load balancer
        if request.headers.get('X-Forwarded-For'):
            return request.headers.get('X-Forwarded-For').split(',')[0].strip()
        elif request.headers.get('X-Real-IP'):
            return request.headers.get('X-Real-IP')
        else:
            return request.remote_addr
    
    def _extract_request_info(self):
        """Estrae informazioni dalla request per audit"""
        info = {
            'action_type': self._determine_action_type(),
            'resource_type': self._determine_resource_type(),
            'resource_id': self._extract_resource_id(),
            'request_data': self._sanitize_request_data()
        }
        return info
    
    def _determine_action_type(self):
        """Determina tipo azione basato su method e endpoint"""
        method = request.method.upper()
        path = request.path.lower()
        
        # Mapping specifici
        if 'login' in path:
            return 'login'
        elif 'logout' in path:
            return 'logout'
        elif method == 'GET':
            return 'view' if '/api/' in path else 'access'
        elif method == 'POST':
            return 'create'
        elif method in ['PUT', 'PATCH']:
            return 'update'
        elif method == 'DELETE':
            return 'delete'
        else:
            return 'unknown'
    
    def _determine_resource_type(self):
        """Estrae tipo risorsa dal path"""
        path_parts = request.path.strip('/').split('/')
        
        # Mapping endpoint → resource type
        resource_mapping = {
            'users': 'user',
            'projects': 'project',
            'tasks': 'task',
            'clients': 'client',
            'invoices': 'invoice',
            'contracts': 'contract',
            'timesheets': 'timesheet',
            'notifications': 'notification',
            'personnel': 'personnel',
            'performance': 'performance',
            'admin': 'admin_config'
        }
        
        for part in path_parts:
            if part in resource_mapping:
                return resource_mapping[part]
        
        return 'unknown'
    
    def _extract_resource_id(self):
        """Estrae ID risorsa dall URL se presente"""
        try:
            # Cerca pattern ID numerici nel path
            path_parts = request.path.strip('/').split('/')
            for part in path_parts:
                if part.isdigit():
                    return part
        except:
            pass
        
        return None
    
    def _sanitize_request_data(self):
        """Sanitizza dati request rimuovendo PII sensibili"""
        if not self.mask_sensitive:
            return None
        
        try:
            data = {}
            
            # Query parameters
            if request.args:
                data['query_params'] = dict(request.args)
            
            # Form data o JSON (solo se non contiene password/sensitive)
            if request.is_json and request.json:
                data['json_payload'] = self._mask_sensitive_fields(request.json)
            elif request.form:
                data['form_data'] = self._mask_sensitive_fields(dict(request.form))
            
            return data if data else None
            
        except Exception as e:
            logger.warning(f"Error sanitizing request data: {e}")
            return None
    
    def _mask_sensitive_fields(self, data):
        """Maschera campi sensibili nei dati"""
        if not isinstance(data, dict):
            return data
        
        sensitive_fields = [
            'password', 'passwd', 'pwd', 'secret', 'token', 'key',
            'ssn', 'social_security', 'credit_card', 'card_number',
            'iban', 'bank_account', 'tax_id', 'fiscal_code'
        ]
        
        masked_data = {}
        for key, value in data.items():
            key_lower = key.lower()
            if any(field in key_lower for field in sensitive_fields):
                masked_data[key] = '***MASKED***'
            elif isinstance(value, dict):
                masked_data[key] = self._mask_sensitive_fields(value)
            else:
                masked_data[key] = value
        
        return masked_data
    
    def _assess_risk_level(self, context, response):
        """Valuta livello rischio dell'operazione"""
        risk_score = 0
        
        # Fattori di rischio
        if context.get('action_type') in ['delete', 'admin_action']:
            risk_score += 30
        
        if context.get('resource_type') in ['user', 'admin_config', 'client']:
            risk_score += 20
        
        if response.status_code >= 400:
            risk_score += 25
        
        if context.get('is_sensitive'):
            risk_score += 25
        
        # Determina livello
        if risk_score >= 70:
            return 'critical'
        elif risk_score >= 50:
            return 'high'
        elif risk_score >= 30:
            return 'medium'
        else:
            return 'low'
    
    def _classify_data(self, context):
        """Classifica dati secondo policy aziendali"""
        resource_type = context.get('resource_type', '')
        
        # Classificazione basata su resource type
        confidential_resources = ['user', 'client', 'contract', 'invoice']
        internal_resources = ['project', 'task', 'timesheet']
        
        if resource_type in confidential_resources:
            return 'confidential'
        elif resource_type in internal_resources:
            return 'internal'
        else:
            return 'public'
    
    def _is_sensitive_operation(self, context):
        """Determina se l'operazione coinvolge dati sensibili"""
        sensitive_resources = ['user', 'client', 'personnel', 'performance']
        sensitive_actions = ['create', 'update', 'delete', 'export']
        
        return (context.get('resource_type') in sensitive_resources or
                context.get('action_type') in sensitive_actions)
    
    def _build_compliance_context(self, context, response):
        """Costruisce context compliance specifico"""
        compliance_ctx = {
            'frameworks': [],
            'requirements': [],
            'data_subject_rights': False,
            'consent_required': False
        }
        
        # GDPR context
        if context.get('resource_type') in ['user', 'client', 'personnel']:
            compliance_ctx['frameworks'].append('GDPR')
            compliance_ctx['data_subject_rights'] = True
            
            if context.get('action_type') in ['create', 'update']:
                compliance_ctx['consent_required'] = True
        
        # ISO 27001 context
        if context.get('action_type') in ['admin_action', 'config_change']:
            compliance_ctx['frameworks'].append('ISO27001')
            compliance_ctx['requirements'].append('A.9.1.1 Access Control Policy')
        
        # SOC 2 context
        if context.get('resource_type') == 'admin_config':
            compliance_ctx['frameworks'].append('SOC2')
            compliance_ctx['requirements'].append('CC6.1 System Operations')
        
        return compliance_ctx
    
    def _async_log_audit(self, audit_data):
        """Log audit asincrono (implementazione futura con Celery/RQ)"""
        # Per ora log sincrono, in futuro implementare con task queue
        self._log_audit_sync(audit_data)
    
    def _log_audit_sync(self, audit_data):
        """Log audit sincrono al database"""
        try:
            from models import ComplianceAuditLog, db
            
            # Crea record audit
            audit_log = ComplianceAuditLog(
                user_id=audit_data.get('user_id'),
                session_id=audit_data.get('session_id'),
                action_type=audit_data.get('action_type'),
                resource_type=audit_data.get('resource_type'),
                resource_id=audit_data.get('resource_id'),
                endpoint=audit_data.get('endpoint'),
                method=audit_data.get('method'),
                ip_address=audit_data.get('ip'),
                user_agent=audit_data.get('user_agent'),
                request_data=audit_data.get('request_data'),
                response_status=audit_data.get('response_status'),
                response_size=audit_data.get('response_size'),
                processing_time_ms=audit_data.get('processing_time_ms'),
                compliance_context=audit_data.get('compliance_context'),
                timestamp=audit_data.get('timestamp'),
                risk_level=audit_data.get('risk_level'),
                data_classification=audit_data.get('data_classification'),
                is_sensitive=audit_data.get('is_sensitive')
            )
            
            db.session.add(audit_log)
            db.session.commit()
            
        except Exception as e:
            logger.error(f"Error logging audit data: {e}")
            # Non propagare l'errore per non bloccare la response
    
    def _check_compliance_events(self, audit_data):
        """Controlla se generare eventi compliance"""
        try:
            # Event per operazioni ad alto rischio
            if audit_data.get('risk_level') in ['high', 'critical']:
                self._create_compliance_event(
                    event_type='high_risk_operation',
                    severity='warning' if audit_data.get('risk_level') == 'high' else 'critical',
                    audit_data=audit_data
                )
            
            # Event per errori di accesso
            if audit_data.get('response_status') in [401, 403]:
                self._create_compliance_event(
                    event_type='access_denied',
                    severity='warning',
                    audit_data=audit_data
                )
            
            # Event per operazioni admin
            if audit_data.get('resource_type') == 'admin_config':
                self._create_compliance_event(
                    event_type='admin_action',
                    severity='info',
                    audit_data=audit_data
                )
                
        except Exception as e:
            logger.error(f"Error checking compliance events: {e}")
    
    def _create_compliance_event(self, event_type, severity, audit_data):
        """Crea evento compliance"""
        try:
            from models import ComplianceEvent, db
            
            # Convert datetime objects to ISO strings for JSON serialization
            clean_audit_data = {}
            for key, value in audit_data.items():
                if isinstance(value, datetime):
                    clean_audit_data[key] = value.isoformat()
                else:
                    clean_audit_data[key] = value
            
            event = ComplianceEvent(
                event_type=event_type,
                severity=severity,
                user_id=audit_data.get('user_id'),
                title=f"{event_type.replace('_', ' ').title()}: {audit_data.get('action_type')} on {audit_data.get('resource_type')}",
                description=f"User {audit_data.get('user_id')} performed {audit_data.get('action_type')} on {audit_data.get('resource_type')} from IP {audit_data.get('ip')}",
                source_ip=audit_data.get('ip'),
                source_system='web',
                event_metadata=clean_audit_data,
                compliance_framework='GDPR,ISO27001',
                regulatory_impact=severity in ['critical', 'high']
            )
            
            db.session.add(event)
            db.session.commit()
            
        except Exception as e:
            logger.error(f"Error creating compliance event: {e}")


# Istanza globale del tracker
compliance_tracker = ComplianceTracker()