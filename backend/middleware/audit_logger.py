"""
Middleware per audit logging di export e download file
Integrato con sistema ComplianceAuditLog esistente per compliance ISO 27001/GDPR
"""

from functools import wraps
from flask import current_app, request, g
from flask_login import current_user
from datetime import datetime
import time
import json

from models_split.audit_compliance import ComplianceAuditLog
from extensions import db


class ExportAuditLogger:
    """Logger specifico per operazioni di export e download"""
    
    @staticmethod
    def log_export_operation(
        action_type: str,
        resource_type: str, 
        resource_id: str = None,
        export_format: str = None,
        record_count: int = None,
        file_size_bytes: int = None,
        export_filters: dict = None,
        success: bool = True,
        error_message: str = None,
        processing_time_ms: int = None
    ):
        """
        Log di operazione export nel sistema audit
        
        Args:
            action_type: Tipo azione (data_export, cv_download, bulk_export)
            resource_type: Tipo risorsa (timesheet, recruiting, personnel, etc.)
            resource_id: ID specifico risorsa (opzionale)
            export_format: Formato export (excel, pdf, csv)
            record_count: Numero record esportati
            file_size_bytes: Dimensione file generato
            export_filters: Filtri applicati all'export
            success: Se operazione è riuscita
            error_message: Messaggio errore se fallita
            processing_time_ms: Tempo elaborazione in ms
        """
        try:
            # Determina livello di rischio basato su operazione
            risk_level = ExportAuditLogger._determine_risk_level(
                action_type, record_count, file_size_bytes
            )
            
            # Prepara context compliance
            compliance_context = {
                'export_format': export_format,
                'record_count': record_count,
                'file_size_bytes': file_size_bytes,
                'export_filters': export_filters or {},
                'success': success,
                'user_agent': request.headers.get('User-Agent'),
                'referer': request.headers.get('Referer')
            }
            
            if error_message:
                compliance_context['error_message'] = error_message
            
            # Determina classificazione dati
            data_classification = ExportAuditLogger._classify_data_sensitivity(
                resource_type, action_type
            )
            
            # Crea log audit
            audit_log = ComplianceAuditLog(
                user_id=current_user.id if current_user.is_authenticated else None,
                session_id=request.cookies.get('session'),
                action_type=action_type,
                resource_type=resource_type,
                resource_id=resource_id,
                endpoint=request.endpoint,
                method=request.method,
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent'),
                response_status=200 if success else 500,
                response_size=file_size_bytes,
                processing_time_ms=processing_time_ms,
                compliance_context=compliance_context,
                risk_level=risk_level,
                data_classification=data_classification,
                is_sensitive=data_classification in ['confidential', 'restricted']
            )
            
            db.session.add(audit_log)
            db.session.commit()
            
        except Exception as e:
            current_app.logger.error(f"Failed to log export audit: {str(e)}")
            # Non propagare errore per non bloccare operazione principale
    
    @staticmethod
    def _determine_risk_level(action_type: str, record_count: int = None, file_size_bytes: int = None) -> str:
        """Determina livello di rischio dell'operazione"""
        # CV download sempre alto rischio (dati personali)
        if action_type == 'cv_download':
            return 'high'
        
        # Export bulk sempre medio/alto rischio
        if action_type == 'bulk_export':
            return 'high'
        
        # Basato su volume dati
        if record_count:
            if record_count > 1000:
                return 'high'
            elif record_count > 100:
                return 'medium'
        
        # Basato su dimensione file
        if file_size_bytes:
            # > 50MB = alto rischio
            if file_size_bytes > 50 * 1024 * 1024:
                return 'high'
            # > 10MB = medio rischio  
            elif file_size_bytes > 10 * 1024 * 1024:
                return 'medium'
        
        return 'low'
    
    @staticmethod
    def _classify_data_sensitivity(resource_type: str, action_type: str) -> str:
        """Classifica sensibilità dati secondo policy aziendale"""
        # CV sempre confidenziali (dati personali)
        if action_type == 'cv_download' or 'cv' in resource_type.lower():
            return 'confidential'
        
        # Dati HR generalmente confidenziali
        if resource_type in ['personnel', 'candidate', 'recruiting']:
            return 'confidential'
        
        # Dati finanziari riservati
        if resource_type in ['invoice', 'expense', 'salary', 'funding']:
            return 'restricted'
        
        # Timesheet e progetti interni
        if resource_type in ['timesheet', 'project', 'task']:
            return 'internal'
        
        # Default pubblico interno
        return 'internal'


def audit_export_operation(
    resource_type: str,
    action_type: str = 'data_export'
):
    """
    Decorator per audit automatico operazioni export
    
    Args:
        resource_type: Tipo risorsa esportata
        action_type: Tipo azione (default: data_export)
    
    Usage:
        @audit_export_operation('timesheet', 'data_export')
        def export_timesheet_data():
            # ...
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                # Esegui funzione originale
                result = func(*args, **kwargs)
                
                # Calcola tempo elaborazione
                processing_time_ms = int((time.time() - start_time) * 1000)
                
                # Estrai informazioni dal result o response
                export_info = ExportAuditLogger._extract_export_info(
                    result, request.args
                )
                
                # Log successo
                ExportAuditLogger.log_export_operation(
                    action_type=action_type,
                    resource_type=resource_type,
                    export_format=request.args.get('format', 'excel'),
                    record_count=export_info.get('record_count'),
                    file_size_bytes=export_info.get('file_size'),
                    export_filters=dict(request.args),
                    success=True,
                    processing_time_ms=processing_time_ms
                )
                
                return result
                
            except Exception as e:
                # Calcola tempo elaborazione anche per errori
                processing_time_ms = int((time.time() - start_time) * 1000)
                
                # Log errore
                ExportAuditLogger.log_export_operation(
                    action_type=action_type,
                    resource_type=resource_type,
                    export_format=request.args.get('format', 'excel'),
                    export_filters=dict(request.args),
                    success=False,
                    error_message=str(e),
                    processing_time_ms=processing_time_ms
                )
                
                # Rilancia eccezione
                raise
        
        return wrapper
    return decorator


def audit_file_download(resource_type: str, action_type: str = 'file_download'):
    """
    Decorator per audit download file specifici
    
    Args:
        resource_type: Tipo risorsa (cv, document, etc.)
        action_type: Tipo azione (default: file_download)
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                # Esegui funzione originale
                result = func(*args, **kwargs)
                
                # Calcola tempo elaborazione
                processing_time_ms = int((time.time() - start_time) * 1000)
                
                # Estrai ID risorsa da args se presente
                resource_id = None
                if args:
                    # Spesso il primo arg è l'ID
                    resource_id = str(args[0]) if args[0] else None
                
                # Log successo download
                ExportAuditLogger.log_export_operation(
                    action_type=action_type,
                    resource_type=resource_type,
                    resource_id=resource_id,
                    success=True,
                    processing_time_ms=processing_time_ms
                )
                
                return result
                
            except Exception as e:
                # Calcola tempo elaborazione anche per errori
                processing_time_ms = int((time.time() - start_time) * 1000)
                
                # Estrai ID risorsa da args se presente
                resource_id = None
                if args:
                    resource_id = str(args[0]) if args[0] else None
                
                # Log errore download
                ExportAuditLogger.log_export_operation(
                    action_type=action_type,
                    resource_type=resource_type,
                    resource_id=resource_id,
                    success=False,
                    error_message=str(e),
                    processing_time_ms=processing_time_ms
                )
                
                # Rilancia eccezione
                raise
        
        return wrapper
    return decorator


# Helper methods per ExportAuditLogger
def _extract_export_info(result, request_args):
    """Estrae informazioni sull'export dal risultato"""
    info = {}
    
    # Se result è Flask Response con file
    if hasattr(result, 'content_length'):
        info['file_size'] = result.content_length
    
    # Prova a stimare record count da parametri request
    if 'limit' in request_args:
        try:
            info['record_count'] = int(request_args['limit'])
        except (ValueError, TypeError):
            pass
    
    return info


# Patch method per ExportAuditLogger
ExportAuditLogger._extract_export_info = staticmethod(_extract_export_info)


def setup_audit_logging(app):
    """
    Configura audit logging globale per l'app
    
    Args:
        app: Flask application instance
    """
    @app.before_request
    def before_request_audit():
        """Setup audit context per request"""
        g.audit_start_time = time.time()
    
    @app.after_request
    def after_request_audit(response):
        """Cleanup audit context dopo request"""
        # Solo per endpoint export/download se configurato
        if (hasattr(g, 'audit_start_time') and 
            current_app.config.get('AUDIT_LOG_EXPORTS', False)):
            
            # Log solo per operazioni export/download rilevanti
            if (request.endpoint and 
                ('export' in request.endpoint or 'download' in request.endpoint)):
                
                processing_time_ms = int((time.time() - g.audit_start_time) * 1000)
                
                # Log operazione generica se non già loggata da decorator
                if not hasattr(g, 'audit_logged'):
                    ExportAuditLogger.log_export_operation(
                        action_type='api_access',
                        resource_type=request.endpoint.split('.')[-1] if '.' in request.endpoint else request.endpoint,
                        success=response.status_code < 400,
                        processing_time_ms=processing_time_ms
                    )
        
        return response