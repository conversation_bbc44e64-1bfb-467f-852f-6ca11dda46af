# Help system models
import json
from .base import db, datetime

class HelpCategory(db.Model):
    """Categorie per organizzare contenuti help."""
    __tablename__ = 'help_categories'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    slug = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text)
    icon = db.Column(db.String(50))
    sort_order = db.Column(db.Integer, default=0)
    is_active = db.Column(db.<PERSON><PERSON>an, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    content_items = db.relationship('HelpContent', backref='category', lazy='dynamic')
    
    def __repr__(self):
        return f'<HelpCategory {self.name}>'
    
    @property
    def active_content_count(self):
        """Conta contenuti attivi nella categoria"""
        return HelpContent.query.filter_by(
            category_id=self.id, 
            is_published=True
        ).count()
    
    def to_dict(self):
        """Converte la categoria in dizionario per serializzazione JSON"""
        return {
            'id': self.id,
            'name': self.name,
            'slug': self.slug,
            'description': self.description,
            'icon': self.icon,
            'sort_order': self.sort_order,
            'is_active': self.is_active,
            'active_content_count': self.active_content_count,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class HelpContent(db.Model):
    """Contenuti di documentazione e help."""
    __tablename__ = 'help_content'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    slug = db.Column(db.String(200), nullable=False, unique=True)
    content = db.Column(db.Text, nullable=False)
    excerpt = db.Column(db.Text)
    
    # Classification
    category_id = db.Column(db.Integer, db.ForeignKey('help_categories.id'), nullable=False)
    content_type = db.Column(db.String(50), default='guide')  # guide, faq, tutorial, video
    difficulty_level = db.Column(db.String(20), default='beginner')  # beginner, intermediate, advanced
    estimated_read_time = db.Column(db.Integer)  # Minutes
    
    # Content metadata
    tags = db.Column(db.Text)  # JSON array of tags
    keywords = db.Column(db.Text)  # For search optimization
    related_modules = db.Column(db.Text)  # JSON array: ['personnel', 'projects', 'crm']
    
    # Publishing
    is_published = db.Column(db.Boolean, default=False)
    featured = db.Column(db.Boolean, default=False)
    view_count = db.Column(db.Integer, default=0)
    
    # Feedback
    helpful_votes = db.Column(db.Integer, default=0)
    not_helpful_votes = db.Column(db.Integer, default=0)
    
    # Metadata
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    creator = db.relationship('User', backref='help_content')
    
    def __repr__(self):
        return f'<HelpContent {self.title}>'
    
    @property
    def helpfulness_ratio(self):
        """Calcola ratio di utilità (helpful vs not helpful)"""
        total_votes = self.helpful_votes + self.not_helpful_votes
        if total_votes == 0:
            return 0.0
        return round((self.helpful_votes / total_votes) * 100, 1)
    
    @property
    def tags_list(self):
        """Restituisce tags come lista Python"""
        if self.tags:
            try:
                return json.loads(self.tags)
            except:
                return []
        return []
    
    @property
    def related_modules_list(self):
        """Restituisce moduli correlati come lista Python"""
        if self.related_modules:
            try:
                return json.loads(self.related_modules)
            except:
                return []
        return []
    
    def increment_view(self):
        """Incrementa counter visualizzazioni"""
        self.view_count += 1
        db.session.commit()
    
    def to_dict(self):
        """Converte il contenuto in dizionario per serializzazione JSON"""
        return {
            'id': self.id,
            'title': self.title,
            'slug': self.slug,
            'content': self.content,
            'excerpt': self.excerpt,
            'category_id': self.category_id,
            'category': self.category.to_dict() if self.category else None,
            'content_type': self.content_type,
            'difficulty_level': self.difficulty_level,
            'estimated_read_time': self.estimated_read_time,
            'tags': self.tags_list,
            'keywords': self.keywords,
            'related_modules': self.related_modules_list,
            'is_published': self.is_published,
            'featured': self.featured,
            'view_count': self.view_count,
            'helpful_votes': self.helpful_votes,
            'not_helpful_votes': self.not_helpful_votes,
            'helpfulness_ratio': self.helpfulness_ratio,
            'created_by': self.created_by,
            'creator': {'id': self.creator.id, 'username': self.creator.username, 'full_name': self.creator.full_name} if self.creator else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class HelpConversation(db.Model):
    """Conversazioni chat con assistente AI help."""
    __tablename__ = 'help_conversations'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    session_id = db.Column(db.String(100), nullable=False)
    title = db.Column(db.String(200))
    messages = db.Column(db.Text, nullable=False)  # JSON
    status = db.Column(db.String(20), default='active')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = db.relationship('User', backref='help_conversations')
    
    def __repr__(self):
        return f'<HelpConversation {self.title}>'
    
    @property
    def messages_list(self):
        """Restituisce messaggi come lista Python"""
        if self.messages:
            try:
                return json.loads(self.messages)
            except:
                return []
        return []
    
    def to_dict(self):
        """Converte la conversazione in dizionario per serializzazione JSON"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'session_id': self.session_id,
            'title': self.title,
            'messages': self.messages_list,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class HelpFeedback(db.Model):
    """Feedback degli utenti sui contenuti help."""
    __tablename__ = 'help_feedback'
    
    id = db.Column(db.Integer, primary_key=True)
    content_id = db.Column(db.Integer, db.ForeignKey('help_content.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    feedback_type = db.Column(db.String(50), nullable=False)  # helpful, not_helpful, suggestion
    feedback_text = db.Column(db.Text)
    rating = db.Column(db.Integer)  # 1-5
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    content = db.relationship('HelpContent', backref='feedback_items')
    user = db.relationship('User', backref='help_feedback')
    
    def __repr__(self):
        return f'<HelpFeedback {self.feedback_type}>'
    
    def to_dict(self):
        """Converte il feedback in dizionario per serializzazione JSON"""
        return {
            'id': self.id,
            'content_id': self.content_id,
            'user_id': self.user_id,
            'feedback_type': self.feedback_type,
            'feedback_text': self.feedback_text,
            'rating': self.rating,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }


class HelpAnalytics(db.Model):
    """Analytics del sistema help."""
    __tablename__ = 'help_analytics'
    
    id = db.Column(db.Integer, primary_key=True)
    event_type = db.Column(db.String(50), nullable=False)
    content_id = db.Column(db.Integer, db.ForeignKey('help_content.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    session_id = db.Column(db.String(100))
    event_metadata = db.Column(db.Text)  # JSON
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    content = db.relationship('HelpContent')
    user = db.relationship('User')
    
    def __repr__(self):
        return f'<HelpAnalytics {self.event_type}>'
    
    @property
    def metadata_dict(self):
        """Restituisce metadata come dizionario Python"""
        if self.event_metadata:
            try:
                return json.loads(self.event_metadata)
            except:
                return {}
        return {}
    
    def to_dict(self):
        """Converte l'analytics in dizionario per serializzazione JSON"""
        return {
            'id': self.id,
            'event_type': self.event_type,
            'content_id': self.content_id,
            'user_id': self.user_id,
            'session_id': self.session_id,
            'metadata': self.metadata_dict,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }