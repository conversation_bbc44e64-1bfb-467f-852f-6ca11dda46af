# CEO AI Assistant Models
from .base import db, datetime, json
from .user import User

class ResearchSession(db.Model):
    """AI Research sessions for strategic analysis"""
    __tablename__ = 'research_sessions'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>ey('users.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    category = db.Column(db.String(50), nullable=False)  # market_analysis, investment, talent, etc.
    status = db.Column(db.String(20), default='running')  # running, completed, failed
    
    # Research parameters
    research_config = db.Column(db.JSON)  # Query templates and parameters
    company_context = db.Column(db.JSON)  # Company profile data used
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    completed_at = db.Column(db.DateTime)
    
    # Relationships
    user = db.relationship('User', backref='research_sessions')
    queries = db.relationship('ResearchQuery', backref='session', lazy=True, cascade='all, delete-orphan')
    insights = db.relationship('StrategicInsight', backref='session', lazy=True, cascade='all, delete-orphan')
    
    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'category': self.category,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'query_count': len(self.queries),
            'insights_count': len(self.insights)
        }

class ResearchQuery(db.Model):
    """Individual research queries within a session"""
    __tablename__ = 'research_queries'
    
    id = db.Column(db.Integer, primary_key=True)
    session_id = db.Column(db.Integer, db.ForeignKey('research_sessions.id'), nullable=False)
    
    # Query details
    query_text = db.Column(db.Text, nullable=False)
    query_type = db.Column(db.String(50))  # market_trend, competitor_analysis, etc.
    
    # API response
    perplexity_response = db.Column(db.JSON)  # Full response from Perplexity
    processed_insights = db.Column(db.JSON)  # Extracted key insights
    
    # Status tracking
    status = db.Column(db.String(20), default='pending')  # pending, running, completed, failed
    error_message = db.Column(db.Text)
    
    # Timestamps
    started_at = db.Column(db.DateTime, default=datetime.utcnow)
    completed_at = db.Column(db.DateTime)
    
    # Performance metrics
    response_time_seconds = db.Column(db.Float)
    token_count = db.Column(db.Integer)
    cost_estimate = db.Column(db.Float)
    
    def to_dict(self):
        return {
            'id': self.id,
            'query_text': self.query_text,
            'query_type': self.query_type,
            'status': self.status,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'response_time': self.response_time_seconds,
            'has_insights': bool(self.processed_insights)
        }

class StrategicInsight(db.Model):
    """AI-generated strategic insights and recommendations"""
    __tablename__ = 'strategic_insights'
    
    id = db.Column(db.Integer, primary_key=True)
    session_id = db.Column(db.Integer, db.ForeignKey('research_sessions.id'), nullable=False)
    
    # Insight details
    insight_type = db.Column(db.String(50))  # opportunity, threat, trend, recommendation
    title = db.Column(db.String(200), nullable=False)
    content = db.Column(db.Text, nullable=False)
    summary = db.Column(db.Text)  # Executive summary
    
    # Scoring and priority
    confidence_score = db.Column(db.Float)  # 0-1, AI confidence level
    priority = db.Column(db.String(20), default='medium')  # low, medium, high, critical
    impact_score = db.Column(db.Integer)  # 1-10 potential business impact
    
    # Action items
    action_items = db.Column(db.JSON)  # Lista azioni raccomandate
    timeline_estimate = db.Column(db.String(50))  # "1-2 weeks", "Q2 2024", etc.
    
    # Source and metadata
    source_queries = db.Column(db.JSON)  # Query IDs that generated this insight
    tags = db.Column(db.JSON)  # Categorization tags
    
    # Status tracking
    status = db.Column(db.String(20), default='new')  # new, reviewed, implemented, dismissed
    reviewed_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    reviewed_at = db.Column(db.DateTime)
    notes = db.Column(db.Text)  # User notes
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, onupdate=datetime.utcnow)
    
    # Relationships
    reviewer = db.relationship('User', foreign_keys=[reviewed_by])
    
    def to_dict(self):
        return {
            'id': self.id,
            'insight_type': self.insight_type,
            'title': self.title,
            'content': self.content,
            'summary': self.summary,
            'confidence_score': self.confidence_score,
            'priority': self.priority,
            'impact_score': self.impact_score,
            'action_items': self.action_items,
            'timeline_estimate': self.timeline_estimate,
            'status': self.status,
            'tags': self.tags,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'reviewed_at': self.reviewed_at.isoformat() if self.reviewed_at else None
        }

class AIInteraction(db.Model):
    """AI Assistant conversation history"""
    __tablename__ = 'ai_interactions'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Conversation details
    query = db.Column(db.Text, nullable=False)
    response = db.Column(db.Text, nullable=False)
    conversation_id = db.Column(db.String(50))  # Group related queries
    
    # Context and categorization
    category = db.Column(db.String(50))  # strategic, operational, financial, etc.
    context_data = db.Column(db.JSON)  # Company data used for context
    
    # AI model details
    model_used = db.Column(db.String(50))  # gpt-4, claude-3, etc.
    confidence_score = db.Column(db.Float)  # AI confidence level
    
    # Performance metrics
    response_time_seconds = db.Column(db.Float)
    token_count_input = db.Column(db.Integer)
    token_count_output = db.Column(db.Integer)
    cost_estimate = db.Column(db.Float)
    
    # User feedback
    user_rating = db.Column(db.Integer)  # 1-5 stars
    user_feedback = db.Column(db.Text)
    
    # Timestamps
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    user = db.relationship('User', backref='ai_interactions')
    
    def to_dict(self):
        return {
            'id': self.id,
            'query': self.query,
            'response': self.response,
            'category': self.category,
            'confidence_score': self.confidence_score,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'user_rating': self.user_rating,
            'conversation_id': self.conversation_id
        }

class ScheduledTask(db.Model):
    """Automated analysis tasks for CEO dashboard"""
    __tablename__ = 'scheduled_tasks'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Task definition
    title = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    task_type = db.Column(db.String(50), nullable=False)  # business_review, risk_analysis, etc.
    
    # Scheduling
    frequency = db.Column(db.String(20), nullable=False)  # daily, weekly, monthly, quarterly
    cron_expression = db.Column(db.String(50))  # For complex scheduling
    last_run = db.Column(db.DateTime)
    next_run = db.Column(db.DateTime)
    
    # Configuration
    config_params = db.Column(db.JSON)  # Task-specific parameters
    data_sources = db.Column(db.JSON)  # Which modules to analyze
    
    # Status and results
    status = db.Column(db.String(20), default='active')  # active, paused, completed, failed
    last_result = db.Column(db.JSON)  # Result of last execution
    error_message = db.Column(db.Text)
    
    # Performance tracking
    average_duration_seconds = db.Column(db.Float)
    success_rate = db.Column(db.Float)  # Percentage of successful runs
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Relationships
    creator = db.relationship('User', backref='scheduled_tasks')
    
    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'task_type': self.task_type,
            'frequency': self.frequency,
            'status': self.status,
            'last_run': self.last_run.isoformat() if self.last_run else None,
            'next_run': self.next_run.isoformat() if self.next_run else None,
            'success_rate': self.success_rate
        }

class CompanyProfile(db.Model):
    """Company profile for AI context"""
    __tablename__ = 'company_profiles'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Company identity
    company_name = db.Column(db.String(100))
    mission = db.Column(db.Text)
    vision = db.Column(db.Text)
    values = db.Column(db.Text)
    
    # Business details
    industry = db.Column(db.String(100))
    business_model = db.Column(db.String(100))  # B2B, B2C, B2B2C, etc.
    company_size = db.Column(db.String(50))  # startup, small, medium, large
    target_market = db.Column(db.Text)
    
    # Strategic context
    competitive_advantages = db.Column(db.Text)
    key_challenges = db.Column(db.Text)
    strategic_objectives = db.Column(db.JSON)  # Short/long term goals
    
    # Market positioning
    market_segment = db.Column(db.String(100))
    geographic_focus = db.Column(db.String(100))  # Local, National, EU, Global
    
    # Financial context
    revenue_model = db.Column(db.String(100))
    current_stage = db.Column(db.String(50))  # seed, growth, mature, etc.
    
    # AI preferences
    analysis_focus_areas = db.Column(db.JSON)  # Which areas to prioritize in AI analysis
    reporting_preferences = db.Column(db.JSON)  # Frequency, format, etc.
    
    # Metadata
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, onupdate=datetime.utcnow)
    updated_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # Relationships
    updater = db.relationship('User', backref='company_profile_updates')
    
    def to_dict(self):
        return {
            'id': self.id,
            'company_name': self.company_name,
            'mission': self.mission,
            'vision': self.vision,
            'industry': self.industry,
            'company_size': self.company_size,
            'target_market': self.target_market,
            'competitive_advantages': self.competitive_advantages,
            'strategic_objectives': self.strategic_objectives,
            'market_segment': self.market_segment,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }