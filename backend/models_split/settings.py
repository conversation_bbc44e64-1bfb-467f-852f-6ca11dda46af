"""
Settings and System Configuration Models
Handles feature flags and system-wide configuration settings.
"""

from .base import *

class FeatureFlag(db.Model):
    """
    Feature flags for enabling/disabling system functionality.
    Allows admin to control feature rollout and system behavior.
    """
    __tablename__ = 'feature_flags'
    
    id = db.Column(db.Integer, primary_key=True)
    feature_key = db.Column(db.String(100), unique=True, nullable=False, index=True)
    display_name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    is_enabled = db.Column(db.Boolean, default=True, nullable=False)  # Default TRUE for zero-break deployment
    category = db.Column(db.String(50), default='core', nullable=False)  # Feature category for organization
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    updated_by = db.Column(db.Integer, db.<PERSON>ey('users.id'), nullable=True)
    
    # Relationships
    updater = db.relationship('User', foreign_keys=[updated_by], backref='updated_feature_flags')
    
    def __repr__(self):
        return f'<FeatureFlag {self.feature_key}: {"enabled" if self.is_enabled else "disabled"}>'
    
    def to_dict(self):
        """Convert to dictionary for API responses"""
        return {
            'id': self.id,
            'feature_key': self.feature_key,
            'display_name': self.display_name,
            'description': self.description,
            'is_enabled': self.is_enabled,
            'category': self.category,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'updated_by': self.updater.username if self.updater else None
        }
    
    @staticmethod
    def is_feature_enabled(feature_key):
        """
        Check if a feature is enabled.
        Returns True if feature not found (fail-safe for new features).
        """
        feature = FeatureFlag.query.filter_by(feature_key=feature_key).first()
        return feature.is_enabled if feature else True