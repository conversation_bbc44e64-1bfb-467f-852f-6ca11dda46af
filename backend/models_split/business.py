# Business Models
from .base import db, datetime

class Product(db.Model):
    __tablename__ = 'products'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    category = db.Column(db.String(64))
    price = db.Column(db.Float)
    status = db.Column(db.String(20), default='active')  # active, discontinued
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<Product {self.name}>'

class Service(db.Model):
    __tablename__ = 'services'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    category = db.Column(db.String(64))
    hourly_rate = db.Column(db.Float)
    status = db.Column(db.String(20), default='active')  # active, discontinued
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<Service {self.name}>'

# Performance Management models
class KPI(db.Model):
    __tablename__ = 'kpis'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    category = db.Column(db.String(64))  # performance, financial, operational, etc.
    
    # Configurazione metrica
    target_value = db.Column(db.Float)
    current_value = db.Column(db.Float, default=0.0)
    unit = db.Column(db.String(32))  # %, €, ore, numero
    frequency = db.Column(db.String(20))  # daily, weekly, monthly, quarterly
    progress = db.Column(db.Float, default=0.0)  # Progress percentage
    
    # Relazioni
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'))
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    project = db.relationship('Project')
    owner = db.relationship('User', backref='owned_kpis')

    def __repr__(self):
        return f'<KPI {self.name}>'

    @property
    def progress_percentage(self):
        """Calculate progress percentage based on current vs target value"""
        if self.target_value and self.target_value > 0 and self.current_value is not None:
            return (self.current_value / self.target_value) * 100
        return 0

class BusinessProcess(db.Model):
    __tablename__ = 'business_processes'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    owner = db.relationship('User', backref='owned_processes')
    steps = db.relationship('ProcessStep', backref='process', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<BusinessProcess {self.name}>'

class ProcessStep(db.Model):
    __tablename__ = 'process_steps'
    
    id = db.Column(db.Integer, primary_key=True)
    process_id = db.Column(db.Integer, db.ForeignKey('business_processes.id'), nullable=False)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    order = db.Column(db.Integer, default=0)
    estimated_duration = db.Column(db.Integer)  # in minutes

    def __repr__(self):
        return f'<ProcessStep {self.name}>'