# Invoicing and Financial Models
from .base import db, datetime, date, json

class PreInvoice(db.Model):
    """Pre-fattura interna prima dell'invio al sistema esterno di fatturazione elettronica"""
    __tablename__ = 'pre_invoices'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Riferimenti base
    client_id = db.Column(db.Integer, db.Foreign<PERSON>ey('clients.id'), nullable=False)
    contract_id = db.Column(db.Integer, db.ForeignKey('contracts.id'))
    
    # Identificativi
    pre_invoice_number = db.Column(db.String(50), unique=True, nullable=False)  # PRE-2024-0001
    
    # Periodo fatturazione
    billing_period_start = db.Column(db.Date, nullable=False)
    billing_period_end = db.Column(db.Date, nullable=False)
    
    # Date e stati
    generated_date = db.Column(db.Date, default=date.today)
    status = db.Column(db.String(20), default='draft')  # draft, ready, sent_external, invoiced
    
    # Calcoli fiscali italiani
    subtotal = db.Column(db.Numeric(10, 2), default=0)
    vat_rate = db.Column(db.Numeric(5, 2), default=22.0)  # IVA %
    vat_amount = db.Column(db.Numeric(10, 2), default=0)
    retention_rate = db.Column(db.Numeric(5, 2), default=0)  # Ritenuta %
    retention_amount = db.Column(db.Numeric(10, 2), default=0)
    total_amount = db.Column(db.Numeric(10, 2), default=0)
    
    # Integrazione esterna (FattureInCloud)
    external_invoice_id = db.Column(db.String(100))  # ID sul sistema esterno
    external_status = db.Column(db.String(50))       # Stato su sistema esterno
    external_pdf_url = db.Column(db.String(500))     # URL PDF ufficiale
    external_sent_date = db.Column(db.Date)          # Data invio sistema esterno
    
    # Metadati
    notes = db.Column(db.Text)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    client = db.relationship('Client', backref='pre_invoices')
    contract = db.relationship('Contract', backref='pre_invoices')
    lines = db.relationship('PreInvoiceLine', backref='pre_invoice', cascade='all, delete-orphan')
    creator = db.relationship('User', backref='created_pre_invoices')
    
    def __repr__(self):
        return f'<PreInvoice {self.pre_invoice_number}: €{self.total_amount}>'
    
    @property
    def display_status(self):
        """Stato leggibile della pre-fattura"""
        statuses = {
            'draft': 'Bozza',
            'ready': 'Pronta',
            'sent_external': 'Inviata a Sistema Esterno',
            'invoiced': 'Fatturata'
        }
        return statuses.get(self.status, self.status)
    
    @property
    def can_edit(self):
        """Verifica se la pre-fattura può essere modificata"""
        return self.status in ['draft', 'ready']
    
    @property
    def can_send_external(self):
        """Verifica se può essere inviata al sistema esterno"""
        return self.status == 'ready' and not self.external_invoice_id
    
    @db.validates('vat_rate')
    def validate_vat_rate(self, key, value):
        """Valida aliquota IVA italiana"""
        if value is not None and (value < 0 or value > 50):
            raise ValueError(f"VAT rate deve essere tra 0% e 50%, ricevuto: {value}%")
        return value

    @db.validates('retention_rate')
    def validate_retention_rate(self, key, value):
        """Valida ritenuta d'acconto italiana"""
        if value is not None and (value < 0 or value > 30):
            raise ValueError(f"Retention rate deve essere tra 0% e 30%, ricevuto: {value}%")
        return value

    @db.validates('subtotal', 'vat_amount', 'retention_amount', 'total_amount')
    def validate_amounts(self, key, value):
        """Valida che gli importi non siano negativi"""
        if value is not None and value < 0:
            raise ValueError(f"{key} non può essere negativo, ricevuto: {value}")
        return value

    @db.validates('billing_period_start', 'billing_period_end')
    def validate_billing_period(self, key, value):
        """Valida coerenza periodo fatturazione"""
        if key == 'billing_period_end' and value and self.billing_period_start:
            if value <= self.billing_period_start:
                raise ValueError("Billing period end deve essere successiva a start")
        elif key == 'billing_period_start' and value and self.billing_period_end:
            if value >= self.billing_period_end:
                raise ValueError("Billing period start deve essere precedente a end")
        return value

    def calculate_totals(self):
        """Ricalcola i totali basandosi sulle righe o subtotal manuale"""
        from decimal import Decimal, ROUND_HALF_UP
        
        # BUG FIX: Solo ricalcola da righe se ci sono righe, altrimenti usa subtotal esistente
        if self.lines:
            self.subtotal = sum(line.total_amount for line in self.lines)
        # Se non ci sono righe, mantieni il subtotal già impostato manualmente
        
        # Assicurati che subtotal sia un Decimal
        if self.subtotal is None:
            self.subtotal = Decimal('0.00')
        elif not isinstance(self.subtotal, Decimal):
            self.subtotal = Decimal(str(self.subtotal))
        
        # Calcoli con arrotondamento corretto a 2 decimali
        vat_amount = self.subtotal * (self.vat_rate / 100)
        self.vat_amount = vat_amount.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        
        retention_amount = self.subtotal * (self.retention_rate / 100)
        self.retention_amount = retention_amount.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        
        total_amount = self.subtotal + self.vat_amount - self.retention_amount
        self.total_amount = total_amount.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)


class PreInvoiceLine(db.Model):
    """Righe della pre-fattura con riferimenti a progetti e timesheet"""
    __tablename__ = 'pre_invoice_lines'
    
    id = db.Column(db.Integer, primary_key=True)
    pre_invoice_id = db.Column(db.Integer, db.ForeignKey('pre_invoices.id'), nullable=False)
    
    # Riferimenti
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'))
    
    # Descrizione e quantità
    description = db.Column(db.String(500), nullable=False)
    total_hours = db.Column(db.Numeric(8, 2), default=0)
    hourly_rate = db.Column(db.Numeric(8, 2), default=0)
    total_amount = db.Column(db.Numeric(10, 2), default=0)
    
    # Riferimenti timesheet entries fatturate
    timesheet_entries_ids = db.Column(db.JSON)  # Lista ID entries fatturate
    
    # Metadati
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    project = db.relationship('Project', backref='pre_invoice_lines')
    
    def __repr__(self):
        return f'<PreInvoiceLine {self.description}: {self.total_hours}h @ €{self.hourly_rate}>'
    
    @property
    def included_timesheet_ids(self):
        """Lista degli ID timesheet inclusi"""
        if self.timesheet_entries_ids:
            return json.loads(self.timesheet_entries_ids)
        return []
    
    @included_timesheet_ids.setter
    def included_timesheet_ids(self, timesheet_ids):
        """Imposta gli ID timesheet inclusi"""
        self.timesheet_entries_ids = json.dumps(timesheet_ids)

    @db.validates('total_hours')
    def validate_total_hours(self, key, value):
        """Valida ore fatturate"""
        if value is not None:
            if value < 0:
                raise ValueError("Ore devono essere positive")
            if value > 1000:  # >1000h in un periodo sembra eccessivo
                raise ValueError("Ore eccessive per periodo fatturazione")
        return value

    @db.validates('hourly_rate')
    def validate_hourly_rate(self, key, value):
        """Valida tariffa oraria"""
        if value is not None:
            if value < 0:
                raise ValueError("Hourly rate deve essere positivo")
            if value > 2000:  # >€2000/ora sembra eccessivo
                raise ValueError("Hourly rate sembra eccessivo")
        return value

    @db.validates('total_amount')
    def validate_total_amount(self, key, value):
        """Valida importo totale"""
        if value is not None and value < 0:
            raise ValueError("Total amount deve essere positivo")
        return value


class CompanyInvoicingSettings(db.Model):
    """Configurazione aziendale per fatturazione (dati fiscali e parametri)"""
    __tablename__ = 'company_invoicing_settings'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Dati aziendali per fatturazione
    company_name = db.Column(db.String(200), nullable=False)
    vat_number = db.Column(db.String(20))  # Partita IVA
    fiscal_code = db.Column(db.String(20))  # Codice Fiscale
    address = db.Column(db.Text)
    phone = db.Column(db.String(20))
    email = db.Column(db.String(100))
    pec = db.Column(db.String(100))  # PEC aziendale
    
    # Parametri fiscali default
    default_vat_rate = db.Column(db.Numeric(5, 2), default=22.0)
    default_retention_rate = db.Column(db.Numeric(5, 2), default=20.0)
    default_payment_terms = db.Column(db.Integer, default=30)  # giorni
    
    # Numerazione fatture
    invoice_prefix = db.Column(db.String(10), default='PRE')
    current_year = db.Column(db.Integer)
    last_number = db.Column(db.Integer, default=0)
    
    # Regime fiscale
    tax_regime = db.Column(db.String(50), default='ordinario')  # ordinario, semplificato, forfettario
    
    # Configurazione 
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<CompanyInvoicingSettings {self.company_name}>'
    
    def generate_next_number(self):
        """Genera il prossimo numero pre-fattura"""
        current_year = datetime.now().year
        if self.current_year != current_year:
            self.current_year = current_year
            self.last_number = 0
        
        self.last_number += 1
        return f"{self.invoice_prefix}-{current_year}-{self.last_number:04d}"


class Invoice(db.Model):
    """Fatture generate per periodo"""
    __tablename__ = 'invoices'

    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=False)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)

    # Periodo di fatturazione
    billing_period_start = db.Column(db.Date, nullable=False)
    billing_period_end = db.Column(db.Date, nullable=False)

    issue_date = db.Column(db.Date, nullable=False)
    due_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default='draft')  # draft, sent, paid, cancelled

    # Totali
    subtotal = db.Column(db.Float, default=0.0)
    tax_rate = db.Column(db.Float, default=0.22)  # 22% IVA default
    tax_amount = db.Column(db.Float, default=0.0)
    total_amount = db.Column(db.Float, default=0.0)

    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    client = db.relationship('Client', backref='invoices')
    lines = db.relationship('InvoiceLine', backref='invoice', lazy=True, cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Invoice {self.invoice_number} - {self.client_id}>'


class InvoiceLine(db.Model):
    """Righe fattura raggruppate per progetto/contratto"""
    __tablename__ = 'invoice_lines'

    id = db.Column(db.Integer, primary_key=True)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'), nullable=False)

    # Raggruppa per progetto/contratto
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=True)
    contract_id = db.Column(db.Integer, db.ForeignKey('contracts.id'), nullable=True)

    description = db.Column(db.Text, nullable=False)  # "Sviluppo CRM - Gennaio 2024"
    total_hours = db.Column(db.Float, nullable=False)  # Somma ore del periodo
    hourly_rate = db.Column(db.Float, nullable=False)
    total_amount = db.Column(db.Float, nullable=False)

    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    project = db.relationship('Project', backref='invoice_lines')
    contract = db.relationship('Contract', backref='invoice_lines')

    def __repr__(self):
        return f'<InvoiceLine {self.invoice_id} - {self.description}>'


class IntegrationSettings(db.Model):
    """Configurazione per integrazioni API esterne (FattureInCloud, etc.)"""
    __tablename__ = 'integration_settings'

    id = db.Column(db.Integer, primary_key=True)
    provider = db.Column(db.String(50), nullable=False)  # 'fattureincloud', 'aruba', etc.

    # Credenziali (criptate)
    api_key_encrypted = db.Column(db.Text)
    company_id = db.Column(db.String(100))

    # Configurazione specifica provider
    settings_json = db.Column(db.JSON)  # Config specifiche per ogni provider

    # Stato integrazione
    is_active = db.Column(db.Boolean, default=False)
    last_sync_date = db.Column(db.DateTime)
    last_error = db.Column(db.Text)

    # Metadati
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    creator = db.relationship('User', backref='created_integrations')

    def __repr__(self):
        return f'<IntegrationSettings {self.provider}: {self.is_active}>'

    @property
    def display_provider(self):
        """Nome leggibile del provider"""
        providers = {
            'fattureincloud': 'Fatture in Cloud',
            'aruba': 'Aruba Fatturazione Elettronica',
            'teamSystem': 'TeamSystem'
        }
        return providers.get(self.provider, self.provider)

    def to_dict(self):
        """Serializzazione per API"""
        return {
            'id': self.id,
            'provider': self.provider,
            'display_provider': self.display_provider,
            'company_id': self.company_id,
            'is_active': self.is_active,
            'last_sync_date': self.last_sync_date.isoformat() if self.last_sync_date else None,
            'last_error': self.last_error,
            'created_by': self.created_by,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }