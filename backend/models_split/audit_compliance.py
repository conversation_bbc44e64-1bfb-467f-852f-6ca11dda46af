# Compliance and Audit Models
from .base import db, datetime, json

class ComplianceAuditLog(db.Model):
    """
    Audit trail completo per compliance (ISO 27001, GDPR, SOC 2)
    Traccia tutte le attività utente senza modificare modelli esistenti
    """
    __tablename__ = 'compliance_audit_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)  # Nullable per azioni anonime
    session_id = db.Column(db.String(128))  # Flask session tracking
    action_type = db.Column(db.String(100), nullable=False)  # login, view, create, update, delete, logout
    resource_type = db.Column(db.String(100))  # project, task, user, invoice, client, etc.
    resource_id = db.Column(db.String(50))  # ID dell'oggetto modificato
    endpoint = db.Column(db.String(255))  # API endpoint chiamato
    method = db.Column(db.String(10))  # GET, POST, PUT, DELETE, PATCH
    ip_address = db.Column(db.String(45))  # IPv4/IPv6
    user_agent = db.Column(db.Text)  # Browser/client info
    request_data = db.Column(db.JSON)  # Payload request (sanitized per PII)
    response_status = db.Column(db.Integer)  # HTTP status code
    response_size = db.Column(db.Integer)  # Response size in bytes
    processing_time_ms = db.Column(db.Integer)  # Request processing time
    compliance_context = db.Column(db.JSON)  # GDPR, ISO context specifico
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, nullable=False, index=True)
    risk_level = db.Column(db.String(20), default='low')  # low, medium, high, critical
    
    # Per compliance tracking
    data_classification = db.Column(db.String(50))  # public, internal, confidential, restricted
    retention_policy = db.Column(db.String(50), default='standard')  # standard, extended, permanent
    is_sensitive = db.Column(db.Boolean, default=False)  # PII/sensitive data access
    
    # Relationships
    user = db.relationship('User', backref='audit_logs', foreign_keys=[user_id])
    
    def __repr__(self):
        return f'<ComplianceAuditLog {self.id}: {self.action_type} by {self.user_id}>'
    
    @property
    def is_high_risk(self):
        """Determina se l'azione è ad alto rischio per compliance"""
        high_risk_actions = ['delete', 'admin_action', 'bulk_update', 'export_data']
        return self.action_type in high_risk_actions or self.risk_level in ['high', 'critical']
    
    @property
    def compliance_tags(self):
        """Tags per compliance reporting"""
        tags = []
        if self.is_sensitive:
            tags.append('PII')
        if self.resource_type in ['user', 'client', 'contact']:
            tags.append('GDPR')
        if self.action_type in ['admin_action', 'config_change']:
            tags.append('SOC2')
        if self.risk_level in ['high', 'critical']:
            tags.append('HIGH_RISK')
        return tags

class ComplianceEvent(db.Model):
    """
    Eventi significativi per compliance e security monitoring
    Aggregazione e alerting su attività sospette o violazioni policy
    """
    __tablename__ = 'compliance_events'
    
    id = db.Column(db.Integer, primary_key=True)
    event_type = db.Column(db.String(100), nullable=False)  # data_access, admin_action, policy_violation, security_breach
    event_category = db.Column(db.String(50), default='operational')  # operational, security, privacy, administrative
    severity = db.Column(db.String(20), default='info')  # info, warning, critical, emergency
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    affected_users = db.Column(db.JSON)  # Lista user_id se evento impatta più utenti
    title = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text, nullable=False)
    
    # Compliance metadata
    compliance_framework = db.Column(db.String(50))  # GDPR, ISO27001, SOC2, HIPAA
    policy_reference = db.Column(db.String(100))  # Riferimento policy aziendale
    regulatory_impact = db.Column(db.Boolean, default=False)  # Impatto su compliance normativa
    
    # Event details
    source_ip = db.Column(db.String(45))
    source_system = db.Column(db.String(100))  # web, api, mobile, system
    affected_resources = db.Column(db.JSON)  # Lista risorse impattate
    risk_score = db.Column(db.Integer, default=0)  # 0-100 risk scoring
    
    # Metadata and context
    event_metadata = db.Column(db.JSON)  # Context specifico per compliance
    evidence_data = db.Column(db.JSON)  # Evidence per audit trail
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False, index=True)
    detected_at = db.Column(db.DateTime)  # Quando l'evento è stato rilevato
    occurred_at = db.Column(db.DateTime)  # Quando l'evento è realmente accaduto
    
    # Resolution tracking
    status = db.Column(db.String(20), default='open')  # open, investigating, resolved, false_positive
    resolved_at = db.Column(db.DateTime)
    resolved_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    resolution_notes = db.Column(db.Text)
    
    # Notifications
    notification_sent = db.Column(db.Boolean, default=False)
    escalated = db.Column(db.Boolean, default=False)
    escalated_at = db.Column(db.DateTime)
    
    # Relationships
    user = db.relationship('User', foreign_keys=[user_id], backref='compliance_events')
    resolver = db.relationship('User', foreign_keys=[resolved_by])
    
    def __repr__(self):
        return f'<ComplianceEvent {self.id}: {self.event_type} - {self.severity}>'
    
    @property
    def is_critical(self):
        """Evento critico che richiede attenzione immediata"""
        return self.severity in ['critical', 'emergency'] or self.risk_score >= 80
    
    @property
    def requires_notification(self):
        """Evento che richiede notifica agli admin/compliance officer"""
        return (self.is_critical or 
                self.regulatory_impact or 
                self.event_category == 'security')
    
    @property
    def age_hours(self):
        """Età dell'evento in ore"""
        if not self.created_at:
            return 0
        return (datetime.utcnow() - self.created_at).total_seconds() / 3600

class CompliancePolicy(db.Model):
    """
    Policy e regole compliance configurabili
    Definisce cosa tracciare e come classificare gli eventi
    """
    __tablename__ = 'compliance_policies'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    policy_type = db.Column(db.String(50), nullable=False)  # access, retention, privacy, security
    
    # Rule configuration
    rules_config = db.Column(db.JSON)  # Configurazione regole specifiche
    trigger_conditions = db.Column(db.JSON)  # Condizioni che triggano la policy
    actions = db.Column(db.JSON)  # Azioni da intraprendere
    
    # Compliance framework mapping
    framework = db.Column(db.String(50))  # GDPR, ISO27001, SOC2
    article_reference = db.Column(db.String(100))  # Art. 6 GDPR, ISO 27001:2013 A.9.1.1
    
    # Status and versioning
    is_active = db.Column(db.Boolean, default=True)
    version = db.Column(db.String(20), default='1.0')
    effective_date = db.Column(db.DateTime, default=datetime.utcnow)
    expiry_date = db.Column(db.DateTime)
    
    # Ownership
    owner_role = db.Column(db.String(50), default='admin')  # chi può modificare
    reviewed_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    last_review_date = db.Column(db.DateTime)
    next_review_date = db.Column(db.DateTime)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    reviewer = db.relationship('User', backref='reviewed_policies')
    
    def __repr__(self):
        return f'<CompliancePolicy {self.id}: {self.name}>'
    
    @property
    def needs_review(self):
        """Policy che necessita revisione"""
        if not self.next_review_date:
            return True
        return datetime.utcnow() > self.next_review_date

class ComplianceReport(db.Model):
    """
    Report compliance generati automaticamente
    Aggregazione dati audit per reporting periodico
    """
    __tablename__ = 'compliance_reports'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    report_type = db.Column(db.String(50), nullable=False)  # audit_trail, access_report, violation_summary
    period_type = db.Column(db.String(20))  # daily, weekly, monthly, quarterly, yearly
    period_start = db.Column(db.DateTime, nullable=False)
    period_end = db.Column(db.DateTime, nullable=False)
    
    # Report content
    summary_data = db.Column(db.JSON)  # Summary metrics
    detailed_data = db.Column(db.JSON)  # Detailed findings
    recommendations = db.Column(db.JSON)  # Action items
    
    # Compliance context
    framework = db.Column(db.String(50))  # GDPR, ISO27001, SOC2
    scope = db.Column(db.String(100))  # Scope del report
    risk_level = db.Column(db.String(20), default='low')
    
    # Status
    status = db.Column(db.String(20), default='draft')  # draft, generated, reviewed, published
    generated_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    generated_at = db.Column(db.DateTime, default=datetime.utcnow)
    reviewed_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    reviewed_at = db.Column(db.DateTime)
    
    # File storage
    file_path = db.Column(db.String(500))  # Path al file PDF/report generato
    file_hash = db.Column(db.String(64))  # Hash per integrità
    
    # Relationships
    generator = db.relationship('User', foreign_keys=[generated_by])
    reviewer = db.relationship('User', foreign_keys=[reviewed_by])
    
    def __repr__(self):
        return f'<ComplianceReport {self.id}: {self.name} ({self.period_start} - {self.period_end})>'

class Risk(db.Model):
    """
    Gestione rischi aziendali per governance e compliance
    Valutazione, monitoraggio e mitigazione dei rischi operativi
    """
    __tablename__ = 'risks'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text, nullable=False)
    
    # Risk categorization
    category = db.Column(db.String(50), nullable=False)  # security, operational, financial, compliance, strategic
    risk_type = db.Column(db.String(50))  # Technical subcategory
    
    # Risk assessment
    probability = db.Column(db.Integer, nullable=False)  # 1-5 likelihood scale
    impact = db.Column(db.Integer, nullable=False)  # 1-5 impact scale
    risk_level = db.Column(db.String(20), nullable=False)  # low, medium, high, critical
    risk_score = db.Column(db.Float)  # Calculated: probability * impact
    
    # Risk management
    status = db.Column(db.String(50), default='identified')  # identified, under_review, mitigated, accepted, transferred
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    responsible_department = db.Column(db.String(100))
    
    # Mitigation
    mitigation_strategy = db.Column(db.Text)
    mitigation_actions = db.Column(db.JSON)  # Lista azioni concrete
    mitigation_deadline = db.Column(db.DateTime)
    mitigation_cost = db.Column(db.Float)
    
    # Compliance & regulatory
    regulatory_requirements = db.Column(db.JSON)  # Requirement normativi collegati
    compliance_framework = db.Column(db.String(50))  # GDPR, ISO27001, SOC2
    
    # Timeline
    identified_date = db.Column(db.DateTime, default=datetime.utcnow)
    last_review_date = db.Column(db.DateTime)
    next_review_date = db.Column(db.DateTime)
    resolved_date = db.Column(db.DateTime)
    
    # Metadata
    tags = db.Column(db.JSON)  # Tags for categorization
    external_references = db.Column(db.JSON)  # Links to external docs/tools
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    owner = db.relationship('User', backref='owned_risks')
    
    def __repr__(self):
        return f'<Risk {self.id}: {self.title} ({self.risk_level})>'
    
    @property
    def risk_score_calculated(self):
        """Calcola risk score basato su probability e impact"""
        if self.probability and self.impact:
            return self.probability * self.impact
        return 0
    
    @property
    def is_overdue(self):
        """Verifica se il rischio è scaduto per review"""
        if not self.next_review_date:
            return False
        return datetime.utcnow() > self.next_review_date
    
    @property
    def requires_immediate_attention(self):
        """Rischi che richiedono attenzione immediata"""
        return (self.risk_level in ['high', 'critical'] and 
                self.status in ['identified', 'under_review'])
    
    def calculate_risk_level(self):
        """Calcola automaticamente il livello di rischio"""
        score = self.risk_score_calculated
        if score >= 20:
            return 'critical'
        elif score >= 15:
            return 'high'
        elif score >= 8:
            return 'medium'
        else:
            return 'low'
    
    def update_risk_score(self):
        """Aggiorna risk score e level"""
        self.risk_score = self.risk_score_calculated
        self.risk_level = self.calculate_risk_level()