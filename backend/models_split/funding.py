# Funding Models
from .base import db, datetime, date, json

class FundingOpportunity(db.Model):
    """Opportunità di bandi e finanziamenti pubblici"""
    __tablename__ = 'funding_opportunities'

    id = db.Column(db.Integer, primary_key=True)

    # Dati base del bando
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    source_entity = db.Column(db.String(128))  # Es: "Regione Lombardia", "EU", "MISE"
    program_name = db.Column(db.String(128))   # Es: "Digital Innovation Hub"
    call_identifier = db.Column(db.String(50), unique=True)  # ID univoco del bando

    # Aspetti finanziari
    total_budget = db.Column(db.Float)  # Budget totale del bando
    max_grant_amount = db.Column(db.Float)  # Massimo finanziamento per progetto
    min_grant_amount = db.Column(db.Float)  # Minimo finanziamento
    contribution_percentage = db.Column(db.Float, default=70.0)  # % finanziamento (es: 70%)

    # Timeline
    publication_date = db.Column(db.Date)
    application_deadline = db.Column(db.Date, nullable=False)
    evaluation_period_end = db.Column(db.Date)
    funding_period_start = db.Column(db.Date)
    project_duration_months = db.Column(db.Integer)

    # Requisiti e criteri
    eligibility_criteria = db.Column(db.Text)  # JSON con criteri di eleggibilità
    evaluation_criteria = db.Column(db.Text)   # JSON con criteri di valutazione
    required_documents = db.Column(db.Text)    # JSON con documenti richiesti
    target_sectors = db.Column(db.Text)        # JSON con settori target
    geographic_scope = db.Column(db.String(100))  # "nazionale", "regionale", "europeo"

    # Gestione stato
    status = db.Column(db.String(20), default='open')  # open, closed, evaluating, completed
    application_procedure = db.Column(db.Text)  # Descrizione procedura applicazione
    contact_info = db.Column(db.Text)  # JSON con contatti

    # URL e documenti
    official_url = db.Column(db.String(255))
    guidelines_url = db.Column(db.String(255))
    form_url = db.Column(db.String(255))

    # Metadati
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    internal_notes = db.Column(db.Text)  # Note interne per valutazione
    match_score = db.Column(db.Float, default=0.0)  # Score AI di compatibilità aziendale

    # Campi AI per ricerca con Perplexity
    ai_generated = db.Column(db.Boolean, default=False)  # Se generato da AI
    ai_search_query = db.Column(db.Text)  # Query utilizzata per la ricerca AI
    ai_match_score = db.Column(db.Float, default=0.0)  # Score di match AI
    ai_content = db.Column(db.Text)  # Contenuto originale AI per riferimento

    # Timestamp
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    applications = db.relationship('FundingApplication', backref='opportunity', lazy='dynamic', cascade='all, delete-orphan')
    creator = db.relationship('User', backref='created_funding_opportunities')

    def __repr__(self):
        return f'<FundingOpportunity {self.title}>'

    @property
    def days_to_deadline(self):
        """Calcola i giorni rimanenti alla scadenza"""
        if not self.application_deadline:
            return None
        from datetime import date
        return (self.application_deadline - date.today()).days

    @property
    def is_deadline_approaching(self):
        """Verifica se la scadenza è vicina (< 30 giorni)"""
        days = self.days_to_deadline
        return days is not None and 0 <= days <= 30

    @property
    def is_expired(self):
        """Verifica se il bando è scaduto"""
        days = self.days_to_deadline
        return days is not None and days < 0

    def to_dict(self):
        """Serializza l'oggetto per JSON"""
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'source_entity': self.source_entity,
            'program_name': self.program_name,
            'call_identifier': self.call_identifier,
            'total_budget': self.total_budget,
            'max_grant_amount': self.max_grant_amount,
            'min_grant_amount': self.min_grant_amount,
            'contribution_percentage': self.contribution_percentage,
            'publication_date': self.publication_date.isoformat() if self.publication_date else None,
            'application_deadline': self.application_deadline.isoformat() if self.application_deadline else None,
            'evaluation_period_end': self.evaluation_period_end.isoformat() if self.evaluation_period_end else None,
            'funding_period_start': self.funding_period_start.isoformat() if self.funding_period_start else None,
            'project_duration_months': self.project_duration_months,
            'eligibility_criteria': self.eligibility_criteria,
            'evaluation_criteria': self.evaluation_criteria,
            'required_documents': self.required_documents,
            'target_sectors': self.target_sectors,
            'geographic_scope': self.geographic_scope,
            'status': self.status,
            'application_procedure': self.application_procedure,
            'contact_info': self.contact_info,
            'official_url': self.official_url,
            'guidelines_url': self.guidelines_url,
            'form_url': self.form_url,
            'created_by': self.created_by,
            'is_active': self.is_active,
            'internal_notes': self.internal_notes,
            'match_score': self.match_score,
            'ai_generated': self.ai_generated,
            'ai_search_query': self.ai_search_query,
            'ai_match_score': self.ai_match_score,
            'ai_content': self.ai_content,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'creator': {
                'id': self.creator.id,
                'full_name': self.creator.full_name
            } if self.creator else None
        }


class FundingApplication(db.Model):
    """Candidature/Applicazioni ai bandi"""
    __tablename__ = 'funding_applications'

    id = db.Column(db.Integer, primary_key=True)
    opportunity_id = db.Column(db.Integer, db.ForeignKey('funding_opportunities.id'), nullable=False)

    # Dati progetto
    project_title = db.Column(db.String(200), nullable=False)
    project_description = db.Column(db.Text)
    requested_amount = db.Column(db.Float, nullable=False)
    approved_amount = db.Column(db.Float, nullable=True)  # Importo approvato (se approvato)

    # Status
    status = db.Column(db.String(20), default='draft')  # draft, submitted, under_review, approved, rejected

    # Metadati
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    creator = db.relationship('User', backref='funding_applications')

    def __repr__(self):
        return f'<FundingApplication {self.project_title}>'

    @property
    def total_project_cost(self):
        """Calcola il costo totale del progetto basato sulle spese"""
        return sum(expense.amount for expense in self.expenses)

    @property
    def funding_ratio(self):
        """Calcola il rapporto di finanziamento approvato vs richiesto"""
        if self.requested_amount and self.approved_amount:
            return (self.approved_amount / self.requested_amount) * 100
        return 0

    @property
    def is_pending_submission(self):
        """Verifica se è in attesa di invio"""
        return self.status == 'draft'

    @property
    def is_under_evaluation(self):
        """Verifica se è in fase di valutazione"""
        return self.status in ['submitted', 'under_review']

    def to_dict(self):
        """Serializza l'oggetto per JSON"""
        return {
            'id': self.id,
            'opportunity_id': self.opportunity_id,
            'project_title': self.project_title,
            'project_description': self.project_description,
            'requested_amount': self.requested_amount,
            'approved_amount': self.approved_amount,
            'status': self.status,
            'created_by': self.created_by,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'creator': {
                'id': self.creator.id,
                'full_name': self.creator.full_name
            } if self.creator else None,
            'opportunity': {
                'id': self.opportunity.id,
                'title': self.opportunity.title,
                'source_entity': self.opportunity.source_entity
            } if self.opportunity else None
        }


class FundingExpense(db.Model):
    """Spese per progetti finanziati da bandi - SEMPRE collegate a una candidatura"""
    __tablename__ = 'funding_expenses'

    id = db.Column(db.Integer, primary_key=True)
    application_id = db.Column(db.Integer, db.ForeignKey('funding_applications.id'), nullable=False)

    # Dati spesa
    description = db.Column(db.String(255), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    expense_date = db.Column(db.Date, nullable=False)
    category = db.Column(db.String(100))

    # Campi di rendicontazione
    is_eligible = db.Column(db.Boolean, default=True)
    notes = db.Column(db.Text)

    # Status approvazione
    approval_status = db.Column(db.String(20), default='pending')  # pending, approved, rejected

    # Metadati
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    application = db.relationship('FundingApplication', backref='expenses')

    def __repr__(self):
        return f'<FundingExpense {self.description}: €{self.amount}>'

    @property
    def is_personnel_cost(self):
        """Verifica se è un costo del personale"""
        personnel_categories = ['salary', 'personnel', 'staff', 'consultant']
        return any(cat in self.category.lower() for cat in personnel_categories) if self.category else False

    @property
    def is_approved(self):
        """Verifica se la spesa è stata approvata"""
        return self.approval_status == 'approved'

    @property
    def is_pending(self):
        """Verifica se la spesa è in attesa di approvazione"""
        return self.approval_status == 'pending'

    def to_dict(self):
        """Serializza l'oggetto per JSON"""
        return {
            'id': self.id,
            'application_id': self.application_id,
            'description': self.description,
            'amount': self.amount,
            'expense_date': self.expense_date.isoformat() if self.expense_date else None,
            'category': self.category,
            'approval_status': self.approval_status,
            'is_eligible': self.is_eligible,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'application': {
                'id': self.application.id,
                'project_title': self.application.project_title
            } if self.application else None
        }