# Certification management models
from .base import db, datetime, date, json

class CertificationStandard(db.Model):
    """Master catalog of available certification standards"""
    __tablename__ = 'certification_standards'
    
    # Primary identification
    code = db.Column(db.String(20), primary_key=True)  # ISO_9001_2015, ISO_27001_2022, etc.
    name = db.Column(db.String(100), nullable=False)
    version = db.Column(db.String(20))  # 2015, 2022, etc.
    
    # Classification
    category = db.Column(db.String(50), nullable=False)  # quality, security, environmental, privacy
    industry_sector = db.Column(db.String(50))  # manufacturing, tech, services, healthcare
    
    # Standard characteristics
    typical_validity_years = db.Column(db.Integer, default=3)
    renewal_notice_months = db.Column(db.Integer, default=6)
    audit_frequency_months = db.Column(db.Integer, default=12)
    
    # Cost information
    estimated_cost_min = db.Column(db.Float)
    estimated_cost_max = db.Column(db.Float)
    currency = db.Column(db.String(3), default='EUR')
    
    # Requirements and tasks (JSON from catalog)
    requirements = db.Column(db.JSON)  # List of requirement descriptions
    preparatory_tasks = db.Column(db.JSON)  # List of preparation tasks
    documentation_required = db.Column(db.JSON)  # Required documentation list
    
    # Metadata
    description = db.Column(db.Text)
    issuing_body = db.Column(db.String(100))
    website_url = db.Column(db.String(255))
    is_active = db.Column(db.Boolean, default=True)
    
    # Tenant configuration
    tenant_enabled = db.Column(db.Boolean, default=True)
    tenant_priority = db.Column(db.Integer, default=50)  # Display priority for tenant
    
    # Relationships
    company_certifications = db.relationship('CompanyCertification', backref='standard', lazy=True)
    
    def __repr__(self):
        return f'<CertificationStandard {self.code}>'
    
    @db.validates('estimated_cost_min', 'estimated_cost_max')
    def validate_cost_estimates(self, key, value):
        """Valida che i costi stimati siano positivi"""
        if value is not None and value < 0:
            raise ValueError(f"{key} deve essere positivo, ricevuto: {value}")
        return value
    
    @db.validates('typical_validity_years')
    def validate_validity_years(self, key, value):
        """Valida che gli anni di validità siano ragionevoli"""
        if value is not None and (value <= 0 or value > 10):
            raise ValueError(f"Typical validity years deve essere tra 1 e 10, ricevuto: {value}")
        return value
    
    @db.validates('renewal_notice_months')
    def validate_renewal_notice(self, key, value):
        """Valida che il preavviso rinnovo sia ragionevole"""
        if value is not None and (value < 1 or value > 24):
            raise ValueError(f"Renewal notice months deve essere tra 1 e 24, ricevuto: {value}")
        return value
    
    def validate_cost_range(self):
        """Valida che estimated_cost_min <= estimated_cost_max"""
        if (self.estimated_cost_min is not None and self.estimated_cost_max is not None and 
            self.estimated_cost_min > self.estimated_cost_max):
            raise ValueError(f"Estimated cost min ({self.estimated_cost_min}) non può essere maggiore di max ({self.estimated_cost_max})")
        return True

class CompanyCertification(db.Model):
    """Company-specific certification instances"""
    __tablename__ = 'company_certifications'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Standard reference
    standard_code = db.Column(db.String(20), db.ForeignKey('certification_standards.code'), nullable=False)
    
    # Certification details
    certificate_number = db.Column(db.String(100))
    certifying_body = db.Column(db.String(100), nullable=False)  # Bureau Veritas, DNV, etc.
    certifying_body_contact = db.Column(db.Text)
    
    # Timeline
    issue_date = db.Column(db.Date, nullable=False)
    expiry_date = db.Column(db.Date, nullable=False)
    next_audit_date = db.Column(db.Date)
    
    # Status management
    status = db.Column(db.String(20), default='active')  # active, expired, suspended, in_renewal, planning
    health_score = db.Column(db.Integer, default=100)  # 0-100 compliance health
    
    # Financial tracking
    initial_cost = db.Column(db.Float)
    annual_maintenance_cost = db.Column(db.Float)
    last_audit_cost = db.Column(db.Float)
    
    # Project association (optional)
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=True)
    
    # Responsibility
    responsible_person_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    backup_person_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # Documentation
    certificate_file_path = db.Column(db.String(255))
    documentation_folder_path = db.Column(db.String(255))
    
    # Audit trail
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Relationships
    project = db.relationship('Project', backref='certifications')
    responsible_person = db.relationship('User', foreign_keys=[responsible_person_id], backref='responsible_certifications')
    backup_person = db.relationship('User', foreign_keys=[backup_person_id], backref='backup_certifications')
    audit_events = db.relationship('CertificationAudit', backref='certification', lazy=True, cascade='all, delete-orphan')
    readiness_tasks = db.relationship('ReadinessTask', backref='certification', lazy=True, cascade='all, delete-orphan')
    documents = db.relationship('CertificationDocument', backref='certification', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<CompanyCertification {self.standard_code}>'
    
    @db.validates('health_score')
    def validate_health_score(self, key, value):
        """Valida che health_score sia in range 0-100"""
        if value is not None and (value < 0 or value > 100):
            raise ValueError(f"Health score deve essere tra 0 e 100, ricevuto: {value}")
        return value
    
    @db.validates('initial_cost', 'annual_maintenance_cost', 'last_audit_cost')
    def validate_costs(self, key, value):
        """Valida che i costi siano non-negativi"""
        if value is not None and value < 0:
            raise ValueError(f"{key} non può essere negativo, ricevuto: {value}")
        return value
    
    def validate_certification_dates(self):
        """Valida consistenza delle date della certificazione"""
        errors = []
        
        # Valida che issue_date sia prima di expiry_date
        if self.issue_date and self.expiry_date and self.issue_date >= self.expiry_date:
            errors.append("Issue date deve essere precedente a expiry date")
        
        # Valida che next_audit_date sia coerente
        if self.issue_date and self.next_audit_date and self.next_audit_date < self.issue_date:
            errors.append("Next audit date non può essere precedente a issue date")
        
        # Valida status vs date
        if self.status == 'active' and self.expiry_date and self.expiry_date < date.today():
            errors.append("Certificazione 'active' non può essere scaduta")
        
        # Valida che certificazione scaduta abbia status appropriato
        if self.expiry_date and self.expiry_date < date.today() and self.status == 'active':
            errors.append("Certificazione scaduta dovrebbe avere status 'expired'")
            
        if errors:
            raise ValueError("Errori di validazione certificazione: " + "; ".join(errors))
        
        return True
    
    @property
    def days_to_expiry(self):
        """Calculate days until certification expires"""
        return (self.expiry_date - date.today()).days if self.expiry_date else None
    
    @property
    def is_expiring_soon(self):
        """Check if certification is expiring within renewal notice period"""
        if not self.expiry_date:
            return False
        renewal_notice_days = (self.standard.renewal_notice_months * 30) if self.standard else 180
        return self.days_to_expiry <= renewal_notice_days
    
    @property
    def readiness_score(self):
        """Calculate readiness score based on tasks completion"""
        tasks = self.readiness_tasks
        if not tasks:
            return 0
        
        total_weight = 0
        weighted_completion = 0
        
        for task in tasks:
            weight = self._get_task_weight(task.priority, task.task_category)
            total_weight += weight
            weighted_completion += (task.completion_percentage * weight / 100)
        
        return round(weighted_completion / total_weight * 100, 1) if total_weight > 0 else 0
    
    def _get_task_weight(self, priority, category):
        """Get weight for task based on priority and category"""
        priority_weights = {'critical': 4, 'high': 3, 'medium': 2, 'low': 1}
        category_weights = {'audit': 1.5, 'documentation': 1.3, 'process': 1.2, 'training': 1.0}
        
        # Safe defaults for None values
        priority = priority or 'medium'
        category = category or 'process'
        
        return priority_weights.get(priority, 2) * category_weights.get(category, 1.0)

class CertificationAudit(db.Model):
    """Audit events and results"""
    __tablename__ = 'certification_audits'
    
    id = db.Column(db.Integer, primary_key=True)
    certification_id = db.Column(db.Integer, db.ForeignKey('company_certifications.id'), nullable=False)
    
    # Audit details
    audit_type = db.Column(db.String(20), nullable=False)  # initial, surveillance, renewal, special
    planned_date = db.Column(db.Date, nullable=False)
    actual_date = db.Column(db.Date)
    duration_days = db.Column(db.Integer, default=1)
    
    # Auditor information
    lead_auditor = db.Column(db.String(100))
    audit_team = db.Column(db.JSON)  # List of auditor names
    auditor_contact = db.Column(db.Text)
    
    # Results
    status = db.Column(db.String(20), default='scheduled')  # scheduled, in_progress, completed, cancelled
    result = db.Column(db.String(20))  # passed, failed, conditional, pending
    overall_score = db.Column(db.Integer)  # 0-100 audit score
    
    # Findings
    major_findings = db.Column(db.Integer, default=0)
    minor_findings = db.Column(db.Integer, default=0)
    observations = db.Column(db.Integer, default=0)
    findings_summary = db.Column(db.Text)
    
    # Documentation
    audit_report_path = db.Column(db.String(255))
    corrective_actions_path = db.Column(db.String(255))
    evidence_folder_path = db.Column(db.String(255))
    
    # Financial
    audit_cost = db.Column(db.Float)
    travel_expenses = db.Column(db.Float)
    
    # Follow-up
    corrective_actions_due = db.Column(db.Date)
    follow_up_audit_date = db.Column(db.Date)
    next_audit_type = db.Column(db.String(20))
    
    # Metadata
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    def __repr__(self):
        return f'<CertificationAudit {self.audit_type} for {self.certification_id}>'

class ReadinessTask(db.Model):
    """Tasks required for certification readiness"""
    __tablename__ = 'readiness_tasks'
    
    id = db.Column(db.Integer, primary_key=True)
    certification_id = db.Column(db.Integer, db.ForeignKey('company_certifications.id'), nullable=False)
    
    # Task details
    task_name = db.Column(db.String(200), nullable=False)
    task_description = db.Column(db.Text)
    task_category = db.Column(db.String(50))  # documentation, training, process, audit
    requirement_reference = db.Column(db.String(100))  # ISO clause reference
    
    # Progress tracking
    completion_percentage = db.Column(db.Integer, default=0)  # 0-100
    status = db.Column(db.String(20), default='not_started')  # not_started, in_progress, completed, blocked
    
    # Responsibility
    assigned_to_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    assigned_department = db.Column(db.String(50))
    
    # Timeline
    due_date = db.Column(db.Date)
    started_date = db.Column(db.Date)
    completed_date = db.Column(db.Date)
    
    # Priority and effort
    priority = db.Column(db.String(20), default='medium')  # low, medium, high, critical
    estimated_hours = db.Column(db.Float)
    actual_hours = db.Column(db.Float)
    
    # Documentation
    evidence_files = db.Column(db.JSON)  # List of evidence file paths
    notes = db.Column(db.Text)
    
    # Dependencies
    depends_on_task_ids = db.Column(db.JSON)  # List of task IDs this depends on
    blocks_task_ids = db.Column(db.JSON)  # List of task IDs this blocks
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Relationships
    assigned_to = db.relationship('User', foreign_keys=[assigned_to_id], backref='assigned_readiness_tasks')
    
    def __repr__(self):
        return f'<ReadinessTask {self.task_name}>'

class CertificationDocument(db.Model):
    """Document management for certifications"""
    __tablename__ = 'certification_documents'
    
    id = db.Column(db.Integer, primary_key=True)
    certification_id = db.Column(db.Integer, db.ForeignKey('company_certifications.id'), nullable=False)
    
    # Document details
    document_name = db.Column(db.String(200), nullable=False)
    document_type = db.Column(db.String(50), nullable=False)  # certificate, manual, procedure, record
    document_category = db.Column(db.String(50))  # quality_manual, procedures, work_instructions, records
    
    # File information
    file_path = db.Column(db.String(500), nullable=False)
    file_size = db.Column(db.Integer)
    file_type = db.Column(db.String(10))  # pdf, docx, xlsx, etc.
    
    # Version control
    version = db.Column(db.String(20), default='1.0')
    is_current_version = db.Column(db.Boolean, default=True)
    previous_version_id = db.Column(db.Integer, db.ForeignKey('certification_documents.id'))
    
    # Metadata
    description = db.Column(db.Text)
    tags = db.Column(db.JSON)  # List of tags for searching
    
    # Lifecycle
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    uploaded_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Access control
    access_level = db.Column(db.String(20), default='internal')  # public, internal, restricted, confidential
    
    # Relationships
    uploaded_by_user = db.relationship('User', backref='uploaded_certification_documents')
    
    def __repr__(self):
        return f'<CertificationDocument {self.document_name}>'