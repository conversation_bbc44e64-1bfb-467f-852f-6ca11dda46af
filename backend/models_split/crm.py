# CRM (Customer Relationship Management) models
from .base import db, datetime
from decimal import Decimal

class Client(db.Model):
    __tablename__ = 'clients'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    industry = db.Column(db.String(64))
    description = db.Column(db.Text)
    website = db.Column(db.String(128))
    address = db.Column(db.String(255))
    email = db.Column(db.String(120))
    phone = db.Column(db.String(20))
    
    # Campi per fatturazione italiana
    vat_number = db.Column(db.String(20))  # Partita IVA
    fiscal_code = db.Column(db.String(20))  # Codice Fiscale
    
    status = db.Column(db.String(20), default='client')  # lead, prospect, client, inactive
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships defined in Project model
    contacts = db.relationship('Contact', backref='client', lazy='dynamic', cascade='all, delete-orphan')
    proposals = db.relationship('Proposal', backref='client', lazy='dynamic')

    def __repr__(self):
        return f'<Client {self.name}>'


class Contact(db.Model):
    __tablename__ = 'contacts'
    
    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=False)
    first_name = db.Column(db.String(64), nullable=False)
    last_name = db.Column(db.String(64), nullable=False)
    position = db.Column(db.String(64))
    email = db.Column(db.String(120))
    phone = db.Column(db.String(20))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<Contact {self.first_name} {self.last_name}>'

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"


class Proposal(db.Model):
    __tablename__ = 'proposals'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(128), nullable=False)
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=False)
    description = db.Column(db.Text)
    value = db.Column(db.Numeric(12, 2))
    status = db.Column(db.String(20), default='draft')  # draft, sent, negotiating, accepted, rejected
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    sent_date = db.Column(db.Date)
    expiry_date = db.Column(db.Date)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    creator = db.relationship('User', backref='created_proposals')

    def __repr__(self):
        return f'<Proposal {self.title}>'


class Contract(db.Model):
    """Contratti con clienti per fatturazione"""
    __tablename__ = 'contracts'

    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=False)
    contract_number = db.Column(db.String(50), unique=True, nullable=False)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    contract_type = db.Column(db.String(20), default='hourly')  # hourly, fixed, retainer
    hourly_rate = db.Column(db.Numeric(8, 2), nullable=True)  # Tariffa base oraria
    budget_hours = db.Column(db.Numeric(8, 2), nullable=True)
    budget_amount = db.Column(db.Numeric(12, 2), nullable=True)
    retainer_amount = db.Column(db.Numeric(12, 2), nullable=True)  # Importo retainer per contratti retainer
    retainer_frequency = db.Column(db.String(20), nullable=True)  # Frequenza retainer: monthly, quarterly, yearly
    milestone_amount = db.Column(db.Numeric(12, 2), nullable=True)  # Importo per milestone
    milestone_count = db.Column(db.Integer, nullable=True)  # Numero di milestone
    subscription_amount = db.Column(db.Numeric(12, 2), nullable=True)  # Importo abbonamento
    subscription_frequency = db.Column(db.String(20), nullable=True)  # Frequenza abbonamento: monthly, yearly
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=True)
    status = db.Column(db.String(20), default='active')  # active, completed, cancelled
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    client = db.relationship('Client', backref='contracts')

    @db.validates('hourly_rate', 'budget_amount', 'retainer_amount', 'milestone_amount', 'subscription_amount')
    def validate_positive_amounts(self, key, value):
        """Valida che gli importi siano positivi"""
        if value is not None and value <= 0:
            raise ValueError(f"{key} deve essere positivo, ricevuto: {value}")
        return value

    @db.validates('budget_hours')
    def validate_budget_hours(self, key, value):
        """Valida ore budget"""
        if value is not None:
            if value <= 0:
                raise ValueError("Budget hours deve essere positivo")
            if value > 10000:  # >10000h sembra eccessivo per un contratto
                raise ValueError("Budget hours sembra eccessivo")
        return value

    @db.validates('start_date', 'end_date')
    def validate_contract_dates(self, key, value):
        """Valida coerenza date contratto"""
        if key == 'end_date' and value and self.start_date:
            if value <= self.start_date:
                raise ValueError("End date deve essere successiva a start date")
        elif key == 'start_date' and value and self.end_date:
            if value >= self.end_date:
                raise ValueError("Start date deve essere precedente a end date")
        return value

    @db.validates('milestone_count')
    def validate_milestone_count(self, key, value):
        """Valida numero milestone"""
        if value is not None:
            if value <= 0:
                raise ValueError("Milestone count deve essere positivo")
            if value > 100:  # >100 milestone sembra eccessivo
                raise ValueError("Milestone count sembra eccessivo")
        return value

    def __repr__(self):
        return f'<Contract {self.contract_number} - {self.client_id}>'