# Association tables for many-to-many relationships
from .base import db

# Project team association table
# DEPRECATED: Sostituito da project_resources per gestione avanzata allocazioni
# TODO: Rimuovere dopo migrazione completata
# project_team = db.<PERSON>('project_team',
#     db.<PERSON>umn('project_id', db.<PERSON>, db.<PERSON>('projects.id'), primary_key=True),
#     db.<PERSON>umn('user_id', db.<PERSON><PERSON>, db.<PERSON>('users.id'), primary_key=True),
#     db.<PERSON>umn('role', db.String(50)),
#     extend_existing=True
# )