# Timesheet management models
from .base import db, datetime

class TimesheetEntry(db.Model):
    """Singola entry di timesheet"""
    __tablename__ = 'timesheet_entries'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.<PERSON><PERSON>, db.<PERSON>ey('users.id'), nullable=False)
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False)
    task_id = db.Column(db.Integer, db.<PERSON>ey('tasks.id'))
    date = db.Column(db.Date, nullable=False)
    hours = db.Column(db.Float, nullable=False)
    description = db.Column(db.Text)
    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Nuovi campi per Task 3.1 + 4
    monthly_timesheet_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>ey('monthly_timesheets.id'), nullable=True)
    billable = db.Column(db.<PERSON>, default=False)
    billing_rate = db.Column(db.Float, nullable=True)  # Tariffa oraria per questa entry
    contract_id = db.Column(db.Integer, db.ForeignKey('contracts.id'), nullable=True)
    invoice_line_id = db.Column(db.Integer, db.ForeignKey('invoice_lines.id'), nullable=True)
    billing_status = db.Column(db.String(20), default='unbilled')  # unbilled, billed, non-billable

    # Relationships
    task = db.relationship('Task', backref='timesheet_entries')
    monthly_timesheet = db.relationship('MonthlyTimesheet', backref='entries')
    contract = db.relationship('Contract', backref='timesheet_entries')
    invoice_line = db.relationship('InvoiceLine', backref='timesheet_entries')

    @db.validates('hours')
    def validate_hours(self, key, value):
        """Valida ore timesheet"""
        if value is not None:
            if value <= 0:
                raise ValueError("Ore devono essere positive")
            if value > 24:
                raise ValueError("Ore eccessive per singolo giorno")
            if value < 0.25:  # Minimo 15 minuti
                raise ValueError("Incrementi ore inappropriati")
        return value

    @db.validates('billing_rate')
    def validate_billing_rate(self, key, value):
        """Valida tariffa billing"""
        if value is not None:
            if value <= 0:
                raise ValueError("Billing rate deve essere positivo")
            if value > 1000:  # >€1000/ora sembra eccessivo
                raise ValueError("Billing rate sembra eccessivo")
        return value

    @db.validates('date')
    def validate_date(self, key, value):
        """Valida data entry"""
        if value is not None:
            from datetime import date, timedelta
            today = date.today()
            
            # Non troppo nel futuro
            if value > today + timedelta(days=7):
                raise ValueError("Non è possibile registrare ore per date future oltre 7 giorni")
            
            # Non troppo nel passato - aumentato a 365 giorni per progetti di lunga durata
            if value < today - timedelta(days=365):
                raise ValueError("Entry troppo vecchia (>365 giorni)")
        return value

    def __repr__(self):
        return f'<TimesheetEntry {self.user_id} - {self.date}>'


class MonthlyTimesheet(db.Model):
    """Contenitore per approvazione mensile timesheet"""
    __tablename__ = 'monthly_timesheets'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    year = db.Column(db.Integer, nullable=False)
    month = db.Column(db.Integer, nullable=False)
    status = db.Column(db.String(20), default='draft')  # draft, submitted, approved, rejected
    submission_date = db.Column(db.DateTime, nullable=True)
    approval_date = db.Column(db.DateTime, nullable=True)
    approved_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    rejection_reason = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = db.relationship('User', foreign_keys=[user_id], backref='monthly_timesheets')
    approver = db.relationship('User', foreign_keys=[approved_by], backref='approved_monthly_timesheets')

    # Unique constraint per user/year/month
    __table_args__ = (db.UniqueConstraint('user_id', 'year', 'month', name='unique_user_month'),)

    @db.validates('month')
    def validate_month(self, key, value):
        """Valida mese"""
        if value is not None and (value < 1 or value > 12):
            raise ValueError("Mese deve essere tra 1 e 12")
        return value

    @db.validates('year')
    def validate_year(self, key, value):
        """Valida anno"""
        if value is not None:
            from datetime import datetime
            current_year = datetime.now().year
            if value > current_year + 1:
                raise ValueError("Anno troppo nel futuro")
            if value < current_year - 5:
                raise ValueError("Anno troppo nel passato")
        return value

    def __repr__(self):
        return f'<MonthlyTimesheet {self.user_id} - {self.year}/{self.month}>'

    @property
    def total_hours(self):
        """Calcola il totale ore del mese"""
        return sum(entry.hours for entry in self.entries)

    @property
    def billable_hours(self):
        """Calcola il totale ore fatturabili del mese"""
        return sum(entry.hours for entry in self.entries if entry.billable)

    @property
    def projects_summary(self):
        """Raggruppa le ore per progetto"""
        from collections import defaultdict
        summary = defaultdict(float)
        for entry in self.entries:
            if entry.project:
                summary[entry.project.name] += entry.hours
        return dict(summary)

    def to_dict(self):
        """Serializza l'oggetto per JSON"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'user': {
                'id': self.user.id,
                'full_name': self.user.full_name,
                'username': self.user.username
            } if self.user else None,
            'year': self.year,
            'month': self.month,
            'status': self.status,
            'total_hours': self.total_hours,
            'billable_hours': self.billable_hours,
            'submission_date': self.submission_date.isoformat() if self.submission_date else None,
            'approval_date': self.approval_date.isoformat() if self.approval_date else None,
            'approved_by': self.approved_by,
            'approver': {
                'id': self.approver.id,
                'full_name': self.approver.full_name
            } if self.approver else None,
            'rejection_reason': self.rejection_reason,
            'projects_summary': self.projects_summary,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }