# Performance Management Models
from .base import db, datetime, date

class PerformanceReview(db.Model):
    """Valutazioni annuali delle performance"""
    __tablename__ = 'performance_reviews'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.<PERSON>ger, db.<PERSON>ey('users.id'), nullable=False)
    reviewer_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    template_id = db.Column(db.Integer, db.<PERSON>ey('performance_templates.id'), nullable=True)
    
    # Periodo di valutazione
    review_period_start = db.Column(db.Date, nullable=False)
    review_period_end = db.Column(db.Date, nullable=False)
    review_year = db.Column(db.Integer, nullable=False)
    
    # Stati e date
    status = db.Column(db.String(20), default='draft')  # draft, in_progress, completed, approved
    due_date = db.Column(db.Date, nullable=True)  # Data entro cui completare la review
    submitted_date = db.Column(db.DateTime, nullable=True)
    completed_date = db.Column(db.DateTime, nullable=True)
    approved_date = db.Column(db.DateTime, nullable=True)
    approved_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    
    # Valutazioni
    overall_rating = db.Column(db.Float, nullable=True)  # Voto complessivo (1-5)
    technical_skills_rating = db.Column(db.Float, nullable=True)
    soft_skills_rating = db.Column(db.Float, nullable=True)
    goals_achievement_rating = db.Column(db.Float, nullable=True)
    communication_rating = db.Column(db.Float, nullable=True)
    teamwork_rating = db.Column(db.Float, nullable=True)
    leadership_rating = db.Column(db.Float, nullable=True)
    initiative_rating = db.Column(db.Float, nullable=True)
    
    # Commenti
    achievements = db.Column(db.Text)  # Risultati raggiunti
    areas_improvement = db.Column(db.Text)  # Aree di miglioramento
    strengths = db.Column(db.Text)  # Punti di forza
    development_goals = db.Column(db.Text)  # Obiettivi di sviluppo
    
    # Commenti del reviewer
    reviewer_comments = db.Column(db.Text)
    employee_comments = db.Column(db.Text)  # Commenti del dipendente
    hr_comments = db.Column(db.Text)  # Commenti HR
    manager_comments = db.Column(db.Text)  # Commenti del manager
    comments = db.Column(db.Text)  # Commenti generali (compatibilità API)
    areas_for_improvement = db.Column(db.Text)  # Campo per compatibilità database
    
    # Raccomandazioni
    promotion_recommendation = db.Column(db.Boolean, default=False)
    salary_increase_recommendation = db.Column(db.Float, nullable=True)
    bonus_recommendation = db.Column(db.Float, nullable=True)
    training_recommendations = db.Column(db.Text)
    
    # Firme digitali
    employee_signed_at = db.Column(db.DateTime, nullable=True)
    manager_signed_at = db.Column(db.DateTime, nullable=True)
    hr_signed_at = db.Column(db.DateTime, nullable=True)
    
    # Metadati
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    
    # Relationships
    employee = db.relationship('User', foreign_keys=[employee_id], backref='employee_reviews')
    reviewer = db.relationship('User', foreign_keys=[reviewer_id], backref='reviewer_reviews')
    approver = db.relationship('User', foreign_keys=[approved_by], backref='approver_reviews')
    template = db.relationship('PerformanceTemplate', backref='reviews')
    feedback_received = db.relationship('PerformanceFeedback', 
                                       foreign_keys='PerformanceFeedback.review_id', 
                                       backref='review')
    goals = db.relationship('PerformanceGoal', backref='review')
    
    def __repr__(self):
        return f'<PerformanceReview {self.employee.full_name} {self.review_year}>'
    
    @db.validates('overall_rating', 'technical_skills_rating', 'soft_skills_rating', 
                  'goals_achievement_rating', 'communication_rating', 'teamwork_rating',
                  'leadership_rating', 'initiative_rating')
    def validate_rating_scale(self, key, value):
        """Valida che tutti i rating siano in scala 1-5"""
        if value is not None and (value < 1.0 or value > 5.0):
            raise ValueError(f"{key} deve essere tra 1.0 e 5.0, ricevuto: {value}")
        return value
    
    @db.validates('salary_increase_recommendation')
    def validate_salary_increase(self, key, value):
        """Valida che l'aumento salariale sia ragionevole"""
        if value is not None and (value < -10.0 or value > 50.0):
            raise ValueError(f"Salary increase deve essere tra -10% e 50%, ricevuto: {value}%")
        return value
    
    def validate_review_period(self):
        """Valida consistenza del periodo di review"""
        errors = []
        
        # Valida che review_period_start sia prima di review_period_end
        if self.review_period_start and self.review_period_end:
            if self.review_period_start >= self.review_period_end:
                errors.append("Review period start deve essere precedente a review period end")
        
        # Valida che review_year sia coerente con il periodo
        if self.review_year and self.review_period_start:
            if self.review_period_start.year != self.review_year:
                errors.append(f"Review year ({self.review_year}) non corrisponde all'anno di inizio periodo ({self.review_period_start.year})")
        
        # Valida logica promozione vs rating
        if (self.promotion_recommendation and self.overall_rating is not None and 
            self.overall_rating < 3.5):
            errors.append(f"Promozione raccomandata con rating troppo basso: {self.overall_rating}/5.0")
        
        # Valida ordine firme digitali
        if self.employee_signed_at and self.manager_signed_at:
            if self.manager_signed_at < self.employee_signed_at:
                errors.append("Manager non può firmare prima del dipendente")
        
        if errors:
            raise ValueError("Errori di validazione review: " + "; ".join(errors))
        
        return True
    
    @property
    def display_status(self):
        """Nome leggibile dello stato"""
        status_map = {
            'draft': 'Bozza',
            'in_progress': 'In corso',
            'completed': 'Completata',
            'approved': 'Approvata'
        }
        return status_map.get(self.status, self.status)
    
    @property
    def linked_goals(self):
        """Ottieni gli obiettivi collegati a questa review"""
        from sqlalchemy.orm import joinedload
        return PerformanceGoal.query.filter(
            db.or_(
                PerformanceGoal.review_id == self.id,
                db.and_(
                    PerformanceGoal.employee_id == self.employee_id,
                    PerformanceGoal.year == self.review_year,
                    PerformanceGoal.review_id.is_(None)
                )
            )
        ).all()
    
    @property
    def goal_completion_rate(self):
        """Calcola il tasso di completamento degli obiettivi"""
        goals = self.linked_goals
        if not goals:
            return None
        completed = sum(1 for g in goals if g.status == 'completed')
        return round((completed / len(goals)) * 100, 1)
    
    @property
    def goals_summary(self):
        """Riassunto degli obiettivi collegati"""
        goals = self.linked_goals
        if not goals:
            return {
                'total': 0,
                'completed': 0,
                'in_progress': 0,
                'active': 0,
                'completion_rate': 0
            }
        
        summary = {
            'total': len(goals),
            'completed': 0,
            'in_progress': 0,
            'active': 0
        }
        
        for goal in goals:
            if goal.status == 'completed':
                summary['completed'] += 1
            elif goal.status == 'in_progress':
                summary['in_progress'] += 1
            elif goal.status == 'active':
                summary['active'] += 1
        
        summary['completion_rate'] = round((summary['completed'] / summary['total']) * 100, 1)
        return summary


class PerformanceFeedback(db.Model):
    """Feedback bidirezionale tra dipendenti"""
    __tablename__ = 'performance_feedbacks'

    id = db.Column(db.Integer, primary_key=True)
    review_id = db.Column(db.Integer, db.ForeignKey('performance_reviews.id'), nullable=True)
    from_user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    to_user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    feedback_type = db.Column(db.String(30), nullable=False)
    content = db.Column(db.Text, nullable=False)
    rating = db.Column(db.Float, nullable=True)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    from_user = db.relationship('User', foreign_keys=[from_user_id], backref='given_feedback')
    to_user = db.relationship('User', foreign_keys=[to_user_id], backref='received_feedback')
    
    def __repr__(self):
        return f'<PerformanceFeedback {self.feedback_type}>'
    
    @db.validates('rating')
    def validate_rating(self, key, value):
        """Valida che rating sia in scala 1-5"""
        if value is not None and (value < 1.0 or value > 5.0):
            raise ValueError(f"Rating deve essere tra 1.0 e 5.0, ricevuto: {value}")
        return value
    
    @db.validates('content')
    def validate_content(self, key, value):
        """Valida che content non sia vuoto"""
        if value is not None and len(value.strip()) == 0:
            raise ValueError("Content non può essere vuoto")
        return value
    
    def validate_feedback_logic(self):
        """Valida logica del feedback"""
        errors = []
        
        # Valida che non sia auto-feedback per tipi inappropriati
        if (self.from_user_id == self.to_user_id and 
            self.feedback_type not in ['self', 'auto-valutazione']):
            errors.append(f"Auto-feedback non permesso per tipo '{self.feedback_type}'")
        
        # Valida che feedback_type sia in lista valida
        valid_types = ['peer', 'upward', 'downward', 'self', '360', 'followup', 
                      'chiarimento', 'criticita', 'riconoscimento', 'suggestion', 'coaching']
        if self.feedback_type and self.feedback_type not in valid_types:
            errors.append(f"Feedback type '{self.feedback_type}' non valido. Tipi permessi: {valid_types}")
        
        if errors:
            raise ValueError("Errori di validazione feedback: " + "; ".join(errors))
        
        return True
    
    @property
    def is_review_feedback(self):
        """True se collegato a una review, False se standalone"""
        return self.review_id is not None
    
    @property
    def display_type(self):
        """Nome leggibile del tipo"""
        type_map = {
            'peer': 'Feedback Collega',
            'upward': 'Feedback verso Manager',
            'downward': 'Feedback verso Subordinato', 
            'self': 'Auto-valutazione',
            '360': 'Feedback 360°',
            'followup': 'Follow-up',
            'chiarimento': 'Richiesta Chiarimento',
            'criticita': 'Segnalazione Criticità',
            'riconoscimento': 'Riconoscimento',
            'suggestion': 'Suggerimento',
            'coaching': 'Coaching/Mentoring'
        }
        return type_map.get(self.feedback_type, self.feedback_type)


class PerformanceGoal(db.Model):
    """Obiettivi di performance"""
    __tablename__ = 'performance_goals'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    review_id = db.Column(db.Integer, db.ForeignKey('performance_reviews.id'))
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    category = db.Column(db.String(50))  # Colonna presente nel DB
    target_year = db.Column(db.Integer)
    start_date = db.Column(db.Date)
    target_date = db.Column(db.Date)
    status = db.Column(db.String(20), default='active')
    progress_percentage = db.Column(db.Integer, default=0)
    completion_date = db.Column(db.Date)
    success_criteria = db.Column(db.Text)
    measurable_outcomes = db.Column(db.JSON)
    weight = db.Column(db.Float)
    priority = db.Column(db.String(20))
    achievement_rating = db.Column(db.Float)
    manager_assessment = db.Column(db.Text)
    employee_self_assessment = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    set_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    quarter = db.Column(db.String(10))
    year = db.Column(db.Integer)
    notes = db.Column(db.Text)
    completion_notes = db.Column(db.Text)
    progress = db.Column(db.Integer)
    
    # Nuovi campi per gestione template e assegnazione
    is_template = db.Column(db.Boolean, default=False)  # True se è un template, False se è obiettivo personale
    template_id = db.Column(db.Integer, db.ForeignKey('performance_goals.id'), nullable=True)  # Link al template di origine
    assigned_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)  # Chi ha assegnato l'obiettivo
    visibility = db.Column(db.String(20), default='private')  # private, public, team
    
    # Relationships
    employee = db.relationship('User', foreign_keys=[employee_id], backref='performance_goals')
    assigned_by = db.relationship('User', foreign_keys=[assigned_by_id], backref='assigned_goals')
    created_by_user = db.relationship('User', foreign_keys=[created_by], backref='created_goals')
    set_by_user = db.relationship('User', foreign_keys=[set_by_id], backref='set_goals')
    
    # Self-referential relationship for template
    template = db.relationship('PerformanceGoal', remote_side=[id], foreign_keys=[template_id], backref='instances')
    
    def __repr__(self):
        return f'<PerformanceGoal {self.title}>'
    
    @db.validates('progress_percentage')
    def validate_progress_percentage(self, key, value):
        """Valida che progress_percentage sia in range valido"""
        if value is not None and (value < 0 or value > 200):
            raise ValueError(f"Progress percentage deve essere tra 0 e 200%, ricevuto: {value}")
        return value
    
    @db.validates('achievement_rating')
    def validate_achievement_rating(self, key, value):
        """Valida che achievement_rating sia in scala 1-5"""
        if value is not None and (value < 1.0 or value > 5.0):
            raise ValueError(f"Achievement rating deve essere tra 1.0 e 5.0, ricevuto: {value}")
        return value
    
    @db.validates('weight')
    def validate_weight(self, key, value):
        """Valida che weight sia positivo"""
        if value is not None and value < 0:
            raise ValueError(f"Weight deve essere positivo, ricevuto: {value}")
        return value
    
    def validate_dates(self):
        """Valida consistenza delle date"""
        errors = []
        
        # Valida che start_date sia prima di target_date
        if self.start_date and self.target_date and self.start_date >= self.target_date:
            errors.append("Start date deve essere precedente alla target date")
        
        # Valida che completion_date sia coerente con status
        if self.status == 'completed' and not self.completion_date:
            errors.append("Goal completato deve avere completion_date")
            
        # Valida che target_year sia coerente con le date
        if self.target_year and self.target_date and self.target_date.year != self.target_year:
            errors.append(f"Target year ({self.target_year}) non corrisponde all'anno della target date ({self.target_date.year})")
            
        # Valida consistenza status e progress
        if self.status == 'completed' and self.progress_percentage is not None and self.progress_percentage < 100:
            errors.append(f"Goal con status 'completed' dovrebbe avere progress >= 100%, trovato: {self.progress_percentage}%")
            
        if errors:
            raise ValueError("Errori di validazione date: " + "; ".join(errors))
        
        return True
    
    @property
    def display_status(self):
        """Nome leggibile dello stato"""
        status_map = {
            'active': 'Attivo',
            'completed': 'Completato',
            'cancelled': 'Annullato',
            'deferred': 'Rinviato'
        }
        return status_map.get(self.status, self.status)
    
    @property
    def display_category(self):
        """Nome leggibile della categoria"""
        category_map = {
            'technical': 'Competenze Tecniche',
            'soft_skills': 'Soft Skills',
            'business': 'Business',
            'career_development': 'Sviluppo Carriera'
        }
        return category_map.get(self.category, self.category)
    
    @property
    def linked_review(self):
        """Ottieni la review collegata"""
        if self.review_id:
            return PerformanceReview.query.get(self.review_id)
        return None
    
    @property
    def available_reviews(self):
        """Ottieni le review disponibili per il collegamento"""
        return PerformanceReview.query.filter(
            PerformanceReview.employee_id == self.employee_id,
            PerformanceReview.review_year == self.year
        ).all()
    
    @property
    def is_linked_to_review(self):
        """Verifica se l'obiettivo è collegato a una review"""
        return self.review_id is not None
    
    @property
    def is_goal_template(self):
        """Verifica se è un template"""
        return self.is_template is True
    
    @property
    def is_assigned_goal(self):
        """Verifica se è un obiettivo assegnato da template"""
        return self.template_id is not None
    
    @property
    def template_source(self):
        """Ottieni il template di origine"""
        if self.template_id:
            return PerformanceGoal.query.get(self.template_id)
        return None
    
    @property
    def assigned_instances(self):
        """Ottieni tutte le istanze assegnate di questo template"""
        if self.is_template:
            return PerformanceGoal.query.filter_by(template_id=self.id).all()
        return []
    
    @property
    def assignment_info(self):
        """Informazioni sull'assegnazione"""
        if self.assigned_by_id:
            return {
                'assigned_by': self.assigned_by,
                'assigned_date': self.created_at,
                'is_from_template': self.template_id is not None
            }
        return None
    
    @classmethod
    def get_templates(cls):
        """Ottieni tutti i template disponibili"""
        return cls.query.filter_by(is_template=True).all()
    
    @classmethod
    def get_goals_for_employee_year(cls, employee_id, year):
        """Ottieni obiettivi per dipendente e anno"""
        return cls.query.filter(
            cls.employee_id == employee_id,
            db.or_(cls.year == year, cls.target_year == year),
            cls.is_template == False
        ).all()
    
    @classmethod
    def create_from_template(cls, template_id, employee_id, assigned_by_id, year=None):
        """Crea un obiettivo da template"""
        template = cls.query.get(template_id)
        if not template or not template.is_template:
            raise ValueError("Template non valido")
        
        goal = cls(
            employee_id=employee_id,
            template_id=template_id,
            assigned_by_id=assigned_by_id,
            title=template.title,
            description=template.description,
            category=template.category,
            year=year or template.year,
            target_year=year or template.target_year,
            priority=template.priority,
            success_criteria=template.success_criteria,
            measurable_outcomes=template.measurable_outcomes,
            weight=template.weight,
            visibility=template.visibility,
            is_template=False
        )
        
        return goal


class PerformanceTemplate(db.Model):
    """Template per valutazioni"""
    __tablename__ = 'performance_templates'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    template_type = db.Column(db.String(50))
    job_level = db.Column(db.String(50))
    department = db.Column(db.String(100))
    evaluation_criteria = db.Column(db.Text)
    rating_scale = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    fields_config = db.Column(db.Text)  # JSON - nome corretto dal DB
    target_role = db.Column(db.String(100))
    is_default = db.Column(db.Boolean, default=False)
    
    def __repr__(self):
        return f'<PerformanceTemplate {self.name}>'
    
    @property
    def display_type(self):
        """Nome leggibile del tipo di template"""
        type_map = {
            'annual': 'Valutazione Annuale',
            'quarterly': 'Valutazione Trimestrale',
            'probation': 'Valutazione Fine Prova',
            '360': 'Feedback 360°',
            'project': 'Valutazione Progetto'
        }
        return type_map.get(self.template_type, self.template_type)


class PerformanceKPI(db.Model):
    """KPI misurabili per gli obiettivi"""
    __tablename__ = 'performance_kpis'

    id = db.Column(db.Integer, primary_key=True)
    goal_id = db.Column(db.Integer, db.ForeignKey('performance_goals.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    target_value = db.Column(db.Float, nullable=False)
    current_value = db.Column(db.Float, default=0)
    unit_of_measure = db.Column(db.String(50))  # %, €, ore, numero
    baseline_value = db.Column(db.Float)
    measurement_frequency = db.Column(db.String(20))  # weekly, monthly, quarterly
    last_measured_at = db.Column(db.Date)
    is_active = db.Column(db.Boolean)
    created_at = db.Column(db.DateTime)
    updated_at = db.Column(db.DateTime)
    measurement_unit = db.Column(db.String(50))
    is_achieved = db.Column(db.Boolean)
    achievement_date = db.Column(db.Date)

    # Relationships
    goal = db.relationship('PerformanceGoal', backref='kpis')

    def __repr__(self):
        return f'<PerformanceKPI {self.name}>'

    @property
    def progress_percentage(self):
        """Calcola la percentuale di progresso verso il target"""
        if self.target_value and self.target_value > 0:
            return round((self.current_value / self.target_value) * 100, 2)
        return 0

    @property
    def is_on_track(self):
        """Verifica se il KPI è in linea con gli obiettivi"""
        return self.progress_percentage >= 80

class PerformanceReward(db.Model):
    """Riconoscimenti e premi performance"""
    __tablename__ = 'performance_rewards'
    
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    title = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    reward_type = db.Column(db.String(50), nullable=False)  # bonus, promotion, recognition, training
    monetary_value = db.Column(db.Float)
    currency = db.Column(db.String(3), default='EUR')
    awarded_date = db.Column(db.Date, nullable=False)
    awarded_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    review_id = db.Column(db.Integer, db.ForeignKey('performance_reviews.id'))
    achievement_reason = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    employee = db.relationship('User', foreign_keys=[employee_id], backref='performance_rewards')
    awarder = db.relationship('User', foreign_keys=[awarded_by], backref='awarded_rewards')
    
    def __repr__(self):
        return f'<PerformanceReward {self.title} - {self.employee.full_name}>'
    
    @property
    def display_type(self):
        """Nome leggibile del tipo di reward"""
        type_map = {
            'bonus': 'Bonus Monetario',
            'promotion': 'Promozione',
            'recognition': 'Riconoscimento',
            'training': 'Formazione Specialistica'
        }
        return type_map.get(self.reward_type, self.reward_type)

class PerformanceReviewParticipant(db.Model):
    __tablename__ = 'performance_review_participants'
    id = db.Column(db.Integer, primary_key=True)
    review_id = db.Column(db.Integer, db.ForeignKey('performance_reviews.id'))

class CoreCompetency(db.Model):
    """Competenze core aziendali per Business Intelligence"""
    __tablename__ = 'core_competencies'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100))
    description = db.Column(db.Text)
    category = db.Column(db.String(50))
    market_positioning = db.Column(db.Text)
    skill_ids = db.Column(db.JSON)
    min_team_size = db.Column(db.Integer)
    avg_proficiency_required = db.Column(db.Float)
    business_value = db.Column(db.Text)
    target_markets = db.Column(db.JSON)
    competitive_advantage = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<CoreCompetency {self.name}>'

    @property
    def skills(self):
        """Ottiene le skills collegate attraverso skill_ids"""
        if not self.skill_ids:
            return []
        # Assumendo che esista un modello Skill
        from models import Skill
        return Skill.query.filter(Skill.id.in_(self.skill_ids)).all()

    @property
    def team_coverage(self):
        """Calcola la copertura del team per questa competenza"""
        if not self.skill_ids:
            return 0
        
        # Conta quanti dipendenti hanno le skill richieste
        from models import UserSkill, User
        from sqlalchemy import func
        
        # Conta dipendenti con skill di questa competenza
        skilled_users = db.session.query(func.count(func.distinct(UserSkill.user_id))).filter(
            UserSkill.skill_id.in_(self.skill_ids),
            UserSkill.proficiency_level >= (self.avg_proficiency_required or 3)
        ).scalar()
        
        # Confronta con team size minimo richiesto
        if self.min_team_size and self.min_team_size > 0:
            return min(100, (skilled_users / self.min_team_size) * 100)
        
        return skilled_users

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'category': self.category,
            'market_positioning': self.market_positioning,
            'skill_ids': self.skill_ids or [],
            'min_team_size': self.min_team_size,
            'avg_proficiency_required': self.avg_proficiency_required,
            'business_value': self.business_value,
            'target_markets': self.target_markets or [],
            'competitive_advantage': self.competitive_advantage,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }