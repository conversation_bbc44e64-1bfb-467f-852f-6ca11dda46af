# User and Authentication models
from .base import db, datetime
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from utils.permissions import ROLE_EMPLOYEE

class User(UserMixin, db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(256))
    first_name = db.Column(db.String(64))
    last_name = db.Column(db.String(64))
    role = db.Column(db.String(50), default=ROLE_EMPLOYEE, nullable=False)
    department = db.Column(db.String(64))  # DEPRECATED: use department_id instead
    department_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>('departments.id'), nullable=True)
    position = db.Column(db.String(64))
    hire_date = db.Column(db.Date)
    phone = db.Column(db.String(20))
    profile_image = db.Column(db.String(255))
    bio = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    dark_mode = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)

    # Password Reset Fields
    reset_token = db.Column(db.String(100), nullable=True, unique=True)
    reset_token_expiry = db.Column(db.DateTime, nullable=True)

    # Relationships
    timesheet_entries = db.relationship('TimesheetEntry', backref='user', lazy='dynamic')
    project_resources = db.relationship('ProjectResource', backref='user', lazy='dynamic')
    created_news = db.relationship('News', backref='author', lazy='dynamic')

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def __repr__(self):
        return f'<User {self.username}>'

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def projects(self):
        """Compatibilità retroattiva - progetti tramite project_resources"""
        return [pr.project for pr in self.project_resources]

    @property
    def team_members(self):
        """Compatibilità retroattiva per progetti dove questo user è membro"""
        # Restituisce tutti gli utenti che lavorano negli stessi progetti
        project_ids = [pr.project_id for pr in self.project_resources]
        if not project_ids:
            return []
        
        from .projects import ProjectResource
        return db.session.query(User).join(ProjectResource).filter(
            ProjectResource.project_id.in_(project_ids),
            User.id != self.id
        ).distinct().all()

    def has_permission(self, permission_name):
        """Check if user has a specific permission"""
        from utils.permissions import user_has_permission
        return user_has_permission(self.role, permission_name)

    def can_view_project(self, project):
        """Verifica se l'utente può visualizzare un progetto"""
        from utils.permissions import user_has_permission, PERMISSION_VIEW_ALL_PROJECTS

        # Admin e manager possono vedere tutti i progetti
        if user_has_permission(self.role, PERMISSION_VIEW_ALL_PROJECTS):
            return True

        # Altrimenti, l'utente può vedere solo i progetti a cui è assegnato tramite project_resources
        return any(pr.project_id == project.id for pr in self.project_resources)

    def get_project_role(self, project):
        """Ottiene il ruolo dell'utente in un progetto specifico"""
        resource = self.project_resources.filter_by(project_id=project.id).first()
        return resource.role if resource else None

    def get_project_allocation(self, project):
        """Ottiene la percentuale di allocazione dell'utente in un progetto"""
        resource = self.project_resources.filter_by(project_id=project.id).first()
        return resource.allocation_percentage if resource else 0.0

    def to_dict(self):
        """Convert user object to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'full_name': self.full_name,
            'role': self.role,
            'department': self.department,
            'department_id': self.department_id,
            'position': self.position,
            'phone': self.phone,
            'profile_image': self.profile_image,
            'bio': self.bio,
            'is_active': self.is_active,
            'dark_mode': self.dark_mode,
            'hire_date': self.hire_date.isoformat() if self.hire_date else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }


class OAuthAccount(db.Model):
    """
    OAuth account linking for enterprise authentication.
    Links external OAuth providers (Google, Microsoft) to existing users.
    NO auto-registration - only linking to pre-existing users.
    """
    __tablename__ = 'oauth_accounts'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    provider = db.Column(db.String(50), nullable=False, index=True)  # 'google', 'microsoft'
    provider_user_id = db.Column(db.String(255), nullable=False)  # ID from OAuth provider
    email = db.Column(db.String(255), nullable=False)  # Email from OAuth provider
    display_name = db.Column(db.String(255))  # Display name from provider
    avatar_url = db.Column(db.String(500))  # Avatar URL from provider
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    last_login = db.Column(db.DateTime)  # Track last OAuth login
    
    # Relationships
    user = db.relationship('User', backref=db.backref('oauth_accounts', lazy='dynamic'))
    
    # Constraints: prevent duplicate OAuth accounts
    __table_args__ = (
        # One OAuth account per provider per user
        db.UniqueConstraint('user_id', 'provider', name='_user_provider_uc'),
        # One provider account can only link to one user
        db.UniqueConstraint('provider', 'provider_user_id', name='_provider_user_uc'),
    )
    
    def __repr__(self):
        return f'<OAuthAccount {self.provider}:{self.email} -> User:{self.user.username}>'
    
    def to_dict(self):
        """Convert to dictionary for API responses"""
        return {
            'id': self.id,
            'provider': self.provider,
            'email': self.email,
            'display_name': self.display_name,
            'avatar_url': self.avatar_url,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'user': {
                'id': self.user.id,
                'email': self.user.email,
                'full_name': self.user.full_name,
                'role': self.user.role,
                'is_active': self.user.is_active
            }
        }