# Engagement and gamification models
from .base import db, datetime, date
from decimal import Decimal
import json

class EngagementCampaign(db.Model):
    """Campagne/iniziative di engagement con date inizio/fine"""
    __tablename__ = 'engagement_campaigns'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    
    # Date campagna
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    
    # Status della campagna
    status = db.Column(db.String(20), default='draft')  # draft, active, completed, archived
    
    # Configurazione punti e obiettivi
    points_multiplier = db.Column(db.Numeric(3, 2), default=Decimal('1.0'))  # Moltiplicatore punti per questa campagna
    objectives_config = db.Column(db.JSON)  # Obiettivi specifici della campagna
    
    # Regole per calcolo punti
    points_rules = db.Column(db.JSON)  # Regole specifiche per azioni durante campagna
    
    # Metadata
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    created_by = db.relationship('User', backref='created_campaigns')
    points = db.relationship('EngagementPoint', backref='campaign', lazy='dynamic')
    rewards = db.relationship('EngagementReward', backref='campaign', lazy='dynamic')
    leaderboards = db.relationship('EngagementLeaderboard', backref='campaign', lazy='dynamic')
    
    def __repr__(self):
        return f'<EngagementCampaign {self.name}>'
    
    @property
    def is_active(self):
        """Controlla se la campagna è attiva"""
        today = date.today()
        return (self.status == 'active' and 
                self.start_date <= today <= self.end_date)
    
    @property
    def days_remaining(self):
        """Giorni rimanenti alla fine della campagna"""
        if not self.is_active:
            return 0
        return (self.end_date - date.today()).days
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'start_date': self.start_date.isoformat() if self.start_date else None,
            'end_date': self.end_date.isoformat() if self.end_date else None,
            'status': self.status,
            'points_multiplier': float(self.points_multiplier),
            'objectives_config': self.objectives_config,
            'points_rules': self.points_rules,
            'is_active': self.is_active,
            'days_remaining': self.days_remaining,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }


class EngagementLevel(db.Model):
    """Livelli di engagement con soglie punti"""
    __tablename__ = 'engagement_levels'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    description = db.Column(db.Text)
    points_threshold = db.Column(db.Integer, nullable=False)  # Punti necessari per raggiungere questo livello
    level_order = db.Column(db.Integer, nullable=False)  # Ordine del livello (1, 2, 3...)
    
    # Ricompense del livello
    rewards_config = db.Column(db.JSON)  # Ricompense automatiche per raggiungere il livello
    
    # Styling
    color_hex = db.Column(db.String(7), default='#3B82F6')  # Colore per UI
    icon_name = db.Column(db.String(50), default='star')  # Nome icona HeroIcon
    
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<EngagementLevel {self.name}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'points_threshold': self.points_threshold,
            'level_order': self.level_order,
            'rewards_config': self.rewards_config,
            'color_hex': self.color_hex,
            'icon_name': self.icon_name,
            'is_active': self.is_active
        }


class EngagementPoint(db.Model):
    """Punti guadagnati dagli utenti tramite audit logs"""
    __tablename__ = 'engagement_points'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    campaign_id = db.Column(db.Integer, db.ForeignKey('engagement_campaigns.id'), nullable=True)
    
    # Punti e source
    points_earned = db.Column(db.Integer, nullable=False)
    source_type = db.Column(db.String(50), nullable=False)  # 'audit_log', 'manual', 'bonus'
    source_id = db.Column(db.Integer)  # ID del log audit che ha generato i punti
    
    # Dettagli azione
    action_type = db.Column(db.String(50))  # login, create, update, delete, etc.
    resource_type = db.Column(db.String(50))  # projects, tasks, clients, etc.
    resource_id = db.Column(db.Integer)
    
    # Metadata
    description = db.Column(db.String(255))  # Descrizione leggibile
    multiplier_applied = db.Column(db.Numeric(3, 2), default=Decimal('1.0'))
    
    earned_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    user = db.relationship('User', backref='engagement_points')
    
    def __repr__(self):
        return f'<EngagementPoint User:{self.user_id} +{self.points_earned}pts>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'campaign_id': self.campaign_id,
            'points_earned': self.points_earned,
            'source_type': self.source_type,
            'source_id': self.source_id,
            'action_type': self.action_type,
            'resource_type': self.resource_type,
            'resource_id': self.resource_id,
            'description': self.description,
            'multiplier_applied': float(self.multiplier_applied),
            'earned_at': self.earned_at.isoformat() if self.earned_at else None,
            'campaign_name': self.campaign.name if self.campaign else None
        }


class EngagementReward(db.Model):
    """Catalogo premi disponibili per riscatto"""
    __tablename__ = 'engagement_rewards'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    
    # Configurazione premio
    points_cost = db.Column(db.Integer, nullable=False)  # Costo in punti
    reward_type = db.Column(db.String(50), nullable=False)  # 'physical', 'digital', 'experience', 'badge'
    
    # Date di disponibilità
    available_from = db.Column(db.Date)
    available_until = db.Column(db.Date)
    
    # Campagna collegata (opzionale)
    campaign_id = db.Column(db.Integer, db.ForeignKey('engagement_campaigns.id'), nullable=True)
    
    # Limiti e stock
    max_redemptions = db.Column(db.Integer)  # Numero massimo di riscatti
    current_redemptions = db.Column(db.Integer, default=0)
    per_user_limit = db.Column(db.Integer, default=1)  # Limite per utente
    
    # Metadata
    image_url = db.Column(db.String(255))
    external_url = db.Column(db.String(255))  # Link esterno per premi digitali
    
    is_active = db.Column(db.Boolean, default=True)
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    created_by = db.relationship('User', backref='created_rewards')
    redemptions = db.relationship('EngagementUserReward', backref='reward', lazy='dynamic')
    
    def __repr__(self):
        return f'<EngagementReward {self.name}>'
    
    @property
    def is_available(self):
        """Controlla se il premio è disponibile"""
        today = date.today()
        
        # Check date availability
        if self.available_from and today < self.available_from:
            return False
        if self.available_until and today > self.available_until:
            return False
            
        # Check stock
        if self.max_redemptions and self.current_redemptions >= self.max_redemptions:
            return False
            
        return self.is_active
    
    @property
    def stock_remaining(self):
        """Calcola stock rimanente"""
        if not self.max_redemptions:
            return None
        return self.max_redemptions - self.current_redemptions
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'points_cost': self.points_cost,
            'reward_type': self.reward_type,
            'available_from': self.available_from.isoformat() if self.available_from else None,
            'available_until': self.available_until.isoformat() if self.available_until else None,
            'campaign_id': self.campaign_id,
            'max_redemptions': self.max_redemptions,
            'current_redemptions': self.current_redemptions,
            'per_user_limit': self.per_user_limit,
            'image_url': self.image_url,
            'external_url': self.external_url,
            'is_active': self.is_active,
            'is_available': self.is_available,
            'stock_remaining': self.stock_remaining,
            'campaign_name': self.campaign.name if self.campaign else None
        }


class EngagementUserReward(db.Model):
    """Premi riscattati dagli utenti"""
    __tablename__ = 'engagement_user_rewards'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    reward_id = db.Column(db.Integer, db.ForeignKey('engagement_rewards.id'), nullable=False)
    
    # Dettagli riscatto
    points_spent = db.Column(db.Integer, nullable=False)
    redemption_status = db.Column(db.String(20), default='pending')  # pending, approved, delivered, cancelled
    
    # Metadata
    redemption_notes = db.Column(db.Text)  # Note dell'utente
    admin_notes = db.Column(db.Text)  # Note dell'admin
    
    redeemed_at = db.Column(db.DateTime, default=datetime.utcnow)
    fulfilled_at = db.Column(db.DateTime)
    
    # Relationships
    user = db.relationship('User', backref='redeemed_rewards')
    
    def __repr__(self):
        return f'<EngagementUserReward User:{self.user_id} Reward:{self.reward_id}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'reward_id': self.reward_id,
            'points_spent': self.points_spent,
            'redemption_status': self.redemption_status,
            'redemption_notes': self.redemption_notes,
            'admin_notes': self.admin_notes,
            'redeemed_at': self.redeemed_at.isoformat() if self.redeemed_at else None,
            'fulfilled_at': self.fulfilled_at.isoformat() if self.fulfilled_at else None,
            'reward_name': self.reward.name if self.reward else None,
            'user_name': self.user.full_name if self.user else None
        }


class EngagementLeaderboard(db.Model):
    """Classifiche per campagne o generali"""
    __tablename__ = 'engagement_leaderboards'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    campaign_id = db.Column(db.Integer, db.ForeignKey('engagement_campaigns.id'), nullable=True)
    
    # Posizione e punti
    ranking_position = db.Column(db.Integer, nullable=False)
    total_points = db.Column(db.Integer, nullable=False)
    
    # Periodo di riferimento
    period_type = db.Column(db.String(20), default='campaign')  # 'campaign', 'monthly', 'weekly', 'all_time'
    period_start = db.Column(db.Date)
    period_end = db.Column(db.Date)
    
    # Metadata
    calculated_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    user = db.relationship('User', backref='leaderboard_entries')
    
    def __repr__(self):
        return f'<EngagementLeaderboard User:{self.user_id} Pos:{self.ranking_position}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'campaign_id': self.campaign_id,
            'ranking_position': self.ranking_position,
            'total_points': self.total_points,
            'period_type': self.period_type,
            'period_start': self.period_start.isoformat() if self.period_start else None,
            'period_end': self.period_end.isoformat() if self.period_end else None,
            'calculated_at': self.calculated_at.isoformat() if self.calculated_at else None,
            'user_name': self.user.full_name if self.user else None,
            'user_email': self.user.email if self.user else None,
            'campaign_name': self.campaign.name if self.campaign else None
        }


class EngagementUserProfile(db.Model):
    """Profilo engagement dell'utente con statistiche aggregate"""
    __tablename__ = 'engagement_user_profiles'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, unique=True)
    
    # Statistiche totali
    total_points = db.Column(db.Integer, default=0)
    total_points_spent = db.Column(db.Integer, default=0)
    available_points = db.Column(db.Integer, default=0)
    
    # Livello attuale
    current_level_id = db.Column(db.Integer, db.ForeignKey('engagement_levels.id'))
    next_level_id = db.Column(db.Integer, db.ForeignKey('engagement_levels.id'))
    
    # Statistiche di attività
    total_logins = db.Column(db.Integer, default=0)
    total_actions = db.Column(db.Integer, default=0)
    activities_completed = db.Column(db.Integer, default=0)  # Attività completate
    achievements_count = db.Column(db.Integer, default=0)  # Numero achievement ottenuti
    streak_days = db.Column(db.Integer, default=0)  # Giorni consecutivi di attività (alias current_streak_days)
    current_streak_days = db.Column(db.Integer, default=0)  # Alias per retrocompatibilità
    last_activity_date = db.Column(db.Date)
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = db.relationship('User', backref='engagement_profile')
    current_level = db.relationship('EngagementLevel', foreign_keys=[current_level_id])
    next_level = db.relationship('EngagementLevel', foreign_keys=[next_level_id])
    
    def __repr__(self):
        return f'<EngagementUserProfile User:{self.user_id} Points:{self.available_points}>'
    
    @property
    def points_to_next_level(self):
        """Punti necessari per il prossimo livello"""
        if not self.next_level:
            return 0
        return max(0, self.next_level.points_threshold - self.total_points)
    
    @property
    def level_progress_percentage(self):
        """Percentuale di progresso verso il prossimo livello"""
        if not self.current_level or not self.next_level:
            return 100
        
        current_threshold = self.current_level.points_threshold
        next_threshold = self.next_level.points_threshold
        current_points = self.total_points
        
        if next_threshold <= current_threshold:
            return 100
            
        progress = (current_points - current_threshold) / (next_threshold - current_threshold)
        return min(100, max(0, progress * 100))
    
    @property
    def current_level_name(self):
        """Nome del livello attuale"""
        return self.current_level.name if self.current_level else 'Novizio'
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'total_points': self.total_points,
            'total_points_spent': self.total_points_spent,
            'available_points': self.available_points,
            'current_level_id': self.current_level_id,
            'next_level_id': self.next_level_id,
            'total_logins': self.total_logins,
            'total_actions': self.total_actions,
            'activities_completed': self.activities_completed,
            'achievements_count': self.achievements_count,
            'streak_days': self.streak_days,
            'current_streak_days': self.current_streak_days,
            'current_level_name': self.current_level_name,
            'last_activity_date': self.last_activity_date.isoformat() if self.last_activity_date else None,
            'points_to_next_level': self.points_to_next_level,
            'level_progress_percentage': self.level_progress_percentage,
            'current_level': self.current_level.to_dict() if self.current_level else None,
            'next_level': self.next_level.to_dict() if self.next_level else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }