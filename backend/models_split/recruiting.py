"""
Recruiting models - Sistema completo gestione recruiting e selezione candidati.
Riutilizza il sistema CV esistente da personnel per analisi competenze.
"""
from .base import db, datetime, date, json
from decimal import Decimal

class JobPosting(db.Model):
    """Posizione lavorativa aperta per recruiting."""
    __tablename__ = 'job_postings'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    requirements = db.Column(db.Text)
    responsibilities = db.Column(db.Text)
    
    # Location & Type
    location = db.Column(db.String(100))
    remote_allowed = db.Column(db.<PERSON>, default=False)
    employment_type = db.Column(db.String(50), default='full_time')  # full_time, part_time, contract, intern
    
    # Compensation
    salary_min = db.Column(db.Numeric(10, 2))
    salary_max = db.Column(db.Numeric(10, 2))
    salary_currency = db.Column(db.String(3), default='EUR')
    
    # Collegamenti diretti (FK verso modelli esistenti)
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=True)
    proposal_id = db.Column(db.Integer, db.ForeignKey('proposals.id'), nullable=True)
    department_id = db.Column(db.Integer, db.ForeignKey('departments.id'), nullable=True)
    
    # Status: draft, active, paused, closed
    status = db.Column(db.String(20), default='draft')
    is_public = db.Column(db.Boolean, default=False)
    
    # Tracking
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    published_at = db.Column(db.DateTime)
    closed_at = db.Column(db.DateTime)
    
    # Relationships
    creator = db.relationship('User', foreign_keys=[created_by], backref='created_job_postings')
    project = db.relationship('Project', backref='job_postings')
    proposal = db.relationship('Proposal', backref='job_postings')
    department = db.relationship('Department', backref='job_postings')
    applications = db.relationship('Application', backref='job_posting', 
                                 cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<JobPosting {self.title}>'
    
    @db.validates('salary_min', 'salary_max')
    def validate_salary_range(self, key, value):
        """Valida che i salari siano positivi"""
        if value is not None and value < 0:
            raise ValueError(f"{key} deve essere positivo, ricevuto: {value}")
        return value
    
    def validate_salary_logic(self):
        """Valida logica del range salariale"""
        errors = []
        
        # Valida che salary_min <= salary_max
        if (self.salary_min is not None and self.salary_max is not None and 
            self.salary_min > self.salary_max):
            errors.append(f"Salary min ({self.salary_min}) non può essere maggiore di salary max ({self.salary_max})")
        
        # Valida coerenza status e pubblicazione
        if self.status == 'draft' and self.is_public:
            errors.append("Job posting in draft non può essere pubblico")
        
        if self.status == 'active' and not self.is_public and not self.published_at:
            errors.append("Job posting attivo dovrebbe essere pubblico o avere published_at")
            
        if errors:
            raise ValueError("Errori di validazione job posting: " + "; ".join(errors))
        
        return True


class Candidate(db.Model):
    """Candidato per posizioni lavorative."""
    __tablename__ = 'candidates'
    
    id = db.Column(db.Integer, primary_key=True)
    first_name = db.Column(db.String(100), nullable=False)
    last_name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    phone = db.Column(db.String(20))
    location = db.Column(db.String(100))
    linkedin_url = db.Column(db.String(255))
    
    # CV Data (riutilizza sistema esistente come UserProfile)
    current_cv_path = db.Column(db.String(255))
    cv_last_updated = db.Column(db.DateTime)
    cv_analysis_data = db.Column(db.Text)  # JSON da extract_skills_from_cv()
    
    # Tracking
    source = db.Column(db.String(50))  # website, linkedin, referral, agency
    # Status: new, screening, interviewing, offered, hired, rejected
    status = db.Column(db.String(20), default='new')
    notes = db.Column(db.Text)
    tags = db.Column(db.Text)  # JSON array di tags
    
    # Se assunto, riferimento all'utente
    hired_as_user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    hired_date = db.Column(db.Date, nullable=True)
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    hired_as_user = db.relationship('User', backref='candidate_profile')
    applications = db.relationship('Application', backref='candidate', 
                                 cascade='all, delete-orphan')
    skills = db.relationship('CandidateSkill', backref='candidate', 
                           cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Candidate {self.first_name} {self.last_name}>'
    
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"


class Application(db.Model):
    """Candidatura di un candidato per una posizione specifica."""
    __tablename__ = 'applications'
    
    id = db.Column(db.Integer, primary_key=True)
    job_posting_id = db.Column(db.Integer, db.ForeignKey('job_postings.id'), nullable=False)
    candidate_id = db.Column(db.Integer, db.ForeignKey('candidates.id'), nullable=False)
    
    # Application info
    applied_at = db.Column(db.DateTime, default=datetime.utcnow)
    cover_letter = db.Column(db.Text)
    
    # Workflow tracking
    current_step = db.Column(db.String(50), default='application_received')
    # Steps: application_received, screening, interview_1, interview_2, offer, hired, rejected
    
    # Status: pending, in_progress, completed, rejected
    status = db.Column(db.String(20), default='pending')
    
    # Evaluation
    overall_score = db.Column(db.Integer)  # 1-10
    interview_notes = db.Column(db.Text)
    rejection_reason = db.Column(db.Text)
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    interviews = db.relationship('InterviewSession', backref='application',
                               cascade='all, delete-orphan')
    workflow_steps = db.relationship('RecruitingWorkflow', backref='application',
                                   cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Application {self.candidate.full_name} -> {self.job_posting.title}>'


class InterviewSession(db.Model):
    """Sessione di colloquio per una candidatura."""
    __tablename__ = 'interview_sessions'
    
    id = db.Column(db.Integer, primary_key=True)
    application_id = db.Column(db.Integer, db.ForeignKey('applications.id'), nullable=False)
    
    # Interview details
    interview_type = db.Column(db.String(50), nullable=False)
    # Types: phone_screening, video_technical, onsite_cultural, final_executive
    scheduled_date = db.Column(db.DateTime, nullable=False)
    duration_minutes = db.Column(db.Integer, default=60)
    location = db.Column(db.String(200))  # URL per video o indirizzo fisico
    
    # Participants
    interviewer_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    additional_interviewers = db.Column(db.Text)  # JSON array di user IDs
    
    # Results
    # Status: scheduled, completed, cancelled, rescheduled
    status = db.Column(db.String(20), default='scheduled')
    score = db.Column(db.Integer)  # 1-10
    notes = db.Column(db.Text)
    feedback = db.Column(db.Text)
    recommendation = db.Column(db.String(20))  # hire, reject, continue
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    completed_at = db.Column(db.DateTime)
    
    # Relationships
    interviewer = db.relationship('User', backref='conducted_interviews')
    
    def __repr__(self):
        return f'<Interview {self.interview_type} - {self.application.candidate.full_name}>'
    
    @db.validates('duration_minutes')
    def validate_duration(self, key, value):
        """Valida che la durata sia ragionevole"""
        if value is not None and (value <= 0 or value > 480):  # Max 8 ore
            raise ValueError(f"Duration deve essere tra 1 e 480 minuti, ricevuto: {value}")
        return value
    
    @db.validates('score')
    def validate_score(self, key, value):
        """Valida che score sia in scala 1-10"""
        if value is not None and (value < 1 or value > 10):
            raise ValueError(f"Score deve essere tra 1 e 10, ricevuto: {value}")
        return value
    
    def validate_interview_logic(self):
        """Valida logica dell'intervista"""
        errors = []
        
        # Valida che scheduled_date sia nel futuro per interview scheduled
        if self.status == 'scheduled' and self.scheduled_date:
            from datetime import datetime
            if self.scheduled_date < datetime.utcnow():
                errors.append("Interview schedulata non può essere nel passato")
        
        # Valida coerenza score e raccomandazione
        if (self.status == 'completed' and self.score is not None and 
            self.recommendation and self.score >= 8 and self.recommendation == 'reject'):
            errors.append(f"Score alto ({self.score}/10) inconsistente con raccomandazione 'reject'")
        
        # Valida tipi di interview validi
        valid_types = ['phone_screening', 'video_technical', 'onsite_cultural', 'final_executive']
        if self.interview_type and self.interview_type not in valid_types:
            errors.append(f"Interview type '{self.interview_type}' non valido. Tipi permessi: {valid_types}")
        
        if errors:
            raise ValueError("Errori di validazione interview: " + "; ".join(errors))
        
        return True


class RecruitingWorkflow(db.Model):
    """Traccia il workflow di recruiting per ogni candidatura."""
    __tablename__ = 'recruiting_workflows'
    
    id = db.Column(db.Integer, primary_key=True)
    application_id = db.Column(db.Integer, db.ForeignKey('applications.id'), nullable=False)
    
    # Workflow step
    step_name = db.Column(db.String(50), nullable=False)
    step_order = db.Column(db.Integer, nullable=False)
    
    # Status: pending, in_progress, completed, skipped
    status = db.Column(db.String(20), default='pending')
    
    # Timing
    started_at = db.Column(db.DateTime)
    completed_at = db.Column(db.DateTime)
    due_date = db.Column(db.DateTime)
    
    # Assignment
    assigned_to = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # Results
    result = db.Column(db.String(20))  # pass, fail, pending
    notes = db.Column(db.Text)
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    assignee = db.relationship('User', backref='assigned_workflow_steps')
    
    def __repr__(self):
        return f'<WorkflowStep {self.step_name} - {self.application.candidate.full_name}>'


class CandidateSkill(db.Model):
    """Competenze estratte dal CV del candidato."""
    __tablename__ = 'candidate_skills'
    
    id = db.Column(db.Integer, primary_key=True)
    candidate_id = db.Column(db.Integer, db.ForeignKey('candidates.id'), nullable=False)
    
    # Skill info (estratto dal CV via AI)
    skill_name = db.Column(db.String(100), nullable=False)
    skill_category = db.Column(db.String(50))  # technical, soft, language, etc
    skill_level = db.Column(db.Integer)  # 1-5 scale
    years_experience = db.Column(db.Integer)
    
    # Source tracking
    extracted_from_cv = db.Column(db.Boolean, default=True)
    confidence_score = db.Column(db.Float)  # AI confidence 0-1
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<CandidateSkill {self.skill_name} - {self.candidate.full_name}>'


class CandidateAIScore(db.Model):
    """AI-generated evaluation scores for candidate-job posting matches."""
    __tablename__ = 'candidate_ai_scores'
    
    id = db.Column(db.Integer, primary_key=True)
    candidate_id = db.Column(db.Integer, db.ForeignKey('candidates.id'), nullable=False)
    job_posting_id = db.Column(db.Integer, db.ForeignKey('job_postings.id'), nullable=False)
    application_id = db.Column(db.Integer, db.ForeignKey('applications.id'), nullable=True)
    
    # Overall AI scores
    overall_score = db.Column(db.Integer, nullable=False)  # 0-100
    confidence_score = db.Column(db.Float, nullable=False)  # 0.0-1.0
    
    # Detailed scoring breakdown
    technical_skills_score = db.Column(db.Integer)  # 0-100
    experience_score = db.Column(db.Integer)  # 0-100
    motivation_score = db.Column(db.Integer)  # 0-100
    cultural_fit_score = db.Column(db.Integer)  # 0-100
    growth_potential_score = db.Column(db.Integer)  # 0-100
    
    # AI reasoning and recommendations
    ai_reasoning = db.Column(db.Text)  # JSON string with detailed analysis
    recommendation = db.Column(db.String(20))  # hire, interview, reject
    strengths = db.Column(db.Text)  # JSON array of strengths
    concerns = db.Column(db.Text)  # JSON array of concerns
    
    # AI metadata
    ai_model_used = db.Column(db.String(50))  # e.g., "gpt-4o"
    evaluation_version = db.Column(db.String(20), default='1.0')  # For tracking algorithm changes
    
    # Tracking
    evaluated_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    candidate = db.relationship('Candidate', backref='ai_scores')
    job_posting = db.relationship('JobPosting', backref='ai_scores')
    application = db.relationship('Application', backref='ai_score')
    evaluator = db.relationship('User', backref='ai_evaluations_created')
    
    def __repr__(self):
        return f'<CandidateAIScore {self.candidate.full_name} -> {self.job_posting.title}: {self.overall_score}/100>'


class AIGeneratedContent(db.Model):
    """Track AI-generated content across the recruiting module."""
    __tablename__ = 'ai_generated_content'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Content metadata
    entity_type = db.Column(db.String(50), nullable=False)  # job_posting, resume_enhancement, interview_questions
    entity_id = db.Column(db.Integer)  # ID of the related entity (if applicable)
    content_type = db.Column(db.String(50), nullable=False)  # description, requirements, questions, etc.
    
    # AI generation data
    prompt_data = db.Column(db.Text)  # JSON of the original prompt/input data
    generated_content = db.Column(db.Text, nullable=False)  # JSON of AI-generated content
    ai_model_used = db.Column(db.String(50))  # e.g., "gpt-4o"
    generation_tokens = db.Column(db.Integer)  # Tokens used for cost tracking
    
    # User interaction
    generated_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    applied_at = db.Column(db.DateTime)  # When/if the content was actually used
    applied_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # Quality tracking
    user_rating = db.Column(db.Integer)  # 1-5 rating from user
    user_feedback = db.Column(db.Text)  # Optional user feedback
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    generator = db.relationship('User', foreign_keys=[generated_by], backref='ai_content_generated')
    applier = db.relationship('User', foreign_keys=[applied_by], backref='ai_content_applied')
    
    def __repr__(self):
        return f'<AIGeneratedContent {self.entity_type}:{self.content_type} by {self.generator.full_name}>'


class RecruitingAIUsage(db.Model):
    """Track AI usage patterns and analytics for recruiting."""
    __tablename__ = 'recruiting_ai_usage'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Usage tracking
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    feature_type = db.Column(db.String(50), nullable=False)  # job_generation, candidate_evaluation, etc.
    action = db.Column(db.String(50), nullable=False)  # generate, evaluate, enhance, etc.
    
    # Performance metrics
    processing_time_ms = db.Column(db.Integer)  # Time taken for AI processing
    tokens_used = db.Column(db.Integer)  # For cost tracking
    success = db.Column(db.Boolean, default=True)
    error_message = db.Column(db.Text)  # If failed
    
    # Context
    entity_type = db.Column(db.String(50))  # Related entity type
    entity_id = db.Column(db.Integer)  # Related entity ID
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    user = db.relationship('User', backref='ai_usage_history')
    
    def __repr__(self):
        return f'<RecruitingAIUsage {self.user.full_name}:{self.feature_type}:{self.action}>'