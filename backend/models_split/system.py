# System Models (Notifications, Logging, Self-Healing, etc.)
from .base import db, datetime
import json

class Notification(db.Model):
    __tablename__ = 'notifications'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>Key('users.id'), nullable=False)
    title = db.Column(db.String(128), nullable=False)
    message = db.Column(db.Text, nullable=False)
    link = db.Column(db.String(255))
    type = db.Column(db.String(50), default='info') # info, success, warning, danger
    is_read = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    user = db.relationship('User', backref='notifications')

    def __repr__(self):
        return f'<Notification {self.id} for {self.user_id}>'

class AdminLog(db.Model):
    """Registra le azioni amministrative sugli utenti."""
    __tablename__ = 'admin_logs'

    id = db.Column(db.Integer, primary_key=True)
    admin_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    action = db.Column(db.String(255), nullable=False)
    target_user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    # Relazioni
    admin = db.relationship('User', foreign_keys=[admin_id], backref='admin_logs')
    target_user = db.relationship('User', foreign_keys=[target_user_id], backref='target_logs')

    def __repr__(self):
        return f'<AdminLog {self.id}: {self.action}>'


# Self-Healing System Models
class SystemHealth(db.Model):
    """Monitora lo stato generale del sistema e trend errori."""
    __tablename__ = 'system_health'
    
    id = db.Column(db.Integer, primary_key=True)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    health_score = db.Column(db.Float, nullable=False)  # 0-100 score
    error_count_24h = db.Column(db.Integer, default=0)
    critical_errors = db.Column(db.Integer, default=0)
    auto_healed_count = db.Column(db.Integer, default=0)
    pending_issues = db.Column(db.Integer, default=0)
    system_load = db.Column(db.Float)  # CPU/Memory se disponibili
    
    # Metadata aggiuntivo
    system_metadata = db.Column(db.Text)  # JSON per metriche extra
    
    def set_metadata(self, data):
        self.system_metadata = json.dumps(data) if data else None
    
    def get_metadata(self):
        return json.loads(self.system_metadata) if self.system_metadata else {}
    
    def __repr__(self):
        return f'<SystemHealth {self.timestamp}: {self.health_score}%>'


class ErrorPattern(db.Model):
    """Traccia pattern di errori ricorrenti per machine learning e prevenzione."""
    __tablename__ = 'error_patterns'
    
    id = db.Column(db.Integer, primary_key=True)
    pattern_hash = db.Column(db.String(64), unique=True, nullable=False)  # Hash del pattern per dedup
    error_type = db.Column(db.String(100), nullable=False)  # javascript_error, api_error, etc
    message_pattern = db.Column(db.Text, nullable=False)  # Pattern regex o substring
    file_pattern = db.Column(db.String(255))  # Pattern file che causa errore
    
    # Statistiche pattern
    occurrence_count = db.Column(db.Integer, default=1)
    first_seen = db.Column(db.DateTime, default=datetime.utcnow)
    last_seen = db.Column(db.DateTime, default=datetime.utcnow)
    severity = db.Column(db.String(20), default='medium')  # critical, high, medium, low
    
    # Auto-healing info
    is_auto_healable = db.Column(db.Boolean, default=False)
    healing_success_rate = db.Column(db.Float, default=0.0)  # 0-1
    last_healing_attempt = db.Column(db.DateTime)
    
    # AI analysis risultati
    ai_analysis = db.Column(db.Text)  # JSON con risultati analisi AI
    suggested_fix = db.Column(db.Text)  # Fix suggerito dall'AI
    
    def set_ai_analysis(self, analysis):
        self.ai_analysis = json.dumps(analysis) if analysis else None
    
    def get_ai_analysis(self):
        return json.loads(self.ai_analysis) if self.ai_analysis else {}
    
    def update_occurrence(self):
        """Aggiorna contatori quando pattern si ripete."""
        self.occurrence_count += 1
        self.last_seen = datetime.utcnow()
    
    def __repr__(self):
        return f'<ErrorPattern {self.pattern_hash}: {self.occurrence_count} occurrences>'


class HealingSession(db.Model):
    """Traccia ogni sessione di healing (manuale o automatica)."""
    __tablename__ = 'healing_sessions'
    
    id = db.Column(db.Integer, primary_key=True)
    error_pattern_id = db.Column(db.Integer, db.ForeignKey('error_patterns.id'))
    
    # Chi ha iniziato healing
    initiated_by = db.Column(db.Integer, db.ForeignKey('users.id'))  # NULL se automatico
    healing_type = db.Column(db.String(20), nullable=False)  # 'manual', 'automatic', 'ai_assisted'
    
    # Timing
    started_at = db.Column(db.DateTime, default=datetime.utcnow)
    completed_at = db.Column(db.DateTime)
    duration_seconds = db.Column(db.Integer)
    
    # Status e risultati
    status = db.Column(db.String(20), default='in_progress')  # in_progress, completed, failed, cancelled
    success = db.Column(db.Boolean)
    
    # Dettagli azione
    claude_prompt_generated = db.Column(db.Text)  # Prompt Claude Code generato
    actions_taken = db.Column(db.Text)  # JSON delle azioni eseguite
    error_before_healing = db.Column(db.Text)  # JSON errore originale
    verification_results = db.Column(db.Text)  # JSON risultati test post-healing
    
    # Feedback e learning
    effectiveness_score = db.Column(db.Float)  # 0-1 quanto è stato efficace
    admin_feedback = db.Column(db.Text)  # Feedback dall'admin
    
    # Relationships
    error_pattern = db.relationship('ErrorPattern', backref='healing_sessions')
    initiated_user = db.relationship('User', foreign_keys=[initiated_by])
    
    def set_actions_taken(self, actions):
        self.actions_taken = json.dumps(actions) if actions else None
    
    def get_actions_taken(self):
        return json.loads(self.actions_taken) if self.actions_taken else []
    
    def mark_completed(self, success=True, effectiveness_score=None):
        """Marca la sessione come completata."""
        self.completed_at = datetime.utcnow()
        self.duration_seconds = int((self.completed_at - self.started_at).total_seconds())
        self.status = 'completed' if success else 'failed'
        self.success = success
        if effectiveness_score is not None:
            self.effectiveness_score = effectiveness_score
    
    def __repr__(self):
        return f'<HealingSession {self.id}: {self.healing_type} - {self.status}>'