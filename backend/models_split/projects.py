# Project management models
from .base import db, datetime, date
from decimal import Decimal

class Project(db.Model):
    __tablename__ = 'projects'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    client_id = db.Column(db.<PERSON>ger, db.ForeignKey('clients.id'))
    contract_id = db.Column(db.Integer, db.ForeignKey('contracts.id'), nullable=True)  # Nullable per progetti interni
    start_date = db.Column(db.Date)
    end_date = db.Column(db.Date)
    status = db.Column(db.String(20), default='planning')  # planning, active, completed, on-hold
    budget = db.Column(db.Numeric(12, 2))
    expenses = db.Column(db.Numeric(12, 2), default=Decimal('0.00'))
    project_type = db.Column(db.String(50), default='service')  # service, license, consulting, product, rd, internal
    is_billable = db.Column(db.<PERSON>, default=True)  # Progetto fatturabile?
    client_daily_rate = db.Column(db.Numeric(8, 2))  # Tariffa giornaliera al cliente
    markup_percentage = db.Column(db.Numeric(5, 2), default=Decimal('0.00'))  # Markup sui costi
    
    # Collegamento con sistema bandi
    funding_source = db.Column(db.String(100))  # "public_funding", "private", "internal", "mixed"
    funding_application_id = db.Column(db.Integer, db.ForeignKey('funding_applications.id'))
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    tasks = db.relationship('Task', backref='project', lazy='dynamic', cascade='all, delete-orphan')
    timesheet_entries = db.relationship('TimesheetEntry', backref='project', lazy='dynamic')
    client = db.relationship('Client', backref='projects')
    contract = db.relationship('Contract', backref='projects')
    # funding_application = db.relationship('FundingApplication', foreign_keys='FundingApplication.linked_project_id', backref='linked_projects')

    def __repr__(self):
        return f'<Project {self.name}>'

    @property
    def team_members(self):
        """Compatibilità retroattiva - team members tramite project_resources"""
        return [pr.user for pr in self.resources]

    def add_team_member(self, user, role=None, allocation_percentage=100.0):
        """Metodo helper per aggiungere team member"""
        existing = ProjectResource.query.filter_by(
            project_id=self.id, user_id=user.id
        ).first()
        
        if not existing:
            resource = ProjectResource(
                project_id=self.id,
                user_id=user.id,
                role=role,
                allocation_percentage=allocation_percentage
            )
            db.session.add(resource)
            return resource
        else:
            # Aggiorna se necessario
            if role and existing.role != role:
                existing.role = role
            if allocation_percentage != existing.allocation_percentage:
                existing.allocation_percentage = allocation_percentage
            return existing

    def remove_team_member(self, user):
        """Metodo helper per rimuovere team member"""
        resource = ProjectResource.query.filter_by(
            project_id=self.id, user_id=user.id
        ).first()
        if resource:
            db.session.delete(resource)
            return True
        return False

    def get_team_member_role(self, user):
        """Ottiene il ruolo di un team member"""
        resource = ProjectResource.query.filter_by(
            project_id=self.id, user_id=user.id
        ).first()
        return resource.role if resource else None

    def get_team_member_allocation(self, user):
        """Ottiene l'allocazione di un team member"""
        resource = ProjectResource.query.filter_by(
            project_id=self.id, user_id=user.id
        ).first()
        return resource.allocation_percentage if resource else 0.0

    @db.validates('budget', 'expenses')
    def validate_financial_amounts(self, key, value):
        """Valida importi finanziari"""
        if value is not None and value < 0:
            if key == 'budget':
                raise ValueError("Budget deve essere positivo")
            elif key == 'expenses':
                raise ValueError("Expenses non possono essere negative")
        return value

    @db.validates('client_daily_rate')
    def validate_daily_rate(self, key, value):
        """Valida tariffa giornaliera"""
        if value is not None:
            if value <= 0:
                raise ValueError("Daily rate deve essere positiva")
            if value > 5000:  # >€5000/giorno sembra eccessivo
                raise ValueError("Daily rate sembra eccessiva")
        return value

    @db.validates('markup_percentage')
    def validate_markup_percentage(self, key, value):
        """Valida markup percentage"""
        if value is not None:
            if value < 0:
                raise ValueError("Markup percentage non può essere negativo")
            if value > 500:  # >500% sembra eccessivo
                raise ValueError("Markup percentage sembra eccessivo")
        return value

    @db.validates('start_date', 'end_date')
    def validate_project_dates(self, key, value):
        """Valida coerenza date progetto"""
        if key == 'end_date' and value and self.start_date:
            if value <= self.start_date:
                raise ValueError("End date deve essere successiva a start date")
        elif key == 'start_date' and value and self.end_date:
            if value >= self.end_date:
                raise ValueError("Start date deve essere precedente a end date")
        return value

    @property
    def remaining_budget(self):
        return self.budget - self.expenses


class Task(db.Model):
    __tablename__ = 'tasks'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False)
    assignee_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    status = db.Column(db.String(20), default='todo')  # todo, in-progress, review, done
    priority = db.Column(db.String(20), default='medium')  # low, medium, high, urgent
    start_date = db.Column(db.Date)  # Data di inizio pianificata
    due_date = db.Column(db.Date)    # Data di fine pianificata
    estimated_hours = db.Column(db.Numeric(6, 2))  # Ore stimate per completare il task
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    assignee = db.relationship('User', backref='assigned_tasks')

    def __repr__(self):
        return f'<Task {self.name}>'

    @property
    def actual_hours(self):
        """Calcola le ore effettive lavorate sul task dai timesheet"""
        return sum(entry.hours for entry in self.timesheet_entries)

    @property
    def hours_variance(self):
        """Calcola la varianza tra ore stimate e ore effettive"""
        if not self.estimated_hours or self.actual_hours == 0:
            return None
        return self.actual_hours - self.estimated_hours

    @property
    def hours_efficiency(self):
        """Calcola l'efficienza in percentuale (stimate/effettive * 100)"""
        if not self.estimated_hours or self.actual_hours == 0:
            return None
        return (self.estimated_hours / self.actual_hours) * 100

    @db.validates('estimated_hours')
    def validate_estimated_hours(self, key, value):
        """Valida ore stimate"""
        if value is not None:
            if value <= 0:
                raise ValueError("Estimated hours devono essere positive")
            if value > 1000:  # >1000h per un task sembra eccessivo
                raise ValueError("Estimated hours eccessive per singolo task")
        return value

    @db.validates('start_date', 'due_date')
    def validate_task_dates(self, key, value):
        """Valida coerenza date task"""
        if key == 'due_date' and value and self.start_date:
            if value <= self.start_date:
                raise ValueError("Due date deve essere successiva a start date")
        elif key == 'start_date' and value and self.due_date:
            if value >= self.due_date:
                raise ValueError("Start date deve essere precedente a due date")
        return value

    @property
    def duration_days(self):
        """Calcola la durata pianificata in giorni"""
        if not self.start_date or not self.due_date:
            return None
        return (self.due_date - self.start_date).days + 1


class TaskDependency(db.Model):
    __tablename__ = 'task_dependencies'

    id = db.Column(db.Integer, primary_key=True)
    task_id = db.Column(db.Integer, db.ForeignKey('tasks.id'), nullable=False)  # Campo DB reale
    depends_on_id = db.Column(db.Integer, db.ForeignKey('tasks.id'), nullable=False)  # Campo DB reale

    # Relationships
    task = db.relationship('Task', foreign_keys=[task_id], backref='dependencies')
    depends_on = db.relationship('Task', foreign_keys=[depends_on_id], backref='dependents')

    @db.validates('task_id', 'depends_on_id')
    def validate_dependency(self, key, value):
        """Valida dipendenza task"""
        if key == 'depends_on_id' and value and self.task_id:
            if value == self.task_id:
                raise ValueError("Task non può dipendere da sé stesso")
        elif key == 'task_id' and value and self.depends_on_id:
            if value == self.depends_on_id:
                raise ValueError("Task non può dipendere da sé stesso")
        return value

    def __repr__(self):
        return f'<TaskDependency {self.task_id} -> {self.depends_on_id}>'


class ProjectResource(db.Model):
    __tablename__ = 'project_resources'

    id = db.Column(db.Integer, primary_key=True)
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    allocation_percentage = db.Column(db.Integer, default=100)
    role = db.Column(db.String(50))

    # Relationships
    project = db.relationship('Project', backref='resources')
    # user relationship è definito in user.py

    @db.validates('allocation_percentage')
    def validate_allocation_percentage(self, key, value):
        """Valida percentuale allocazione"""
        if value is not None:
            if value <= 0:
                raise ValueError("Allocation percentage deve essere positiva")
            if value > 100:
                raise ValueError("Allocation percentage non può superare 100%")
        return value

    def __repr__(self):
        return f'<ProjectResource Project:{self.project_id} User:{self.user_id} {self.allocation_percentage}%>'


class ProjectKPI(db.Model):
    __tablename__ = 'project_kpis'

    """KPI specifici del progetto"""
    id = db.Column(db.Integer, primary_key=True)
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False)
    kpi_id = db.Column(db.Integer, db.ForeignKey('kpis.id'), nullable=False)  # Fixed foreign key
    target_value = db.Column(db.Numeric(12, 2))
    current_value = db.Column(db.Numeric(12, 2), default=Decimal('0.00'))

    # Relationships
    project = db.relationship('Project', backref='kpis')
    kpi = db.relationship('KPI', backref='project_kpis')

    def __repr__(self):
        return f'<ProjectKPI {self.id} - Project {self.project_id} KPI {self.kpi_id}>'

    @property
    def performance_percentage(self):
        if not self.target_value:
            return None
        return (self.current_value / self.target_value) * 100


class ProjectExpense(db.Model):
    __tablename__ = 'project_expenses'

    """Spese del progetto"""
    id = db.Column(db.Integer, primary_key=True)
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    category = db.Column(db.String(50), nullable=False)
    description = db.Column(db.String(255), nullable=False)
    amount = db.Column(db.Numeric(10, 2), nullable=False)
    billing_type = db.Column(db.String(20))  # Campo DB reale
    date = db.Column(db.Date, nullable=False)  # Campo DB reale
    receipt_path = db.Column(db.String(255))
    status = db.Column(db.String(20))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    project = db.relationship('Project', backref='project_expenses')
    user = db.relationship('User', foreign_keys=[user_id], backref='submitted_expenses')

    def __repr__(self):
        return f'<ProjectExpense {self.project_id}: {self.amount}>'


class ProjectKPITemplate(db.Model):
    __tablename__ = 'project_kpi_templates'

    """Template per KPI di progetto"""
    id = db.Column(db.Integer, primary_key=True)
    project_type = db.Column(db.String(50))
    kpi_name = db.Column(db.String(100))
    target_min = db.Column(db.Float)
    target_max = db.Column(db.Float)
    warning_threshold = db.Column(db.Float)
    unit = db.Column(db.String(10))
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<ProjectKPITemplate {self.project_type}.{self.kpi_name}>'

    @property
    def display_name(self):
        """Nome leggibile del KPI"""
        names = {
            'margin_percentage': 'Margine Netto',
            'utilization_rate': 'Utilization Rate',
            'cost_per_hour': 'Costo per Ora',
            'cost_revenue_ratio': 'Rapporto C/R'
        }
        return names.get(self.kpi_name, self.kpi_name)

    def to_dict(self):
        return {
            'id': self.id,
            'project_type': self.project_type,
            'kpi_name': self.kpi_name,
            'display_name': self.display_name,
            'target_min': self.target_min,
            'target_max': self.target_max,
            'warning_threshold': self.warning_threshold,
            'unit': self.unit,
            'description': self.description,
            'is_active': self.is_active
        }


class ProjectKPITarget(db.Model):
    __tablename__ = 'project_kpi_targets'
    
    """Target KPI per progetto (istanza di un template)"""
    id = db.Column(db.Integer, primary_key=True)
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False)
    kpi_template_id = db.Column(db.Integer, db.ForeignKey('project_kpi_templates.id'), nullable=False)
    
    # Valori target e attuali
    target_value = db.Column(db.Float)
    current_value = db.Column(db.Float, default=0)
    
    # Soglie per alerts
    warning_threshold = db.Column(db.Float)  # Percentuale per warning (es. 80%)
    critical_threshold = db.Column(db.Float)  # Percentuale per critical (es. 90%)
    
    # Tracking
    last_calculated_at = db.Column(db.DateTime)
    status = db.Column(db.String(20), default='on_track')  # on_track, warning, critical, completed
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    project = db.relationship('Project', backref='kpi_targets')
    template = db.relationship('ProjectKPITemplate', backref='project_instances')

    def __repr__(self):
        return f'<ProjectKPITarget Project:{self.project_id} KPI:{self.kpi_template_id}>'

    @property
    def achievement_percentage(self):
        if not self.target_value or self.target_value == 0:
            return 0
        return round((self.current_value / self.target_value) * 100, 2)
    
    @property
    def display_name(self):
        """Nome leggibile del KPI"""
        if self.template:
            names = {
                'margin_percentage': 'Margine Netto',
                'utilization_rate': 'Utilization Rate',
                'cost_per_hour': 'Costo per Ora',
                'cost_revenue_ratio': 'Rapporto C/R'
            }
            return names.get(self.template.kpi_name, self.template.kpi_name)
        return 'KPI Non Definito'

    def update_status(self):
        """Aggiorna lo stato basato sui threshold"""
        percentage = self.achievement_percentage
        
        if percentage >= 100:
            self.status = 'completed'
        elif self.critical_threshold and percentage >= self.critical_threshold:
            self.status = 'critical'
        elif self.warning_threshold and percentage >= self.warning_threshold:
            self.status = 'warning'
        else:
            self.status = 'on_track'
        
        return self.status


class ProjectFundingLink(db.Model):
    """Collegamento tra progetti e finanziamenti"""
    __tablename__ = 'project_funding_links'
    
    id = db.Column(db.Integer, primary_key=True)
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False)
    funding_application_id = db.Column(db.Integer, db.ForeignKey('funding_applications.id'), nullable=False)
    allocation_percentage = db.Column(db.Float, default=100.0)  # Percentuale del progetto coperta dal finanziamento
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    project = db.relationship('Project', backref='funding_links')
    funding_application = db.relationship('FundingApplication', backref='project_links')
    
    # Unique constraint
    __table_args__ = (
        db.UniqueConstraint('project_id', 'funding_application_id', name='unique_project_funding'),
    )
    
    def __repr__(self):
        return f'<ProjectFundingLink Project:{self.project_id} Funding:{self.funding_application_id}>'