# CEO Insights - Configurazione API

## Modelli Perplexity Supportati

Il sistema CEO Insights utilizza i modelli Perplexity più recenti per la market intelligence:

### Modalità Sonar Pro (Veloce)
- **Modello**: `sonar-pro` (200k context)
- **Utilizzo**: Quick strategic insights (1-2 minuti)
- **Configurazione**: `PERPLEXITY_SONAR_MODEL=sonar-pro`

### Modalità Sonar Deep (Approfondita)  
- **Modello**: `sonar-deep-research` (128k context)
- **Utilizzo**: Comprehensive market analysis (3-5 minuti)
- **Configurazione**: `PERPLEXITY_SONAR_DEEP_MODEL=sonar-deep-research`

### Modalità Advanced Reasoning (Futura)
- **Modello**: `sonar-reasoning-pro` (128k context)
- **Utilizzo**: Complex strategic analysis with advanced reasoning
- **Configurazione**: `PERPLEXITY_REASONING_MODEL=sonar-reasoning-pro`

## Configurazione Variabili Ambiente

### Obbligatorie
```bash
# API Keys richieste
OPENAI_API_KEY=sk-xxx
PERPLEXITY_API_KEY=pplx-xxx
```

### Opzionali (con valori di default)
```bash
# OpenAI Models per CEO Insights
OPENAI_CEO_PRO_MODEL=gpt-4o-mini          # Sonar Pro mode
OPENAI_CEO_DEEP_MODEL=o3-mini             # Sonar Deep synthesis

# Perplexity Models per Market Intelligence  
PERPLEXITY_SONAR_MODEL=sonar-pro          # Pro mode (200k context)
PERPLEXITY_SONAR_DEEP_MODEL=sonar-deep-research    # Deep mode (128k context)
PERPLEXITY_REASONING_MODEL=sonar-reasoning-pro     # Advanced reasoning (128k context)
```

## Modalità di Funzionamento

### 🔥 Sonar Pro Mode
1. **Market Research**: Perplexity `sonar-pro` (200k context, 2000 tokens)
2. **Strategic Analysis**: OpenAI `gpt-4o-mini` 
3. **Sintesi**: Direct response
4. **Tempo**: 1-2 minuti
5. **Costo**: Basso

### 🔬 Sonar Deep Mode  
1. **Market Research**: Perplexity `sonar-deep-research` (128k context, 4000 tokens)
2. **Strategic Analysis**: OpenAI `gpt-4o-mini`
3. **Sintesi**: OpenAI `o3-mini` (con fallback a `gpt-4o-mini`)
4. **Tempo**: 3-5 minuti  
5. **Costo**: Medio-Alto

## Migliori Pratiche

### Utilizzo Sonar Pro
- Quick market updates
- Immediate strategic questions
- Daily briefings
- Competitive intelligence rapida

### Utilizzo Sonar Deep
- Comprehensive market analysis
- Strategic planning sessions
- Investment decisions
- Market entry analysis
- Merger & acquisition research

## Monitoraggio e Troubleshooting

### Log da Monitorare
```bash
# Successo Perplexity
✅ SONAR RESEARCH COMPLETED: 3247 characters using sonar-pro

# Errore configurazione
❌ PERPLEXITY API KEY NOT CONFIGURED
❌ CEO insights require Perplexity API key

# Fallback modello
⚠️ o3-mini not available, using gpt-4o-mini for synthesis
```

### Errori Comuni
- **API Key mancante**: Verificare `OPENAI_API_KEY` e `PERPLEXITY_API_KEY`
- **Modello non supportato**: Usare solo i modelli dalla lista ufficiale
- **Timeout**: Aumentare timeout per research complesse
- **Rate limiting**: Implementare retry logic se necessario

## Performance e Costi

### Context Length per Model
| Model | Context Length | Tipo | Costo |
|-------|----------------|------|-------|
| sonar-pro | 200k | Chat Completion | Medio |
| sonar-deep-research | 128k | Chat Completion | Alto |
| sonar-reasoning-pro | 128k | Chat Completion | Alto |
| sonar-reasoning | 128k | Chat Completion | Medio |
| sonar | 128k | Chat Completion | Basso |

### Raccomandazioni
- **Sviluppo**: Usa `sonar` per testing
- **Produzione Pro**: Usa `sonar-pro` per bilanciare velocità/qualità  
- **Produzione Deep**: Usa `sonar-deep-research` per analisi critiche
- **Future**: `sonar-reasoning-pro` per ragionamento avanzato