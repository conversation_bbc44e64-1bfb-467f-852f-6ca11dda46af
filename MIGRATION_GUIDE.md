# 📋 Guida Migrazione Database - Normalizzazione Project Team

## 🎯 **Obiettivo**

Normalizzare la gestione dei team di progetto eliminando la ridondanza tra:
- `project_team` (association table semplice)
- `project_resources` (tabella completa con allocazioni)

E rimuovere la tabella orfana `company_communications`.

## 📊 **Situazione Attuale**

### ❌ **Problemi Identificati**

1. **Ridondanza Dati**: 
   - `project_team` gestisce membership semplice
   - `project_resources` gestisce allocazioni avanzate
   - Stesso utente può essere in entrambe le tabelle

2. **Tabella Orfana**:
   - `company_communications` non ha modello SQLAlchemy
   - Non è utilizzata dal sistema (si usa `news`)

### ✅ **Soluzione**

- **Unificare** tutto in `project_resources`
- **Eliminare** `project_team` 
- **Eliminare** `company_communications`
- **Mantenere compatibilità** con codice esistente

## 🚀 **Processo di Migrazione**

### **STEP 1: Backup Database**
```bash
# PostgreSQL
pg_dump -h localhost -U username -d database_name > backup_$(date +%Y%m%d_%H%M%S).sql

# SQLite (se in sviluppo)
cp instance/datportal.db instance/datportal_backup_$(date +%Y%m%d_%H%M%S).db
```

### **STEP 2: Eseguire Test Pre-Migrazione**
```bash
cd backend
python tests/test_project_team_migration.py
```

### **STEP 3: Eseguire Script Migrazione**
```bash
cd backend
python -m db.migrate_project_team_normalization
```

### **STEP 4: Verificare Migrazione**
```bash
# Test post-migrazione
python tests/test_project_team_migration.py

# Verifica manuale
python -c "
from app import create_app
from models import Project, User, ProjectResource
app = create_app()
with app.app_context():
    project = Project.query.first()
    print(f'Team members: {len(project.team_members)}')
    print(f'Resources: {len(project.resources)}')
"
```

## 📝 **Modifiche Apportate**

### **1. Modelli SQLAlchemy**

#### **User Model** (`backend/models_split/user.py`)
```python
# PRIMA
projects = db.relationship('Project', secondary=project_team, backref='team_members')

# DOPO  
project_resources = db.relationship('ProjectResource', backref='user', lazy='dynamic')

@property
def projects(self):
    """Compatibilità retroattiva"""
    return [pr.project for pr in self.project_resources]
```

#### **Project Model** (`backend/models_split/projects.py`)
```python
# AGGIUNTO
@property
def team_members(self):
    """Compatibilità retroattiva"""
    return [pr.user for pr in self.resources]

def add_team_member(self, user, role=None, allocation_percentage=100.0):
    """Metodo helper per aggiungere team member"""
    # Implementazione...

def remove_team_member(self, user):
    """Metodo helper per rimuovere team member"""
    # Implementazione...
```

### **2. Blueprint API**

#### **Projects API** (`backend/blueprints/api/projects.py`)
```python
# Aggiornati endpoint:
# POST /api/projects/{id}/team
# DELETE /api/projects/{id}/team/{user_id}  
# PUT /api/projects/{id}/team/{user_id}
```

#### **Resources API** (`backend/blueprints/api/resources.py`)
```python
# Rimosso codice ridondante per project_team
```

### **3. Association Table**

#### **Associations** (`backend/models_split/associations.py`)
```python
# COMMENTATO (da rimuovere dopo test)
# project_team = db.Table('project_team', ...)
```

## 🔧 **Compatibilità Retroattiva**

### **Proprietà Mantenute**
- `project.team_members` → Restituisce utenti tramite `project_resources`
- `user.projects` → Restituisce progetti tramite `project_resources`
- `user in project.team_members` → Funziona ancora

### **Nuovi Metodi**
- `project.add_team_member(user, role, allocation)`
- `project.remove_team_member(user)`
- `user.get_project_role(project)`
- `user.get_project_allocation(project)`

## 🧪 **Test**

### **Test Automatici**
```bash
# Test migrazione
python backend/tests/test_project_team_migration.py

# Test integrazione
python -m pytest backend/tests/api/test_projects.py -v
python -m pytest backend/tests/api/test_resources.py -v
```

### **Test Manuali**
1. **Frontend**: Verifica gestione team progetti
2. **API**: Test endpoint team management
3. **Permessi**: Verifica can_view_project
4. **Timesheet**: Verifica associazioni utente-progetto

## 📊 **Verifica Post-Migrazione**

### **Query di Controllo**
```sql
-- Verifica che project_team non esista più
SELECT COUNT(*) FROM information_schema.tables 
WHERE table_name = 'project_team';

-- Verifica che company_communications non esista più  
SELECT COUNT(*) FROM information_schema.tables 
WHERE table_name = 'company_communications';

-- Verifica integrità project_resources
SELECT pr.project_id, pr.user_id, pr.role, pr.allocation_percentage,
       p.name as project_name, u.first_name, u.last_name
FROM project_resources pr
JOIN projects p ON pr.project_id = p.id
JOIN users u ON pr.user_id = u.id
ORDER BY pr.project_id, pr.user_id;
```

## 🚨 **Rollback (Se Necessario)**

### **Script di Rollback**
```sql
-- Solo se necessario ripristinare project_team
CREATE TABLE project_team (
    project_id INTEGER REFERENCES projects(id),
    user_id INTEGER REFERENCES users(id),
    role VARCHAR(50),
    PRIMARY KEY (project_id, user_id)
);

-- Popola da project_resources
INSERT INTO project_team (project_id, user_id, role)
SELECT project_id, user_id, role 
FROM project_resources;
```

## 📋 **Checklist Post-Migrazione**

- [ ] **Database**: Tabelle `project_team` e `company_communications` rimosse
- [ ] **Test**: Tutti i test passano
- [ ] **API**: Endpoint team management funzionanti
- [ ] **Frontend**: Gestione team progetti funzionante
- [ ] **Permessi**: Controlli accesso progetti funzionanti
- [ ] **Performance**: Nessuna regressione prestazioni
- [ ] **Documentazione**: Schema database aggiornato

## 🎉 **Benefici Post-Migrazione**

1. **Eliminata Ridondanza**: Un solo sistema per gestire team
2. **Dati Più Ricchi**: Allocazioni percentuali e ruoli dettagliati
3. **Codice Più Pulito**: Meno complessità nella gestione team
4. **Migliori Performance**: Meno JOIN necessari
5. **Maggiore Flessibilità**: Gestione allocazioni avanzate

## 📞 **Supporto**

In caso di problemi:
1. Controlla i log dell'applicazione
2. Esegui i test di verifica
3. Consulta questo documento
4. Ripristina dal backup se necessario

---

**Data Migrazione**: `$(date +%Y-%m-%d)`  
**Versione**: `1.0.0`  
**Responsabile**: `Sistema di Migrazione Automatica` 