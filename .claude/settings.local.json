{"permissions": {"allow": ["Bash(grep:*)", "<PERSON><PERSON>(python:*)", "Bash(npm run build:*)", "Bash(timeout 10s npm run dev)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(timeout:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(rg:*)", "Bash(rm:*)", "Bash(flask db migrate:*)", "Bash(PYTHONPATH=/home/<USER>/workspace/backend python -c \"from app import create_app, db; app = create_app(); print(''App created successfully'')\")", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm test)", "Bash(npm test:*)", "Bash(npm install)", "Bash(npm install:*)", "Bash(pip install:*)", "<PERSON><PERSON>(uv run:*)", "Bash(git push:*)", "Bash(node:*)", "Bash(npm run test:*)", "Bash(for file in *.vue)", "Bash(do)", "Bash(if grep -q \"alert(\" \"$file\")", "<PERSON><PERSON>(then)", "Bash(echo \"Processing $file\")", "Bash(fi)", "Bash(done)", "<PERSON><PERSON>(curl:*)", "WebFetch(domain:www.ardec-spa.it)", "Bash(git add:*)"], "deny": []}}