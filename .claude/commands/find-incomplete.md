---
description: Scan the codebase for unfinished, placeholder, or mock components and propose next steps
allowed-tools:
  <PERSON><PERSON>(git grep:*),
  <PERSON><PERSON>(ripgrep:*),
  <PERSON><PERSON>(find:*),
  <PERSON><PERSON>(xargs:*),
  <PERSON><PERSON>(git rev-parse:*),
  <PERSON><PERSON>(cat:*),
  <PERSON><PERSON>(head:*),
  <PERSON><PERSON>(sort:*),
  <PERSON><PERSON>(wc:*)
---

## Context 

- Current commit: !`git rev-parse --short HEAD`

- Candidate fragments (keywords):  
  !`git grep -n -E 'TODO|FIXME|WIP|NOT(_|-)?(IMPLEMENT(ED)?|IMP)|PLACEHOLDER|DUMMY|MOCK|STUB|TBD' || true`

## Your task

1. Parse the grep output above; group hits into *features/components* that appear incomplete.  
2. For each group provide:  
   - **File & line(s)** where the gap sits (quote ±3 lines of context).  
   - **Risk level** (`blocker`, `important`, `cosmetic`).  
   - **Effort estimate** (`S`, `M`, `L`).  
3. Produce an **action plan**: concrete steps, suggested owner (if obvious), and any quick wins (e.g., delete dead code).  
4. Output  
   - A Markdown table summarising all groups.  
   - A bullet list of the **top-5 critical items**.  
5. End the response with a `#next-actions` heading so it’s easy to jump to with editors.