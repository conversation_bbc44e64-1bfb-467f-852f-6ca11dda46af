# Flask Configuration
FLASK_DEBUG=True
DATABASE_URL=postgresql://username:password@localhost/dbname

# AI Configuration  
OPENAI_API_KEY=your_openai_api_key_here
PERPLEXITY_API_KEY=your_perplexity_api_key_here

# OpenAI Model Configuration
# Available models: gpt-4o-mini, gpt-4o, gpt-3.5-turbo
# gpt-4o-mini is recommended for production (faster, cheaper)
OPENAI_DEFAULT_MODEL=gpt-4o-mini
OPENAI_ANALYSIS_MODEL=gpt-4o-mini  # For certification analysis, CV parsing, etc.
OPENAI_CHAT_MODEL=gpt-4o-mini      # For chat completions and general queries

# Perplexity Model Configuration
PERPLEXITY_DEFAULT_MODEL=sonar

# Email Configuration - Tophost SMTP
MAIL_SERVER=mail.tophost.it
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USE_SSL=False
MAIL_USERNAME=yourdomain.it  # MAILBOX name (not full email)
MAIL_PASSWORD=your_mailbox_password
MAIL_DEFAULT_SENDER=<EMAIL>

# Session Configuration
SESSION_COOKIE_SECURE=False  # Set to True in production with HTTPS

ENABLE_ENGAGEMENT_SYSTEM=false              # Default disabled                                 
ENGAGEMENT_POINTS_MULTIPLIER=1.0             # Moltiplicatore punti                            
ENGAGEMENT_BATCH_PROCESSING_INTERVAL=900     # 15min processing                                
ENGAGEMENT_LEADERBOARD_LIMIT=50              # Top 50                                          
ENGAGEMENT_CLEANUP_RETENTION_DAYS=365        # 1 anno retention                                
ENGAGEMENT_NOTIFICATION_FREQUENCY=daily      # daily/weekly/disabled                          
ENGAGEMENT_STREAK_BONUS_ENABLED=true         # Bonus streak                                    
ENGAGEMENT_TEAM_CHALLENGES_ENABLED=true      # Sfide team      

SONAR_TOKEN=                                                 
SONAR_HOST_URL=http://localhost:9000 
SONAR_PROJECT_KEY=datportal   

# CACHE
REDIS_URL = "redis://xxxx" 
CACHE_TYPE=simple #simple or redis

# Configurazione per i test
# Fatture in Cloud
FIC_APP_ID=1480770
FIC_CLIENT_ID=xxxxx
FIC_REDIRECT_URI=http://localhost:8080/callback
FIC_BASE_URL=https://api-v2.fattureincloud.it
FIC_ACCESS_TOKEN=a/xxxxxxxxx.xxxxxx.xxxxxxx

# Google OAuth
GOOGLE_CLIENT_ID=xxxxx
GOOGLE_CLIENT_SECRET=xxxxx

# Microsoft OAuth (da configurare)                                                                                                
MICROSOFT_CLIENT_ID=your-microsoft-app-id                                                                                         
MICROSOFT_CLIENT_SECRET=your-microsoft-app-secret 

OAUTH_REDIRECT_URI=http://localhost:5000/api/auth/oauth/callback
OAUTH_REQUIRE_EXISTING_USER=true


# AI Configuration
OPENAI_CEO_PRO_MODEL=gpt-4o-mini
OPENAI_CEO_DEEP_MODEL=o3-mini

# Sonar Pro Mode (Veloce)
PERPLEXITY_SONAR_MODEL=sonar-pro          # 200k context, veloce
# Sonar Deep Mode (Approfondita)  
PERPLEXITY_SONAR_DEEP_MODEL=sonar-deep-research    # 128k context, comprehensive
# Advanced Reasoning (Futuro)
PERPLEXITY_REASONING_MODEL=sonar-reasoning-pro     # 128k context, advanced reasoning