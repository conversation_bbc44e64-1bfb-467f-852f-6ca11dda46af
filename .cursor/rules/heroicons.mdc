---
description:
globs:
alwaysApply: false
---
# **HeroIcon System Usage**

Questo progetto utilizza un sistema di icone centralizzato basato su Heroicons con mappatura degli alias.

## **Struttura del Sistema**

- **Componente principale**: `HeroIcon.vue` in `frontend/src/components/icons/`
- **Mappatura alias**: `IconLibrary.js` in `frontend/src/components/icons/`
- **Icone supportate**: Heroicons v2 (outline e solid)

## **Come Usare le Icone**

### **✅ Utilizzo Corretto**

```vue
<template>
  <!-- Icona con nome mappato -->
  <HeroIcon name="user" size="md" />
  
  <!-- Icona con classe personalizzata -->
  <HeroIcon name="check-circle" size="lg" class="text-green-500" />
  
  <!-- Icona con alias -->
  <HeroIcon name="search" size="sm" class="mr-2" />
  
  <!-- Icona dinamica -->
  <HeroIcon :name="iconName" size="xl" />
</template>

<script setup>
import HeroIcon from '@/components/icons/HeroIcon.vue'
</script>
```

### **❌ Errori Comuni da Evitare**

```vue
<!-- SBAGLIATO: Nome icona non mappato -->
<HeroIcon name="LightningBoltIcon" />

<!-- SBAGLIATO: Nomi vecchi Heroicons v1 -->
<HeroIcon name="lightning-bolt-icon" />

<!-- SBAGLIATO: Importazione diretta Heroicons -->
import { LightningBoltIcon } from '@heroicons/vue/24/outline'
```

## **Nomi Icone Disponibili**

### **Icone Comuni Mappate in IconLibrary.js**

```javascript
// Azioni
'add': 'plus',
'search': 'magnifying-glass',
'edit': 'pencil',
'delete': 'trash',
'save': 'check',

// Navigazione  
'menu': 'bars-3',
'back': 'arrow-left',
'next': 'chevron-right',

// Status
'success': 'check-circle',
'error': 'x-circle',
'warning': 'exclamation-triangle',
'loading': 'arrow-path',

// Business
'user': 'user',
'project': 'folder',
'dashboard': 'squares-plus',
'funding': 'banknotes',
'lightning-bolt': 'bolt',  // ⚠️ Corretto mapping
```

## **Aggiungere Nuove Icone**

### **1. Verificare Esistenza in Heroicons**
- Controllare su [heroicons.com](https://heroicons.com)
- Usare il nome **esatto** da Heroicons v2

### **2. Aggiungere Alias in IconLibrary.js**

```javascript
// In frontend/src/components/icons/IconLibrary.js
export const iconAliases = {
  // ... existing aliases ...
  
  // Nuove icone
  'my-custom-name': 'heroicons-exact-name',
  'opportunity': 'light-bulb',
  'grants': 'gift',
}
```

### **3. Testare l'Icona**

```vue
<template>
  <!-- Test della nuova icona -->
  <HeroIcon name="my-custom-name" size="md" />
</template>
```

## **Proprietà Supportate**

### **name** (required)
- **Tipo**: `String`
- **Valore**: Nome dell'icona o alias da IconLibrary.js
- **Esempio**: `"user"`, `"search"`, `"lightning-bolt"`

### **size** (optional)
- **Tipo**: `String`
- **Valori**: `"xs"`, `"sm"`, `"md"`, `"lg"`, `"xl"`, `"2xl"`
- **Default**: `"md"`

### **class** (optional)
- **Tipo**: `String`
- **Uso**: Classi Tailwind per colori, margini, etc.
- **Esempio**: `"text-blue-500 mr-2"`

## **Esempi Pratici**

### **Dashboard Stats**
```vue
<div class="flex items-center gap-2">
  <HeroIcon name="currency-euro" size="sm" class="text-green-600" />
  <span>€125,000</span>
</div>
```

### **Buttons con Icone**
```vue
<button class="btn-primary flex items-center gap-2">
  <HeroIcon name="plus" size="sm" />
  <span>Aggiungi</span>
</button>
```

### **Status Indicators**
```vue
<div class="flex items-center gap-2">
  <HeroIcon name="check-circle" size="sm" class="text-green-500" />
  <span>Completato</span>
</div>
```

## **Troubleshooting**

### **Icona Non Appare**
1. **Controllare mapping** in `IconLibrary.js`
2. **Verificare nome** su heroicons.com
3. **Aggiungere alias** se necessario

### **Icona "Tutta Blu"**
- **Problema**: Icona non trovata, viene usato fallback
- **Soluzione**: Verificare nome in `iconAliases`

### **Console Error: "Icon not found"**
- **Causa**: Nome icona non esiste in Heroicons
- **Fix**: Usare nome corretto o aggiungere mapping

## **Best Practices**

- **Usare sempre alias** invece di nomi Heroicons diretti
- **Mantenere consistenza** nelle dimensioni per elementi simili
- **Aggiungere nuovi alias** per icone usate frequentemente
- **Testare sempre** le nuove icone prima del commit
- **Documentare** nuovi alias aggiunti

## **Riferimenti**

- **Heroicons v2**: [heroicons.com](https://heroicons.com)
- **Componente**: [`HeroIcon.vue`](mdc:frontend/src/components/icons/HeroIcon.vue)
- **Mappatura**: [`IconLibrary.js`](mdc:frontend/src/components/icons/IconLibrary.js)
- **Design System**: [`design_system_plan.md`](mdc:docs/design_system_plan.md)
