import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useToast } from '@/composables/useToast'
import api from '@/utils/api'

export const useCrmStore = defineStore('crm', () => {
  const { showToast } = useToast()
  
  // State
  const clients = ref([])
  const contacts = ref([])
  const proposals = ref([])
  const contracts = ref([])
  const invoices = ref([])
  const loading = ref(false)
  const currentClient = ref(null)
  const currentProposal = ref(null)
  const currentContract = ref(null)

  // Pipeline stats
  const pipelineStats = ref({
    totalValue: 0,
    conversionRate: 0,
    avgCycleTime: 0,
    proposalsByStatus: {}
  })

  // Computed
  const clientsCount = computed(() => clients.value.length)
  const activeProposals = computed(() => 
    proposals.value.filter(p => ['draft', 'sent', 'negotiating'].includes(p.status))
  )
  const totalPipelineValue = computed(() => 
    activeProposals.value.reduce((sum, p) => sum + (p.value || 0), 0)
  )
  
  // Pipeline analytics
  const proposalsByStatus = computed(() => {
    const statusCounts = {}
    proposals.value.forEach(proposal => {
      statusCounts[proposal.status] = (statusCounts[proposal.status] || 0) + 1
    })
    return statusCounts
  })
  
  const pipelineValueByStatus = computed(() => {
    const statusValues = {}
    proposals.value.forEach(proposal => {
      const status = proposal.status
      statusValues[status] = (statusValues[status] || 0) + (proposal.value || 0)
    })
    return statusValues
  })

  // Actions - Clients
  const fetchClients = async (params = {}) => {
    loading.value = true
    try {
      const response = await api.get('/api/clients', { params })
      if (response.data.success) {
        clients.value = response.data.data.clients
      }
    } catch (error) {
      showToast('Errore nel caricamento clienti', 'error')
      console.error('Error fetching clients:', error)
    } finally {
      loading.value = false
    }
  }

  const getClient = async (id) => {
    loading.value = true
    try {
      const response = await api.get(`/api/clients/${id}`)
      if (response.data.success) {
        currentClient.value = response.data.data.client
        return response.data.data.client
      }
    } catch (error) {
      showToast('Errore nel caricamento cliente', 'error')
      console.error('Error fetching client:', error)
    } finally {
      loading.value = false
    }
  }

  const createClient = async (clientData) => {
    loading.value = true
    try {
      const response = await api.post('/api/clients', clientData)
      if (response.data.success) {
        clients.value.push(response.data.data)
        showToast('Cliente creato con successo', 'success')
        return response.data.data
      }
    } catch (error) {
      showToast('Errore nella creazione cliente', 'error')
      console.error('Error creating client:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateClient = async (id, clientData) => {
    loading.value = true
    try {
      const response = await api.put(`/api/clients/${id}`, clientData)
      if (response.data.success) {
        const index = clients.value.findIndex(c => c.id === id)
        if (index !== -1) {
          clients.value[index] = response.data.data
        }
        currentClient.value = response.data.data
        showToast('Cliente aggiornato con successo', 'success')
        return response.data.data
      }
    } catch (error) {
      showToast('Errore nell\'aggiornamento cliente', 'error')
      console.error('Error updating client:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const deleteClient = async (id) => {
    loading.value = true
    try {
      const response = await api.delete(`/api/clients/${id}`)
      if (response.data.success) {
        clients.value = clients.value.filter(c => c.id !== id)
        showToast('Cliente eliminato con successo', 'success')
      }
    } catch (error) {
      showToast('Errore nell\'eliminazione cliente', 'error')
      console.error('Error deleting client:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // Actions - Contacts
  const fetchContacts = async (clientId = null) => {
    loading.value = true
    try {
      const params = clientId ? { client_id: clientId } : {}
      const response = await api.get('/api/contacts', { params })
      if (response.data.success) {
        contacts.value = response.data.data.contacts
      }
    } catch (error) {
      showToast('Errore nel caricamento contatti', 'error')
      console.error('Error fetching contacts:', error)
    } finally {
      loading.value = false
    }
  }

  const createContact = async (contactData) => {
    loading.value = true
    try {
      const response = await api.post('/api/contacts', contactData)
      if (response.data.success) {
        contacts.value.push(response.data.data)
        showToast('Contatto creato con successo', 'success')
        return response.data.data
      }
    } catch (error) {
      showToast('Errore nella creazione contatto', 'error')
      console.error('Error creating contact:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateContact = async (id, contactData) => {
    loading.value = true
    try {
      const response = await api.put(`/api/contacts/${id}`, contactData)
      if (response.data.success) {
        const index = contacts.value.findIndex(c => c.id === id)
        if (index !== -1) {
          contacts.value[index] = response.data.data
        }
        showToast('Contatto aggiornato con successo', 'success')
        return response.data.data
      }
    } catch (error) {
      showToast('Errore nell\'aggiornamento contatto', 'error')
      console.error('Error updating contact:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const deleteContact = async (id) => {
    loading.value = true
    try {
      const response = await api.delete(`/api/contacts/${id}`)
      if (response.data.success) {
        contacts.value = contacts.value.filter(c => c.id !== id)
        showToast('Contatto eliminato con successo', 'success')
      }
    } catch (error) {
      showToast('Errore nell\'eliminazione contatto', 'error')
      console.error('Error deleting contact:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // Actions - Proposals
  const fetchProposals = async (params = {}) => {
    loading.value = true
    try {
      const response = await api.get('/api/proposals', { params })
      if (response.data.success) {
        proposals.value = response.data.data.proposals
        updatePipelineStats()
      }
    } catch (error) {
      showToast('Errore nel caricamento proposte', 'error')
      console.error('Error fetching proposals:', error)
    } finally {
      loading.value = false
    }
  }

  const getProposal = async (id) => {
    loading.value = true
    try {
      const response = await api.get(`/api/proposals/${id}`)
      if (response.data.success) {
        currentProposal.value = response.data.data
        return response.data.data
      }
    } catch (error) {
      showToast('Errore nel caricamento proposta', 'error')
      console.error('Error fetching proposal:', error)
    } finally {
      loading.value = false
    }
  }

  const createProposal = async (proposalData) => {
    loading.value = true
    try {
      const response = await api.post('/api/proposals', proposalData)
      if (response.data.success) {
        proposals.value.push(response.data.data)
        updatePipelineStats()
        showToast('Proposta creata con successo', 'success')
        return response.data.data
      }
    } catch (error) {
      showToast('Errore nella creazione proposta', 'error')
      console.error('Error creating proposal:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateProposal = async (id, proposalData) => {
    loading.value = true
    try {
      const response = await api.put(`/api/proposals/${id}`, proposalData)
      if (response.data.success) {
        const index = proposals.value.findIndex(p => p.id === id)
        if (index !== -1) {
          proposals.value[index] = response.data.data
        }
        currentProposal.value = response.data.data
        updatePipelineStats()
        // Toast rimosso qui per evitare duplicazione - gestito dal componente
        return response.data.data
      }
    } catch (error) {
      // Toast di errore rimosso qui per evitare duplicazione - gestito dal componente
      console.error('Error updating proposal:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateProposalStatus = async (id, status) => {
    loading.value = true
    try {
      const response = await api.patch(`/api/proposals/${id}/status`, { status })
      if (response.data.success) {
        const index = proposals.value.findIndex(p => p.id === id)
        if (index !== -1) {
          proposals.value[index] = { ...proposals.value[index], status }
        }
        updatePipelineStats()
        showToast('Stato proposta aggiornato', 'success')
      }
    } catch (error) {
      showToast('Errore nell\'aggiornamento stato', 'error')
      console.error('Error updating proposal status:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const deleteProposal = async (id) => {
    loading.value = true
    try {
      const response = await api.delete(`/api/proposals/${id}`)
      if (response.data.success) {
        proposals.value = proposals.value.filter(p => p.id !== id)
        updatePipelineStats()
        showToast('Proposta eliminata con successo', 'success')
      }
    } catch (error) {
      showToast('Errore nell\'eliminazione proposta', 'error')
      console.error('Error deleting proposal:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const generateProposalWithAI = async (generationData) => {
    loading.value = true
    try {
      const response = await api.post('/api/proposals/generate-ai', generationData)
      if (response.data.success) {
        showToast({
          type: 'success',
          title: 'Proposta generata con AI',
          message: 'La proposta è stata generata con successo. Controlla il pannello AI per vedere i dettagli e applicarli al form.',
          duration: 5000
        })
        return response.data.data
      }
    } catch (error) {
      showToast({
        type: 'error',
        title: 'Errore nella generazione AI',
        message: 'Si è verificato un errore durante la generazione della proposta. Riprova più tardi.',
        duration: 6000
      })
      console.error('Error generating proposal with AI:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const convertProposalToProject = async (proposalId, conversionData = {}) => {
    loading.value = true
    try {
      const response = await api.post(`/api/proposals/${proposalId}/convert-to-project`, conversionData)
      if (response.data.success) {
        // Aggiorna lo stato della proposta nella lista locale se presente
        const proposalIndex = proposals.value.findIndex(p => p.id === proposalId)
        if (proposalIndex !== -1) {
          // La proposta rimane accettata, ma potremmo aggiungere un flag "converted"
          proposals.value[proposalIndex] = { 
            ...proposals.value[proposalIndex], 
            converted_to_project: true,
            project_id: response.data.data.project.id
          }
        }
        
        showToast({
          type: 'success',
          title: 'Progetto creato con successo',
          message: `La proposta è stata convertita nel progetto "${response.data.data.project.name}". Vai alla sezione progetti per gestirlo.`,
          duration: 6000
        })
        return response.data.data
      }
    } catch (error) {
      showToast({
        type: 'error',
        title: 'Errore nella conversione',
        message: error.response?.data?.message || 'Si è verificato un errore durante la conversione della proposta.',
        duration: 6000
      })
      console.error('Error converting proposal to project:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const createContractFromProposal = async (proposalId, contractData = {}) => {
    loading.value = true
    try {
      const response = await api.post(`/api/proposals/${proposalId}/create-contract`, contractData)
      if (response.data.success) {
        // Aggiorna lo stato della proposta nella lista locale se presente
        const proposalIndex = proposals.value.findIndex(p => p.id === proposalId)
        if (proposalIndex !== -1) {
          proposals.value[proposalIndex] = { 
            ...proposals.value[proposalIndex], 
            converted_to_contract: true,
            contract_id: response.data.data.contract.id
          }
        }
        
        // Aggiorna la lista contratti se già caricata
        if (contracts.value.length > 0) {
          contracts.value.unshift(response.data.data.contract)
        }
        
        showToast({
          type: 'success',
          title: 'Contratto creato con successo',
          message: `La proposta è stata convertita nel contratto "${response.data.data.contract.title}". Ora puoi creare un progetto dal contratto.`,
          duration: 6000
        })
        return response.data.data
      }
    } catch (error) {
      showToast({
        type: 'error',
        title: 'Errore nella creazione contratto',
        message: error.response?.data?.message || 'Si è verificato un errore durante la creazione del contratto.',
        duration: 6000
      })
      console.error('Error creating contract from proposal:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // Actions - Contracts
  const fetchContracts = async (params = {}) => {
    loading.value = true
    try {
      const response = await api.get('/api/contracts', { params })
      if (response.data.success) {
        contracts.value = response.data.data.contracts
      }
    } catch (error) {
      showToast('Errore nel caricamento contratti', 'error')
      console.error('Error fetching contracts:', error)
    } finally {
      loading.value = false
    }
  }

  const getContract = async (id) => {
    loading.value = true
    try {
      const response = await api.get(`/api/contracts/${id}`)
      if (response.data.success) {
        currentContract.value = response.data.data.contract
        return response.data.data.contract
      }
    } catch (error) {
      showToast('Errore nel caricamento contratto', 'error')
      console.error('Error fetching contract:', error)
    } finally {
      loading.value = false
    }
  }

  const convertContractToProject = async (contractId, projectData = {}) => {
    loading.value = true
    try {
      const response = await api.post(`/api/contracts/${contractId}/create-project`, projectData)
      if (response.data.success) {
        // Aggiorna lo stato del contratto nella lista locale se presente
        const contractIndex = contracts.value.findIndex(c => c.id === contractId)
        if (contractIndex !== -1) {
          contracts.value[contractIndex] = { 
            ...contracts.value[contractIndex], 
            converted_to_project: true,
            project_id: response.data.data.project.id
          }
        }
        
        // Aggiorna il contratto corrente se è quello che stiamo convertendo
        if (currentContract.value?.id === contractId) {
          currentContract.value = {
            ...currentContract.value,
            converted_to_project: true,
            project_id: response.data.data.project.id
          }
        }
        
        showToast({
          type: 'success',
          title: 'Progetto creato con successo',
          message: `Il contratto è stato convertito nel progetto "${response.data.data.project.name}". Vai alla sezione progetti per gestirlo.`,
          duration: 6000
        })
        return response.data.data
      }
    } catch (error) {
      showToast({
        type: 'error',
        title: 'Errore nella conversione',
        message: error.response?.data?.message || 'Si è verificato un errore durante la conversione del contratto.',
        duration: 6000
      })
      console.error('Error converting contract to project:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // Helper functions
  const updatePipelineStats = () => {
    const proposalsByStatus = proposals.value.reduce((acc, proposal) => {
      acc[proposal.status] = (acc[proposal.status] || 0) + 1
      return acc
    }, {})

    pipelineStats.value = {
      totalValue: totalPipelineValue.value,
      conversionRate: calculateConversionRate(),
      avgCycleTime: calculateAvgCycleTime(),
      proposalsByStatus
    }
  }

  const calculateConversionRate = () => {
    const total = proposals.value.length
    const accepted = proposals.value.filter(p => p.status === 'accepted').length
    return total > 0 ? Math.round((accepted / total) * 100) : 0
  }

  const calculateAvgCycleTime = () => {
    const closedProposals = proposals.value.filter(p => 
      ['accepted', 'rejected'].includes(p.status) && p.sent_date
    )
    
    if (closedProposals.length === 0) return 0
    
    const totalDays = closedProposals.reduce((sum, proposal) => {
      const sentDate = new Date(proposal.sent_date)
      const closedDate = new Date(proposal.updated_at)
      const daysDiff = Math.ceil((closedDate - sentDate) / (1000 * 60 * 60 * 24))
      return sum + daysDiff
    }, 0)
    
    return Math.round(totalDays / closedProposals.length)
  }

  // Reset functions
  const resetCurrentClient = () => {
    currentClient.value = null
  }

  const resetCurrentProposal = () => {
    currentProposal.value = null
  }

  const resetCurrentContract = () => {
    currentContract.value = null
  }

  // Recent activities
  const recentActivities = ref([])

  const fetchRecentActivities = async (limit = 10) => {
    try {
      const response = await api.get('/api/dashboard/recent-activities', { 
        params: { limit } 
      })
      if (response.data.success) {
        recentActivities.value = response.data.data.activities
      }
    } catch (error) {
      console.error('Error fetching recent activities:', error)
      showToast('Errore nel caricamento attività recenti', 'error')
    }
  }

  // Dashboard data fetching
  const fetchDashboardData = async () => {
    try {
      await Promise.all([
        fetchClients(),
        fetchProposals(),
        fetchContracts(),
        fetchRecentActivities(10)
      ])
      updatePipelineStats()
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
      showToast('Errore nel caricamento dei dati dashboard', 'error')
    }
  }

  return {
    // State
    clients,
    contacts,
    proposals,
    contracts,
    invoices,
    loading,
    currentClient,
    currentProposal,
    currentContract,
    pipelineStats,
    
    // Computed
    clientsCount,
    activeProposals,
    totalPipelineValue,
    proposalsByStatus,
    pipelineValueByStatus,
    recentActivities,
    
    // Actions
    fetchClients,
    getClient,
    createClient,
    updateClient,
    deleteClient,
    fetchContacts,
    createContact,
    updateContact,
    deleteContact,
    fetchProposals,
    getProposal,
    createProposal,
    updateProposal,
    updateProposalStatus,
    deleteProposal,
    generateProposalWithAI,
    convertProposalToProject,
    createContractFromProposal,
    fetchContracts,
    getContract,
    convertContractToProject,
    updatePipelineStats,
    fetchDashboardData,
    fetchRecentActivities,
    resetCurrentClient,
    resetCurrentProposal,
    resetCurrentContract
  }
})