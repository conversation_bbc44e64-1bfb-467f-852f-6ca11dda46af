<template>
  <BaseModal
    :is-open="isOpen"
    :title="policy?.name || 'Dettaglio Policy'"
    size="xl"
    @close="$emit('close')"
  >
    <div v-if="policy" class="space-y-6">
      <!-- Header Info -->
      <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Tipo Policy
            </label>
            <span :class="['inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium', getPolicyTypeBadgeClass(policy.policy_type)]">
              {{ getPolicyTypeLabel(policy.policy_type) }}
            </span>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Status
            </label>
            <span :class="['inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium', getStatusBadgeClass(policy.status)]">
              {{ getStatusLabel(policy.status) }}
            </span>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Versione
            </label>
            <span class="text-sm text-gray-900 dark:text-gray-100">
              v{{ policy.version || '1.0' }}
            </span>
          </div>
        </div>
      </div>

      <!-- Basic Information -->
      <div class="space-y-4">
        <div v-if="policy.description">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Descrizione
          </label>
          <p class="text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
            {{ policy.description }}
          </p>
        </div>

        <div v-if="policy.framework">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Framework di Riferimento
          </label>
          <div class="flex items-center">
            <HeroIcon name="shield-check" size="sm" class="text-blue-600 dark:text-blue-400 mr-2" />
            <span class="text-sm text-gray-900 dark:text-gray-100">{{ policy.framework }}</span>
          </div>
        </div>
      </div>

      <!-- Dates -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div v-if="policy.effective_date">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Data Effettiva
          </label>
          <div class="flex items-center">
            <HeroIcon name="calendar" size="sm" class="text-green-600 dark:text-green-400 mr-2" />
            <span class="text-sm text-gray-900 dark:text-gray-100">
              {{ formatDate(policy.effective_date) }}
            </span>
          </div>
        </div>

        <div v-if="policy.expiry_date">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Data Scadenza
          </label>
          <div class="flex items-center">
            <HeroIcon name="calendar" size="sm" :class="getExpiryIconClass(policy.expiry_date)" />
            <span :class="['text-sm ml-2', getExpiryClass(policy.expiry_date)]">
              {{ formatDate(policy.expiry_date) }}
            </span>
          </div>
        </div>

        <div v-if="policy.next_review_date">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Prossima Revisione
          </label>
          <div class="flex items-center">
            <HeroIcon name="calendar" size="sm" class="text-orange-600 dark:text-orange-400 mr-2" />
            <span class="text-sm text-gray-900 dark:text-gray-100">
              {{ formatDate(policy.next_review_date) }}
            </span>
          </div>
        </div>
      </div>

      <!-- Policy Content -->
      <div v-if="policy.content">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Contenuto Policy
        </label>
        <div class="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-4 max-h-60 overflow-y-auto">
          <pre class="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">{{ policy.content }}</pre>
        </div>
      </div>

      <!-- Compliance Requirements -->
      <div v-if="policy.compliance_requirements">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Requisiti di Conformità
        </label>
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <pre class="text-sm text-blue-700 dark:text-blue-300 whitespace-pre-wrap">{{ policy.compliance_requirements }}</pre>
        </div>
      </div>

      <!-- Violation Penalties -->
      <div v-if="policy.violation_penalties">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Sanzioni per Violazione
        </label>
        <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <pre class="text-sm text-red-700 dark:text-red-300 whitespace-pre-wrap">{{ policy.violation_penalties }}</pre>
        </div>
      </div>

      <!-- Ownership -->
      <div v-if="policy.owner || policy.approver" class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div v-if="policy.owner">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Proprietario Policy
          </label>
          <div class="flex items-center">
            <HeroIcon name="user" size="sm" class="text-purple-600 dark:text-purple-400 mr-2" />
            <span class="text-sm text-gray-900 dark:text-gray-100">{{ policy.owner }}</span>
          </div>
        </div>

        <div v-if="policy.approver">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Approvatore
          </label>
          <div class="flex items-center">
            <HeroIcon name="check-circle" size="sm" class="text-green-600 dark:text-green-400 mr-2" />
            <span class="text-sm text-gray-900 dark:text-gray-100">{{ policy.approver }}</span>
          </div>
        </div>
      </div>

      <!-- Metadata -->
      <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs text-gray-500 dark:text-gray-400">
          <div v-if="policy.created_at">
            <span class="font-medium">Creato:</span>
            {{ formatDate(policy.created_at) }}
          </div>
          <div v-if="policy.updated_at">
            <span class="font-medium">Aggiornato:</span>
            {{ formatDate(policy.updated_at) }}
          </div>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <template #actions>
      <StandardButton
        variant="secondary"
        @click="$emit('close')"
      >
        Chiudi
      </StandardButton>
    </template>
  </BaseModal>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
import BaseModal from '@/components/ui/BaseModal.vue'
import StandardButton from '@/components/ui/StandardButton.vue'
import HeroIcon from '@/components/icons/HeroIcon.vue'

// Props
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  policy: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['close'])

// Utility functions
const getPolicyTypeLabel = (type) => {
  if (!type) return 'N/A'
  const labels = {
    security: 'Sicurezza',
    privacy: 'Privacy',
    operational: 'Operativa',
    hr: 'Risorse Umane',
    financial: 'Finanziaria'
  }
  return labels[type] || type
}

const getPolicyTypeBadgeClass = (type) => {
  const classes = {
    security: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-200',
    privacy: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-200',
    operational: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-200',
    hr: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-200',
    financial: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-200'
  }
  return classes[type] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-200'
}

const getStatusLabel = (status) => {
  if (!status) return 'N/A'
  const labels = {
    active: 'Attiva',
    draft: 'Bozza',
    review: 'In Revisione',
    expired: 'Scaduta'
  }
  return labels[status] || status
}

const getStatusBadgeClass = (status) => {
  const classes = {
    active: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-200',
    draft: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-200',
    review: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-200',
    expired: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-200'
  }
  return classes[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-200'
}

const getExpiryClass = (expiryDate) => {
  const now = new Date()
  const expiry = new Date(expiryDate)
  const daysUntilExpiry = Math.ceil((expiry - now) / (1000 * 60 * 60 * 24))
  
  if (daysUntilExpiry < 0) {
    return 'text-red-600 dark:text-red-400 font-medium'
  } else if (daysUntilExpiry <= 30) {
    return 'text-orange-600 dark:text-orange-400 font-medium'
  } else {
    return 'text-gray-900 dark:text-gray-100'
  }
}

const getExpiryIconClass = (expiryDate) => {
  const now = new Date()
  const expiry = new Date(expiryDate)
  const daysUntilExpiry = Math.ceil((expiry - now) / (1000 * 60 * 60 * 24))
  
  if (daysUntilExpiry < 0) {
    return 'text-red-600 dark:text-red-400 mr-2'
  } else if (daysUntilExpiry <= 30) {
    return 'text-orange-600 dark:text-orange-400 mr-2'
  } else {
    return 'text-gray-600 dark:text-gray-400 mr-2'
  }
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  try {
    const date = new Date(dateString)
    if (isNaN(date.getTime())) return '-'
    return date.toLocaleDateString('it-IT', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  } catch (error) {
    return '-'
  }
}
</script>