<template>
  <div class="relative">
    <button
      @click="showSearch = !showSearch"
      class="p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
    >
      <span class="sr-only">Cerca</span>
      <HeroIcon name="search" size="md" />
    </button>

    <!-- Search Modal -->
    <div 
      v-if="showSearch"
      class="fixed inset-0 z-50 overflow-y-auto"
      @click.self="showSearch = false"
    >
      <div class="flex items-start justify-center min-h-screen pt-16 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-black bg-opacity-25 transition-opacity"></div>
        
        <div class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
          <div>
            <div class="flex items-center">
              <div class="flex-1">
                <input
                  ref="searchInput"
                  v-model="searchQuery"
                  @input="handleSearch"
                  @keydown.escape="showSearch = false"
                  @keydown.enter="handleEnterKey"
                  @keydown.up="navigateResults(-1)"
                  @keydown.down="navigateResults(1)"
                  type="text"
                  placeholder="Cerca progetti, persone, documenti..."
                  class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                />
              </div>
              <button
                @click="showSearch = false"
                class="ml-3 p-2 text-gray-400 hover:text-gray-600"
              >
                <HeroIcon name="close" size="md" />
              </button>
            </div>
            
            <!-- Search Results -->
            <div v-if="searchResults.length > 0" class="mt-4 max-h-64 overflow-y-auto">
              <div class="space-y-1">
                <div
                  v-for="(result, index) in searchResults"
                  :key="result.id"
                  @click="selectResult(result)"
                  :class="[
                    'flex items-center px-3 py-2 rounded-md cursor-pointer',
                    index === selectedIndex ? 'bg-primary-50' : 'hover:bg-gray-50'
                  ]"
                >
                  <div class="flex-shrink-0">
                    <div :class="getResultIconClass(result.type)">
                      <HeroIcon :name="getResultIconName(result.type)" size="sm" />
                    </div>
                  </div>
                  <div class="ml-3 flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 truncate">{{ result.title }}</p>
                    <p class="text-xs text-gray-500 truncate">{{ result.description }}</p>
                  </div>
                  <div class="ml-2 text-xs text-gray-400">
                    {{ getResultTypeLabel(result.type) }}
                  </div>
                </div>
              </div>
            </div>
            
            <!-- No Results -->
            <div v-else-if="searchQuery && !isSearching" class="mt-4 text-center py-4">
              <p class="text-sm text-gray-500">Nessun risultato trovato</p>
            </div>
            
            <!-- Search Tips -->
            <div v-else-if="!searchQuery" class="mt-4 text-center py-4">
              <p class="text-xs text-gray-400">Inizia a digitare per cercare...</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import HeroIcon from '@/components/icons/HeroIcon.vue'
import api from '@/utils/api.js'

const router = useRouter()
const showSearch = ref(false)
const searchQuery = ref('')
const searchResults = ref([])
const selectedIndex = ref(-1)
const isSearching = ref(false)
const searchInput = ref(null)

// Search API configuration
const SEARCH_DEBOUNCE_MS = 300
let searchTimeout = null

watch(showSearch, async (value) => {
  if (value) {
    await nextTick()
    searchInput.value?.focus()
  } else {
    searchQuery.value = ''
    searchResults.value = []
    selectedIndex.value = -1
  }
})

async function handleSearch() {
  if (!searchQuery.value.trim()) {
    searchResults.value = []
    return
  }
  
  // Clear previous timeout
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  
  isSearching.value = true
  
  // Debounce search to avoid too many API calls
  searchTimeout = setTimeout(async () => {
    try {
      const response = await api.get('/search', {
        params: {
          q: searchQuery.value.trim(),
          limit: 8
        }
      })
      
      if (response.data.success) {
        searchResults.value = response.data.data || []
      } else {
        console.error('Search failed:', response.data.message)
        searchResults.value = []
      }
    } catch (error) {
      console.error('Search API error:', error)
      searchResults.value = []
      
      // Fallback: show a helpful message for empty results
      if (searchQuery.value.trim().length >= 2) {
        searchResults.value = [{
          id: 'error',
          type: 'error',
          title: 'Ricerca non disponibile',
          description: 'Il servizio di ricerca non è attualmente disponibile. Riprova più tardi.',
          path: '#'
        }]
      }
    } finally {
      selectedIndex.value = -1
      isSearching.value = false
    }
  }, SEARCH_DEBOUNCE_MS)
}

function navigateResults(direction) {
  if (searchResults.value.length === 0) return
  
  const newIndex = selectedIndex.value + direction
  if (newIndex >= 0 && newIndex < searchResults.value.length) {
    selectedIndex.value = newIndex
  }
}

function handleEnterKey() {
  if (selectedIndex.value >= 0 && searchResults.value[selectedIndex.value]) {
    selectResult(searchResults.value[selectedIndex.value])
  }
}

function selectResult(result) {
  // Don't navigate for error entries
  if (result.type === 'error' || result.path === '#') {
    return
  }
  
  showSearch.value = false
  router.push(result.path)
}

function getResultIconClass(type) {
  const classes = {
    project: 'h-6 w-6 rounded bg-primary-100 text-primary-600 flex items-center justify-center',
    person: 'h-6 w-6 rounded bg-green-100 text-green-600 flex items-center justify-center',
    document: 'h-6 w-6 rounded bg-yellow-100 text-yellow-600 flex items-center justify-center',
    task: 'h-6 w-6 rounded bg-purple-100 text-purple-600 flex items-center justify-center',
    error: 'h-6 w-6 rounded bg-red-100 text-red-600 flex items-center justify-center'
  }
  return classes[type] || classes.document
}

function getResultIconName(type) {
  const icons = {
    project: 'folder',
    person: 'user',
    document: 'document',
    task: 'clipboard-document-list',
    error: 'exclamation-triangle'
  }
  return icons[type] || 'document'
}

function getResultTypeLabel(type) {
  const labels = {
    project: 'Progetto',
    person: 'Persona',
    document: 'Documento',
    task: 'Task',
    error: 'Errore'
  }
  return labels[type] || 'Elemento'
}
</script>