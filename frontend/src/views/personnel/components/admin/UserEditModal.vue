<template>
  <BaseModal
    :show="show"
    title="Modifica Utente"
    size="lg"
    @close="$emit('close')"
  >
    <form @submit.prevent="saveUser" class="space-y-6">
      <!-- Basic Info -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <StandardInput
          v-model="formData.full_name"
          label="Nome Completo"
          required
          :error="errors.full_name"
        />

        <StandardInput
          v-model="formData.email"
          type="email"
          label="Email"
          required
          :error="errors.email"
        />
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <StandardInput
          v-model="formData.username"
          label="Username"
          required
          :error="errors.username"
        />

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Ruolo <span class="text-red-500">*</span>
          </label>
          <select
            v-model="formData.role"
            required
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="employee">Dipendente</option>
            <option value="manager">Manager</option>
            <option value="admin">Amministratore</option>
          </select>
          <p v-if="errors.role" class="mt-1 text-sm text-red-600">{{ errors.role }}</p>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Dipartimento
          </label>
          <select
            v-model="formData.department_id"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="">-- Seleziona Dipartimento --</option>
            <option v-for="dept in departments" :key="dept.id" :value="dept.id">
              {{ dept.name }}
            </option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Stato
          </label>
          <select
            v-model="formData.is_active"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option :value="true">Attivo</option>
            <option :value="false">Disattivato</option>
          </select>
        </div>
      </div>

      <!-- Additional Info -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <StandardInput
          v-model="formData.phone"
          type="tel"
          label="Telefono"
          :error="errors.phone"
        />

        <StandardInput
          v-model="formData.position"
          label="Posizione"
          :error="errors.position"
        />
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <StandardInput
          v-model.number="formData.hourly_rate"
          type="number"
          label="Tariffa Oraria (€)"
          min="0"
          step="0.01"
          :error="errors.hourly_rate"
        />

        <StandardInput
          v-model="formData.hire_date"
          type="date"
          label="Data Assunzione"
          :error="errors.hire_date"
        />
      </div>

      <StandardInput
        v-model="formData.bio"
        input-type="textarea"
        label="Biografia"
        :rows="3"
        :error="errors.bio"
      />

      <!-- Error Display -->
      <div v-if="error" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
        <div class="flex">
          <HeroIcon name="exclamation-triangle" size="sm" class="text-red-400 mr-2 mt-0.5" />
          <div>
            <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
              Errore nell'aggiornamento
            </h3>
            <p class="text-sm text-red-700 dark:text-red-300 mt-1">{{ error }}</p>
          </div>
        </div>
      </div>
    </form>

    <template #footer>
      <div class="flex justify-end space-x-3">
        <StandardButton
          variant="secondary"
          @click="$emit('close')"
        >
          Annulla
        </StandardButton>
        <StandardButton
          type="submit"
          :loading="loading"
          @click="saveUser"
        >
          Salva Modifiche
        </StandardButton>
      </div>
    </template>
  </BaseModal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import BaseModal from '@/components/ui/BaseModal.vue'
import StandardInput from '@/components/ui/StandardInput.vue'
import StandardButton from '@/components/ui/StandardButton.vue'
import HeroIcon from '@/components/icons/HeroIcon.vue'
import { usePersonnelStore } from '@/stores/personnel'
import { useToast } from '@/composables/useToast'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  user: {
    type: Object,
    default: null
  },
  departments: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['close', 'saved'])

const personnelStore = usePersonnelStore()
const { showSuccess, showError } = useToast()

const loading = ref(false)
const error = ref('')
const errors = ref({})

// Form data
const formData = reactive({
  full_name: '',
  email: '',
  username: '',
  role: 'employee',
  department_id: '',
  phone: '',
  position: '',
  hourly_rate: null,
  hire_date: '',
  bio: '',
  is_active: true
})

// Watch for user changes to populate form
watch(() => props.user, (newUser) => {
  if (newUser) {
    // Reset form
    Object.keys(formData).forEach(key => {
      formData[key] = ''
    })
    
    // Populate with user data
    Object.keys(formData).forEach(key => {
      if (newUser[key] !== undefined && newUser[key] !== null) {
        formData[key] = newUser[key]
      }
    })
    
    // Format hire_date for input
    if (newUser.hire_date) {
      const date = new Date(newUser.hire_date)
      formData.hire_date = date.toISOString().split('T')[0]
    }
  }
}, { immediate: true })

const validateForm = () => {
  errors.value = {}
  
  if (!formData.full_name || formData.full_name.trim() === '') {
    errors.value.full_name = 'Il nome completo è obbligatorio'
  }
  
  if (!formData.email || formData.email.trim() === '') {
    errors.value.email = 'L\'email è obbligatoria'
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
    errors.value.email = 'Formato email non valido'
  }
  
  if (!formData.username || formData.username.trim() === '') {
    errors.value.username = 'Lo username è obbligatorio'
  }
  
  if (!formData.role) {
    errors.value.role = 'Il ruolo è obbligatorio'
  }
  
  return Object.keys(errors.value).length === 0
}

const saveUser = async () => {
  if (!validateForm()) {
    return
  }
  
  loading.value = true
  error.value = ''
  
  try {
    // Prepare data for API
    const userData = { ...formData }
    
    // Convert hire_date back to proper format if provided
    if (userData.hire_date) {
      userData.hire_date = new Date(userData.hire_date).toISOString()
    }
    
    // Convert department_id to number if provided
    if (userData.department_id) {
      userData.department_id = parseInt(userData.department_id)
    } else {
      userData.department_id = null
    }
    
    // Update user via store
    await personnelStore.updateUser(props.user.id, userData)
    
    showSuccess('Utente aggiornato con successo')
    emit('saved', userData)
    emit('close')
    
  } catch (err) {
    console.error('Error updating user:', err)
    error.value = err.message || 'Errore nell\'aggiornamento dell\'utente'
    showError('Errore nell\'aggiornamento dell\'utente')
  } finally {
    loading.value = false
  }
}
</script>