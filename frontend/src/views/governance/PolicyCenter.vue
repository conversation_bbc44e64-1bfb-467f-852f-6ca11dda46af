<template>
  <div class="policy-center">
    <!-- Header con filtri -->
    <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Policy Center</h1>
          <p class="text-gray-600 mt-1">Gestione policy e regole di compliance</p>
        </div>
        <div class="mt-4 lg:mt-0">
          <button 
            @click="openCreatePolicy"
            class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
          >
            <HeroIcon name="plus" class="h-5 w-5 mr-2" />
            Nuova Policy
          </button>
        </div>
      </div>

      <!-- Stats -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-blue-50 rounded-lg p-4">
          <div class="flex items-center">
            <HeroIcon name="document-text" class="h-8 w-8 text-blue-600" />
            <div class="ml-3">
              <p class="text-sm text-blue-600 font-medium">Policy Attive</p>
              <p class="text-2xl font-bold text-blue-900">{{ activePolicies }}</p>
            </div>
          </div>
        </div>
        <div class="bg-yellow-50 rounded-lg p-4">
          <div class="flex items-center">
            <HeroIcon name="clock" class="h-8 w-8 text-yellow-600" />
            <div class="ml-3">
              <p class="text-sm text-yellow-600 font-medium">In Revisione</p>
              <p class="text-2xl font-bold text-yellow-900">{{ reviewPolicies }}</p>
            </div>
          </div>
        </div>
        <div class="bg-red-50 rounded-lg p-4">
          <div class="flex items-center">
            <HeroIcon name="exclamation-triangle" class="h-8 w-8 text-red-600" />
            <div class="ml-3">
              <p class="text-sm text-red-600 font-medium">Scadute</p>
              <p class="text-2xl font-bold text-red-900">{{ expiredPolicies }}</p>
            </div>
          </div>
        </div>
        <div class="bg-green-50 rounded-lg p-4">
          <div class="flex items-center">
            <HeroIcon name="check-circle" class="h-8 w-8 text-green-600" />
            <div class="ml-3">
              <p class="text-sm text-green-600 font-medium">Totali</p>
              <p class="text-2xl font-bold text-green-900">{{ policies.length }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Filtri -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Tipo Policy</label>
          <select 
            v-model="filters.policy_type" 
            @change="applyFilters"
            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Tutte</option>
            <option value="security">Sicurezza</option>
            <option value="privacy">Privacy</option>
            <option value="operational">Operativa</option>
            <option value="hr">Risorse Umane</option>
            <option value="financial">Finanziaria</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Framework</label>
          <select 
            v-model="filters.framework" 
            @change="applyFilters"
            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Tutti</option>
            <option value="GDPR">GDPR</option>
            <option value="ISO27001">ISO 27001</option>
            <option value="SOX">SOX</option>
            <option value="HIPAA">HIPAA</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
          <select 
            v-model="filters.status" 
            @change="applyFilters"
            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Tutti</option>
            <option value="active">Attiva</option>
            <option value="draft">Bozza</option>
            <option value="review">In Revisione</option>
            <option value="expired">Scaduta</option>
          </select>
        </div>

        <div class="flex items-end">
          <button 
            @click="resetFilters"
            class="w-full bg-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors"
          >
            Reset Filtri
          </button>
        </div>
      </div>
    </div>

    <!-- Lista policy -->
    <div class="bg-white rounded-lg shadow-sm">
      <div v-if="loading" class="flex justify-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>

      <div v-else-if="error" class="p-6 text-center text-red-600">
        <HeroIcon name="exclamation-triangle" class="h-8 w-8 mx-auto mb-2" />
        <p>{{ error }}</p>
        <button 
          @click="loadPolicies"
          class="mt-3 text-blue-600 hover:text-blue-800 font-medium"
        >
          Riprova
        </button>
      </div>

      <div v-else-if="policies.length === 0" class="p-12 text-center text-gray-500">
        <HeroIcon name="document-text" class="h-12 w-12 mx-auto mb-4 text-gray-300" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">Nessuna policy trovata</h3>
        <p class="text-gray-600 mb-6">Inizia aggiungendo la prima policy aziendale</p>
        <button 
          @click="openCreatePolicy"
          class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          Crea Prima Policy
        </button>
      </div>

      <div v-else>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Policy
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tipo
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Framework
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Versione
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Data Effettiva
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Scadenza
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Azioni
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="policy in policies" :key="policy.id" class="hover:bg-gray-50">
                <td class="px-6 py-4">
                  <div class="text-sm font-medium text-gray-900">{{ policy.name || 'N/A' }}</div>
                  <div class="text-sm text-gray-500">{{ policy.description || 'N/A' }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                        :class="getPolicyTypeBadgeClass(policy.policy_type)">
                    {{ getPolicyTypeLabel(policy.policy_type) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ policy.framework || '-' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  v{{ policy.version || '1.0' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ formatDate(policy.effective_date) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <span v-if="policy.expiry_date" :class="getExpiryClass(policy.expiry_date)">
                    {{ formatDate(policy.expiry_date) }}
                  </span>
                  <span v-else class="text-gray-400">-</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                        :class="getStatusBadgeClass(policy.status)">
                    {{ getStatusLabel(policy.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button @click="editPolicy(policy)" class="text-indigo-600 hover:text-indigo-900 mr-3">
                    <HeroIcon name="pencil" class="h-4 w-4" />
                  </button>
                  <button @click="viewPolicy(policy)" class="text-gray-600 hover:text-gray-900">
                    <HeroIcon name="eye" class="h-4 w-4" />
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Policy Modal -->
    <PolicyFormModal
      v-if="showPolicyModal"
      :is-open="showPolicyModal"
      :policy="selectedPolicy"
      @close="closePolicyModal"
      @saved="onPolicySaved"
    />

    <!-- Policy Detail Modal -->
    <PolicyDetailModal
      v-if="showDetailModal"
      :is-open="showDetailModal"
      :policy="selectedPolicy"
      @close="closeDetailModal"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import HeroIcon from '@/components/icons/HeroIcon.vue'
import PolicyFormModal from '@/components/governance/PolicyFormModal.vue'
import PolicyDetailModal from '@/components/governance/PolicyDetailModal.vue'
import { useGovernanceStore } from '@/stores/governance'

export default {
  name: 'PolicyCenter',
  components: {
    HeroIcon,
    PolicyFormModal,
    PolicyDetailModal
  },
  setup() {
    // Store
    const governanceStore = useGovernanceStore()

    // State
    const showPolicyModal = ref(false)
    const showDetailModal = ref(false)
    const selectedPolicy = ref(null)
    const filters = ref({
      policy_type: '',
      framework: '',
      status: ''
    })

    // Computed
    const policies = computed(() => governanceStore.policies || [])
    const loading = computed(() => governanceStore.loading)
    const error = computed(() => governanceStore.error)
    
    const activePolicies = computed(() => 
      policies.value.filter(p => p.status === 'active').length
    )
    
    const reviewPolicies = computed(() => 
      policies.value.filter(p => p.status === 'review').length
    )
    
    const expiredPolicies = computed(() => 
      policies.value.filter(p => p.status === 'expired').length
    )

    // Methods
    const loadPolicies = async () => {
      try {
        await governanceStore.fetchPolicies()
      } catch (error) {
        console.error('Error loading policies:', error)
      }
    }

    const applyFilters = async () => {
      try {
        const params = Object.fromEntries(
          Object.entries(filters.value).filter(([_, value]) => value)
        )
        await governanceStore.fetchPolicies(params)
      } catch (error) {
        console.error('Error applying filters:', error)
      }
    }

    const resetFilters = () => {
      filters.value = {
        policy_type: '',
        framework: '',
        status: ''
      }
      loadPolicies()
    }

    const openCreatePolicy = () => {
      selectedPolicy.value = null
      showPolicyModal.value = true
    }

    const editPolicy = (policy) => {
      selectedPolicy.value = policy
      showPolicyModal.value = true
    }

    const closePolicyModal = () => {
      showPolicyModal.value = false
      selectedPolicy.value = null
    }

    const onPolicySaved = () => {
      loadPolicies()
    }

    const viewPolicy = (policy) => {
      selectedPolicy.value = policy
      showDetailModal.value = true
    }

    const closeDetailModal = () => {
      showDetailModal.value = false
      selectedPolicy.value = null
    }

    // Utility functions
    const getPolicyTypeLabel = (type) => {
      if (!type) return 'N/A'
      const labels = {
        security: 'Sicurezza',
        privacy: 'Privacy',
        operational: 'Operativa',
        hr: 'Risorse Umane',
        financial: 'Finanziaria'
      }
      return labels[type] || type
    }

    const getPolicyTypeBadgeClass = (type) => {
      const classes = {
        security: 'bg-red-100 text-red-800',
        privacy: 'bg-purple-100 text-purple-800',
        operational: 'bg-blue-100 text-blue-800',
        hr: 'bg-green-100 text-green-800',
        financial: 'bg-yellow-100 text-yellow-800'
      }
      return classes[type] || 'bg-gray-100 text-gray-800'
    }

    const getStatusLabel = (status) => {
      if (!status) return 'N/A'
      const labels = {
        active: 'Attiva',
        draft: 'Bozza',
        review: 'In Revisione',
        expired: 'Scaduta'
      }
      return labels[status] || status
    }

    const getStatusBadgeClass = (status) => {
      const classes = {
        active: 'bg-green-100 text-green-800',
        draft: 'bg-gray-100 text-gray-800',
        review: 'bg-yellow-100 text-yellow-800',
        expired: 'bg-red-100 text-red-800'
      }
      return classes[status] || 'bg-gray-100 text-gray-800'
    }

    const getExpiryClass = (expiryDate) => {
      const now = new Date()
      const expiry = new Date(expiryDate)
      const daysUntilExpiry = Math.ceil((expiry - now) / (1000 * 60 * 60 * 24))
      
      if (daysUntilExpiry < 0) {
        return 'text-red-600 font-medium'
      } else if (daysUntilExpiry <= 30) {
        return 'text-orange-600 font-medium'
      } else {
        return 'text-gray-900'
      }
    }

    const formatDate = (dateString) => {
      if (!dateString) return '-'
      try {
        const date = new Date(dateString)
        if (isNaN(date.getTime())) return '-'
        return date.toLocaleDateString('it-IT', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        })
      } catch (error) {
        return '-'
      }
    }

    // Lifecycle
    onMounted(() => {
      loadPolicies()
    })

    return {
      // State
      showPolicyModal,
      showDetailModal,
      selectedPolicy,
      filters,
      
      // Computed
      policies,
      loading,
      error,
      activePolicies,
      reviewPolicies,
      expiredPolicies,
      
      // Methods
      loadPolicies,
      applyFilters,
      resetFilters,
      openCreatePolicy,
      editPolicy,
      closePolicyModal,
      onPolicySaved,
      viewPolicy,
      closeDetailModal,
      getPolicyTypeLabel,
      getPolicyTypeBadgeClass,
      getStatusLabel,
      getStatusBadgeClass,
      getExpiryClass,
      formatDate
    }
  }
}
</script>