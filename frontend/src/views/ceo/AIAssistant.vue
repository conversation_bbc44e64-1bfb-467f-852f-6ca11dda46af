<template>
  <div class="max-w-4xl mx-auto p-6">
    <PageHeader
      title="Assistente AI"
      subtitle="Intelligenza strategica potenziata da AI avanzata"
    />
    
    <!-- AI Search Mode Selection -->
    <div class="mb-6 bg-white rounded-lg shadow-sm p-4 border">
      <h3 class="text-lg font-semibold text-gray-900 mb-3">Modalità di Ricerca AI</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <button
          @click="selectSearchMode('pro')"
          :class="searchMode === 'pro' ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20' : 'border-gray-200 dark:border-gray-700'"
          class="p-4 border-2 rounded-lg hover:border-primary-300 dark:hover:border-primary-600 transition-all text-left"
        >
          <div class="flex items-start gap-3">
            <HeroIcon name="bolt" size="sm" :class="searchMode === 'pro' ? 'text-primary-600 dark:text-primary-400' : 'text-gray-500 dark:text-gray-400'" />
            <div>
              <h4 class="font-medium text-gray-900">Sonar Pro</h4>
              <p class="text-sm text-gray-600 mb-2">Insights strategici rapidi (1-2 min)</p>
              <div class="flex items-center gap-2 text-xs">
                <span class="bg-green-100 text-green-800 px-2 py-1 rounded">Veloce</span>
                <span class="text-gray-500">• Contesto aziendale • Trend chiave • Azioni immediate</span>
              </div>
            </div>
          </div>
        </button>
        
        <button
          @click="selectSearchMode('deep')"
          :class="searchMode === 'deep' ? 'border-secondary-500 bg-secondary-50 dark:bg-secondary-900/20' : 'border-gray-200 dark:border-gray-700'"
          class="p-4 border-2 rounded-lg hover:border-secondary-300 dark:hover:border-secondary-600 transition-all text-left"
        >
          <div class="flex items-start gap-3">
            <HeroIcon name="magnifying-glass" size="sm" :class="searchMode === 'deep' ? 'text-secondary-600 dark:text-secondary-400' : 'text-gray-500 dark:text-gray-400'" />
            <div>
              <h4 class="font-medium text-gray-900">Sonar Deep</h4>
              <p class="text-sm text-gray-600 mb-2">Analisi approfondita (3-5 min)</p>
              <div class="flex items-center gap-2 text-xs">
                <span class="bg-secondary-100 text-secondary-800 dark:bg-secondary-900 dark:text-secondary-200 px-2 py-1 rounded">Approfondita</span>
                <span class="text-gray-500">• Ricerca di mercato • Analisi concorrenti • Roadmap strategica</span>
              </div>
            </div>
          </div>
        </button>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4">
      <button
        v-for="suggestion in quickSuggestions"
        :key="suggestion.id"
        @click="sendMessage(suggestion.query)"
        class="p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-primary-300 dark:hover:border-primary-600 hover:shadow-sm transition-all text-left"
      >
        <div class="flex items-center gap-3">
          <HeroIcon :name="suggestion.icon" size="sm" class="text-primary-600 dark:text-primary-400" />
          <div>
            <h3 class="font-medium text-gray-900">{{ suggestion.title }}</h3>
            <p class="text-sm text-gray-600">{{ suggestion.description }}</p>
          </div>
        </div>
      </button>
    </div>
    
    <!-- Chat Interface -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 flex flex-col" style="height: 600px;">
      <!-- Chat Header -->
      <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
        <div class="flex items-center gap-3">
          <div class="w-8 h-8 bg-primary-600 dark:bg-primary-500 rounded-full flex items-center justify-center">
            <HeroIcon name="cpu-chip" size="sm" class="text-white" />
          </div>
          <div>
            <h3 class="font-medium text-gray-900 dark:text-white">Assistente AI Strategico</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">Online</p>
          </div>
        </div>
        <button 
          @click="clearChat"
          class="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
          title="Cancella conversazione"
        >
          <HeroIcon name="trash" size="sm" />
        </button>
      </div>
      
      <!-- Messages Area -->
      <div ref="messagesContainer" class="flex-1 p-4 overflow-y-auto space-y-4">
        <!-- Welcome Message -->
        <div v-if="messages.length === 0" class="text-center py-8">
          <HeroIcon name="sparkles" size="lg" class="mx-auto mb-3 text-primary-600 dark:text-primary-400" />
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Benvenuto nell'Assistente AI</h3>
          <p class="text-gray-600 dark:text-gray-400 mb-4">Fai domande strategiche sulla tua attività o prova uno dei suggerimenti sopra.</p>
        </div>
        
        <!-- Chat Messages -->
        <div
          v-for="message in messages"
          :key="message.id"
          class="flex"
          :class="message.type === 'user' ? 'justify-end' : 'justify-start'"
        >
          <div
            class="max-w-[80%] rounded-lg px-4 py-2"
            :class="message.type === 'user' 
              ? 'bg-primary-600 text-white dark:bg-primary-500' 
              : 'bg-gray-100 text-gray-900 dark:bg-gray-700 dark:text-white'"
          >
            <div v-if="message.type === 'assistant'" class="flex items-start gap-2 mb-2">
              <HeroIcon name="cpu-chip" size="sm" class="text-primary-600 dark:text-primary-400 mt-1" />
              <span class="font-medium text-primary-600 dark:text-primary-400">Assistente AI</span>
            </div>
            <MarkdownContent 
              v-if="message.type === 'assistant'" 
              :content="message.content" 
            />
            <div v-else class="whitespace-pre-wrap">{{ message.content }}</div>
            <div 
              class="text-xs mt-2 opacity-75"
              :class="message.type === 'user' ? 'text-primary-200 dark:text-primary-300' : 'text-gray-500 dark:text-gray-400'"
            >
              {{ formatTime(message.timestamp) }}
            </div>
          </div>
        </div>
        
        <!-- Typing Indicator -->
        <div v-if="isTyping" class="flex justify-start">
          <div class="bg-gray-100 dark:bg-gray-700 rounded-lg px-4 py-2">
            <div class="flex items-center gap-2">
              <HeroIcon name="cpu-chip" size="sm" class="text-primary-600 dark:text-primary-400" />
              <div class="flex space-x-1">
                <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Input Area -->
      <div class="border-t border-gray-200 dark:border-gray-700 p-4">
        <form @submit.prevent="handleSubmit" class="flex gap-3">
          <input
            ref="messageInput"
            v-model="currentMessage"
            type="text"
            placeholder="Fai domande strategiche sulla tua attività..."
            class="flex-1 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:focus:ring-primary-400"
            :disabled="isTyping"
          />
          <StandardButton
            type="submit"
            variant="primary"
            icon="paper-airplane"
            :disabled="!currentMessage.trim() || isTyping"
            class="px-6"
          />
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, nextTick, onMounted } from 'vue'
import PageHeader from '@/components/design-system/PageHeader.vue'
import HeroIcon from '@/components/icons/HeroIcon.vue'
import StandardButton from '@/components/ui/StandardButton.vue'
import MarkdownContent from '@/components/design-system/MarkdownContent.vue'
import api from '@/utils/api'
import { useToast } from '@/composables/useToast'

export default {
  name: 'AIAssistant',
  components: {
    PageHeader,
    HeroIcon,
    StandardButton,
    MarkdownContent
  },
  setup() {
    const { showToast } = useToast()
    
    // Reactive data
    const messages = ref([])
    const currentMessage = ref('')
    const isTyping = ref(false)
    const messagesContainer = ref(null)
    const messageInput = ref(null)
    const researchConfig = ref(null)
    const searchMode = ref('pro') // Default to pro mode
    
    // Quick suggestions based on industry
    const quickSuggestions = computed(() => {
      const industry = researchConfig.value?.company?.industry || 'Software/Technology'
      
      const suggestions = {
        'Software/Technology': [
          {
            id: 1,
            title: 'Analisi di Mercato',
            description: 'Tendenze attuali nel nostro mercato tecnologico',
            icon: 'chart-bar',
            query: 'Quali sono le tendenze attuali del mercato nello sviluppo software che potrebbero impattare la nostra attività?'
          },
          {
            id: 2,
            title: 'Strategia Talenti',
            description: 'Assunzione e retention degli sviluppatori',
            icon: 'users',
            query: 'Dovremmo espandere il nostro team di sviluppo o esternalizzare progetti data la nostra crescita attuale?'
          },
          {
            id: 3,
            title: 'Innovazione Tecnologica',
            description: 'Tecnologie emergenti da adottare',
            icon: 'cpu-chip',
            query: 'Quali tecnologie emergenti dovremmo considerare di adottare per rimanere competitivi?'
          }
        ],
        'Manufacturing': [
          {
            id: 1,
            title: 'Catena di Fornitura',
            description: 'Ottimizza l\'efficienza della catena di fornitura',
            icon: 'truck',
            query: 'Come possiamo ottimizzare la nostra catena di fornitura per ridurre i costi e migliorare l\'efficienza?'
          },
          {
            id: 2,
            title: 'Analisi Costi',
            description: 'Ottimizzazione costi di produzione',
            icon: 'calculator',
            query: 'Quali sono le migliori strategie per ridurre i nostri costi di produzione senza compromettere la qualità?'
          },
          {
            id: 3,
            title: 'Metriche di Qualità',
            description: 'Migliora la qualità del prodotto',
            icon: 'shield-check',
            query: 'Come possiamo migliorare le nostre metriche di qualità e ridurre i tassi di difetto?'
          }
        ],
        'Services': [
          {
            id: 1,
            title: 'Soddisfazione Cliente',
            description: 'Migliora la qualità del servizio',
            icon: 'heart',
            query: 'Come possiamo migliorare i nostri punteggi di soddisfazione cliente e la consegna del servizio?'
          },
          {
            id: 2,
            title: 'Strategia di Crescita',
            description: 'Espandi l\'offerta di servizi',
            icon: 'arrow-trending-up',
            query: 'Quali nuove linee di servizio dovremmo considerare per guidare la crescita?'
          },
          {
            id: 3,
            title: 'Efficienza',
            description: 'Ottimizza la consegna del servizio',
            icon: 'clock',
            query: 'Come possiamo migliorare l\'efficienza della consegna del servizio e l\'utilizzo delle risorse?'
          }
        ],
        'Consulting': [
          {
            id: 1,
            title: 'Posizione di Mercato',
            description: 'Analizza il vantaggio competitivo',
            icon: 'trophy',
            query: 'Come possiamo rafforzare la nostra posizione di mercato e differenziarci dai concorrenti?'
          },
          {
            id: 2,
            title: 'Sviluppo Competenze',
            description: 'Costruisci capacità specializzate',
            icon: 'academic-cap',
            query: 'Quali nuove aree di competenza dovremmo sviluppare per cogliere le opportunità emergenti?'
          },
          {
            id: 3,
            title: 'Pipeline Clienti',
            description: 'Migliora l\'acquisizione clienti',
            icon: 'funnel',
            query: 'Come possiamo migliorare la nostra acquisizione clienti e i tassi di vincita delle proposte?'
          }
        ]
      }
      
      return suggestions[industry] || suggestions['Software/Technology']
    })
    
    // Load research configuration and company profile
    const loadConfiguration = async () => {
      try {
        const response = await api.get('/api/ceo/config')
        if (response.data.success) {
          researchConfig.value = {
            company: response.data.data.company,
            research: response.data.data.research_config
          }
        } else {
          throw new Error('Failed to load config')
        }
      } catch (error) {
        console.error('Error loading configuration:', error)
        // Use default config
        researchConfig.value = {
          company: { industry: 'Software/Technology' },
          research: {}
        }
      }
    }
    
    // Select search mode function
    const selectSearchMode = (mode) => {
      searchMode.value = mode
      showToast(`Passato alla modalità Sonar ${mode === 'pro' ? 'Pro' : 'Deep'}`, 'success')
    }
    
    
    // Handle form submission
    const handleSubmit = () => {
      if (currentMessage.value.trim()) {
        sendMessage(currentMessage.value.trim())
        currentMessage.value = ''
      }
    }
    
    // Send message function
    const sendMessage = async (message) => {
      // Add user message
      const userMessage = {
        id: Date.now(),
        type: 'user',
        content: message,
        timestamp: new Date()
      }
      messages.value.push(userMessage)
      
      // Show typing indicator
      isTyping.value = true
      await scrollToBottom()
      
      // Simulate AI processing delay based on search mode
      const processingTime = searchMode.value === 'pro' 
        ? 1000 + Math.random() * 1500  // Pro: 1-2.5 seconds
        : 2500 + Math.random() * 2500  // Deep: 2.5-5 seconds
      
      await new Promise(resolve => setTimeout(resolve, processingTime))
      
      // Call real AI API instead of generating mock response
      let aiResponse
      try {
        const response = await api.post('/api/ceo/assistant/query', {
          query: message,
          search_mode: searchMode.value,
          category: 'strategic',
          conversation_id: localStorage.getItem('ceo-conversation-id') || Date.now().toString()
        })
        
        if (response.data.success) {
          aiResponse = response.data.data.response
          
          // Save conversation ID for session persistence
          localStorage.setItem('ceo-conversation-id', response.data.data.conversation_id)
        } else {
          throw new Error('API response unsuccessful')
        }
      } catch (error) {
        console.error('❌ CEO AI service error:', error)
        
        // Show error message instead of fallback
        aiResponse = `❌ **CEO AI Service Unavailable**

I'm sorry, but I'm unable to process your request at this time. The CEO AI service requires proper API configuration.

**What you can do:**
- Contact your system administrator to configure OpenAI and Perplexity API keys
- Try again later once the service is properly configured
- For urgent strategic questions, please contact your business consultant directly

**Configuration Required:**
- OpenAI API key (for strategic analysis)
- Perplexity API key (for market intelligence)

*This service provides real-time strategic intelligence and cannot operate without proper API access.*`
        
        showToast('CEO AI service requires API configuration', 'error')
      }
      
      const assistantMessage = {
        id: Date.now() + 1,
        type: 'assistant',
        content: aiResponse,
        timestamp: new Date()
      }
      
      messages.value.push(assistantMessage)
      isTyping.value = false
      
      // Save to session storage for persistence
      saveMessages()
      
      await scrollToBottom()
      
      // Focus input for next message
      nextTick(() => {
        if (messageInput.value) {
          messageInput.value.focus()
        }
      })
    }
    
    // Clear chat function
    const clearChat = () => {
      messages.value = []
      localStorage.removeItem('ceo-chat-messages')
      showToast('Chat cleared', 'success')
    }
    
    // Format time helper
    const formatTime = (timestamp) => {
      return new Date(timestamp).toLocaleTimeString('it-IT', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }
    
    // Scroll to bottom of messages
    const scrollToBottom = async () => {
      await nextTick()
      if (messagesContainer.value) {
        messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
      }
    }
    
    // Save messages to localStorage
    const saveMessages = () => {
      localStorage.setItem('ceo-chat-messages', JSON.stringify(messages.value))
    }
    
    // Load messages from localStorage
    const loadMessages = () => {
      const saved = localStorage.getItem('ceo-chat-messages')
      if (saved) {
        try {
          messages.value = JSON.parse(saved)
        } catch (error) {
          console.error('Error loading saved messages:', error)
        }
      }
    }
    
    // Mount lifecycle
    onMounted(async () => {
      await loadConfiguration()
      loadMessages()
      
      // Focus input
      nextTick(() => {
        if (messageInput.value) {
          messageInput.value.focus()
        }
      })
    })
    
    return {
      messages,
      currentMessage,
      isTyping,
      messagesContainer,
      messageInput,
      searchMode,
      quickSuggestions,
      selectSearchMode,
      handleSubmit,
      sendMessage,
      clearChat,
      formatTime
    }
  }
}
</script>

<style scoped>
/* All button styles now handled by StandardButton component */
</style>