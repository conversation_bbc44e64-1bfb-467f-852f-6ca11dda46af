<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- <PERSON> Header -->
    <PageHeader
      :title="isEditing ? 'Modifica Contratto' : 'Nuovo Contratto'"
      :subtitle="isEditing ? 'Aggiorna i dettagli del contratto' : 'Crea un nuovo contratto commerciale'"
      :breadcrumbs="[
        { name: 'CRM', href: '/app/crm' },
        { name: '<PERSON><PERSON><PERSON>', href: '/app/crm/contracts' },
        { name: isEditing ? 'Modifica' : 'Nuovo', href: '#', current: true }
      ]"
      :loading="initialLoading"
    />

    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Form -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
        <form @submit.prevent="submitForm" class="p-6 space-y-8">
          <!-- Sezione Informazioni Base -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Informazioni Base</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">Dati principali del contratto</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Cliente -->
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Cliente *
                </label>
                <select
                  v-model="form.client_id"
                  required
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Seleziona cliente</option>
                  <option 
                    v-for="client in clients" 
                    :key="client.id" 
                    :value="client.id"
                  >
                    {{ client.name }}
                  </option>
                </select>
                <p v-if="errors.client_id" class="mt-1 text-sm text-red-600 dark:text-red-400">
                  {{ errors.client_id }}
                </p>
              </div>

              <!-- Tipo Contratto -->
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Tipo Contratto *
                </label>
                <select
                  v-model="form.contract_type"
                  required
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Seleziona tipo</option>
                  <option 
                    v-for="option in contractTypeOptions" 
                    :key="option.value" 
                    :value="option.value"
                  >
                    {{ option.label }}
                  </option>
                </select>
                <p v-if="errors.contract_type" class="mt-1 text-sm text-red-600 dark:text-red-400">
                  {{ errors.contract_type }}
                </p>
              </div>

              <!-- Titolo -->
              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Titolo Contratto *
                </label>
                <input
                  v-model="form.title"
                  type="text"
                  required
                  placeholder="Es. Sviluppo piattaforma e-commerce"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <p v-if="errors.title" class="mt-1 text-sm text-red-600 dark:text-red-400">
                  {{ errors.title }}
                </p>
              </div>

              <!-- Numero Contratto -->
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Numero Contratto
                </label>
                <input
                  v-model="form.contract_number"
                  type="text"
                  placeholder="Auto-generato se vuoto"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  Lascia vuoto per auto-generazione (YYYY-NNNN)
                </p>
              </div>

              <!-- Stato -->
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Stato
                </label>
                <select
                  v-model="form.status"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="active">Attivo</option>
                  <option value="completed">Completato</option>
                  <option value="cancelled">Cancellato</option>
                </select>
              </div>

              <!-- Descrizione -->
              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Descrizione
                </label>
                <textarea
                  v-model="form.description"
                  rows="4"
                  placeholder="Descrivi i dettagli del contratto, scope, deliverable, etc..."
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                ></textarea>
              </div>
            </div>
          </div>

          <!-- Sezione Dettagli Finanziari (condizionale) -->
          <div v-if="form.contract_type" class="border-t border-gray-200 dark:border-gray-700 pt-8">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Dettagli Finanziari</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">Configurazione economica del contratto</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Campi per Contratto Orario -->
              <template v-if="form.contract_type === 'hourly'">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Tariffa Oraria (€) *
                  </label>
                  <input
                    v-model.number="form.hourly_rate"
                    type="number"
                    step="0.01"
                    min="0"
                    required
                    placeholder="0.00"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Budget Ore (opzionale)
                  </label>
                  <input
                    v-model.number="form.budget_hours"
                    type="number"
                    step="0.5"
                    min="0"
                    placeholder="0"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Massimo ore fatturabili
                  </p>
                </div>
              </template>

              <!-- Campi per Contratto Fisso -->
              <template v-else-if="form.contract_type === 'fixed'">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Importo Fisso (€) *
                  </label>
                  <input
                    v-model.number="form.budget_amount"
                    type="number"
                    step="0.01"
                    min="0"
                    required
                    placeholder="0.00"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </template>

              <!-- Campi per Retainer -->
              <template v-else-if="form.contract_type === 'retainer'">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Importo Retainer (€) *
                  </label>
                  <input
                    v-model.number="form.retainer_amount"
                    type="number"
                    step="0.01"
                    min="0"
                    required
                    placeholder="0.00"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Frequenza *
                  </label>
                  <select
                    v-model="form.retainer_frequency"
                    required
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Seleziona frequenza</option>
                    <option value="monthly">Mensile</option>
                    <option value="quarterly">Trimestrale</option>
                    <option value="yearly">Annuale</option>
                  </select>
                </div>
              </template>

              <!-- Campi per Milestone -->
              <template v-else-if="form.contract_type === 'milestone'">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Valore per Milestone (€) *
                  </label>
                  <input
                    v-model.number="form.milestone_amount"
                    type="number"
                    step="0.01"
                    min="0"
                    required
                    placeholder="0.00"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Numero Milestone *
                  </label>
                  <input
                    v-model.number="form.milestone_count"
                    type="number"
                    min="1"
                    required
                    placeholder="1"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </template>

              <!-- Campi per Subscription -->
              <template v-else-if="form.contract_type === 'subscription'">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Importo Abbonamento (€) *
                  </label>
                  <input
                    v-model.number="form.subscription_amount"
                    type="number"
                    step="0.01"
                    min="0"
                    required
                    placeholder="0.00"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Frequenza *
                  </label>
                  <select
                    v-model="form.subscription_frequency"
                    required
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Seleziona frequenza</option>
                    <option value="monthly">Mensile</option>
                    <option value="yearly">Annuale</option>
                  </select>
                </div>
              </template>
            </div>
          </div>

          <!-- Sezione Periodo Contratto -->
          <div class="border-t border-gray-200 dark:border-gray-700 pt-8">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Periodo Contratto</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">Date di validità del contratto</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Data Inizio -->
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Data Inizio *
                </label>
                <input
                  v-model="form.start_date"
                  type="date"
                  required
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <!-- Data Fine -->
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Data Fine (opzionale)
                </label>
                <input
                  v-model="form.end_date"
                  type="date"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  Lascia vuoto per contratto a tempo indeterminato
                </p>
              </div>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              @click="router.push('/app/crm/contracts')"
              class="btn-secondary"
            >
              Annulla
            </button>
            <button
              type="submit"
              :disabled="loading"
              class="btn-primary"
            >
              <span v-if="loading" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Salvataggio...
              </span>
              <span v-else>{{ isEditing ? 'Aggiorna Contratto' : 'Crea Contratto' }}</span>
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Loading overlay -->
    <div v-if="initialLoading" class="fixed inset-0 bg-gray-600 bg-opacity-50 dark:bg-gray-900 dark:bg-opacity-70 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6">
        <div class="flex items-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-4"></div>
          <span class="text-lg font-medium text-gray-900 dark:text-white">Caricamento...</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useCrmStore } from '@/stores/crm'
import { useToast } from '@/composables/useToast'
import { getContractTypeOptions, CONTRACT_TYPES } from '@/constants/contractTypes'
import PageHeader from '@/components/design-system/PageHeader.vue'

const route = useRoute()
const router = useRouter()
const crmStore = useCrmStore()
const { showToast } = useToast()

// State
const loading = ref(false)
const initialLoading = ref(false)
const errors = ref({})

const form = ref({
  client_id: '',
  title: '',
  description: '',
  contract_type: '',
  contract_number: '',
  status: 'active',
  hourly_rate: null,
  budget_hours: null,
  budget_amount: null,
  retainer_amount: null,
  retainer_frequency: '',
  milestone_amount: null,
  milestone_count: null,
  subscription_amount: null,
  subscription_frequency: '',
  start_date: '',
  end_date: ''
})

// Computed
const isEditing = computed(() => {
  return route.params.id && route.params.id !== 'new'
})

const contractId = computed(() => {
  return isEditing.value ? parseInt(route.params.id) : null
})

const clients = computed(() => crmStore.clients || [])

const contractTypeOptions = computed(() => getContractTypeOptions())

// Methods
const loadContract = async () => {
  if (!isEditing.value) return
  
  try {
    initialLoading.value = true
    const response = await fetch(`/api/contracts/${contractId.value}`)
    
    if (response.ok) {
      const result = await response.json()
      const contract = result.data.contract
      
      form.value = {
        client_id: contract.client_id || '',
        title: contract.title || '',
        description: contract.description || '',
        contract_type: contract.contract_type || '',
        contract_number: contract.contract_number || '',
        status: contract.status || 'active',
        hourly_rate: contract.hourly_rate || null,
        budget_hours: contract.budget_hours || null,
        budget_amount: contract.total_budget || null,
        retainer_amount: contract.retainer_amount || null,
        retainer_frequency: contract.retainer_frequency || '',
        milestone_amount: contract.milestone_amount || null,
        milestone_count: contract.milestone_count || null,
        subscription_amount: contract.subscription_amount || null,
        subscription_frequency: contract.subscription_frequency || '',
        start_date: contract.start_date || '',
        end_date: contract.end_date || ''
      }
    } else {
      throw new Error('Contratto non trovato')
    }
  } catch (error) {
    console.error('Error loading contract:', error)
    showToast('Errore nel caricamento del contratto', 'error')
    router.push('/app/crm/contracts')
  } finally {
    initialLoading.value = false
  }
}

const loadClients = async () => {
  if (crmStore.clients.length === 0) {
    await crmStore.fetchClients()
  }
}

const validateForm = () => {
  errors.value = {}
  
  if (!form.value.client_id) {
    errors.value.client_id = 'Seleziona un cliente'
  }
  
  if (!form.value.title.trim()) {
    errors.value.title = 'Il titolo del contratto è obbligatorio'
  }
  
  if (!form.value.contract_type) {
    errors.value.contract_type = 'Seleziona il tipo di contratto'
  }
  
  // Validate financial fields based on type
  if (form.value.contract_type === CONTRACT_TYPES.HOURLY) {
    if (!form.value.hourly_rate || form.value.hourly_rate <= 0) {
      errors.value.hourly_rate = 'Inserisci una tariffa oraria valida'
    }
  } else if (form.value.contract_type === CONTRACT_TYPES.FIXED) {
    if (!form.value.budget_amount || form.value.budget_amount <= 0) {
      errors.value.budget_amount = 'Inserisci un importo valido'
    }
  } else if (form.value.contract_type === CONTRACT_TYPES.RETAINER) {
    if (!form.value.retainer_amount || form.value.retainer_amount <= 0) {
      errors.value.retainer_amount = 'Inserisci un importo retainer valido'
    }
    if (!form.value.retainer_frequency) {
      errors.value.retainer_frequency = 'Seleziona la frequenza'
    }
  } else if (form.value.contract_type === CONTRACT_TYPES.MILESTONE) {
    if (!form.value.milestone_amount || form.value.milestone_amount <= 0) {
      errors.value.milestone_amount = 'Inserisci un importo per milestone valido'
    }
    if (!form.value.milestone_count || form.value.milestone_count <= 0) {
      errors.value.milestone_count = 'Inserisci un numero di milestone valido'
    }
  } else if (form.value.contract_type === CONTRACT_TYPES.SUBSCRIPTION) {
    if (!form.value.subscription_amount || form.value.subscription_amount <= 0) {
      errors.value.subscription_amount = 'Inserisci un importo abbonamento valido'
    }
    if (!form.value.subscription_frequency) {
      errors.value.subscription_frequency = 'Seleziona la frequenza'
    }
  }
  
  // Validate dates
  if (form.value.start_date && form.value.end_date) {
    const startDate = new Date(form.value.start_date)
    const endDate = new Date(form.value.end_date)
    if (startDate > endDate) {
      errors.value.dates = 'La data di inizio non può essere successiva alla data di fine'
    }
  }
  
  return Object.keys(errors.value).length === 0
}

const submitForm = async () => {
  if (!validateForm()) {
    showToast('Correggi gli errori nel form', 'error')
    return
  }
  
  try {
    loading.value = true
    
    const formData = {
      client_id: parseInt(form.value.client_id),
      title: form.value.title,
      description: form.value.description,
      contract_type: form.value.contract_type,
      contract_number: form.value.contract_number || undefined,
      status: form.value.status,
      start_date: form.value.start_date || null,
      end_date: form.value.end_date || null
    }
    
    // Add financial fields based on type
    if (form.value.contract_type === CONTRACT_TYPES.HOURLY) {
      formData.hourly_rate = form.value.hourly_rate
      formData.budget_hours = form.value.budget_hours || null
    } else if (form.value.contract_type === CONTRACT_TYPES.FIXED) {
      formData.total_budget = form.value.budget_amount
    } else if (form.value.contract_type === CONTRACT_TYPES.RETAINER) {
      formData.retainer_amount = form.value.retainer_amount
      formData.retainer_frequency = form.value.retainer_frequency
    } else if (form.value.contract_type === CONTRACT_TYPES.MILESTONE) {
      formData.milestone_amount = form.value.milestone_amount
      formData.milestone_count = form.value.milestone_count
    } else if (form.value.contract_type === CONTRACT_TYPES.SUBSCRIPTION) {
      formData.subscription_amount = form.value.subscription_amount
      formData.subscription_frequency = form.value.subscription_frequency
    }
    
    const url = isEditing.value ? `/api/contracts/${contractId.value}` : '/api/contracts/'
    const method = isEditing.value ? 'PUT' : 'POST'
    
    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(formData)
    })
    
    if (response.ok) {
      const result = await response.json()
      showToast(
        isEditing.value 
          ? 'Contratto aggiornato con successo' 
          : 'Contratto creato con successo', 
        'success'
      )
      
      if (isEditing.value) {
        router.push(`/app/crm/contracts/${contractId.value}`)
      } else {
        router.push(`/app/crm/contracts/${result.data.id}`)
      }
    } else {
      const errorResult = await response.json()
      throw new Error(errorResult.message || 'Errore nel salvataggio')
    }
  } catch (error) {
    console.error('Error saving contract:', error)
    showToast(
      isEditing.value 
        ? 'Errore nell\'aggiornamento del contratto' 
        : 'Errore nella creazione del contratto', 
      'error'
    )
  } finally {
    loading.value = false
  }
}

// Lifecycle
onMounted(async () => {
  await loadClients()
  
  // Pre-populate client if from query params
  if (route.query.client_id && !isEditing.value) {
    form.value.client_id = parseInt(route.query.client_id)
  }
  
  // Set default start date to today
  if (!isEditing.value) {
    form.value.start_date = new Date().toISOString().split('T')[0]
  }
  
  if (isEditing.value) {
    await loadContract()
  }
})
</script>