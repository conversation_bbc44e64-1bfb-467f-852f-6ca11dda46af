<template>
  <ListPageTemplate
    title="Contratti"
    subtitle="Gestione contratti e accordi commerciali"
    :data="filteredContracts"
    :columns="columns"
    :stats="contractStats"
    :loading="loading"
    :can-create="true"
    create-label="Nuovo Contratto"
    search-placeholder="<PERSON><PERSON>, numero contratto..."
    empty-message="Inizia creando il tuo primo contratto"
    results-label="contratti"
    @create="createContract"
  >
    <!-- Custom filters slot -->
    <template #filters>
      <div class="flex space-x-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Cliente</label>
          <select
            v-model="filters.client_id"
            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="">Tutti i clienti</option>
            <option v-for="client in clients" :key="client.id" :value="client.id">
              {{ client.name }}
            </option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Tipo</label>
          <select
            v-model="filters.contract_type"
            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="">Tutti i tipi</option>
            <option value="hourly">Orario</option>
            <option value="fixed">Fisso</option>
            <option value="retainer">Retainer</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Stato</label>
          <select
            v-model="filters.status"
            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="">Tutti gli stati</option>
            <option value="active">Attivo</option>
            <option value="completed">Completato</option>
            <option value="cancelled">Cancellato</option>
          </select>
        </div>
        <div class="flex items-end">
          <StandardButton
            variant="secondary"
            size="sm"
            @click="resetFilters"
          >
            Reset Filtri
          </StandardButton>
        </div>
      </div>
    </template>

    <!-- Custom table content -->
    <template #content="{ data }">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Contratto
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Cliente
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Tipo
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Valore
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Periodo
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Stato
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Azioni
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="contract in data" :key="contract.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
              <td class="px-6 py-4 whitespace-nowrap">
                <div>
                  <div class="text-sm font-medium text-gray-900 dark:text-white">
                    {{ contract.contract_number }}
                  </div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">
                    {{ contract.title }}
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900 dark:text-white">
                  {{ contract.client?.name || 'N/A' }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" :class="getTypeClass(contract.contract_type)">
                  {{ getTypeLabel(contract.contract_type) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                <div v-if="contract.contract_type === 'hourly'">
                  €{{ formatCurrency(contract.hourly_rate) }}/ora
                  <div v-if="contract.budget_hours" class="text-xs text-gray-500 dark:text-gray-400">
                    Max: {{ contract.budget_hours }}h
                  </div>
                </div>
                <div v-else-if="contract.budget_amount">
                  €{{ formatCurrency(contract.budget_amount) }}
                </div>
                <div v-else class="text-gray-400 dark:text-gray-500">N/A</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                <div>
                  {{ formatDate(contract.start_date) }}
                </div>
                <div v-if="contract.end_date" class="text-xs text-gray-500 dark:text-gray-400">
                  al {{ formatDate(contract.end_date) }}
                </div>
                <div v-else class="text-xs text-gray-500 dark:text-gray-400">
                  Indeterminato
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <StatusBadge :status="contract.status" type="contract" />
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <ActionButtonGroup
                  :show-labels="false"
                  size="sm"
                  @view="viewContract(contract.id)"
                  @edit="editContract(contract.id)"
                  @delete="deleteContract(contract.id)"
                  delete-message="Sei sicuro di voler eliminare questo contratto?"
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </template>

    <!-- Custom empty state -->
    <template #empty-state>
      <div class="text-center py-12">
        <HeroIcon name="document-text" class="w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-600" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Nessun contratto trovato</h3>
        <p class="text-gray-500 dark:text-gray-400 mb-4">
          {{ hasFilters ? 'Prova a modificare i filtri di ricerca' : 'Inizia creando il tuo primo contratto' }}
        </p>
        <StandardButton
          v-if="!hasFilters"
          variant="primary"
          icon="plus"
          :to="'/app/crm/contracts/new'"
        >
          Crea Primo Contratto
        </StandardButton>
      </div>
    </template>
  </ListPageTemplate>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useCrmStore } from '@/stores/crm'
import { useToast } from '@/composables/useToast'
import StatusBadge from '@/components/ui/StatusBadge.vue'
import StandardButton from '@/components/ui/StandardButton.vue'
import ActionButtonGroup from '@/components/ui/ActionButtonGroup.vue'
import { getContractTypeLabel, getContractTypeClass } from '@/constants/contractTypes'
import ListPageTemplate from '@/components/design-system/templates/ListPageTemplate.vue'
import HeroIcon from '@/components/icons/HeroIcon.vue'

const router = useRouter()
const crmStore = useCrmStore()
const { showToast } = useToast()

// State
const loading = ref(false)
const contracts = ref([])
const clients = ref([])

// Filters
const filters = ref({
  client_id: '',
  contract_type: '',
  status: ''
})

// Table columns configuration
const columns = [
  { key: 'contract_number', label: 'Contratto' },
  { key: 'client', label: 'Cliente' },
  { key: 'contract_type', label: 'Tipo' },
  { key: 'budget_amount', label: 'Valore' },
  { key: 'start_date', label: 'Periodo' },
  { key: 'status', label: 'Stato' },
  { key: 'actions', label: 'Azioni' }
]

// Stats for the dashboard
const contractStats = computed(() => {
  const totalValue = contracts.value.reduce((sum, contract) => {
    if (contract.contract_type === 'hourly' && contract.budget_hours) {
      return sum + (contract.hourly_rate * contract.budget_hours)
    }
    return sum + (contract.budget_amount || 0)
  }, 0)

  return [
    {
      label: 'Totale Contratti',
      value: contracts.value.length,
      icon: 'document-text',
      iconClass: 'text-blue-500'
    },
    {
      label: 'Contratti Attivi',
      value: contracts.value.filter(c => c.status === 'active').length,
      icon: 'check-circle',
      iconClass: 'text-green-500'
    },
    {
      label: 'Valore Totale',
      value: `€${formatCurrency(totalValue)}`,
      icon: 'currency-euro',
      iconClass: 'text-purple-500'
    },
    {
      label: 'Completati',
      value: contracts.value.filter(c => c.status === 'completed').length,
      icon: 'archive-box',
      iconClass: 'text-gray-500'
    }
  ]
})

// Computed
const filteredContracts = computed(() => {
  let filtered = contracts.value
  
  // Client filter
  if (filters.value.client_id) {
    filtered = filtered.filter(contract => 
      contract.client_id === parseInt(filters.value.client_id)
    )
  }
  
  // Type filter
  if (filters.value.contract_type) {
    filtered = filtered.filter(contract => 
      contract.contract_type === filters.value.contract_type
    )
  }
  
  // Status filter
  if (filters.value.status) {
    filtered = filtered.filter(contract => 
      contract.status === filters.value.status
    )
  }
  
  return filtered
})

const hasFilters = computed(() => {
  return filters.value.client_id || filters.value.contract_type || filters.value.status
})

// Methods
const loadContracts = async () => {
  try {
    loading.value = true
    // Costruisci query params per filtri
    const params = new URLSearchParams()
    if (filters.value.client_id) params.append('client_id', filters.value.client_id)
    if (filters.value.contract_type) params.append('type', filters.value.contract_type)
    if (filters.value.status) params.append('status', filters.value.status)
    
    const queryString = params.toString()
    const url = `/api/contracts/${queryString ? '?' + queryString : ''}`
    
    const response = await fetch(url)
    
    if (response.ok) {
      const result = await response.json()
      // Backend restituisce contracts con total_budget, mappiamo a budget_amount
      contracts.value = (result.data?.contracts || []).map(contract => ({
        ...contract,
        budget_amount: contract.total_budget
      }))
    } else {
      throw new Error('Errore nel caricamento contratti')
    }
  } catch (error) {
    console.error('Error loading contracts:', error)
    showToast('Errore nel caricamento dei contratti', 'error')
  } finally {
    loading.value = false
  }
}

const loadClients = async () => {
  if (crmStore.clients.length === 0) {
    await crmStore.fetchClients()
  }
  clients.value = crmStore.clients
}

const createContract = () => {
  router.push('/app/crm/contracts/new')
}

const viewContract = (contractId) => {
  router.push(`/app/crm/contracts/${contractId}`)
}

const editContract = (contractId) => {
  router.push(`/app/crm/contracts/${contractId}/edit`)
}

const resetFilters = () => {
  filters.value = {
    client_id: '',
    contract_type: '',
    status: ''
  }
}

const deleteContract = async (contractId) => {
  if (confirm('Sei sicuro di voler eliminare questo contratto?')) {
    try {
      const response = await fetch(`/api/contracts/${contractId}`, {
        method: 'DELETE'
      })
      
      if (response.ok) {
        contracts.value = contracts.value.filter(c => c.id !== contractId)
        showToast('Contratto eliminato con successo', 'success')
      } else {
        throw new Error('Errore nella eliminazione')
      }
    } catch (error) {
      console.error('Error deleting contract:', error)
      showToast('Errore nell\'eliminazione del contratto', 'error')
    }
  }
}

// Utility functions
const formatCurrency = (value) => {
  return new Intl.NumberFormat('it-IT').format(value || 0)
}

const formatDate = (dateString) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('it-IT')
}

const getTypeLabel = getContractTypeLabel
const getTypeClass = (type) => {
  const baseClass = getContractTypeClass(type)
  // Add dark mode support
  const darkModeMap = {
    'bg-blue-100 text-blue-800': 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
    'bg-green-100 text-green-800': 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    'bg-purple-100 text-purple-800': 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400',
    'bg-orange-100 text-orange-800': 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400',
    'bg-indigo-100 text-indigo-800': 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400',
    'bg-gray-100 text-gray-800': 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
  }
  return darkModeMap[baseClass] || baseClass
}

// Watchers
watch(
  () => [filters.value.client_id, filters.value.contract_type, filters.value.status],
  () => {
    // Auto-reload data when filters change
    loadContracts()
  },
  { deep: true }
)

// Lifecycle
onMounted(async () => {
  await Promise.all([
    loadContracts(),
    loadClients()
  ])
})
</script>