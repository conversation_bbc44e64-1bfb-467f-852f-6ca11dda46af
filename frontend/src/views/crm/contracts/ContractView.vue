<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- <PERSON> Header -->
    <PageHeader
      :title="contract?.contract_number || 'Caricamento...'"
      subtitle="Dettaglio contratto"
      :breadcrumbs="[
        { name: 'CRM', href: '/app/crm' },
        { name: '<PERSON><PERSON><PERSON>', href: '/app/crm/contracts' },
        { name: contract?.contract_number || 'Dettaglio', href: '#', current: true }
      ]"
      :loading="loading"
    >
      <template #actions>
        <div v-if="contract" class="flex space-x-4">
          <router-link
            :to="`/app/crm/contracts/${contract.id}/edit`"
            class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <HeroIcon name="pencil" size="sm" class="mr-2" />
            Modifica
          </router-link>
          <button
            v-if="contract.status === 'active' && canConvertToProject"
            @click="showConvertModal = true"
            class="btn-primary"
          >
            <HeroIcon name="arrow-up-tray" size="sm" class="mr-2" />
            Converti in Progetto
          </button>
        </div>
      </template>
    </PageHeader>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Loading State -->
      <div v-if="loading" class="bg-white dark:bg-gray-800 rounded-lg shadow p-8">
        <div class="flex justify-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      </div>

      <!-- Contract Details -->
      <div v-else-if="contract" class="space-y-6">
        <!-- Stats Grid -->
        <StatsGrid :stats="contractStats" />

        <!-- Overview Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
          <div class="px-6 py-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white">Informazioni Generali</h2>
          </div>
          <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">{{ contract.title }}</h3>
                <div class="space-y-2 text-sm">
                  <div class="flex justify-between">
                    <span class="text-gray-500 dark:text-gray-400">Cliente:</span>
                    <router-link
                      :to="`/app/crm/clients/${contract.client_id}`"
                      class="font-medium text-primary-600 hover:text-primary-800 dark:text-primary-400 dark:hover:text-primary-300 transition-colors"
                    >
                      {{ contract.client?.name || 'N/A' }}
                    </router-link>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-500 dark:text-gray-400">Numero Contratto:</span>
                    <span class="font-medium text-gray-900 dark:text-white">{{ contract.contract_number }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-500 dark:text-gray-400">Tipo:</span>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" :class="getTypeClass(contract.contract_type)">
                      {{ getTypeLabel(contract.contract_type) }}
                    </span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-500 dark:text-gray-400">Stato:</span>
                    <StatusBadge :status="contract.status" type="contract" />
                  </div>
                </div>
              </div>
              
              <div>
                <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Dettagli Finanziari</h4>
                <div class="space-y-2 text-sm">
                  <div v-if="contract.contract_type === 'hourly'">
                    <div class="flex justify-between">
                      <span class="text-gray-500 dark:text-gray-400">Tariffa Oraria:</span>
                      <span class="font-medium text-gray-900 dark:text-white">€{{ formatCurrency(contract.hourly_rate) }}/ora</span>
                    </div>
                    <div v-if="contract.budget_hours" class="flex justify-between">
                      <span class="text-gray-500 dark:text-gray-400">Budget Ore:</span>
                      <span class="font-medium text-gray-900 dark:text-white">{{ contract.budget_hours }} ore</span>
                    </div>
                  </div>
                  <div v-else-if="contract.budget_amount" class="flex justify-between">
                    <span class="text-gray-500 dark:text-gray-400">Budget Totale:</span>
                    <span class="font-medium text-gray-900 dark:text-white">€{{ formatCurrency(contract.budget_amount) }}</span>
                  </div>
                  
                  <div class="flex justify-between">
                    <span class="text-gray-500 dark:text-gray-400">Data Inizio:</span>
                    <span class="font-medium text-gray-900 dark:text-white">{{ formatDate(contract.start_date) }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-500 dark:text-gray-400">Data Fine:</span>
                    <span class="font-medium text-gray-900 dark:text-white">{{ contract.end_date ? formatDate(contract.end_date) : 'Indeterminata' }}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div v-if="contract.description" class="mt-6">
              <h4 class="text-md font-medium text-gray-900 dark:text-white mb-2">Descrizione</h4>
              <p class="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">{{ contract.description }}</p>
            </div>
          </div>
        </div>

        <!-- Related Projects -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
          <div class="px-6 py-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white">Progetti Collegati ({{ relatedProjects.length }})</h2>
          </div>
          <div class="p-6">
            <div v-if="relatedProjects.length > 0" class="space-y-4">
              <div v-for="project in relatedProjects" :key="project.id" class="border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <div class="flex justify-between items-start">
                  <div>
                    <h4 class="font-medium text-gray-900 dark:text-white">{{ project.name }}</h4>
                    <p v-if="project.description" class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ project.description }}</p>
                    <div class="mt-2">
                      <StatusBadge :status="project.status" type="project" />
                    </div>
                  </div>
                  <router-link
                    :to="`/app/projects/${project.id}`"
                    class="text-primary-600 hover:text-primary-800 dark:text-primary-400 dark:hover:text-primary-300 text-sm font-medium transition-colors"
                  >
                    Visualizza
                  </router-link>
                </div>
              </div>
            </div>
            <div v-else class="text-center py-8 text-gray-500 dark:text-gray-400">
              <HeroIcon name="briefcase" size="lg" class="mx-auto mb-4 text-gray-300 dark:text-gray-600" />
              <p>Nessun progetto collegato</p>
              <p class="text-sm">I progetti creati da questo contratto appariranno qui</p>
            </div>
          </div>
        </div>

        <!-- Recent Invoices -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
          <div class="px-6 py-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white">Fatture Recenti ({{ recentInvoices.length }})</h2>
          </div>
          <div class="p-6">
            <div v-if="recentInvoices.length > 0" class="space-y-4">
              <div v-for="invoice in recentInvoices" :key="invoice.id" class="border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <div class="flex justify-between items-start">
                  <div>
                    <h4 class="font-medium text-gray-900 dark:text-white">{{ invoice.invoice_number }}</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400">{{ formatDate(invoice.issue_date) }} - {{ formatDate(invoice.due_date) }}</p>
                    <div class="mt-2 flex items-center space-x-4">
                      <StatusBadge :status="invoice.status" type="invoice" />
                      <span class="text-sm font-medium text-gray-900 dark:text-white">€{{ formatCurrency(invoice.total_amount) }}</span>
                    </div>
                  </div>
                  <router-link
                    :to="`/app/invoices/${invoice.id}`"
                    class="text-primary-600 hover:text-primary-800 dark:text-primary-400 dark:hover:text-primary-300 text-sm font-medium transition-colors"
                  >
                    Visualizza
                  </router-link>
                </div>
              </div>
            </div>
            <div v-else class="text-center py-8 text-gray-500 dark:text-gray-400">
              <HeroIcon name="document-text" size="lg" class="mx-auto mb-4 text-gray-300 dark:text-gray-600" />
              <p>Nessuna fattura</p>
              <p class="text-sm">Le fatture generate per questo contratto appariranno qui</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Error State -->
      <div v-else class="bg-white dark:bg-gray-800 rounded-lg shadow p-8">
        <div class="text-center">
          <HeroIcon name="exclamation-circle" size="xl" class="mx-auto mb-4 text-gray-300 dark:text-gray-600" />
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Contratto non trovato</h3>
          <p class="text-gray-500 dark:text-gray-400 mb-4">Il contratto richiesto non esiste o non è accessibile</p>
          <router-link
            to="/app/crm/contracts"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 dark:bg-primary-700 dark:hover:bg-primary-800 transition-colors"
          >
            Torna alla Lista Contratti
          </router-link>
        </div>
      </div>
    </div>

    <!-- Convert to Project Modal -->
    <div v-if="showConvertModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 dark:bg-gray-900 dark:bg-opacity-70 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800 dark:border-gray-600">
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Converti in Progetto</h3>
          
          <form @submit.prevent="convertToProject" class="space-y-4">
            <div>
              <label for="project_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Nome Progetto *
              </label>
              <input
                id="project_name"
                v-model="convertForm.name"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
                :placeholder="contract?.title || ''"
              />
            </div>
            
            <div>
              <label for="project_description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Descrizione
              </label>
              <textarea
                id="project_description"
                v-model="convertForm.description"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
                :placeholder="contract?.description || ''"
              ></textarea>
            </div>
            
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label for="project_start" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Data Inizio
                </label>
                <input
                  id="project_start"
                  v-model="convertForm.start_date"
                  type="date"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
              
              <div>
                <label for="project_end" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Data Fine
                </label>
                <input
                  id="project_end"
                  v-model="convertForm.end_date"
                  type="date"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>
            
            <div class="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                @click="cancelConvert"
                class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
              >
                Annulla
              </button>
              <button
                type="submit"
                :disabled="converting"
                class="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 disabled:opacity-50 dark:bg-green-700 dark:hover:bg-green-800 transition-colors"
              >
                <span v-if="converting" class="flex items-center">
                  <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Conversione...
                </span>
                <span v-else>Crea Progetto</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useCrmStore } from '@/stores/crm'
import { useToast } from '@/composables/useToast'
import HeroIcon from '@/components/icons/HeroIcon.vue'
import StatusBadge from '@/components/ui/StatusBadge.vue'
import PageHeader from '@/components/design-system/PageHeader.vue'
import StatsGrid from '@/components/design-system/StatsGrid.vue'
import { getContractTypeLabel, getContractTypeClass } from '@/constants/contractTypes'

const route = useRoute()
const router = useRouter()
const { showToast } = useToast()
const crmStore = useCrmStore()

// State
const loading = ref(false)
const converting = ref(false)
const contract = ref(null)
const relatedProjects = ref([])
const recentInvoices = ref([])
const showConvertModal = ref(false)

const convertForm = ref({
  name: '',
  description: '',
  start_date: '',
  end_date: ''
})

// Computed
const contractId = computed(() => parseInt(route.params.id))

const canConvertToProject = computed(() => {
  return contract.value && !relatedProjects.value.length
})

const contractStats = computed(() => {
  if (!contract.value) return []
  
  const stats = []
  
  // Budget/Value stat
  if (contract.value.contract_type === 'hourly') {
    stats.push({
      name: 'Tariffa Oraria',
      value: `€${formatCurrency(contract.value.hourly_rate)}`,
      subtitle: 'per ora',
      icon: 'clock'
    })
    
    if (contract.value.budget_hours) {
      stats.push({
        name: 'Budget Ore',
        value: contract.value.budget_hours,
        subtitle: 'ore totali',
        icon: 'calendar'
      })
    }
  } else if (contract.value.budget_amount) {
    stats.push({
      name: 'Budget Totale',
      value: `€${formatCurrency(contract.value.budget_amount)}`,
      subtitle: 'valore contratto',
      icon: 'banknotes'
    })
  }
  
  // Projects count
  stats.push({
    name: 'Progetti',
    value: relatedProjects.value.length,
    subtitle: 'collegati',
    icon: 'folder'
  })
  
  // Invoices count
  stats.push({
    name: 'Fatture',
    value: recentInvoices.value.length,
    subtitle: 'generate',
    icon: 'document-text'
  })
  
  // Duration
  if (contract.value.start_date) {
    const startDate = new Date(contract.value.start_date)
    const endDate = contract.value.end_date ? new Date(contract.value.end_date) : new Date()
    const diffTime = Math.abs(endDate - startDate)
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    stats.push({
      name: 'Durata',
      value: diffDays,
      subtitle: 'giorni',
      icon: 'calendar-days'
    })
  }
  
  return stats
})

// Methods
const loadContract = async () => {
  try {
    loading.value = true
    const response = await fetch(`/api/contracts/${contractId.value}`)
    
    if (response.ok) {
      const result = await response.json()
      // Il backend restituisce i dati in result.data.contract
      const contractData = result.data.contract
      // Mappiamo total_budget a budget_amount per coerenza
      contract.value = {
        ...contractData,
        budget_amount: contractData.total_budget
      }
      
      // Initialize convert form with contract data
      if (contract.value) {
        convertForm.value.name = contract.value.title
        convertForm.value.description = contract.value.description || ''
        convertForm.value.start_date = contract.value.start_date || new Date().toISOString().split('T')[0]
        convertForm.value.end_date = contract.value.end_date || ''
      }
    } else {
      throw new Error('Contratto non trovato')
    }
  } catch (error) {
    console.error('Error loading contract:', error)
    showToast('Errore nel caricamento del contratto', 'error')
  } finally {
    loading.value = false
  }
}

const loadRelatedData = async () => {
  try {
    // Load related projects - I progetti sono già inclusi nella risposta del contratto
    if (contract.value?.projects) {
      relatedProjects.value = contract.value.projects
    }
    
    // Load recent invoices for this client
    const invoicesResponse = await fetch(`/api/invoices/?client_id=${contract.value?.client_id}&limit=5`)
    if (invoicesResponse.ok) {
      const invoicesResult = await invoicesResponse.json()
      recentInvoices.value = invoicesResult.data?.invoices || []
    }
  } catch (error) {
    console.error('Error loading related data:', error)
  }
}

const convertToProject = async () => {
  try {
    converting.value = true
    
    const projectData = {
      name: convertForm.value.name,
      description: convertForm.value.description,
      start_date: convertForm.value.start_date,
      end_date: convertForm.value.end_date,
      status: 'planning'
    }
    
    const result = await crmStore.convertContractToProject(contract.value.id, projectData)
    showConvertModal.value = false
    
    // Redirect to new project
    router.push(`/app/projects/${result.project.id}`)
  } catch (error) {
    console.error('Error converting to project:', error)
    // Toast gestito dal store
  } finally {
    converting.value = false
  }
}

const cancelConvert = () => {
  showConvertModal.value = false
  // Reset form
  if (contract.value) {
    convertForm.value.name = contract.value.title
    convertForm.value.description = contract.value.description || ''
    convertForm.value.start_date = contract.value.start_date || new Date().toISOString().split('T')[0]
    convertForm.value.end_date = contract.value.end_date || ''
  }
}

// Utility functions
const formatCurrency = (value) => {
  return new Intl.NumberFormat('it-IT').format(value || 0)
}

const formatDate = (dateString) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('it-IT')
}

const getTypeLabel = getContractTypeLabel
const getTypeClass = getContractTypeClass

// Lifecycle
onMounted(async () => {
  await loadContract()
  if (contract.value) {
    await loadRelatedData()
  }
})
</script>