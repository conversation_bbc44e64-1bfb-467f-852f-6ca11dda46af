# Controllo Qualità del Codice

Questa cartella contiene gli strumenti e le configurazioni per il controllo qualità del codice di DatPortal.

## SonarQube

SonarQube è uno strumento di analisi statica del codice che aiuta a identificare problemi di qualità, bug, vulnerabilità e code smells.

### Requisiti

- Docker e docker-compose
- SonarScanner (per eseguire l'analisi)

### Installazione di SonarScanner

#### macOS
```bash
brew install sonar-scanner
```

#### Linux
```bash
wget https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-4.8.0.2856-linux.zip
unzip sonar-scanner-cli-4.8.0.2856-linux.zip
mv sonar-scanner-4.8.0.2856-linux /opt/sonar-scanner
echo 'export PATH=$PATH:/opt/sonar-scanner/bin' >> ~/.bashrc
source ~/.bashrc
```

#### Windows
1. Scarica SonarScanner da [qui](https://docs.sonarqube.org/latest/analysis/scan/sonarscanner/)
2. Estrai il file zip
3. Aggiungi la cartella `bin` al PATH di sistema

### Utilizzo

Lo script `sonar_analysis.sh` fornisce diverse funzionalità:

#### Avviare SonarQube
```bash
./quality/sonar_analysis.sh start
```
SonarQube sarà disponibile all'indirizzo http://localhost:9000 (credenziali default: admin/admin)

#### Configurare i test
```bash
./quality/sonar_analysis.sh setup-tests
```
Questo comando configura pytest, coverage e crea un test di esempio per il backend.

#### Eseguire l'analisi
```bash
./quality/sonar_analysis.sh analyze
```

#### Verificare lo stato di SonarQube
```bash
./quality/sonar_analysis.sh status
```

#### Fermare SonarQube
```bash
./quality/sonar_analysis.sh stop
```

### Workflow completo

1. Avvia SonarQube: `./quality/sonar_analysis.sh start`
2. Accedi a SonarQube su http://localhost:9000 (admin/admin123)
3. Crea progetto: `curl -X POST -u admin:admin123 "http://localhost:9000/api/projects/create" -d "name=DatPortal&project=datportal"`
4. Genera token in Account > Security e aggiungilo in `.env` come `SONAR_TOKEN=tuo-token`
5. Configura i test: `./quality/sonar_analysis.sh setup-tests`
6. Esegui i test con coverage: `cd backend && pytest --cov=app --cov-report=xml`
7. Esegui l'analisi: `source .env && sonar-scanner -Dproject.settings="quality/sonar-project.properties" -Dsonar.login="$SONAR_TOKEN"`
8. Visualizza i risultati su http://localhost:9000/dashboard?id=datportal
9. Scarica report: `source .env && ./quality/download_report.sh -t "$SONAR_TOKEN"`

### Report e Download

Lo script `download_report.sh` permette di scaricare i risultati dell'analisi in formato JSON e HTML:

- **Report JSON**: Contiene tutte le metriche del progetto (bugs, vulnerabilità, code smells, coverage, etc.)
- **Report HTML**: Visualizzazione formattata dei risultati con grafici e tabelle
- **Issues JSON**: Lista dettagliata di tutti i problemi rilevati

I report vengono salvati nella directory `./security/static/reports/` con timestamp per tracciare le analisi nel tempo.

### Gestione Report

#### Auto-Cleanup Policy
- **Mantenimento**: Solo il set di report più recente viene mantenuto automaticamente
- **Formato**: `issues_YYYYMMDD_HHMMSS.json`, `sonarqube_report_YYYYMMDD_HHMMSS.html/json`
- **Cleanup**: Rimuovere manualmente i report obsoleti per ottimizzare storage
- **Storage**: Evitare accumulo eccessivo per migliorare performance dashboard

### Configurazione

Il file `sonar-project.properties` contiene la configurazione per l'analisi SonarQube. 

**Esclusioni configurate:**
- File di ambiente e dipendenze (`venv/`, `node_modules/`, etc.)
- File di seed del database (`backend/db/seed/**`)
- File di migrazione temporanei (`backend/db_*_migration.py`)
- File di test temporanei (`test-pages.js`, `frontend/tests/auto/test-pages.cjs`)

Questo assicura che SonarQube analizzi solo il codice di produzione, escludendo dati sensibili nei seed e file temporanei.

### Token SonarQube

Dopo il primo accesso a SonarQube:
1. Vai su http://localhost:9000
2. Accedi con admin/admin (cambia la password se richiesto)
3. Vai su Account > Security
4. Genera un token
5. Aggiungi il token al file `.env`:
   ```
   SONAR_TOKEN=il-tuo-token
   ```
6. Ora puoi eseguire l'analisi con: `./quality/sonar_analysis.sh analyze` 