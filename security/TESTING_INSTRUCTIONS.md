# 🛡️ Security Dashboard - Istruzioni Test

## Come Testare il Security Dashboard

### 1. **Prerequisiti**
- ✅ Feature flag `security_dashboard` abilitato nel database
- ✅ Utente con ruolo `admin` 
- ✅ Backend Flask in esecuzione (`python app.py`)
- ✅ Frontend Vue.js in esecuzione (`npm run dev`)

### 2. **Accesso al Dashboard**
1. Effettua login come utente admin
2. Naviga in **Amministrazione** → **Sicurezza** 
3. URL: `http://localhost:5173/app/admin/security`

### 3. **Funzionalità Disponibili**

#### **📊 Dashboard Overview**
- **Problemi Totali**: Somma di vulnerabilità statiche + dinamiche
- **Critici**: Vulnerabilità severity=critical (priorità massima)
- **Alta Priorità**: Vulnerabilità severity=high
- **Risk Score**: Punteggio calcolato con pesi per severity

#### **🔍 Analisi Statica (SonarQube)**
- **Report Automatici**: Legge i file da `security/static/reports/`
- **Formati Supportati**: JSON con struttura SonarQube
- **Severity Mapping**: Blocker/Critical → Critical, Major → High, ecc.

#### **🔥 Test Dinamici (Penetration Testing)**
- **Scansione Automatica**: Click su "Avvia Scansione"
- **6 Tipi di Test**:
  - SQL Injection
  - Cross-Site Scripting (XSS)
  - Authentication Bypass
  - CSRF Protection
  - Information Disclosure
  - File Upload Vulnerabilities

#### **📋 Lista Vulnerabilità**
- **Visualizzazione Unificata**: Combina static + dynamic
- **Filtri per Severity**: Critical, High, Medium, Low
- **Paginazione**: 10 vulnerabilità per pagina
- **Azioni**: Dettagli e risoluzione per severity critiche/high

### 4. **Test della Scansione Dinamica**

#### **Come Avviare una Scansione**
1. Nella sezione "Test Dinamici", clicca **"Avvia Scansione"**
2. La scansione parte automaticamente su `http://localhost:5000`
3. Durata stimata: 2-5 minuti
4. I risultati appaiono automaticamente nel dashboard

#### **Cosa Viene Testato**
- **12 Endpoint**: API automaticamente discovery
- **6 Tipi di Vulnerabilità**: SQL injection, XSS, auth bypass, ecc.
- **156 Richieste**: Test completi con payload specializzati
- **Report HTML + JSON**: Generati automaticamente

### 5. **Interpretazione Risultati**

#### **Severity Levels**
- 🔴 **Critical (9.0-10.0)**: Vulnerabilità immediate, impatto grave
- 🟠 **High (7.0-8.9)**: Vulnerabilità significative, risolvere presto
- 🟡 **Medium (4.0-6.9)**: Vulnerabilità moderate, pianificare fix
- 🟢 **Low (0.1-3.9)**: Vulnerabilità minori, bassa priorità

#### **Azioni Consigliate**
- **Critical**: Correzione immediata richiesta
- **High**: Correzione entro 7 giorni
- **Medium**: Correzione entro 30 giorni
- **Low**: Correzione quando possibile

### 6. **Risoluzione Problemi**

#### **"Failed to fetch security reports"**
- ✅ Verificare che l'utente abbia ruolo `admin`
- ✅ Controllare che feature flag `security_dashboard` sia abilitato
- ✅ Assicurarsi che il backend sia in esecuzione

#### **"Nessun test dinamico disponibile"**
- ✅ Verificare che la directory `security/dynamic/reports/` esista
- ✅ Eseguire una scansione manuale per popolare i dati
- ✅ Controllare i permessi della directory

#### **Scansione non si avvia**
- ✅ Verificare che `pentest.py` sia eseguibile
- ✅ Controllare che le dipendenze siano installate (`pip install -r requirements.txt`)
- ✅ Verificare che il target URL sia raggiungibile

### 7. **File di Configurazione**

#### **Report Directory**
- **Static**: `security/static/reports/` (SonarQube JSON)
- **Dynamic**: `security/dynamic/reports/` (Pentest JSON/HTML)

#### **Report Management Policy**
- **Auto-cleanup**: Solo il report più recente viene mantenuto per tipo
- **Pentest Reports**: Mantenere solo l'ultimo `pentest_report_YYYYMMDD_HHMMSS.json`
- **SonarQube Reports**: Mantenere solo l'ultimo set `issues_*.json`, `sonarqube_report_*.html/json`
- **Storage**: Evitare accumulo eccessivo di file per ottimizzare spazio e performance

#### **Dipendenze**
- `requests>=2.28.0` (HTTP requests)
- `jinja2>=3.1.0` (HTML report generation)

### 8. **Esempi di Vulnerabilità**

Il tool troverà vulnerabilità reali come:
- **SQL Injection** su parametri non validati
- **XSS** in campi di ricerca
- **Authentication Bypass** su endpoint protetti
- **CSRF** su operazioni state-changing
- **Information Disclosure** di file sensibili

### 9. **Sicurezza**

⚠️ **IMPORTANTE**: 
- Utilizzare solo su ambienti di test/sviluppo
- Non eseguire su sistemi di produzione
- I test sono invasivi e possono generare errori
- Ottenere autorizzazione esplicita prima del testing

### 10. **Supporto**

Per problemi o domande:
- Controllare i log del backend Flask
- Verificare la console del browser (F12)
- Esaminare i report generati in `security/dynamic/reports/`

---

✅ **Il Security Dashboard è ora pronto per essere testato!**