# DatPortal Dynamic Security Testing

Strumento di penetration testing automatizzato per DatPortal che esegue test dinamici di sicurezza e genera report HTML dettagliati.

## Installazione

```bash
cd security/dynamic
pip install -r requirements.txt
```

## Utilizzo Base

```bash
# Scansione semplice
python pentest.py --target http://localhost:5000

# Scansione con autenticazione
python pentest.py --target http://localhost:5000 --session-cookie "your_session_cookie"

# Specifica directory output
python pentest.py --target http://localhost:5000 --output-dir ./custom_reports

# Solo report JSON
python pentest.py --target http://localhost:5000 --format json
```

## Test Eseguiti

### 🔍 Scoperta Endpoint
- Identifica automaticamente endpoint API e pagine dell'applicazione
- Testa la disponibilità e accessibilità

### 💉 SQL Injection
- Test di iniezione SQL su parametri GET/POST
- Rilevamento automatico di errori SQL
- Payload avanzati per bypass di filtri

### 🌐 Cross-Site Scripting (XSS)
- Test XSS riflesso su parametri di input
- Payload per bypass di filtri XSS
- Verifica encoding output

### 🔐 Authentication Bypass
- Test accesso non autorizzato a endpoint protetti
- Verifica controlli di autenticazione
- Test privilege escalation

### 🛡️ CSRF Protection
- Verifica presenza token CSRF
- Test operazioni state-changing senza protezione
- Controllo header di sicurezza

### 📄 Information Disclosure
- Ricerca file sensibili esposti
- Test endpoint di debug
- Verifica esposizione configurazioni

### 📁 File Upload Security
- Test upload file maliciosi
- Verifica validazione tipi file
- Test esecuzione codice remoto

## Formato Report

I report vengono generati in formato HTML e JSON con:

- **Summary Dashboard**: Statistiche vulnerabilità per severità
- **Dettagli Vulnerabilità**: Descrizione, impatto, raccomandazioni
- **Evidenze**: Payload e response utilizzati
- **CVSS Scoring**: Punteggi di rischio standardizzati
- **Metadata Scansione**: Durata, endpoint testati, timestamp

## Livelli di Severità

- **Critical (9.0-10.0)**: Vulnerabilità con impatto immediato e grave
- **High (7.0-8.9)**: Vulnerabilità con impatto significativo
- **Medium (4.0-6.9)**: Vulnerabilità con impatto moderato
- **Low (0.1-3.9)**: Vulnerabilità con impatto limitato

## Integrazione Dashboard

I report generati sono automaticamente leggibili dalla Security Dashboard di DatPortal attraverso l'API `/api/security/reports`.

## Gestione Report

### Auto-Cleanup Policy
- **Mantenimento**: Solo il report più recente viene mantenuto automaticamente
- **Naming**: `pentest_report_YYYYMMDD_HHMMSS.json/html`
- **Cleanup**: Rimuovere manualmente i report obsoleti per ottimizzare storage
- **Location**: `./reports/` (default) o directory personalizzata con `--output-dir`

## Sicurezza

⚠️ **ATTENZIONE**: Questo tool esegue test invasivi che potrebbero:
- Generare errori nell'applicazione target
- Creare log di sicurezza
- Attivare sistemi di monitoraggio

Utilizzare solo su applicazioni di cui si ha l'autorizzazione esplicita per il testing.

## Esempi Output

```bash
🔍 Starting DatPortal Security Scan...
📍 Discovered 12 endpoints
💉 Testing SQL Injection...
🌐 Testing Cross-Site Scripting...
🔐 Testing Authentication Bypass...
🛡️ Testing CSRF Protection...
📄 Testing Information Disclosure...
📁 Testing File Upload Security...
✅ Scan completed in 23.45s
🎯 Found 3 vulnerabilities
📄 JSON report saved: ./reports/pentest_report_20250708_143022.json
🌐 HTML report saved: ./reports/pentest_report_20250708_143022.html
```