 schema_name |           table_name            |          column_name           | data_type | not_null | has_default 
-------------+---------------------------------+--------------------------------+-----------+----------+-------------
 public      | admin_logs                      | id                             | int4      | t        | t
 public      | admin_logs                      | admin_id                       | int4      | t        | f
 public      | admin_logs                      | action                         | varchar   | t        | f
 public      | admin_logs                      | target_user_id                 | int4      | f        | f
 public      | admin_logs                      | timestamp                      | timestamp | t        | t
 public      | ai_generated_content            | id                             | int4      | t        | t
 public      | ai_generated_content            | entity_type                    | varchar   | t        | f
 public      | ai_generated_content            | entity_id                      | int4      | f        | f
 public      | ai_generated_content            | content_type                   | varchar   | t        | f
 public      | ai_generated_content            | prompt_data                    | text      | f        | f
 public      | ai_generated_content            | generated_content              | text      | t        | f
 public      | ai_generated_content            | ai_model_used                  | varchar   | f        | f
 public      | ai_generated_content            | generation_tokens              | int4      | f        | f
 public      | ai_generated_content            | generated_by                   | int4      | t        | f
 public      | ai_generated_content            | applied_at                     | timestamp | f        | f
 public      | ai_generated_content            | applied_by                     | int4      | f        | f
 public      | ai_generated_content            | user_rating                    | int4      | f        | f
 public      | ai_generated_content            | user_feedback                  | text      | f        | f
 public      | ai_generated_content            | created_at                     | timestamp | f        | f
 public      | ai_generated_content            | updated_at                     | timestamp | f        | f
 public      | ai_interactions                 | id                             | int4      | t        | t
 public      | ai_interactions                 | user_id                        | int4      | t        | f
 public      | ai_interactions                 | query                          | text      | t        | f
 public      | ai_interactions                 | response                       | text      | t        | f
 public      | ai_interactions                 | conversation_id                | varchar   | f        | f
 public      | ai_interactions                 | category                       | varchar   | f        | f
 public      | ai_interactions                 | context_data                   | json      | f        | f
 public      | ai_interactions                 | model_used                     | varchar   | f        | f
 public      | ai_interactions                 | confidence_score               | float8    | f        | f
 public      | ai_interactions                 | response_time_seconds          | float8    | f        | f
 public      | ai_interactions                 | token_count_input              | int4      | f        | f
 public      | ai_interactions                 | token_count_output             | int4      | f        | f
 public      | ai_interactions                 | cost_estimate                  | float8    | f        | f
 public      | ai_interactions                 | user_rating                    | int4      | f        | f
 public      | ai_interactions                 | user_feedback                  | text      | f        | f
 public      | ai_interactions                 | timestamp                      | timestamp | f        | f
 public      | alembic_version                 | version_num                    | varchar   | t        | f
 public      | applications                    | id                             | int4      | t        | t
 public      | applications                    | job_posting_id                 | int4      | t        | f
 public      | applications                    | candidate_id                   | int4      | t        | f
 public      | applications                    | applied_at                     | timestamp | f        | f
 public      | applications                    | cover_letter                   | text      | f        | f
 public      | applications                    | current_step                   | varchar   | f        | f
 public      | applications                    | status                         | varchar   | f        | f
 public      | applications                    | overall_score                  | int4      | f        | f
 public      | applications                    | interview_notes                | text      | f        | f
 public      | applications                    | rejection_reason               | text      | f        | f
 public      | applications                    | created_at                     | timestamp | f        | f
 public      | applications                    | updated_at                     | timestamp | f        | f
 public      | bi_reports                      | id                             | int4      | t        | t
 public      | bi_reports                      | name                           | varchar   | t        | f
 public      | bi_reports                      | description                    | text      | f        | f
 public      | bi_reports                      | report_type                    | varchar   | t        | f
 public      | bi_reports                      | data_sources                   | json      | f        | f
 public      | bi_reports                      | filters                        | json      | f        | f
 public      | bi_reports                      | chart_config                   | json      | f        | f
 public      | bi_reports                      | schedule_config                | json      | f        | f
 public      | bi_reports                      | export_formats                 | json      | f        | f
 public      | bi_reports                      | is_scheduled                   | bool      | f        | f
 public      | bi_reports                      | last_generated                 | timestamp | f        | f
 public      | bi_reports                      | next_generation                | timestamp | f        | f
 public      | bi_reports                      | status                         | varchar   | f        | f
 public      | bi_reports                      | created_by                     | int4      | f        | f
 public      | bi_reports                      | created_at                     | timestamp | f        | f
 public      | bi_reports                      | updated_at                     | timestamp | f        | f
 public      | business_processes              | id                             | int4      | t        | t
 public      | business_processes              | name                           | varchar   | t        | f
 public      | business_processes              | description                    | text      | f        | f
 public      | business_processes              | owner_id                       | int4      | f        | f
 public      | business_processes              | status                         | varchar   | f        | f
 public      | business_processes              | created_at                     | timestamp | f        | f
 public      | business_processes              | updated_at                     | timestamp | f        | f
 public      | candidate_ai_scores             | id                             | int4      | t        | t
 public      | candidate_ai_scores             | candidate_id                   | int4      | t        | f
 public      | candidate_ai_scores             | job_posting_id                 | int4      | t        | f
 public      | candidate_ai_scores             | application_id                 | int4      | f        | f
 public      | candidate_ai_scores             | overall_score                  | int4      | t        | f
 public      | candidate_ai_scores             | confidence_score               | float8    | t        | f
 public      | candidate_ai_scores             | technical_skills_score         | int4      | f        | f
 public      | candidate_ai_scores             | experience_score               | int4      | f        | f
 public      | candidate_ai_scores             | motivation_score               | int4      | f        | f
 public      | candidate_ai_scores             | cultural_fit_score             | int4      | f        | f
 public      | candidate_ai_scores             | growth_potential_score         | int4      | f        | f
 public      | candidate_ai_scores             | ai_reasoning                   | text      | f        | f
 public      | candidate_ai_scores             | recommendation                 | varchar   | f        | f
 public      | candidate_ai_scores             | strengths                      | text      | f        | f
 public      | candidate_ai_scores             | concerns                       | text      | f        | f
 public      | candidate_ai_scores             | ai_model_used                  | varchar   | f        | f
 public      | candidate_ai_scores             | evaluation_version             | varchar   | f        | f
 public      | candidate_ai_scores             | evaluated_by                   | int4      | t        | f
 public      | candidate_ai_scores             | created_at                     | timestamp | f        | f
 public      | candidate_ai_scores             | updated_at                     | timestamp | f        | f
 public      | candidate_skills                | id                             | int4      | t        | t
 public      | candidate_skills                | candidate_id                   | int4      | t        | f
 public      | candidate_skills                | skill_name                     | varchar   | t        | f
 public      | candidate_skills                | skill_category                 | varchar   | f        | f
 public      | candidate_skills                | skill_level                    | int4      | f        | f
 public      | candidate_skills                | years_experience               | int4      | f        | f
 public      | candidate_skills                | extracted_from_cv              | bool      | f        | f
 public      | candidate_skills                | confidence_score               | float8    | f        | f
 public      | candidate_skills                | created_at                     | timestamp | f        | f
 public      | candidate_skills                | updated_at                     | timestamp | f        | f
 public      | candidates                      | id                             | int4      | t        | t
 public      | candidates                      | first_name                     | varchar   | t        | f
 public      | candidates                      | last_name                      | varchar   | t        | f
 public      | candidates                      | email                          | varchar   | t        | f
 public      | candidates                      | phone                          | varchar   | f        | f
 public      | candidates                      | location                       | varchar   | f        | f
 public      | candidates                      | linkedin_url                   | varchar   | f        | f
 public      | candidates                      | current_cv_path                | varchar   | f        | f
 public      | candidates                      | cv_last_updated                | timestamp | f        | f
 public      | candidates                      | cv_analysis_data               | text      | f        | f
 public      | candidates                      | source                         | varchar   | f        | f
 public      | candidates                      | status                         | varchar   | f        | f
 public      | candidates                      | notes                          | text      | f        | f
 public      | candidates                      | tags                           | text      | f        | f
 public      | candidates                      | hired_as_user_id               | int4      | f        | f
 public      | candidates                      | hired_date                     | date      | f        | f
 public      | candidates                      | created_at                     | timestamp | f        | f
 public      | candidates                      | updated_at                     | timestamp | f        | f
 public      | case_studies                    | id                             | int4      | t        | t
 public      | case_studies                    | title                          | varchar   | t        | f
 public      | case_studies                    | overview                       | text      | t        | f
 public      | case_studies                    | content                        | text      | f        | f
 public      | case_studies                    | case_type                      | varchar   | t        | f
 public      | case_studies                    | primary_sector                 | varchar   | f        | f
 public      | case_studies                    | secondary_sectors              | json      | f        | f
 public      | case_studies                    | project_id                     | int4      | f        | f
 public      | case_studies                    | client_id                      | int4      | f        | f
 public      | case_studies                    | technologies                   | json      | f        | f
 public      | case_studies                    | business_kpis                  | json      | f        | f
 public      | case_studies                    | implementation_duration        | int4      | f        | f
 public      | case_studies                    | team_size                      | int4      | f        | f
 public      | case_studies                    | status                         | varchar   | f        | f
 public      | case_studies                    | generated_by_ai                | bool      | f        | f
 public      | case_studies                    | ai_prompt_used                 | text      | f        | f
 public      | case_studies                    | target_audience                | varchar   | f        | f
 public      | case_studies                    | created_by                     | int4      | t        | f
 public      | case_studies                    | approved_by                    | int4      | f        | f
 public      | case_studies                    | created_at                     | timestamp | f        | f
 public      | case_studies                    | updated_at                     | timestamp | f        | f
 public      | case_studies                    | approved_at                    | timestamp | f        | f
 public      | certification_audits            | id                             | int4      | t        | t
 public      | certification_audits            | certification_id               | int4      | t        | f
 public      | certification_audits            | audit_type                     | varchar   | t        | f
 public      | certification_audits            | planned_date                   | date      | t        | f
 public      | certification_audits            | actual_date                    | date      | f        | f
 public      | certification_audits            | duration_days                  | int4      | f        | f
 public      | certification_audits            | lead_auditor                   | varchar   | f        | f
 public      | certification_audits            | audit_team                     | json      | f        | f
 public      | certification_audits            | auditor_contact                | text      | f        | f
 public      | certification_audits            | status                         | varchar   | f        | f
 public      | certification_audits            | result                         | varchar   | f        | f
 public      | certification_audits            | overall_score                  | int4      | f        | f
 public      | certification_audits            | major_findings                 | int4      | f        | f
 public      | certification_audits            | minor_findings                 | int4      | f        | f
 public      | certification_audits            | observations                   | int4      | f        | f
 public      | certification_audits            | findings_summary               | text      | f        | f
 public      | certification_audits            | audit_report_path              | varchar   | f        | f
 public      | certification_audits            | corrective_actions_path        | varchar   | f        | f
 public      | certification_audits            | evidence_folder_path           | varchar   | f        | f
 public      | certification_audits            | audit_cost                     | float8    | f        | f
 public      | certification_audits            | travel_expenses                | float8    | f        | f
 public      | certification_audits            | corrective_actions_due         | date      | f        | f
 public      | certification_audits            | follow_up_audit_date           | date      | f        | f
 public      | certification_audits            | next_audit_type                | varchar   | f        | f
 public      | certification_audits            | notes                          | text      | f        | f
 public      | certification_audits            | created_at                     | timestamp | f        | f
 public      | certification_audits            | updated_at                     | timestamp | f        | f
 public      | certification_audits            | created_by                     | int4      | t        | f
 public      | certification_documents         | id                             | int4      | t        | t
 public      | certification_documents         | certification_id               | int4      | t        | f
 public      | certification_documents         | document_name                  | varchar   | t        | f
 public      | certification_documents         | document_type                  | varchar   | t        | f
 public      | certification_documents         | document_category              | varchar   | f        | f
 public      | certification_documents         | file_path                      | varchar   | t        | f
 public      | certification_documents         | file_size                      | int4      | f        | f
 public      | certification_documents         | file_type                      | varchar   | f        | f
 public      | certification_documents         | version                        | varchar   | f        | f
 public      | certification_documents         | is_current_version             | bool      | f        | f
 public      | certification_documents         | previous_version_id            | int4      | f        | f
 public      | certification_documents         | description                    | text      | f        | f
 public      | certification_documents         | tags                           | json      | f        | f
 public      | certification_documents         | created_at                     | timestamp | f        | f
 public      | certification_documents         | updated_at                     | timestamp | f        | f
 public      | certification_documents         | uploaded_by                    | int4      | t        | f
 public      | certification_documents         | access_level                   | varchar   | f        | f
 public      | certification_standards         | code                           | varchar   | t        | f
 public      | certification_standards         | name                           | varchar   | t        | f
 public      | certification_standards         | version                        | varchar   | f        | f
 public      | certification_standards         | category                       | varchar   | t        | f
 public      | certification_standards         | industry_sector                | varchar   | f        | f
 public      | certification_standards         | typical_validity_years         | int4      | f        | f
 public      | certification_standards         | renewal_notice_months          | int4      | f        | f
 public      | certification_standards         | audit_frequency_months         | int4      | f        | f
 public      | certification_standards         | estimated_cost_min             | float8    | f        | f
 public      | certification_standards         | estimated_cost_max             | float8    | f        | f
 public      | certification_standards         | currency                       | varchar   | f        | f
 public      | certification_standards         | requirements                   | json      | f        | f
 public      | certification_standards         | preparatory_tasks              | json      | f        | f
 public      | certification_standards         | documentation_required         | json      | f        | f
 public      | certification_standards         | description                    | text      | f        | f
 public      | certification_standards         | issuing_body                   | varchar   | f        | f
 public      | certification_standards         | website_url                    | varchar   | f        | f
 public      | certification_standards         | is_active                      | bool      | f        | f
 public      | certification_standards         | tenant_enabled                 | bool      | f        | f
 public      | certification_standards         | tenant_priority                | int4      | f        | f
 public      | clients                         | id                             | int4      | t        | t
 public      | clients                         | name                           | varchar   | t        | f
 public      | clients                         | industry                       | varchar   | f        | f
 public      | clients                         | description                    | text      | f        | f
 public      | clients                         | website                        | varchar   | f        | f
 public      | clients                         | address                        | varchar   | f        | f
 public      | clients                         | created_at                     | timestamp | f        | f
 public      | clients                         | updated_at                     | timestamp | f        | f
 public      | clients                         | status                         | varchar   | f        | t
 public      | clients                         | email                          | varchar   | f        | f
 public      | clients                         | phone                          | varchar   | f        | f
 public      | clients                         | vat_number                     | varchar   | f        | f
 public      | clients                         | fiscal_code                    | varchar   | f        | f
 public      | communication_reactions         | id                             | int4      | t        | t
 public      | communication_reactions         | user_id                        | int4      | t        | f
 public      | communication_reactions         | target_type                    | varchar   | t        | f
 public      | communication_reactions         | target_id                      | int4      | t        | f
 public      | communication_reactions         | reaction_type                  | varchar   | t        | f
 public      | communication_reactions         | created_at                     | timestamp | f        | t
 public      | company_certifications          | id                             | int4      | t        | t
 public      | company_certifications          | standard_code                  | varchar   | t        | f
 public      | company_certifications          | certificate_number             | varchar   | f        | f
 public      | company_certifications          | certifying_body                | varchar   | t        | f
 public      | company_certifications          | certifying_body_contact        | text      | f        | f
 public      | company_certifications          | issue_date                     | date      | t        | f
 public      | company_certifications          | expiry_date                    | date      | t        | f
 public      | company_certifications          | next_audit_date                | date      | f        | f
 public      | company_certifications          | status                         | varchar   | f        | f
 public      | company_certifications          | health_score                   | int4      | f        | f
 public      | company_certifications          | initial_cost                   | float8    | f        | f
 public      | company_certifications          | annual_maintenance_cost        | float8    | f        | f
 public      | company_certifications          | last_audit_cost                | float8    | f        | f
 public      | company_certifications          | responsible_person_id          | int4      | t        | f
 public      | company_certifications          | backup_person_id               | int4      | f        | f
 public      | company_certifications          | certificate_file_path          | varchar   | f        | f
 public      | company_certifications          | documentation_folder_path      | varchar   | f        | f
 public      | company_certifications          | created_at                     | timestamp | f        | f
 public      | company_certifications          | updated_at                     | timestamp | f        | f
 public      | company_certifications          | created_by                     | int4      | t        | f
 public      | company_certifications          | project_id                     | int4      | f        | f
 public      | company_communications          | id                             | int4      | t        | t
 public      | company_communications          | title                          | varchar   | t        | f
 public      | company_communications          | content                        | text      | t        | f
 public      | company_communications          | author_id                      | int4      | t        | f
 public      | company_communications          | image_url                      | varchar   | f        | f
 public      | company_communications          | is_published                   | bool      | f        | f
 public      | company_communications          | created_at                     | timestamp | f        | f
 public      | company_communications          | updated_at                     | timestamp | f        | f
 public      | company_communications          | communication_type             | varchar   | f        | t
 public      | company_communications          | target_audience                | varchar   | f        | f
 public      | company_communications          | priority_level                 | varchar   | f        | t
 public      | company_communications          | is_pinned                      | bool      | f        | t
 public      | company_communications          | scheduled_at                   | timestamp | f        | f
 public      | company_communications          | expires_at                     | timestamp | f        | f
 public      | company_communications          | tags                           | _text     | f        | f
 public      | company_communications          | allow_comments                 | bool      | f        | t
 public      | company_communications          | view_count                     | int4      | f        | t
 public      | company_event_registrations     | id                             | int4      | t        | t
 public      | company_event_registrations     | event_id                       | int4      | t        | f
 public      | company_event_registrations     | user_id                        | int4      | t        | f
 public      | company_event_registrations     | status                         | varchar   | f        | t
 public      | company_event_registrations     | registered_at                  | timestamp | f        | t
 public      | company_event_registrations     | notes                          | text      | f        | f
 public      | company_events                  | id                             | int4      | t        | t
 public      | company_events                  | title                          | varchar   | t        | f
 public      | company_events                  | description                    | text      | f        | f
 public      | company_events                  | project_id                     | int4      | f        | f
 public      | company_events                  | start_time                     | timestamp | t        | f
 public      | company_events                  | end_time                       | timestamp | t        | f
 public      | company_events                  | location                       | varchar   | f        | f
 public      | company_events                  | event_type                     | varchar   | f        | f
 public      | company_events                  | created_by                     | int4      | f        | f
 public      | company_events                  | created_at                     | timestamp | f        | f
 public      | company_events                  | is_company_wide                | bool      | f        | f
 public      | company_events                  | max_participants               | int4      | f        | f
 public      | company_events                  | registration_required          | bool      | f        | f
 public      | company_events                  | registration_deadline          | timestamp | f        | f
 public      | company_events                  | is_public                      | bool      | f        | f
 public      | company_events                  | tags                           | _varchar  | f        | f
 public      | company_events                  | allow_comments                 | bool      | f        | f
 public      | company_events                  | updated_at                     | timestamp | f        | f
 public      | company_invoicing_settings      | id                             | int4      | t        | t
 public      | company_invoicing_settings      | company_name                   | varchar   | t        | f
 public      | company_invoicing_settings      | vat_number                     | varchar   | f        | f
 public      | company_invoicing_settings      | fiscal_code                    | varchar   | f        | f
 public      | company_invoicing_settings      | address                        | text      | f        | f
 public      | company_invoicing_settings      | phone                          | varchar   | f        | f
 public      | company_invoicing_settings      | email                          | varchar   | f        | f
 public      | company_invoicing_settings      | pec                            | varchar   | f        | f
 public      | company_invoicing_settings      | default_vat_rate               | numeric   | f        | f
 public      | company_invoicing_settings      | default_retention_rate         | numeric   | f        | f
 public      | company_invoicing_settings      | default_payment_terms          | int4      | f        | f
 public      | company_invoicing_settings      | invoice_prefix                 | varchar   | f        | f
 public      | company_invoicing_settings      | current_year                   | int4      | f        | f
 public      | company_invoicing_settings      | last_number                    | int4      | f        | f
 public      | company_invoicing_settings      | tax_regime                     | varchar   | f        | f
 public      | company_invoicing_settings      | is_active                      | bool      | f        | f
 public      | company_invoicing_settings      | created_at                     | timestamp | f        | f
 public      | company_invoicing_settings      | updated_at                     | timestamp | f        | f
 public      | company_profiles                | id                             | int4      | t        | t
 public      | company_profiles                | company_name                   | varchar   | f        | f
 public      | company_profiles                | mission                        | text      | f        | f
 public      | company_profiles                | vision                         | text      | f        | f
 public      | company_profiles                | values                         | text      | f        | f
 public      | company_profiles                | industry                       | varchar   | f        | f
 public      | company_profiles                | business_model                 | varchar   | f        | f
 public      | company_profiles                | company_size                   | varchar   | f        | f
 public      | company_profiles                | target_market                  | text      | f        | f
 public      | company_profiles                | competitive_advantages         | text      | f        | f
 public      | company_profiles                | key_challenges                 | text      | f        | f
 public      | company_profiles                | strategic_objectives           | json      | f        | f
 public      | company_profiles                | market_segment                 | varchar   | f        | f
 public      | company_profiles                | geographic_focus               | varchar   | f        | f
 public      | company_profiles                | revenue_model                  | varchar   | f        | f
 public      | company_profiles                | current_stage                  | varchar   | f        | f
 public      | company_profiles                | analysis_focus_areas           | json      | f        | f
 public      | company_profiles                | reporting_preferences          | json      | f        | f
 public      | company_profiles                | is_active                      | bool      | f        | f
 public      | company_profiles                | created_at                     | timestamp | f        | f
 public      | company_profiles                | updated_at                     | timestamp | f        | f
 public      | company_profiles                | updated_by                     | int4      | f        | f
 public      | compliance_audit_logs           | id                             | int4      | t        | t
 public      | compliance_audit_logs           | user_id                        | int4      | f        | f
 public      | compliance_audit_logs           | session_id                     | varchar   | f        | f
 public      | compliance_audit_logs           | action_type                    | varchar   | t        | f
 public      | compliance_audit_logs           | resource_type                  | varchar   | f        | f
 public      | compliance_audit_logs           | resource_id                    | varchar   | f        | f
 public      | compliance_audit_logs           | endpoint                       | varchar   | f        | f
 public      | compliance_audit_logs           | method                         | varchar   | f        | f
 public      | compliance_audit_logs           | ip_address                     | varchar   | f        | f
 public      | compliance_audit_logs           | user_agent                     | text      | f        | f
 public      | compliance_audit_logs           | request_data                   | json      | f        | f
 public      | compliance_audit_logs           | response_status                | int4      | f        | f
 public      | compliance_audit_logs           | response_size                  | int4      | f        | f
 public      | compliance_audit_logs           | processing_time_ms             | int4      | f        | f
 public      | compliance_audit_logs           | compliance_context             | json      | f        | f
 public      | compliance_audit_logs           | timestamp                      | timestamp | t        | f
 public      | compliance_audit_logs           | risk_level                     | varchar   | f        | f
 public      | compliance_audit_logs           | data_classification            | varchar   | f        | f
 public      | compliance_audit_logs           | retention_policy               | varchar   | f        | f
 public      | compliance_audit_logs           | is_sensitive                   | bool      | f        | f
 public      | compliance_events               | id                             | int4      | t        | t
 public      | compliance_events               | event_type                     | varchar   | t        | f
 public      | compliance_events               | event_category                 | varchar   | f        | f
 public      | compliance_events               | severity                       | varchar   | f        | f
 public      | compliance_events               | user_id                        | int4      | f        | f
 public      | compliance_events               | affected_users                 | json      | f        | f
 public      | compliance_events               | title                          | varchar   | t        | f
 public      | compliance_events               | description                    | text      | t        | f
 public      | compliance_events               | compliance_framework           | varchar   | f        | f
 public      | compliance_events               | policy_reference               | varchar   | f        | f
 public      | compliance_events               | regulatory_impact              | bool      | f        | f
 public      | compliance_events               | source_ip                      | varchar   | f        | f
 public      | compliance_events               | source_system                  | varchar   | f        | f
 public      | compliance_events               | affected_resources             | json      | f        | f
 public      | compliance_events               | risk_score                     | int4      | f        | f
 public      | compliance_events               | event_metadata                 | json      | f        | f
 public      | compliance_events               | evidence_data                  | json      | f        | f
 public      | compliance_events               | created_at                     | timestamp | t        | f
 public      | compliance_events               | detected_at                    | timestamp | f        | f
 public      | compliance_events               | occurred_at                    | timestamp | f        | f
 public      | compliance_events               | status                         | varchar   | f        | f
 public      | compliance_events               | resolved_at                    | timestamp | f        | f
 public      | compliance_events               | resolved_by                    | int4      | f        | f
 public      | compliance_events               | resolution_notes               | text      | f        | f
 public      | compliance_events               | notification_sent              | bool      | f        | f
 public      | compliance_events               | escalated                      | bool      | f        | f
 public      | compliance_events               | escalated_at                   | timestamp | f        | f
 public      | compliance_policies             | id                             | int4      | t        | t
 public      | compliance_policies             | name                           | varchar   | t        | f
 public      | compliance_policies             | description                    | text      | f        | f
 public      | compliance_policies             | policy_type                    | varchar   | t        | f
 public      | compliance_policies             | rules_config                   | json      | f        | f
 public      | compliance_policies             | trigger_conditions             | json      | f        | f
 public      | compliance_policies             | actions                        | json      | f        | f
 public      | compliance_policies             | framework                      | varchar   | f        | f
 public      | compliance_policies             | article_reference              | varchar   | f        | f
 public      | compliance_policies             | is_active                      | bool      | f        | f
 public      | compliance_policies             | version                        | varchar   | f        | f
 public      | compliance_policies             | effective_date                 | timestamp | f        | f
 public      | compliance_policies             | expiry_date                    | timestamp | f        | f
 public      | compliance_policies             | owner_role                     | varchar   | f        | f
 public      | compliance_policies             | reviewed_by                    | int4      | f        | f
 public      | compliance_policies             | last_review_date               | timestamp | f        | f
 public      | compliance_policies             | next_review_date               | timestamp | f        | f
 public      | compliance_policies             | created_at                     | timestamp | f        | f
 public      | compliance_policies             | updated_at                     | timestamp | f        | f
 public      | compliance_reports              | id                             | int4      | t        | t
 public      | compliance_reports              | name                           | varchar   | t        | f
 public      | compliance_reports              | report_type                    | varchar   | t        | f
 public      | compliance_reports              | period_type                    | varchar   | f        | f
 public      | compliance_reports              | period_start                   | timestamp | t        | f
 public      | compliance_reports              | period_end                     | timestamp | t        | f
 public      | compliance_reports              | summary_data                   | json      | f        | f
 public      | compliance_reports              | detailed_data                  | json      | f        | f
 public      | compliance_reports              | recommendations                | json      | f        | f
 public      | compliance_reports              | framework                      | varchar   | f        | f
 public      | compliance_reports              | scope                          | varchar   | f        | f
 public      | compliance_reports              | risk_level                     | varchar   | f        | f
 public      | compliance_reports              | status                         | varchar   | f        | f
 public      | compliance_reports              | generated_by                   | int4      | f        | f
 public      | compliance_reports              | generated_at                   | timestamp | f        | f
 public      | compliance_reports              | reviewed_by                    | int4      | f        | f
 public      | compliance_reports              | reviewed_at                    | timestamp | f        | f
 public      | compliance_reports              | file_path                      | varchar   | f        | f
 public      | compliance_reports              | file_hash                      | varchar   | f        | f
 public      | contacts                        | id                             | int4      | t        | t
 public      | contacts                        | client_id                      | int4      | t        | f
 public      | contacts                        | first_name                     | varchar   | t        | f
 public      | contacts                        | last_name                      | varchar   | t        | f
 public      | contacts                        | position                       | varchar   | f        | f
 public      | contacts                        | email                          | varchar   | f        | f
 public      | contacts                        | phone                          | varchar   | f        | f
 public      | contacts                        | notes                          | text      | f        | f
 public      | contacts                        | created_at                     | timestamp | f        | f
 public      | contacts                        | updated_at                     | timestamp | f        | f
 public      | contracts                       | id                             | int4      | t        | t
 public      | contracts                       | client_id                      | int4      | t        | f
 public      | contracts                       | contract_number                | varchar   | t        | f
 public      | contracts                       | title                          | varchar   | t        | f
 public      | contracts                       | description                    | text      | f        | f
 public      | contracts                       | contract_type                  | varchar   | f        | f
 public      | contracts                       | hourly_rate                    | float8    | f        | f
 public      | contracts                       | budget_hours                   | float8    | f        | f
 public      | contracts                       | budget_amount                  | float8    | f        | f
 public      | contracts                       | start_date                     | date      | t        | f
 public      | contracts                       | end_date                       | date      | f        | f
 public      | contracts                       | status                         | varchar   | f        | f
 public      | contracts                       | created_at                     | timestamp | f        | f
 public      | contracts                       | updated_at                     | timestamp | f        | f
 public      | contracts                       | milestone_amount               | float8    | f        | f
 public      | contracts                       | milestone_count                | int4      | f        | f
 public      | contracts                       | subscription_frequency         | varchar   | f        | f
 public      | contracts                       | subscription_amount            | float8    | f        | f
 public      | contracts                       | retainer_amount                | float8    | f        | f
 public      | contracts                       | retainer_frequency             | varchar   | f        | f
 public      | core_competencies               | id                             | int4      | t        | t
 public      | core_competencies               | name                           | varchar   | t        | f
 public      | core_competencies               | description                    | text      | f        | f
 public      | core_competencies               | category                       | varchar   | f        | f
 public      | core_competencies               | market_positioning             | text      | f        | f
 public      | core_competencies               | skill_ids                      | json      | f        | f
 public      | core_competencies               | min_team_size                  | int4      | f        | f
 public      | core_competencies               | avg_proficiency_required       | float8    | f        | f
 public      | core_competencies               | business_value                 | text      | f        | f
 public      | core_competencies               | target_markets                 | json      | f        | f
 public      | core_competencies               | competitive_advantage          | text      | f        | f
 public      | core_competencies               | is_active                      | bool      | f        | f
 public      | core_competencies               | created_at                     | timestamp | f        | f
 public      | core_competencies               | updated_at                     | timestamp | f        | f
 public      | departments                     | id                             | int4      | t        | t
 public      | departments                     | name                           | varchar   | t        | f
 public      | departments                     | description                    | text      | f        | f
 public      | departments                     | manager_id                     | int4      | f        | f
 public      | departments                     | parent_id                      | int4      | f        | f
 public      | departments                     | budget                         | float8    | f        | t
 public      | departments                     | is_active                      | bool      | f        | t
 public      | departments                     | created_at                     | timestamp | f        | t
 public      | departments                     | updated_at                     | timestamp | f        | t
 public      | direct_messages                 | id                             | int4      | t        | t
 public      | direct_messages                 | sender_id                      | int4      | t        | f
 public      | direct_messages                 | recipient_id                   | int4      | t        | f
 public      | direct_messages                 | message                        | text      | t        | f
 public      | direct_messages                 | is_read                        | bool      | f        | t
 public      | direct_messages                 | read_at                        | timestamp | f        | f
 public      | direct_messages                 | created_at                     | timestamp | f        | t
 public      | direct_messages                 | updated_at                     | timestamp | f        | t
 public      | documents                       | id                             | int4      | t        | t
 public      | documents                       | title                          | varchar   | t        | f
 public      | documents                       | description                    | text      | f        | f
 public      | documents                       | category                       | varchar   | f        | f
 public      | documents                       | file_path                      | varchar   | t        | f
 public      | documents                       | uploaded_by                    | int4      | t        | f
 public      | documents                       | version                        | varchar   | f        | f
 public      | documents                       | created_at                     | timestamp | f        | f
 public      | documents                       | updated_at                     | timestamp | f        | f
 public      | employee_job_levels             | id                             | int4      | t        | t
 public      | employee_job_levels             | user_id                        | int4      | t        | f
 public      | employee_job_levels             | job_level_id                   | int4      | t        | f
 public      | employee_job_levels             | start_date                     | date      | t        | f
 public      | employee_job_levels             | end_date                       | date      | f        | f
 public      | employee_job_levels             | current_salary                 | float8    | f        | f
 public      | employee_job_levels             | notes                          | text      | f        | f
 public      | employee_job_levels             | is_active                      | bool      | f        | f
 public      | employee_job_levels             | created_by                     | int4      | f        | f
 public      | employee_job_levels             | created_at                     | timestamp | f        | f
 public      | engagement_campaigns            | id                             | int4      | t        | t
 public      | engagement_campaigns            | name                           | varchar   | t        | f
 public      | engagement_campaigns            | description                    | text      | f        | f
 public      | engagement_campaigns            | start_date                     | date      | t        | f
 public      | engagement_campaigns            | end_date                       | date      | t        | f
 public      | engagement_campaigns            | status                         | varchar   | f        | f
 public      | engagement_campaigns            | points_multiplier              | numeric   | f        | f
 public      | engagement_campaigns            | objectives_config              | json      | f        | f
 public      | engagement_campaigns            | points_rules                   | json      | f        | f
 public      | engagement_campaigns            | created_by_id                  | int4      | f        | f
 public      | engagement_campaigns            | created_at                     | timestamp | f        | f
 public      | engagement_campaigns            | updated_at                     | timestamp | f        | f
 public      | engagement_leaderboards         | id                             | int4      | t        | t
 public      | engagement_leaderboards         | user_id                        | int4      | t        | f
 public      | engagement_leaderboards         | campaign_id                    | int4      | f        | f
 public      | engagement_leaderboards         | ranking_position               | int4      | t        | f
 public      | engagement_leaderboards         | total_points                   | int4      | t        | f
 public      | engagement_leaderboards         | period_type                    | varchar   | f        | f
 public      | engagement_leaderboards         | period_start                   | date      | f        | f
 public      | engagement_leaderboards         | period_end                     | date      | f        | f
 public      | engagement_leaderboards         | calculated_at                  | timestamp | f        | f
 public      | engagement_levels               | id                             | int4      | t        | t
 public      | engagement_levels               | name                           | varchar   | t        | f
 public      | engagement_levels               | description                    | text      | f        | f
 public      | engagement_levels               | points_threshold               | int4      | t        | f
 public      | engagement_levels               | level_order                    | int4      | t        | f
 public      | engagement_levels               | rewards_config                 | json      | f        | f
 public      | engagement_levels               | color_hex                      | varchar   | f        | f
 public      | engagement_levels               | icon_name                      | varchar   | f        | f
 public      | engagement_levels               | is_active                      | bool      | f        | f
 public      | engagement_levels               | created_at                     | timestamp | f        | f
 public      | engagement_points               | id                             | int4      | t        | t
 public      | engagement_points               | user_id                        | int4      | t        | f
 public      | engagement_points               | campaign_id                    | int4      | f        | f
 public      | engagement_points               | points_earned                  | int4      | t        | f
 public      | engagement_points               | source_type                    | varchar   | t        | f
 public      | engagement_points               | source_id                      | int4      | f        | f
 public      | engagement_points               | action_type                    | varchar   | f        | f
 public      | engagement_points               | resource_type                  | varchar   | f        | f
 public      | engagement_points               | resource_id                    | int4      | f        | f
 public      | engagement_points               | description                    | varchar   | f        | f
 public      | engagement_points               | multiplier_applied             | numeric   | f        | f
 public      | engagement_points               | earned_at                      | timestamp | f        | f
 public      | engagement_points               | created_at                     | timestamp | f        | f
 public      | engagement_rewards              | id                             | int4      | t        | t
 public      | engagement_rewards              | name                           | varchar   | t        | f
 public      | engagement_rewards              | description                    | text      | f        | f
 public      | engagement_rewards              | points_cost                    | int4      | t        | f
 public      | engagement_rewards              | reward_type                    | varchar   | t        | f
 public      | engagement_rewards              | available_from                 | date      | f        | f
 public      | engagement_rewards              | available_until                | date      | f        | f
 public      | engagement_rewards              | campaign_id                    | int4      | f        | f
 public      | engagement_rewards              | max_redemptions                | int4      | f        | f
 public      | engagement_rewards              | current_redemptions            | int4      | f        | f
 public      | engagement_rewards              | per_user_limit                 | int4      | f        | f
 public      | engagement_rewards              | image_url                      | varchar   | f        | f
 public      | engagement_rewards              | external_url                   | varchar   | f        | f
 public      | engagement_rewards              | is_active                      | bool      | f        | f
 public      | engagement_rewards              | created_by_id                  | int4      | f        | f
 public      | engagement_rewards              | created_at                     | timestamp | f        | f
 public      | engagement_rewards              | updated_at                     | timestamp | f        | f
 public      | engagement_user_profiles        | id                             | int4      | t        | t
 public      | engagement_user_profiles        | user_id                        | int4      | t        | f
 public      | engagement_user_profiles        | total_points                   | int4      | f        | f
 public      | engagement_user_profiles        | total_points_spent             | int4      | f        | f
 public      | engagement_user_profiles        | available_points               | int4      | f        | f
 public      | engagement_user_profiles        | current_level_id               | int4      | f        | f
 public      | engagement_user_profiles        | next_level_id                  | int4      | f        | f
 public      | engagement_user_profiles        | total_logins                   | int4      | f        | f
 public      | engagement_user_profiles        | total_actions                  | int4      | f        | f
 public      | engagement_user_profiles        | streak_days                    | int4      | f        | f
 public      | engagement_user_profiles        | last_activity_date             | date      | f        | f
 public      | engagement_user_profiles        | created_at                     | timestamp | f        | f
 public      | engagement_user_profiles        | updated_at                     | timestamp | f        | f
 public      | engagement_user_profiles        | activities_completed           | int4      | f        | t
 public      | engagement_user_profiles        | achievements_count             | int4      | f        | t
 public      | engagement_user_profiles        | current_streak_days            | int4      | f        | t
 public      | engagement_user_rewards         | id                             | int4      | t        | t
 public      | engagement_user_rewards         | user_id                        | int4      | t        | f
 public      | engagement_user_rewards         | reward_id                      | int4      | t        | f
 public      | engagement_user_rewards         | points_spent                   | int4      | t        | f
 public      | engagement_user_rewards         | redemption_status              | varchar   | f        | f
 public      | engagement_user_rewards         | redemption_notes               | text      | f        | f
 public      | engagement_user_rewards         | admin_notes                    | text      | f        | f
 public      | engagement_user_rewards         | redeemed_at                    | timestamp | f        | f
 public      | engagement_user_rewards         | fulfilled_at                   | timestamp | f        | f
 public      | error_patterns                  | id                             | int4      | t        | t
 public      | error_patterns                  | pattern_hash                   | varchar   | t        | f
 public      | error_patterns                  | error_type                     | varchar   | t        | f
 public      | error_patterns                  | message_pattern                | text      | t        | f
 public      | error_patterns                  | file_pattern                   | varchar   | f        | f
 public      | error_patterns                  | occurrence_count               | int4      | f        | f
 public      | error_patterns                  | first_seen                     | timestamp | f        | f
 public      | error_patterns                  | last_seen                      | timestamp | f        | f
 public      | error_patterns                  | severity                       | varchar   | f        | f
 public      | error_patterns                  | is_auto_healable               | bool      | f        | f
 public      | error_patterns                  | healing_success_rate           | float8    | f        | f
 public      | error_patterns                  | last_healing_attempt           | timestamp | f        | f
 public      | error_patterns                  | ai_analysis                    | text      | f        | f
 public      | error_patterns                  | suggested_fix                  | text      | f        | f
 public      | feature_flags                   | id                             | int4      | t        | t
 public      | feature_flags                   | feature_key                    | varchar   | t        | f
 public      | feature_flags                   | display_name                   | varchar   | t        | f
 public      | feature_flags                   | description                    | text      | f        | f
 public      | feature_flags                   | is_enabled                     | bool      | t        | f
 public      | feature_flags                   | created_at                     | timestamp | t        | f
 public      | feature_flags                   | updated_at                     | timestamp | t        | f
 public      | feature_flags                   | updated_by                     | int4      | f        | f
 public      | feature_flags                   | category                       | varchar   | t        | t
 public      | forum_comments                  | id                             | int4      | t        | t
 public      | forum_comments                  | topic_id                       | int4      | t        | f
 public      | forum_comments                  | author_id                      | int4      | t        | f
 public      | forum_comments                  | content                        | text      | t        | f
 public      | forum_comments                  | parent_comment_id              | int4      | f        | f
 public      | forum_comments                  | is_edited                      | bool      | f        | t
 public      | forum_comments                  | edited_at                      | timestamp | f        | f
 public      | forum_comments                  | created_at                     | timestamp | f        | t
 public      | forum_comments                  | updated_at                     | timestamp | f        | t
 public      | forum_topics                    | id                             | int4      | t        | t
 public      | forum_topics                    | title                          | varchar   | t        | f
 public      | forum_topics                    | description                    | text      | f        | f
 public      | forum_topics                    | author_id                      | int4      | t        | f
 public      | forum_topics                    | category                       | varchar   | f        | f
 public      | forum_topics                    | is_pinned                      | bool      | f        | t
 public      | forum_topics                    | is_locked                      | bool      | f        | t
 public      | forum_topics                    | view_count                     | int4      | f        | t
 public      | forum_topics                    | reply_count                    | int4      | f        | t
 public      | forum_topics                    | last_activity_at               | timestamp | f        | t
 public      | forum_topics                    | created_at                     | timestamp | f        | t
 public      | forum_topics                    | updated_at                     | timestamp | f        | t
 public      | funding_applications            | id                             | int4      | t        | t
 public      | funding_applications            | opportunity_id                 | int4      | t        | f
 public      | funding_applications            | project_title                  | varchar   | t        | f
 public      | funding_applications            | project_description            | text      | f        | f
 public      | funding_applications            | project_duration_months        | int4      | f        | f
 public      | funding_applications            | requested_amount               | float8    | t        | f
 public      | funding_applications            | co_financing_amount            | float8    | f        | f
 public      | funding_applications            | project_manager_id             | int4      | f        | f
 public      | funding_applications            | team_composition               | text      | f        | f
 public      | funding_applications            | budget_breakdown               | text      | f        | f
 public      | funding_applications            | status                         | varchar   | f        | f
 public      | funding_applications            | submission_date                | timestamp | f        | f
 public      | funding_applications            | evaluation_feedback            | text      | f        | f
 public      | funding_applications            | approval_date                  | timestamp | f        | f
 public      | funding_applications            | rejection_reason               | text      | f        | f
 public      | funding_applications            | documents_checklist            | text      | f        | f
 public      | funding_applications            | attachments_path               | varchar   | f        | f
 public      | funding_applications            | approved_amount                | float8    | f        | f
 public      | funding_applications            | funding_percentage             | float8    | f        | f
 public      | funding_applications            | linked_project_id              | int4      | f        | f
 public      | funding_applications            | created_by                     | int4      | t        | f
 public      | funding_applications            | internal_notes                 | text      | f        | f
 public      | funding_applications            | priority_score                 | float8    | f        | f
 public      | funding_applications            | created_at                     | timestamp | f        | f
 public      | funding_applications            | updated_at                     | timestamp | f        | f
 public      | funding_expenses                | id                             | int4      | t        | t
 public      | funding_expenses                | application_id                 | int4      | t        | f
 public      | funding_expenses                | project_id                     | int4      | f        | f
 public      | funding_expenses                | description                    | varchar   | t        | f
 public      | funding_expenses                | amount                         | float8    | t        | f
 public      | funding_expenses                | expense_date                   | date      | t        | f
 public      | funding_expenses                | category                       | varchar   | f        | f
 public      | funding_expenses                | timesheet_entry_id             | int4      | f        | f
 public      | funding_expenses                | is_eligible                    | bool      | f        | f
 public      | funding_expenses                | approval_status                | varchar   | f        | f
 public      | funding_expenses                | receipt_path                   | varchar   | f        | f
 public      | funding_expenses                | notes                          | text      | f        | f
 public      | funding_expenses                | approved_by                    | int4      | f        | f
 public      | funding_expenses                | approved_date                  | timestamp | f        | f
 public      | funding_expenses                | rejection_reason               | text      | f        | f
 public      | funding_expenses                | created_at                     | timestamp | f        | f
 public      | funding_expenses                | updated_at                     | timestamp | f        | f
 public      | funding_opportunities           | id                             | int4      | t        | t
 public      | funding_opportunities           | title                          | varchar   | t        | f
 public      | funding_opportunities           | description                    | text      | f        | f
 public      | funding_opportunities           | source_entity                  | varchar   | f        | f
 public      | funding_opportunities           | program_name                   | varchar   | f        | f
 public      | funding_opportunities           | call_identifier                | varchar   | f        | f
 public      | funding_opportunities           | total_budget                   | float8    | f        | f
 public      | funding_opportunities           | max_grant_amount               | float8    | f        | f
 public      | funding_opportunities           | min_grant_amount               | float8    | f        | f
 public      | funding_opportunities           | contribution_percentage        | float8    | f        | f
 public      | funding_opportunities           | publication_date               | date      | f        | f
 public      | funding_opportunities           | application_deadline           | date      | t        | f
 public      | funding_opportunities           | evaluation_period_end          | date      | f        | f
 public      | funding_opportunities           | funding_period_start           | date      | f        | f
 public      | funding_opportunities           | project_duration_months        | int4      | f        | f
 public      | funding_opportunities           | eligibility_criteria           | text      | f        | f
 public      | funding_opportunities           | evaluation_criteria            | text      | f        | f
 public      | funding_opportunities           | required_documents             | text      | f        | f
 public      | funding_opportunities           | target_sectors                 | text      | f        | f
 public      | funding_opportunities           | geographic_scope               | varchar   | f        | f
 public      | funding_opportunities           | status                         | varchar   | f        | f
 public      | funding_opportunities           | application_procedure          | text      | f        | f
 public      | funding_opportunities           | contact_info                   | text      | f        | f
 public      | funding_opportunities           | official_url                   | varchar   | f        | f
 public      | funding_opportunities           | guidelines_url                 | varchar   | f        | f
 public      | funding_opportunities           | form_url                       | varchar   | f        | f
 public      | funding_opportunities           | created_by                     | int4      | t        | f
 public      | funding_opportunities           | is_active                      | bool      | f        | f
 public      | funding_opportunities           | internal_notes                 | text      | f        | f
 public      | funding_opportunities           | match_score                    | float8    | f        | f
 public      | funding_opportunities           | created_at                     | timestamp | f        | f
 public      | funding_opportunities           | updated_at                     | timestamp | f        | f
 public      | funding_opportunities           | ai_generated                   | bool      | f        | t
 public      | funding_opportunities           | ai_search_query                | text      | f        | f
 public      | funding_opportunities           | ai_match_score                 | float8    | f        | t
 public      | funding_opportunities           | ai_content                     | text      | f        | f
 public      | healing_sessions                | id                             | int4      | t        | t
 public      | healing_sessions                | error_pattern_id               | int4      | f        | f
 public      | healing_sessions                | initiated_by                   | int4      | f        | f
 public      | healing_sessions                | healing_type                   | varchar   | t        | f
 public      | healing_sessions                | started_at                     | timestamp | f        | f
 public      | healing_sessions                | completed_at                   | timestamp | f        | f
 public      | healing_sessions                | duration_seconds               | int4      | f        | f
 public      | healing_sessions                | status                         | varchar   | f        | f
 public      | healing_sessions                | success                        | bool      | f        | f
 public      | healing_sessions                | claude_prompt_generated        | text      | f        | f
 public      | healing_sessions                | actions_taken                  | text      | f        | f
 public      | healing_sessions                | error_before_healing           | text      | f        | f
 public      | healing_sessions                | verification_results           | text      | f        | f
 public      | healing_sessions                | effectiveness_score            | float8    | f        | f
 public      | healing_sessions                | admin_feedback                 | text      | f        | f
 public      | help_analytics                  | id                             | int4      | t        | t
 public      | help_analytics                  | date                           | date      | t        | f
 public      | help_analytics                  | period_type                    | varchar   | f        | f
 public      | help_analytics                  | total_views                    | int4      | f        | f
 public      | help_analytics                  | unique_users                   | int4      | f        | f
 public      | help_analytics                  | most_viewed_content_id         | int4      | f        | f
 public      | help_analytics                  | chat_sessions                  | int4      | f        | f
 public      | help_analytics                  | avg_messages_per_session       | float8    | f        | f
 public      | help_analytics                  | ai_resolution_rate             | float8    | f        | f
 public      | help_analytics                  | avg_satisfaction_score         | float8    | f        | f
 public      | help_analytics                  | total_searches                 | int4      | f        | f
 public      | help_analytics                  | no_results_searches            | int4      | f        | f
 public      | help_analytics                  | top_search_terms               | text      | f        | f
 public      | help_analytics                  | bounce_rate                    | float8    | f        | f
 public      | help_analytics                  | avg_session_duration           | float8    | f        | f
 public      | help_analytics                  | calculated_at                  | timestamp | f        | f
 public      | help_categories                 | id                             | int4      | t        | t
 public      | help_categories                 | name                           | varchar   | t        | f
 public      | help_categories                 | slug                           | varchar   | t        | f
 public      | help_categories                 | description                    | text      | f        | f
 public      | help_categories                 | icon                           | varchar   | f        | f
 public      | help_categories                 | color                          | varchar   | f        | f
 public      | help_categories                 | parent_id                      | int4      | f        | f
 public      | help_categories                 | sort_order                     | int4      | f        | f
 public      | help_categories                 | is_active                      | bool      | f        | f
 public      | help_categories                 | is_public                      | bool      | f        | f
 public      | help_categories                 | required_permission            | varchar   | f        | f
 public      | help_categories                 | created_by                     | int4      | t        | f
 public      | help_categories                 | created_at                     | timestamp | f        | f
 public      | help_categories                 | updated_at                     | timestamp | f        | f
 public      | help_content                    | id                             | int4      | t        | t
 public      | help_content                    | title                          | varchar   | t        | f
 public      | help_content                    | slug                           | varchar   | t        | f
 public      | help_content                    | content                        | text      | t        | f
 public      | help_content                    | excerpt                        | text      | f        | f
 public      | help_content                    | category_id                    | int4      | t        | f
 public      | help_content                    | content_type                   | varchar   | f        | f
 public      | help_content                    | difficulty_level               | varchar   | f        | f
 public      | help_content                    | estimated_read_time            | int4      | f        | f
 public      | help_content                    | tags                           | text      | f        | f
 public      | help_content                    | keywords                       | text      | f        | f
 public      | help_content                    | related_modules                | text      | f        | f
 public      | help_content                    | status                         | varchar   | f        | f
 public      | help_content                    | is_published                   | bool      | f        | f
 public      | help_content                    | published_at                   | timestamp | f        | f
 public      | help_content                    | version                        | varchar   | f        | f
 public      | help_content                    | previous_version_id            | int4      | f        | f
 public      | help_content                    | is_public                      | bool      | f        | f
 public      | help_content                    | required_permission            | varchar   | f        | f
 public      | help_content                    | featured                       | bool      | f        | f
 public      | help_content                    | view_count                     | int4      | f        | f
 public      | help_content                    | helpful_votes                  | int4      | f        | f
 public      | help_content                    | not_helpful_votes              | int4      | f        | f
 public      | help_content                    | created_by                     | int4      | t        | f
 public      | help_content                    | updated_by                     | int4      | f        | f
 public      | help_content                    | reviewed_by                    | int4      | f        | f
 public      | help_content                    | created_at                     | timestamp | f        | f
 public      | help_content                    | updated_at                     | timestamp | f        | f
 public      | help_content                    | reviewed_at                    | timestamp | f        | f
 public      | help_conversations              | id                             | int4      | t        | t
 public      | help_conversations              | user_id                        | int4      | t        | f
 public      | help_conversations              | session_id                     | varchar   | t        | f
 public      | help_conversations              | title                          | varchar   | f        | f
 public      | help_conversations              | category                       | varchar   | f        | f
 public      | help_conversations              | current_module                 | varchar   | f        | f
 public      | help_conversations              | messages                       | text      | t        | f
 public      | help_conversations              | message_count                  | int4      | f        | f
 public      | help_conversations              | ai_confidence_avg              | float8    | f        | f
 public      | help_conversations              | escalated_to_human             | bool      | f        | f
 public      | help_conversations              | escalation_reason              | text      | f        | f
 public      | help_conversations              | status                         | varchar   | f        | f
 public      | help_conversations              | resolution_type                | varchar   | f        | f
 public      | help_conversations              | user_satisfaction              | int4      | f        | f
 public      | help_conversations              | user_feedback                  | text      | f        | f
 public      | help_conversations              | started_at                     | timestamp | f        | f
 public      | help_conversations              | last_activity                  | timestamp | f        | f
 public      | help_conversations              | resolved_at                    | timestamp | f        | f
 public      | help_conversations              | created_at                     | timestamp | f        | f
 public      | help_conversations              | updated_at                     | timestamp | f        | f
 public      | help_feedback                   | id                             | int4      | t        | t
 public      | help_feedback                   | user_id                        | int4      | t        | f
 public      | help_feedback                   | content_id                     | int4      | f        | f
 public      | help_feedback                   | conversation_id                | int4      | f        | f
 public      | help_feedback                   | feedback_type                  | varchar   | t        | f
 public      | help_feedback                   | rating                         | int4      | f        | f
 public      | help_feedback                   | is_helpful                     | bool      | f        | f
 public      | help_feedback                   | feedback_text                  | text      | f        | f
 public      | help_feedback                   | suggestion                     | text      | f        | f
 public      | help_feedback                   | current_page                   | varchar   | f        | f
 public      | help_feedback                   | user_agent                     | varchar   | f        | f
 public      | help_feedback                   | status                         | varchar   | f        | f
 public      | help_feedback                   | admin_response                 | text      | f        | f
 public      | help_feedback                   | responded_by                   | int4      | f        | f
 public      | help_feedback                   | responded_at                   | timestamp | f        | f
 public      | help_feedback                   | created_at                     | timestamp | f        | f
 public      | help_feedback                   | updated_at                     | timestamp | f        | f
 public      | hr_analytics                    | id                             | int4      | t        | t
 public      | hr_analytics                    | date                           | date      | t        | f
 public      | hr_analytics                    | total_conversations            | int4      | f        | f
 public      | hr_analytics                    | unique_users                   | int4      | f        | f
 public      | hr_analytics                    | avg_response_time_ms           | float8    | f        | f
 public      | hr_analytics                    | category_distribution          | text      | f        | f
 public      | hr_analytics                    | feedback_distribution          | text      | f        | f
 public      | hr_analytics                    | total_kb_entries               | int4      | f        | f
 public      | hr_analytics                    | ai_generated_entries           | int4      | f        | f
 public      | hr_analytics                    | created_at                     | timestamp | f        | f
 public      | hr_chat_conversations           | id                             | int4      | t        | t
 public      | hr_chat_conversations           | user_id                        | int4      | t        | f
 public      | hr_chat_conversations           | session_id                     | varchar   | t        | f
 public      | hr_chat_conversations           | user_message                   | text      | t        | f
 public      | hr_chat_conversations           | bot_response                   | text      | t        | f
 public      | hr_chat_conversations           | category_detected              | varchar   | f        | f
 public      | hr_chat_conversations           | confidence_score               | varchar   | f        | f
 public      | hr_chat_conversations           | kb_entries_used                | text      | f        | f
 public      | hr_chat_conversations           | response_time_ms               | int4      | f        | f
 public      | hr_chat_conversations           | user_feedback                  | varchar   | f        | f
 public      | hr_chat_conversations           | created_at                     | timestamp | f        | f
 public      | hr_content_templates            | id                             | int4      | t        | t
 public      | hr_content_templates            | name                           | varchar   | t        | f
 public      | hr_content_templates            | category                       | varchar   | t        | f
 public      | hr_content_templates            | description                    | text      | f        | f
 public      | hr_content_templates            | prompt_template                | text      | t        | f
 public      | hr_content_templates            | required_fields                | text      | f        | f
 public      | hr_content_templates            | output_format                  | text      | f        | f
 public      | hr_content_templates            | usage_count                    | int4      | f        | f
 public      | hr_content_templates            | last_used                      | timestamp | f        | f
 public      | hr_content_templates            | is_active                      | bool      | f        | f
 public      | hr_content_templates            | created_at                     | timestamp | f        | f
 public      | hr_knowledge_base               | id                             | int4      | t        | t
 public      | hr_knowledge_base               | title                          | varchar   | t        | f
 public      | hr_knowledge_base               | content                        | text      | t        | f
 public      | hr_knowledge_base               | category                       | varchar   | t        | f
 public      | hr_knowledge_base               | tags                           | text      | f        | f
 public      | hr_knowledge_base               | is_active                      | bool      | f        | f
 public      | hr_knowledge_base               | created_with_ai                | bool      | f        | f
 public      | hr_knowledge_base               | ai_sources                     | text      | f        | f
 public      | hr_knowledge_base               | ai_confidence                  | varchar   | f        | f
 public      | hr_knowledge_base               | created_by                     | int4      | t        | f
 public      | hr_knowledge_base               | created_at                     | timestamp | f        | f
 public      | hr_knowledge_base               | updated_at                     | timestamp | f        | f
 public      | integration_settings            | id                             | int4      | t        | t
 public      | integration_settings            | provider                       | varchar   | t        | f
 public      | integration_settings            | api_key_encrypted              | text      | f        | f
 public      | integration_settings            | company_id                     | varchar   | f        | f
 public      | integration_settings            | settings_json                  | json      | f        | f
 public      | integration_settings            | is_active                      | bool      | f        | f
 public      | integration_settings            | last_sync_date                 | timestamp | f        | f
 public      | integration_settings            | last_error                     | text      | f        | f
 public      | integration_settings            | created_by                     | int4      | t        | f
 public      | integration_settings            | created_at                     | timestamp | f        | f
 public      | integration_settings            | updated_at                     | timestamp | f        | f
 public      | interview_sessions              | id                             | int4      | t        | t
 public      | interview_sessions              | application_id                 | int4      | t        | f
 public      | interview_sessions              | interview_type                 | varchar   | t        | f
 public      | interview_sessions              | scheduled_date                 | timestamp | t        | f
 public      | interview_sessions              | duration_minutes               | int4      | f        | f
 public      | interview_sessions              | location                       | varchar   | f        | f
 public      | interview_sessions              | interviewer_id                 | int4      | t        | f
 public      | interview_sessions              | additional_interviewers        | text      | f        | f
 public      | interview_sessions              | status                         | varchar   | f        | f
 public      | interview_sessions              | score                          | int4      | f        | f
 public      | interview_sessions              | notes                          | text      | f        | f
 public      | interview_sessions              | feedback                       | text      | f        | f
 public      | interview_sessions              | recommendation                 | varchar   | f        | f
 public      | interview_sessions              | created_at                     | timestamp | f        | f
 public      | interview_sessions              | updated_at                     | timestamp | f        | f
 public      | interview_sessions              | completed_at                   | timestamp | f        | f
 public      | invoice_lines                   | id                             | int4      | t        | t
 public      | invoice_lines                   | invoice_id                     | int4      | t        | f
 public      | invoice_lines                   | project_id                     | int4      | f        | f
 public      | invoice_lines                   | contract_id                    | int4      | f        | f
 public      | invoice_lines                   | description                    | text      | t        | f
 public      | invoice_lines                   | total_hours                    | float8    | t        | f
 public      | invoice_lines                   | hourly_rate                    | float8    | t        | f
 public      | invoice_lines                   | total_amount                   | float8    | t        | f
 public      | invoice_lines                   | created_at                     | timestamp | f        | f
 public      | invoices                        | id                             | int4      | t        | t
 public      | invoices                        | client_id                      | int4      | t        | f
 public      | invoices                        | invoice_number                 | varchar   | t        | f
 public      | invoices                        | billing_period_start           | date      | t        | f
 public      | invoices                        | billing_period_end             | date      | t        | f
 public      | invoices                        | issue_date                     | date      | t        | f
 public      | invoices                        | due_date                       | date      | t        | f
 public      | invoices                        | status                         | varchar   | f        | f
 public      | invoices                        | subtotal                       | float8    | f        | f
 public      | invoices                        | tax_rate                       | float8    | f        | f
 public      | invoices                        | tax_amount                     | float8    | f        | f
 public      | invoices                        | total_amount                   | float8    | f        | f
 public      | invoices                        | notes                          | text      | f        | f
 public      | invoices                        | created_at                     | timestamp | f        | f
 public      | invoices                        | updated_at                     | timestamp | f        | f
 public      | job_levels                      | id                             | int4      | t        | t
 public      | job_levels                      | name                           | varchar   | t        | f
 public      | job_levels                      | description                    | text      | f        | f
 public      | job_levels                      | is_active                      | bool      | f        | f
 public      | job_levels                      | created_at                     | timestamp | f        | f
 public      | job_levels                      | updated_at                     | timestamp | f        | f
 public      | job_levels                      | level_number                   | int4      | t        | f
 public      | job_levels                      | min_salary                     | float8    | f        | f
 public      | job_levels                      | max_salary                     | float8    | f        | f
 public      | job_levels                      | typical_years_experience       | int4      | f        | f
 public      | job_levels                      | is_management                  | bool      | f        | t
 public      | job_levels                      | parent_level_id                | int4      | f        | f
 public      | job_levels                      | level                          | varchar   | f        | f
 public      | job_levels                      | benefits                       | text      | f        | f
 public      | job_levels                      | requirements                   | text      | f        | f
 public      | job_levels                      | category                       | varchar   | f        | f
 public      | job_postings                    | id                             | int4      | t        | t
 public      | job_postings                    | title                          | varchar   | t        | f
 public      | job_postings                    | description                    | text      | f        | f
 public      | job_postings                    | requirements                   | text      | f        | f
 public      | job_postings                    | responsibilities               | text      | f        | f
 public      | job_postings                    | location                       | varchar   | f        | f
 public      | job_postings                    | remote_allowed                 | bool      | f        | f
 public      | job_postings                    | employment_type                | varchar   | f        | f
 public      | job_postings                    | salary_min                     | numeric   | f        | f
 public      | job_postings                    | salary_max                     | numeric   | f        | f
 public      | job_postings                    | salary_currency                | varchar   | f        | f
 public      | job_postings                    | project_id                     | int4      | f        | f
 public      | job_postings                    | proposal_id                    | int4      | f        | f
 public      | job_postings                    | department_id                  | int4      | f        | f
 public      | job_postings                    | status                         | varchar   | f        | f
 public      | job_postings                    | is_public                      | bool      | f        | f
 public      | job_postings                    | created_by                     | int4      | t        | f
 public      | job_postings                    | created_at                     | timestamp | f        | f
 public      | job_postings                    | updated_at                     | timestamp | f        | f
 public      | job_postings                    | published_at                   | timestamp | f        | f
 public      | job_postings                    | closed_at                      | timestamp | f        | f
 public      | kpis                            | id                             | int4      | t        | t
 public      | kpis                            | name                           | varchar   | t        | f
 public      | kpis                            | description                    | text      | f        | f
 public      | kpis                            | category                       | varchar   | f        | f
 public      | kpis                            | target_value                   | float8    | f        | f
 public      | kpis                            | current_value                  | float8    | f        | f
 public      | kpis                            | unit                           | varchar   | f        | f
 public      | kpis                            | frequency                      | varchar   | f        | f
 public      | kpis                            | created_at                     | timestamp | f        | f
 public      | kpis                            | updated_at                     | timestamp | f        | f
 public      | kpis                            | progress                       | float8    | f        | t
 public      | kpis                            | project_id                     | int4      | f        | f
 public      | kpis                            | owner_id                       | int4      | f        | f
 public      | market_prospects                | id                             | int4      | t        | t
 public      | market_prospects                | company_name                   | varchar   | t        | f
 public      | market_prospects                | sector                         | varchar   | f        | f
 public      | market_prospects                | size_category                  | varchar   | f        | f
 public      | market_prospects                | location                       | varchar   | f        | f
 public      | market_prospects                | website                        | varchar   | f        | f
 public      | market_prospects                | contact_info                   | json      | f        | f
 public      | market_prospects                | technology_needs               | json      | f        | f
 public      | market_prospects                | matching_competencies          | json      | f        | f
 public      | market_prospects                | fit_score                      | float8    | f        | f
 public      | market_prospects                | estimated_budget_min           | float8    | f        | f
 public      | market_prospects                | estimated_budget_max           | float8    | f        | f
 public      | market_prospects                | lead_status                    | varchar   | f        | f
 public      | market_prospects                | source                         | varchar   | f        | f
 public      | market_prospects                | source_data                    | json      | f        | f
 public      | market_prospects                | notes                          | text      | f        | f
 public      | market_prospects                | assigned_to                    | int4      | f        | f
 public      | market_prospects                | created_at                     | timestamp | f        | f
 public      | market_prospects                | updated_at                     | timestamp | f        | f
 public      | monthly_timesheets              | id                             | int4      | t        | t
 public      | monthly_timesheets              | user_id                        | int4      | t        | f
 public      | monthly_timesheets              | year                           | int4      | t        | f
 public      | monthly_timesheets              | month                          | int4      | t        | f
 public      | monthly_timesheets              | status                         | varchar   | f        | f
 public      | monthly_timesheets              | submission_date                | timestamp | f        | f
 public      | monthly_timesheets              | approval_date                  | timestamp | f        | f
 public      | monthly_timesheets              | approved_by                    | int4      | f        | f
 public      | monthly_timesheets              | rejection_reason               | text      | f        | f
 public      | monthly_timesheets              | created_at                     | timestamp | f        | f
 public      | monthly_timesheets              | updated_at                     | timestamp | f        | f
 public      | news                            | id                             | int4      | t        | t
 public      | news                            | title                          | varchar   | t        | f
 public      | news                            | content                        | text      | t        | f
 public      | news                            | author_id                      | int4      | t        | f
 public      | news                            | image_url                      | varchar   | f        | f
 public      | news                            | is_published                   | bool      | f        | f
 public      | news                            | created_at                     | timestamp | f        | f
 public      | news                            | updated_at                     | timestamp | f        | f
 public      | notifications                   | id                             | int4      | t        | t
 public      | notifications                   | user_id                        | int4      | t        | f
 public      | notifications                   | title                          | varchar   | t        | f
 public      | notifications                   | message                        | text      | t        | f
 public      | notifications                   | link                           | varchar   | f        | f
 public      | notifications                   | is_read                        | bool      | f        | f
 public      | notifications                   | created_at                     | timestamp | f        | f
 public      | notifications                   | type                           | varchar   | f        | t
 public      | oauth_accounts                  | id                             | int4      | t        | t
 public      | oauth_accounts                  | user_id                        | int4      | t        | f
 public      | oauth_accounts                  | provider                       | varchar   | t        | f
 public      | oauth_accounts                  | provider_user_id               | varchar   | t        | f
 public      | oauth_accounts                  | email                          | varchar   | t        | f
 public      | oauth_accounts                  | display_name                   | varchar   | f        | f
 public      | oauth_accounts                  | avatar_url                     | varchar   | f        | f
 public      | oauth_accounts                  | created_at                     | timestamp | t        | f
 public      | oauth_accounts                  | last_login                     | timestamp | f        | f
 public      | performance_feedbacks           | id                             | int4      | t        | t
 public      | performance_feedbacks           | review_id                      | int4      | t        | f
 public      | performance_feedbacks           | feedback_giver_id              | int4      | t        | f
 public      | performance_feedbacks           | feedback_type                  | varchar   | t        | f
 public      | performance_feedbacks           | title                          | varchar   | t        | f
 public      | performance_feedbacks           | content                        | text      | t        | f
 public      | performance_feedbacks           | rating                         | float8    | f        | f
 public      | performance_feedbacks           | technical_feedback             | text      | f        | f
 public      | performance_feedbacks           | behavioral_feedback            | text      | f        | f
 public      | performance_feedbacks           | suggestions                    | text      | f        | f
 public      | performance_feedbacks           | is_anonymous                   | bool      | f        | t
 public      | performance_feedbacks           | is_visible_to_employee         | bool      | f        | t
 public      | performance_feedbacks           | created_at                     | timestamp | f        | t
 public      | performance_feedbacks           | updated_at                     | timestamp | f        | t
 public      | performance_feedbacks           | feedback_period                | varchar   | f        | t
 public      | performance_feedbacks           | strengths                      | text      | f        | f
 public      | performance_feedbacks           | improvements                   | text      | f        | f
 public      | performance_feedbacks           | status                         | varchar   | f        | t
 public      | performance_feedbacks           | submitted_date                 | timestamp | f        | f
 public      | performance_feedbacks           | from_user_id                   | int4      | f        | f
 public      | performance_feedbacks           | to_user_id                     | int4      | f        | f
 public      | performance_feedbacks           | priority                       | varchar   | f        | t
 public      | performance_feedbacks           | requires_response              | bool      | f        | t
 public      | performance_feedbacks           | acknowledged_date              | timestamp | f        | f
 public      | performance_feedbacks           | resolved_date                  | timestamp | f        | f
 public      | performance_goals               | id                             | int4      | t        | t
 public      | performance_goals               | employee_id                    | int4      | t        | f
 public      | performance_goals               | review_id                      | int4      | f        | f
 public      | performance_goals               | title                          | varchar   | t        | f
 public      | performance_goals               | description                    | text      | t        | f
 public      | performance_goals               | category                       | varchar   | f        | f
 public      | performance_goals               | target_year                    | int4      | t        | f
 public      | performance_goals               | start_date                     | date      | f        | f
 public      | performance_goals               | target_date                    | date      | f        | f
 public      | performance_goals               | status                         | varchar   | f        | t
 public      | performance_goals               | progress_percentage            | float8    | f        | t
 public      | performance_goals               | completion_date                | date      | f        | f
 public      | performance_goals               | success_criteria               | text      | f        | f
 public      | performance_goals               | measurable_outcomes            | json      | f        | f
 public      | performance_goals               | weight                         | float8    | f        | t
 public      | performance_goals               | priority                       | varchar   | f        | t
 public      | performance_goals               | achievement_rating             | float8    | f        | f
 public      | performance_goals               | manager_assessment             | text      | f        | f
 public      | performance_goals               | employee_self_assessment       | text      | f        | f
 public      | performance_goals               | created_at                     | timestamp | f        | t
 public      | performance_goals               | updated_at                     | timestamp | f        | t
 public      | performance_goals               | created_by                     | int4      | f        | f
 public      | performance_goals               | set_by_id                      | int4      | f        | f
 public      | performance_goals               | quarter                        | varchar   | f        | f
 public      | performance_goals               | year                           | int4      | f        | f
 public      | performance_goals               | notes                          | text      | f        | f
 public      | performance_goals               | completion_notes               | text      | f        | f
 public      | performance_goals               | progress                       | int4      | f        | t
 public      | performance_goals               | is_template                    | bool      | f        | t
 public      | performance_goals               | template_id                    | int4      | f        | f
 public      | performance_goals               | assigned_by_id                 | int4      | f        | f
 public      | performance_goals               | visibility                     | varchar   | f        | t
 public      | performance_kpis                | id                             | int4      | t        | t
 public      | performance_kpis                | goal_id                        | int4      | t        | f
 public      | performance_kpis                | name                           | varchar   | t        | f
 public      | performance_kpis                | description                    | text      | f        | f
 public      | performance_kpis                | unit_of_measure                | varchar   | f        | f
 public      | performance_kpis                | target_value                   | float8    | t        | f
 public      | performance_kpis                | current_value                  | float8    | f        | t
 public      | performance_kpis                | baseline_value                 | float8    | f        | f
 public      | performance_kpis                | measurement_frequency          | varchar   | f        | f
 public      | performance_kpis                | last_measured_at               | date      | f        | f
 public      | performance_kpis                | is_active                      | bool      | f        | t
 public      | performance_kpis                | created_at                     | timestamp | f        | t
 public      | performance_kpis                | updated_at                     | timestamp | f        | t
 public      | performance_kpis                | measurement_unit               | varchar   | f        | f
 public      | performance_kpis                | is_achieved                    | bool      | f        | t
 public      | performance_kpis                | achievement_date               | date      | f        | f
 public      | performance_review_participants | id                             | int4      | t        | t
 public      | performance_review_participants | review_id                      | int4      | t        | f
 public      | performance_review_participants | participant_id                 | int4      | t        | f
 public      | performance_review_participants | role                           | varchar   | t        | f
 public      | performance_review_participants | invited_at                     | timestamp | f        | t
 public      | performance_review_participants | responded_at                   | timestamp | f        | f
 public      | performance_review_participants | is_completed                   | bool      | f        | t
 public      | performance_review_participants | participant_role               | varchar   | f        | f
 public      | performance_review_participants | invitation_sent                | bool      | f        | t
 public      | performance_review_participants | invitation_date                | timestamp | f        | f
 public      | performance_review_participants | feedback_completed             | bool      | f        | t
 public      | performance_review_participants | completion_date                | timestamp | f        | f
 public      | performance_review_participants | created_at                     | timestamp | f        | f
 public      | performance_reviews             | id                             | int4      | t        | t
 public      | performance_reviews             | employee_id                    | int4      | t        | f
 public      | performance_reviews             | reviewer_id                    | int4      | t        | f
 public      | performance_reviews             | review_year                    | int4      | t        | f
 public      | performance_reviews             | review_period_start            | date      | t        | f
 public      | performance_reviews             | review_period_end              | date      | t        | f
 public      | performance_reviews             | status                         | varchar   | f        | t
 public      | performance_reviews             | due_date                       | date      | f        | f
 public      | performance_reviews             | overall_rating                 | float8    | f        | f
 public      | performance_reviews             | technical_skills_rating        | float8    | f        | f
 public      | performance_reviews             | soft_skills_rating             | float8    | f        | f
 public      | performance_reviews             | goals_achievement_rating       | float8    | f        | f
 public      | performance_reviews             | leadership_rating              | float8    | f        | f
 public      | performance_reviews             | teamwork_rating                | float8    | f        | f
 public      | performance_reviews             | communication_rating           | float8    | f        | f
 public      | performance_reviews             | initiative_rating              | float8    | f        | f
 public      | performance_reviews             | strengths                      | text      | f        | f
 public      | performance_reviews             | areas_for_improvement          | text      | f        | f
 public      | performance_reviews             | manager_comments               | text      | f        | f
 public      | performance_reviews             | employee_comments              | text      | f        | f
 public      | performance_reviews             | hr_comments                    | text      | f        | f
 public      | performance_reviews             | promotion_recommendation       | bool      | f        | t
 public      | performance_reviews             | salary_increase_recommendation | float8    | f        | f
 public      | performance_reviews             | bonus_recommendation           | float8    | f        | f
 public      | performance_reviews             | training_recommendations       | text      | f        | f
 public      | performance_reviews             | employee_signed_at             | timestamp | f        | f
 public      | performance_reviews             | manager_signed_at              | timestamp | f        | f
 public      | performance_reviews             | hr_signed_at                   | timestamp | f        | f
 public      | performance_reviews             | created_at                     | timestamp | f        | t
 public      | performance_reviews             | updated_at                     | timestamp | f        | t
 public      | performance_reviews             | created_by                     | int4      | f        | f
 public      | performance_reviews             | template_id                    | int4      | f        | f
 public      | performance_reviews             | comments                       | text      | f        | f
 public      | performance_reviews             | submitted_date                 | timestamp | f        | f
 public      | performance_reviews             | completed_date                 | timestamp | f        | f
 public      | performance_reviews             | approved_date                  | timestamp | f        | f
 public      | performance_reviews             | approved_by                    | int4      | f        | f
 public      | performance_reviews             | achievements                   | text      | f        | f
 public      | performance_reviews             | areas_improvement              | text      | f        | f
 public      | performance_reviews             | development_goals              | text      | f        | f
 public      | performance_reviews             | reviewer_comments              | text      | f        | f
 public      | performance_rewards             | id                             | int4      | t        | t
 public      | performance_rewards             | employee_id                    | int4      | t        | f
 public      | performance_rewards             | goal_id                        | int4      | f        | f
 public      | performance_rewards             | review_id                      | int4      | f        | f
 public      | performance_rewards             | reward_type                    | varchar   | t        | f
 public      | performance_rewards             | title                          | varchar   | t        | f
 public      | performance_rewards             | description                    | text      | f        | f
 public      | performance_rewards             | monetary_value                 | float8    | f        | f
 public      | performance_rewards             | percentage_value               | float8    | f        | f
 public      | performance_rewards             | awarded_date                   | date      | f        | f
 public      | performance_rewards             | effective_date                 | date      | f        | f
 public      | performance_rewards             | expiry_date                    | date      | f        | f
 public      | performance_rewards             | status                         | varchar   | f        | t
 public      | performance_rewards             | approved_by                    | int4      | f        | f
 public      | performance_rewards             | approved_at                    | timestamp | f        | f
 public      | performance_rewards             | disbursed_at                   | timestamp | f        | f
 public      | performance_rewards             | notes                          | text      | f        | f
 public      | performance_rewards             | created_at                     | timestamp | f        | t
 public      | performance_rewards             | updated_at                     | timestamp | f        | t
 public      | performance_rewards             | created_by                     | int4      | f        | f
 public      | performance_rewards             | currency                       | varchar   | f        | t
 public      | performance_rewards             | award_date                     | date      | f        | f
 public      | performance_rewards             | approved_date                  | date      | f        | f
 public      | performance_rewards             | awarded_by_id                  | int4      | f        | f
 public      | performance_rewards             | approved_by_hr                 | int4      | f        | f
 public      | performance_templates           | id                             | int4      | t        | t
 public      | performance_templates           | name                           | varchar   | t        | f
 public      | performance_templates           | description                    | text      | f        | f
 public      | performance_templates           | template_type                  | varchar   | f        | f
 public      | performance_templates           | job_level                      | varchar   | f        | f
 public      | performance_templates           | department                     | varchar   | f        | f
 public      | performance_templates           | evaluation_criteria            | json      | f        | f
 public      | performance_templates           | rating_scale                   | json      | f        | f
 public      | performance_templates           | is_active                      | bool      | f        | t
 public      | performance_templates           | created_at                     | timestamp | f        | t
 public      | performance_templates           | updated_at                     | timestamp | f        | t
 public      | performance_templates           | created_by                     | int4      | f        | f
 public      | performance_templates           | fields_config                  | text      | f        | f
 public      | performance_templates           | target_role                    | varchar   | f        | f
 public      | performance_templates           | is_default                     | bool      | f        | t
 public      | personnel_rates                 | id                             | int4      | t        | t
 public      | personnel_rates                 | user_id                        | int4      | t        | f
 public      | personnel_rates                 | daily_rate                     | float8    | t        | f
 public      | personnel_rates                 | valid_from                     | date      | t        | f
 public      | personnel_rates                 | valid_to                       | date      | f        | f
 public      | personnel_rates                 | currency                       | varchar   | f        | f
 public      | personnel_rates                 | notes                          | varchar   | f        | f
 public      | personnel_rates                 | created_at                     | timestamp | f        | f
 public      | poll_options                    | id                             | int4      | t        | t
 public      | poll_options                    | poll_id                        | int4      | t        | f
 public      | poll_options                    | option_text                    | varchar   | t        | f
 public      | poll_options                    | vote_count                     | int4      | f        | t
 public      | poll_options                    | created_at                     | timestamp | f        | t
 public      | poll_votes                      | id                             | int4      | t        | t
 public      | poll_votes                      | poll_id                        | int4      | t        | f
 public      | poll_votes                      | option_id                      | int4      | t        | f
 public      | poll_votes                      | user_id                        | int4      | t        | f
 public      | poll_votes                      | created_at                     | timestamp | f        | t
 public      | polls                           | id                             | int4      | t        | t
 public      | polls                           | title                          | varchar   | t        | f
 public      | polls                           | description                    | text      | f        | f
 public      | polls                           | author_id                      | int4      | t        | f
 public      | polls                           | is_anonymous                   | bool      | f        | t
 public      | polls                           | multiple_choice                | bool      | f        | t
 public      | polls                           | expires_at                     | timestamp | f        | f
 public      | polls                           | is_active                      | bool      | f        | t
 public      | polls                           | total_votes                    | int4      | f        | t
 public      | polls                           | created_at                     | timestamp | f        | t
 public      | polls                           | updated_at                     | timestamp | f        | t
 public      | pre_invoice_lines               | id                             | int4      | t        | t
 public      | pre_invoice_lines               | pre_invoice_id                 | int4      | t        | f
 public      | pre_invoice_lines               | project_id                     | int4      | f        | f
 public      | pre_invoice_lines               | description                    | varchar   | t        | f
 public      | pre_invoice_lines               | total_hours                    | numeric   | f        | f
 public      | pre_invoice_lines               | hourly_rate                    | numeric   | f        | f
 public      | pre_invoice_lines               | total_amount                   | numeric   | f        | f
 public      | pre_invoice_lines               | timesheet_entries_ids          | json      | f        | f
 public      | pre_invoice_lines               | created_at                     | timestamp | f        | f
 public      | pre_invoices                    | id                             | int4      | t        | t
 public      | pre_invoices                    | client_id                      | int4      | t        | f
 public      | pre_invoices                    | contract_id                    | int4      | f        | f
 public      | pre_invoices                    | pre_invoice_number             | varchar   | t        | f
 public      | pre_invoices                    | billing_period_start           | date      | t        | f
 public      | pre_invoices                    | billing_period_end             | date      | t        | f
 public      | pre_invoices                    | generated_date                 | date      | f        | f
 public      | pre_invoices                    | status                         | varchar   | f        | f
 public      | pre_invoices                    | subtotal                       | numeric   | f        | f
 public      | pre_invoices                    | vat_rate                       | numeric   | f        | f
 public      | pre_invoices                    | vat_amount                     | numeric   | f        | f
 public      | pre_invoices                    | retention_rate                 | numeric   | f        | f
 public      | pre_invoices                    | retention_amount               | numeric   | f        | f
 public      | pre_invoices                    | total_amount                   | numeric   | f        | f
 public      | pre_invoices                    | external_invoice_id            | varchar   | f        | f
 public      | pre_invoices                    | external_status                | varchar   | f        | f
 public      | pre_invoices                    | external_pdf_url               | varchar   | f        | f
 public      | pre_invoices                    | external_sent_date             | date      | f        | f
 public      | pre_invoices                    | notes                          | text      | f        | f
 public      | pre_invoices                    | created_by                     | int4      | t        | f
 public      | pre_invoices                    | created_at                     | timestamp | f        | f
 public      | pre_invoices                    | updated_at                     | timestamp | f        | f
 public      | process_steps                   | id                             | int4      | t        | t
 public      | process_steps                   | process_id                     | int4      | t        | f
 public      | process_steps                   | name                           | varchar   | t        | f
 public      | process_steps                   | description                    | text      | f        | f
 public      | process_steps                   | order                          | int4      | t        | f
 public      | process_steps                   | responsible_role               | varchar   | f        | f
 public      | process_steps                   | estimated_time                 | float8    | f        | f
 public      | products                        | id                             | int4      | t        | t
 public      | products                        | name                           | varchar   | t        | f
 public      | products                        | description                    | text      | f        | f
 public      | products                        | category                       | varchar   | f        | f
 public      | products                        | price                          | float8    | f        | f
 public      | products                        | status                         | varchar   | f        | f
 public      | products                        | created_at                     | timestamp | f        | f
 public      | products                        | updated_at                     | timestamp | f        | f
 public      | project_expenses                | id                             | int4      | t        | t
 public      | project_expenses                | project_id                     | int4      | t        | f
 public      | project_expenses                | user_id                        | int4      | t        | f
 public      | project_expenses                | category                       | varchar   | t        | f
 public      | project_expenses                | description                    | varchar   | t        | f
 public      | project_expenses                | amount                         | float8    | t        | f
 public      | project_expenses                | billing_type                   | varchar   | f        | f
 public      | project_expenses                | date                           | date      | t        | f
 public      | project_expenses                | receipt_path                   | varchar   | f        | f
 public      | project_expenses                | status                         | varchar   | f        | f
 public      | project_expenses                | created_at                     | timestamp | f        | f
 public      | project_funding_links           | id                             | int4      | t        | t
 public      | project_funding_links           | project_id                     | int4      | t        | f
 public      | project_funding_links           | funding_application_id         | int4      | t        | f
 public      | project_funding_links           | percentage_allocation          | float8    | f        | f
 public      | project_funding_links           | linked_at                      | timestamp | f        | f
 public      | project_funding_links           | notes                          | text      | f        | f
 public      | project_funding_links           | created_by                     | int4      | t        | f
 public      | project_funding_links           | created_at                     | timestamp | f        | f
 public      | project_funding_links           | updated_at                     | timestamp | f        | f
 public      | project_kpi_targets             | id                             | int4      | t        | t
 public      | project_kpi_targets             | project_id                     | int4      | t        | f
 public      | project_kpi_targets             | kpi_name                       | varchar   | t        | f
 public      | project_kpi_targets             | target_value                   | float8    | f        | f
 public      | project_kpi_targets             | warning_threshold              | float8    | f        | f
 public      | project_kpi_targets             | custom_description             | text      | f        | f
 public      | project_kpi_targets             | created_by                     | int4      | t        | f
 public      | project_kpi_targets             | created_at                     | timestamp | f        | f
 public      | project_kpi_targets             | updated_at                     | timestamp | f        | f
 public      | project_kpi_templates           | id                             | int4      | t        | t
 public      | project_kpi_templates           | project_type                   | varchar   | t        | f
 public      | project_kpi_templates           | kpi_name                       | varchar   | t        | f
 public      | project_kpi_templates           | target_min                     | float8    | f        | f
 public      | project_kpi_templates           | target_max                     | float8    | f        | f
 public      | project_kpi_templates           | warning_threshold              | float8    | f        | f
 public      | project_kpi_templates           | unit                           | varchar   | f        | f
 public      | project_kpi_templates           | description                    | text      | f        | f
 public      | project_kpi_templates           | is_active                      | bool      | f        | f
 public      | project_kpi_templates           | created_at                     | timestamp | f        | f
 public      | project_kpi_templates           | updated_at                     | timestamp | f        | f
 public      | project_kpis                    | id                             | int4      | t        | t
 public      | project_kpis                    | project_id                     | int4      | t        | f
 public      | project_kpis                    | kpi_id                         | int4      | t        | f
 public      | project_kpis                    | target_value                   | float8    | f        | f
 public      | project_kpis                    | current_value                  | float8    | f        | f
 public      | project_resources               | id                             | int4      | t        | t
 public      | project_resources               | project_id                     | int4      | t        | f
 public      | project_resources               | user_id                        | int4      | t        | f
 public      | project_resources               | allocation_percentage          | int4      | f        | t
 public      | project_resources               | role                           | varchar   | f        | f
 public      | project_team                    | project_id                     | int4      | t        | f
 public      | project_team                    | user_id                        | int4      | t        | f
 public      | project_team                    | role                           | varchar   | f        | f
 public      | projects                        | id                             | int4      | t        | t
 public      | projects                        | name                           | varchar   | t        | f
 public      | projects                        | description                    | text      | f        | f
 public      | projects                        | client_id                      | int4      | f        | f
 public      | projects                        | start_date                     | date      | f        | f
 public      | projects                        | end_date                       | date      | f        | f
 public      | projects                        | status                         | varchar   | f        | f
 public      | projects                        | budget                         | float8    | f        | f
 public      | projects                        | expenses                       | float8    | f        | f
 public      | projects                        | created_at                     | timestamp | f        | f
 public      | projects                        | updated_at                     | timestamp | f        | f
 public      | projects                        | is_billable                    | bool      | f        | t
 public      | projects                        | client_daily_rate              | float8    | f        | f
 public      | projects                        | markup_percentage              | float8    | f        | t
 public      | projects                        | project_type                   | varchar   | f        | t
 public      | projects                        | contract_id                    | int4      | f        | f
 public      | projects                        | funding_source                 | varchar   | f        | f
 public      | projects                        | funding_application_id         | int4      | f        | f
 public      | proposals                       | id                             | int4      | t        | t
 public      | proposals                       | title                          | varchar   | t        | f
 public      | proposals                       | client_id                      | int4      | t        | f
 public      | proposals                       | description                    | text      | f        | f
 public      | proposals                       | value                          | float8    | f        | f
 public      | proposals                       | status                         | varchar   | f        | f
 public      | proposals                       | created_by                     | int4      | f        | f
 public      | proposals                       | sent_date                      | date      | f        | f
 public      | proposals                       | expiry_date                    | date      | f        | f
 public      | proposals                       | created_at                     | timestamp | f        | f
 public      | proposals                       | updated_at                     | timestamp | f        | f
 public      | readiness_tasks                 | id                             | int4      | t        | t
 public      | readiness_tasks                 | certification_id               | int4      | t        | f
 public      | readiness_tasks                 | task_name                      | varchar   | t        | f
 public      | readiness_tasks                 | task_description               | text      | f        | f
 public      | readiness_tasks                 | task_category                  | varchar   | f        | f
 public      | readiness_tasks                 | requirement_reference          | varchar   | f        | f
 public      | readiness_tasks                 | completion_percentage          | int4      | f        | f
 public      | readiness_tasks                 | status                         | varchar   | f        | f
 public      | readiness_tasks                 | assigned_to_id                 | int4      | f        | f
 public      | readiness_tasks                 | assigned_department            | varchar   | f        | f
 public      | readiness_tasks                 | due_date                       | date      | f        | f
 public      | readiness_tasks                 | started_date                   | date      | f        | f
 public      | readiness_tasks                 | completed_date                 | date      | f        | f
 public      | readiness_tasks                 | priority                       | varchar   | f        | f
 public      | readiness_tasks                 | estimated_hours                | float8    | f        | f
 public      | readiness_tasks                 | actual_hours                   | float8    | f        | f
 public      | readiness_tasks                 | evidence_files                 | json      | f        | f
 public      | readiness_tasks                 | notes                          | text      | f        | f
 public      | readiness_tasks                 | depends_on_task_ids            | json      | f        | f
 public      | readiness_tasks                 | blocks_task_ids                | json      | f        | f
 public      | readiness_tasks                 | created_at                     | timestamp | f        | f
 public      | readiness_tasks                 | updated_at                     | timestamp | f        | f
 public      | readiness_tasks                 | created_by                     | int4      | t        | f
 public      | recruiting_ai_usage             | id                             | int4      | t        | t
 public      | recruiting_ai_usage             | user_id                        | int4      | t        | f
 public      | recruiting_ai_usage             | feature_type                   | varchar   | t        | f
 public      | recruiting_ai_usage             | action                         | varchar   | t        | f
 public      | recruiting_ai_usage             | processing_time_ms             | int4      | f        | f
 public      | recruiting_ai_usage             | tokens_used                    | int4      | f        | f
 public      | recruiting_ai_usage             | success                        | bool      | f        | f
 public      | recruiting_ai_usage             | error_message                  | text      | f        | f
 public      | recruiting_ai_usage             | entity_type                    | varchar   | f        | f
 public      | recruiting_ai_usage             | entity_id                      | int4      | f        | f
 public      | recruiting_ai_usage             | created_at                     | timestamp | f        | f
 public      | recruiting_workflows            | id                             | int4      | t        | t
 public      | recruiting_workflows            | application_id                 | int4      | t        | f
 public      | recruiting_workflows            | step_name                      | varchar   | t        | f
 public      | recruiting_workflows            | step_order                     | int4      | t        | f
 public      | recruiting_workflows            | status                         | varchar   | f        | f
 public      | recruiting_workflows            | started_at                     | timestamp | f        | f
 public      | recruiting_workflows            | completed_at                   | timestamp | f        | f
 public      | recruiting_workflows            | due_date                       | timestamp | f        | f
 public      | recruiting_workflows            | assigned_to                    | int4      | f        | f
 public      | recruiting_workflows            | result                         | varchar   | f        | f
 public      | recruiting_workflows            | notes                          | text      | f        | f
 public      | recruiting_workflows            | created_at                     | timestamp | f        | f
 public      | recruiting_workflows            | updated_at                     | timestamp | f        | f
 public      | regulations                     | id                             | int4      | t        | t
 public      | regulations                     | title                          | varchar   | t        | f
 public      | regulations                     | content                        | text      | t        | f
 public      | regulations                     | category                       | varchar   | f        | f
 public      | regulations                     | is_active                      | bool      | f        | f
 public      | regulations                     | created_at                     | timestamp | f        | f
 public      | regulations                     | updated_at                     | timestamp | f        | f
 public      | research_queries                | id                             | int4      | t        | t
 public      | research_queries                | session_id                     | int4      | t        | f
 public      | research_queries                | query_text                     | text      | t        | f
 public      | research_queries                | query_type                     | varchar   | f        | f
 public      | research_queries                | perplexity_response            | json      | f        | f
 public      | research_queries                | processed_insights             | json      | f        | f
 public      | research_queries                | status                         | varchar   | f        | f
 public      | research_queries                | error_message                  | text      | f        | f
 public      | research_queries                | started_at                     | timestamp | f        | f
 public      | research_queries                | completed_at                   | timestamp | f        | f
 public      | research_queries                | response_time_seconds          | float8    | f        | f
 public      | research_queries                | token_count                    | int4      | f        | f
 public      | research_queries                | cost_estimate                  | float8    | f        | f
 public      | research_sessions               | id                             | int4      | t        | t
 public      | research_sessions               | user_id                        | int4      | t        | f
 public      | research_sessions               | title                          | varchar   | t        | f
 public      | research_sessions               | category                       | varchar   | t        | f
 public      | research_sessions               | status                         | varchar   | f        | f
 public      | research_sessions               | research_config                | json      | f        | f
 public      | research_sessions               | company_context                | json      | f        | f
 public      | research_sessions               | created_at                     | timestamp | f        | f
 public      | research_sessions               | completed_at                   | timestamp | f        | f
 public      | risks                           | id                             | int4      | t        | t
 public      | risks                           | title                          | varchar   | t        | f
 public      | risks                           | description                    | text      | t        | f
 public      | risks                           | category                       | varchar   | t        | f
 public      | risks                           | risk_type                      | varchar   | f        | f
 public      | risks                           | probability                    | int4      | t        | f
 public      | risks                           | impact                         | int4      | t        | f
 public      | risks                           | risk_level                     | varchar   | t        | f
 public      | risks                           | risk_score                     | float8    | f        | f
 public      | risks                           | status                         | varchar   | f        | f
 public      | risks                           | owner_id                       | int4      | f        | f
 public      | risks                           | responsible_department         | varchar   | f        | f
 public      | risks                           | mitigation_strategy            | text      | f        | f
 public      | risks                           | mitigation_actions             | json      | f        | f
 public      | risks                           | mitigation_deadline            | timestamp | f        | f
 public      | risks                           | mitigation_cost                | float8    | f        | f
 public      | risks                           | regulatory_requirements        | json      | f        | f
 public      | risks                           | compliance_framework           | varchar   | f        | f
 public      | risks                           | identified_date                | timestamp | f        | f
 public      | risks                           | last_review_date               | timestamp | f        | f
 public      | risks                           | next_review_date               | timestamp | f        | f
 public      | risks                           | resolved_date                  | timestamp | f        | f
 public      | risks                           | tags                           | json      | f        | f
 public      | risks                           | external_references            | json      | f        | f
 public      | risks                           | created_at                     | timestamp | f        | f
 public      | risks                           | updated_at                     | timestamp | f        | f
 public      | scheduled_tasks                 | id                             | int4      | t        | t
 public      | scheduled_tasks                 | title                          | varchar   | t        | f
 public      | scheduled_tasks                 | description                    | text      | f        | f
 public      | scheduled_tasks                 | task_type                      | varchar   | t        | f
 public      | scheduled_tasks                 | frequency                      | varchar   | t        | f
 public      | scheduled_tasks                 | cron_expression                | varchar   | f        | f
 public      | scheduled_tasks                 | last_run                       | timestamp | f        | f
 public      | scheduled_tasks                 | next_run                       | timestamp | f        | f
 public      | scheduled_tasks                 | config_params                  | json      | f        | f
 public      | scheduled_tasks                 | data_sources                   | json      | f        | f
 public      | scheduled_tasks                 | status                         | varchar   | f        | f
 public      | scheduled_tasks                 | last_result                    | json      | f        | f
 public      | scheduled_tasks                 | error_message                  | text      | f        | f
 public      | scheduled_tasks                 | average_duration_seconds       | float8    | f        | f
 public      | scheduled_tasks                 | success_rate                   | float8    | f        | f
 public      | scheduled_tasks                 | created_at                     | timestamp | f        | f
 public      | scheduled_tasks                 | updated_at                     | timestamp | f        | f
 public      | scheduled_tasks                 | created_by                     | int4      | t        | f
 public      | services                        | id                             | int4      | t        | t
 public      | services                        | name                           | varchar   | t        | f
 public      | services                        | description                    | text      | f        | f
 public      | services                        | category                       | varchar   | f        | f
 public      | services                        | hourly_rate                    | float8    | f        | f
 public      | services                        | status                         | varchar   | f        | f
 public      | services                        | created_at                     | timestamp | f        | f
 public      | services                        | updated_at                     | timestamp | f        | f
 public      | skills                          | id                             | int4      | t        | t
 public      | skills                          | name                           | varchar   | t        | f
 public      | skills                          | category                       | varchar   | f        | f
 public      | skills                          | description                    | text      | f        | f
 public      | startup_resources               | id                             | int4      | t        | t
 public      | startup_resources               | title                          | varchar   | t        | f
 public      | startup_resources               | description                    | text      | f        | f
 public      | startup_resources               | resource_type                  | varchar   | f        | f
 public      | startup_resources               | link                           | varchar   | f        | f
 public      | startup_resources               | is_active                      | bool      | f        | f
 public      | startup_resources               | created_at                     | timestamp | f        | f
 public      | startup_resources               | updated_at                     | timestamp | f        | f
 public      | strategic_insights              | id                             | int4      | t        | t
 public      | strategic_insights              | session_id                     | int4      | t        | f
 public      | strategic_insights              | insight_type                   | varchar   | f        | f
 public      | strategic_insights              | title                          | varchar   | t        | f
 public      | strategic_insights              | content                        | text      | t        | f
 public      | strategic_insights              | summary                        | text      | f        | f
 public      | strategic_insights              | confidence_score               | float8    | f        | f
 public      | strategic_insights              | priority                       | varchar   | f        | f
 public      | strategic_insights              | impact_score                   | int4      | f        | f
 public      | strategic_insights              | action_items                   | json      | f        | f
 public      | strategic_insights              | timeline_estimate              | varchar   | f        | f
 public      | strategic_insights              | source_queries                 | json      | f        | f
 public      | strategic_insights              | tags                           | json      | f        | f
 public      | strategic_insights              | status                         | varchar   | f        | f
 public      | strategic_insights              | reviewed_by                    | int4      | f        | f
 public      | strategic_insights              | reviewed_at                    | timestamp | f        | f
 public      | strategic_insights              | notes                          | text      | f        | f
 public      | strategic_insights              | created_at                     | timestamp | f        | f
 public      | strategic_insights              | updated_at                     | timestamp | f        | f
 public      | system_health                   | id                             | int4      | t        | t
 public      | system_health                   | timestamp                      | timestamp | t        | f
 public      | system_health                   | health_score                   | float8    | t        | f
 public      | system_health                   | error_count_24h                | int4      | f        | f
 public      | system_health                   | critical_errors                | int4      | f        | f
 public      | system_health                   | auto_healed_count              | int4      | f        | f
 public      | system_health                   | pending_issues                 | int4      | f        | f
 public      | system_health                   | system_load                    | float8    | f        | f
 public      | system_health                   | system_metadata                | text      | f        | f
 public      | task_dependencies               | id                             | int4      | t        | t
 public      | task_dependencies               | task_id                        | int4      | t        | f
 public      | task_dependencies               | depends_on_id                  | int4      | t        | f
 public      | tasks                           | id                             | int4      | t        | t
 public      | tasks                           | name                           | varchar   | t        | f
 public      | tasks                           | description                    | text      | f        | f
 public      | tasks                           | project_id                     | int4      | t        | f
 public      | tasks                           | assignee_id                    | int4      | f        | f
 public      | tasks                           | status                         | varchar   | f        | f
 public      | tasks                           | priority                       | varchar   | f        | f
 public      | tasks                           | due_date                       | date      | f        | f
 public      | tasks                           | created_at                     | timestamp | f        | f
 public      | tasks                           | updated_at                     | timestamp | f        | f
 public      | tasks                           | start_date                     | date      | f        | f
 public      | tasks                           | estimated_hours                | float8    | f        | f
 public      | technical_offers                | id                             | int4      | t        | t
 public      | technical_offers                | title                          | varchar   | t        | f
 public      | technical_offers                | description                    | text      | f        | f
 public      | technical_offers                | core_competency_id             | int4      | f        | f
 public      | technical_offers                | target_sector                  | varchar   | f        | f
 public      | technical_offers                | technology_stack               | json      | f        | f
 public      | technical_offers                | team_composition               | json      | f        | f
 public      | technical_offers                | estimated_duration_days        | int4      | f        | f
 public      | technical_offers                | estimated_cost_min             | float8    | f        | f
 public      | technical_offers                | estimated_cost_max             | float8    | f        | f
 public      | technical_offers                | deliverables                   | json      | f        | f
 public      | technical_offers                | success_metrics                | json      | f        | f
 public      | technical_offers                | risk_factors                   | json      | f        | f
 public      | technical_offers                | generated_by_ai                | bool      | f        | f
 public      | technical_offers                | ai_prompt_used                 | text      | f        | f
 public      | technical_offers                | status                         | varchar   | f        | f
 public      | technical_offers                | created_by                     | int4      | f        | f
 public      | technical_offers                | created_at                     | timestamp | f        | f
 public      | technical_offers                | updated_at                     | timestamp | f        | f
 public      | time_off_requests               | id                             | int4      | t        | t
 public      | time_off_requests               | user_id                        | int4      | t        | f
 public      | time_off_requests               | request_type                   | varchar   | t        | f
 public      | time_off_requests               | start_date                     | date      | t        | f
 public      | time_off_requests               | end_date                       | date      | t        | f
 public      | time_off_requests               | status                         | varchar   | f        | f
 public      | time_off_requests               | notes                          | text      | f        | f
 public      | time_off_requests               | submission_date                | timestamp | f        | f
 public      | time_off_requests               | approval_date                  | timestamp | f        | f
 public      | time_off_requests               | approved_by                    | int4      | f        | f
 public      | time_off_requests               | rejection_reason               | text      | f        | f
 public      | time_off_requests               | created_at                     | timestamp | f        | f
 public      | time_off_requests               | updated_at                     | timestamp | f        | f
 public      | timesheet_entries               | id                             | int4      | t        | t
 public      | timesheet_entries               | user_id                        | int4      | t        | f
 public      | timesheet_entries               | project_id                     | int4      | t        | f
 public      | timesheet_entries               | task_id                        | int4      | f        | f
 public      | timesheet_entries               | date                           | date      | t        | f
 public      | timesheet_entries               | hours                          | float8    | t        | f
 public      | timesheet_entries               | description                    | text      | f        | f
 public      | timesheet_entries               | status                         | varchar   | f        | f
 public      | timesheet_entries               | created_at                     | timestamp | f        | f
 public      | timesheet_entries               | monthly_timesheet_id           | int4      | f        | f
 public      | timesheet_entries               | billable                       | bool      | f        | t
 public      | timesheet_entries               | billing_rate                   | float8    | f        | f
 public      | timesheet_entries               | contract_id                    | int4      | f        | f
 public      | timesheet_entries               | invoice_line_id                | int4      | f        | f
 public      | timesheet_entries               | billing_status                 | varchar   | f        | t
 public      | user_profiles                   | id                             | int4      | t        | t
 public      | user_profiles                   | user_id                        | int4      | t        | f
 public      | user_profiles                   | employee_id                    | varchar   | f        | f
 public      | user_profiles                   | job_title                      | varchar   | f        | f
 public      | user_profiles                   | birth_date                     | date      | f        | f
 public      | user_profiles                   | address                        | text      | f        | f
 public      | user_profiles                   | emergency_contact_name         | varchar   | f        | f
 public      | user_profiles                   | emergency_contact_phone        | varchar   | f        | f
 public      | user_profiles                   | emergency_contact_relationship | varchar   | f        | f
 public      | user_profiles                   | employment_type                | varchar   | f        | t
 public      | user_profiles                   | work_location                  | varchar   | f        | f
 public      | user_profiles                   | salary                         | float8    | f        | f
 public      | user_profiles                   | salary_currency                | varchar   | f        | t
 public      | user_profiles                   | probation_end_date             | date      | f        | f
 public      | user_profiles                   | contract_end_date              | date      | f        | f
 public      | user_profiles                   | notice_period_days             | int4      | f        | t
 public      | user_profiles                   | weekly_hours                   | float8    | f        | t
 public      | user_profiles                   | daily_hours                    | float8    | f        | t
 public      | user_profiles                   | profile_completion             | float8    | f        | t
 public      | user_profiles                   | notes                          | text      | f        | f
 public      | user_profiles                   | created_at                     | timestamp | f        | t
 public      | user_profiles                   | updated_at                     | timestamp | f        | t
 public      | user_profiles                   | current_cv_path                | varchar   | f        | f
 public      | user_profiles                   | cv_last_updated                | timestamp | f        | f
 public      | user_profiles                   | cv_analysis_data               | text      | f        | f
 public      | user_skills                     | id                             | int4      | t        | t
 public      | user_skills                     | user_id                        | int4      | t        | f
 public      | user_skills                     | skill_id                       | int4      | t        | f
 public      | user_skills                     | proficiency_level              | int4      | f        | f
 public      | user_skills                     | years_experience               | float8    | f        | f
 public      | user_skills                     | last_used                      | date      | f        | f
 public      | user_skills                     | certified                      | bool      | f        | f
 public      | user_skills                     | certification_date             | date      | f        | f
 public      | user_skills                     | certification_name             | varchar   | f        | f
 public      | user_skills_detailed            | id                             | int4      | t        | t
 public      | user_skills_detailed            | user_id                        | int4      | t        | f
 public      | user_skills_detailed            | skill_id                       | int4      | t        | f
 public      | user_skills_detailed            | proficiency_level              | int4      | f        | t
 public      | user_skills_detailed            | years_experience               | float8    | f        | t
 public      | user_skills_detailed            | is_certified                   | bool      | f        | t
 public      | user_skills_detailed            | certification_name             | varchar   | f        | f
 public      | user_skills_detailed            | certification_date             | date      | f        | f
 public      | user_skills_detailed            | certification_expiry           | date      | f        | f
 public      | user_skills_detailed            | self_assessed                  | bool      | f        | t
 public      | user_skills_detailed            | manager_assessed               | bool      | f        | t
 public      | user_skills_detailed            | manager_assessment_date        | date      | f        | f
 public      | user_skills_detailed            | notes                          | text      | f        | f
 public      | user_skills_detailed            | created_at                     | timestamp | f        | t
 public      | user_skills_detailed            | updated_at                     | timestamp | f        | t
 public      | user_skills_detailed            | last_used                      | date      | f        | f
 public      | user_skills_detailed            | certified                      | bool      | f        | t
 public      | users                           | id                             | int4      | t        | t
 public      | users                           | username                       | varchar   | t        | f
 public      | users                           | email                          | varchar   | t        | f
 public      | users                           | password_hash                  | varchar   | f        | f
 public      | users                           | first_name                     | varchar   | f        | f
 public      | users                           | last_name                      | varchar   | f        | f
 public      | users                           | role                           | varchar   | t        | t
 public      | users                           | department                     | varchar   | f        | f
 public      | users                           | position                       | varchar   | f        | f
 public      | users                           | hire_date                      | date      | f        | f
 public      | users                           | phone                          | varchar   | f        | f
 public      | users                           | profile_image                  | varchar   | f        | f
 public      | users                           | bio                            | text      | f        | f
 public      | users                           | is_active                      | bool      | f        | f
 public      | users                           | dark_mode                      | bool      | f        | f
 public      | users                           | created_at                     | timestamp | f        | f
 public      | users                           | last_login                     | timestamp | f        | f
 public      | users                           | reset_token                    | varchar   | f        | f
 public      | users                           | reset_token_expiry             | timestamp | f        | f
 public      | users                           | department_id                  | int4      | f        | f
(1637 rows)

