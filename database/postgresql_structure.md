# Struttura Database PostgreSQL - DatPortal

## Panoramica
Database PostgreSQL con 115 tabelle che gestiscono un sistema completo di gestione aziendale con focus su sicurezza, compliance, gestione progetti e risorse umane.

**Database**: `neondb`  
**Host**: `ep-summer-waterfall-a5qeb92v.us-east-2.aws.neon.tech`  
**Provider**: Neon (PostgreSQL cloud)

## Tabelle Principali

### 🔐 Autenticazione e Utenti
- **users**: Utenti del sistema con autenticazione
- **oauth_accounts**: Account OAuth per autenticazione esterna
- **user_profiles**: Profili estesi degli utenti
- **departments**: Struttura organizzativa aziendale
- **job_levels**: Livelli lavorativi e gerarchie
- **employee_job_levels**: Storico livelli per dipendente

### 👥 Gestione Risorse Umane
- **personnel_rates**: Tariffe del personale
- **skills**: Competenze e skill disponibili
- **user_skills**: Competenze associate agli utenti
- **user_skills_detailed**: Dettagli competenze utenti
- **time_off_requests**: Richieste di permessi e ferie
- **performance_reviews**: Valutazioni delle performance
- **performance_feedbacks**: Feedback bidirezionali
- **performance_goals**: Obiettivi di performance
- **performance_rewards**: Premi e riconoscimenti
- **performance_templates**: Template per valutazioni
- **performance_kpis**: KPI di performance
- **performance_review_participants**: Partecipanti alle review
- **core_competencies**: Competenze core aziendali

### 📊 Gestione Progetti
- **projects**: Progetti aziendali
- **tasks**: Task e attività dei progetti
- **task_dependencies**: Dipendenze tra task
- **project_resources**: Risorse allocate ai progetti
- **project_expenses**: Spese di progetto
- **project_kpis**: KPI specifici per progetto
- **project_kpi_templates**: Template KPI progetti
- **project_kpi_targets**: Target KPI per progetto
- **project_team**: Team assegnati ai progetti
- **project_funding_links**: Collegamenti a finanziamenti

### ⏰ Timesheet e Tracciamento
- **timesheet_entries**: Registrazioni ore lavorate
- **monthly_timesheets**: Timesheet mensili aggregati

### 🏢 CRM e Clienti
- **clients**: Anagrafica clienti
- **contacts**: Contatti clienti
- **proposals**: Proposte commerciali
- **contracts**: Contratti attivi

### 💰 Fatturazione e Invoicing
- **pre_invoices**: Pre-fatture interne
- **pre_invoice_lines**: Righe delle pre-fatture
- **invoices**: Fatture emesse
- **invoice_lines**: Righe delle fatture
- **company_invoicing_settings**: Configurazione fatturazione
- **integration_settings**: Impostazioni integrazioni esterne

### 🎯 Recruiting e Selezione
- **job_postings**: Annunci di lavoro
- **candidates**: Candidati
- **applications**: Candidature
- **interview_sessions**: Sessioni di colloquio
- **recruiting_workflows**: Workflow di selezione
- **candidate_skills**: Competenze candidati
- **candidate_ai_scores**: Punteggi AI candidati
- **recruiting_ai_usage**: Utilizzo AI nel recruiting

### 🔒 Sicurezza e Compliance
- **compliance_audit_logs**: Log di audit per compliance
- **compliance_events**: Eventi di compliance
- **compliance_policies**: Policy di compliance
- **compliance_reports**: Report di compliance
- **risks**: Gestione rischi aziendali
- **certification_standards**: Standard di certificazione
- **company_certifications**: Certificazioni aziendali
- **certification_audits**: Audit delle certificazioni
- **certification_documents**: Documenti certificazioni
- **readiness_tasks**: Task di preparazione certificazioni

### 💡 Engagement e Gamification
- **engagement_campaigns**: Campagne di engagement
- **engagement_levels**: Livelli di engagement
- **engagement_points**: Punti engagement utenti
- **engagement_rewards**: Catalogo premi
- **engagement_user_rewards**: Premi riscattati
- **engagement_leaderboards**: Classifiche engagement
- **engagement_user_profiles**: Profili engagement utenti

### 💼 Business Intelligence
- **case_studies**: Case study aziendali
- **technical_offers**: Offerte tecniche
- **market_prospects**: Prospect di mercato
- **bi_reports**: Report business intelligence
- **products**: Prodotti aziendali
- **services**: Servizi offerti
- **kpis**: KPI aziendali generali
- **business_processes**: Processi aziendali
- **process_steps**: Step dei processi

### 🎓 Formazione e Supporto
- **help_categories**: Categorie help center
- **help_content**: Contenuti help center
- **help_conversations**: Conversazioni supporto
- **help_feedback**: Feedback help center
- **help_analytics**: Analytics help center
- **hr_knowledge_base**: Knowledge base HR
- **hr_chat_conversations**: Chat HR
- **hr_content_templates**: Template contenuti HR
- **hr_analytics**: Analytics HR

### 💬 Comunicazione
- **forum_topics**: Topic del forum aziendale
- **forum_comments**: Commenti forum
- **polls**: Sondaggi aziendali
- **poll_options**: Opzioni sondaggi
- **poll_votes**: Voti sondaggi
- **direct_messages**: Messaggi diretti
- **communication_reactions**: Reazioni comunicazioni
- **company_events**: Eventi aziendali
- **company_event_registrations**: Registrazioni eventi
- **company_communications**: Comunicazioni aziendali

### 💰 Finanziamenti e Bandi
- **funding_opportunities**: Opportunità di finanziamento
- **funding_applications**: Domande di finanziamento
- **funding_expenses**: Spese per finanziamenti

### 📋 CEO e Strategia
- **research_sessions**: Sessioni di ricerca CEO
- **research_queries**: Query di ricerca
- **strategic_insights**: Insight strategici
- **ai_interactions**: Interazioni AI
- **scheduled_tasks**: Task programmati
- **company_profiles**: Profili aziendali

### 🔧 Sistema e Amministrazione
- **admin_logs**: Log amministrativi
- **notifications**: Notifiche sistema
- **feature_flags**: Flag delle funzionalità
- **system_health**: Stato salute sistema
- **error_patterns**: Pattern di errori
- **healing_sessions**: Sessioni auto-healing
- **ai_generated_content**: Contenuti generati AI
- **news**: News aziendali
- **documents**: Documenti sistema
- **regulations**: Regolamenti
- **startup_resources**: Risorse startup

## Relazioni Principali

### Utenti e Organizzazione
```
users ←→ departments (manager_id)
users ←→ user_profiles (1:1)
users ←→ user_skills (1:N)
users ←→ employee_job_levels (1:N)
```

### Progetti e Lavoro
```
projects ←→ clients (client_id)
projects ←→ contracts (contract_id)
projects ←→ tasks (1:N)
projects ←→ project_team (N:M con users)
tasks ←→ task_dependencies (1:N)
```

### Timesheet e Fatturazione
```
timesheet_entries ←→ users (user_id)
timesheet_entries ←→ projects (project_id)
timesheet_entries ←→ tasks (task_id)
pre_invoices ←→ clients (client_id)
pre_invoices ←→ contracts (contract_id)
```

### Performance e HR
```
performance_reviews ←→ users (employee_id, reviewer_id)
performance_goals ←→ users (employee_id)
performance_feedbacks ←→ users (from_user_id, to_user_id)
```

### Recruiting
```
job_postings ←→ projects (project_id)
applications ←→ candidates (candidate_id)
applications ←→ job_postings (job_posting_id)
interview_sessions ←→ applications (application_id)
```

### Compliance e Sicurezza
```
company_certifications ←→ certification_standards (standard_id)
certification_audits ←→ company_certifications (certification_id)
compliance_events ←→ users (user_id)
risks ←→ users (owner_id)
```

## Caratteristiche Tecniche

### Tipi di Dati Comuni
- **ID**: `integer` con auto-increment
- **Timestamp**: `timestamp without time zone`
- **Date**: `date`
- **Testo**: `varchar(n)` per stringhe limitate, `text` per contenuti lunghi
- **Booleani**: `boolean`
- **Numerici**: `numeric(p,s)` per valori monetari, `float` per percentuali
- **JSON**: `json` per dati strutturati

### Convenzioni
- **Primary Key**: Sempre `id` di tipo `integer`
- **Foreign Key**: Formato `{table}_id`
- **Timestamp**: `created_at`, `updated_at` standard
- **Soft Delete**: Campo `is_active` booleano
- **Audit**: Campi `created_by`, `updated_by` dove applicabile

### Indici e Vincoli
- **Unique Constraints**: Email, username, codici identificativi
- **Foreign Key Constraints**: Integrità referenziale completa
- **Check Constraints**: Validazione dati (es. rating 1-5)
- **Partial Indexes**: Per query performance su dati filtrati

## Sicurezza Database

### Autenticazione
- Password hash con algoritmo sicuro
- Supporto OAuth per autenticazione esterna
- Token di reset password con scadenza

### Autorizzazione
- Sistema di ruoli (admin, manager, employee)
- Permessi granulari per modulo
- Audit trail completo delle azioni

### Compliance
- Log di audit per tutte le operazioni sensibili
- Tracciamento accessi e modifiche
- Retention policy per dati storici
- Supporto GDPR con campi privacy

## Performance e Scalabilità

### Ottimizzazioni
- Indici strategici su colonne frequentemente interrogate
- Partitioning per tabelle con grandi volumi (timesheet, logs)
- Connection pooling per gestione connessioni
- Query optimization con EXPLAIN ANALYZE

### Monitoring
- Metriche performance query
- Monitoraggio spazio disco
- Alert su slow queries
- Health check automatici

## Backup e Disaster Recovery

### Strategia Backup
- Backup completo giornaliero
- Backup incrementale ogni 4 ore
- Point-in-time recovery
- Test di restore mensili

### Alta Disponibilità
- Replica sincrona per failover
- Load balancing per letture
- Monitoring automatico
- Failover automatico < 30 secondi

---

*Documento generato automaticamente dal database PostgreSQL in produzione*
*Ultimo aggiornamento: $(date)* 