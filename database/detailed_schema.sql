-- =====================================================
-- DATPORTAL DATABASE SCHEMA
-- PostgreSQL Database Structure
-- Generated from production database
-- =====================================================

-- Database: neondb
-- Host: ep-summer-waterfall-a5qeb92v.us-east-2.aws.neon.tech
-- Tables: 115 total

-- =====================================================
-- CORE SYSTEM TABLES
-- =====================================================

-- Versioning table for migrations
CREATE TABLE IF NOT EXISTS alembic_version (
    version_num VARCHAR(32) NOT NULL,
    CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num)
);

-- =====================================================
-- USERS AND AUTHENTICATION
-- =====================================================

-- Main users table
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(64) UNIQUE NOT NULL,
    email VARCHAR(120) UNIQUE NOT NULL,
    password_hash VARCHAR(256),
    first_name VARCHAR(64),
    last_name VARCHAR(64),
    role VARCHAR(50) NOT NULL DEFAULT 'employee',
    department VARCHAR(64), -- DEPRECATED: use department_id
    department_id INTEGER,
    position VARCHAR(64),
    hire_date DATE,
    phone VARCHAR(20),
    profile_image VARCHAR(255),
    bio TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    dark_mode BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP WITHOUT TIME ZONE,
    reset_token VARCHAR(100) UNIQUE,
    reset_token_expiry TIMESTAMP WITHOUT TIME ZONE
);

-- OAuth accounts for external authentication
CREATE TABLE oauth_accounts (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    provider VARCHAR(50) NOT NULL,
    provider_user_id VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    display_name VARCHAR(255),
    avatar_url VARCHAR(500),
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT oauth_accounts_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id),
    CONSTRAINT _user_provider_uc UNIQUE (user_id, provider),
    CONSTRAINT _provider_user_uc UNIQUE (provider, provider_user_id)
);

-- Departments organizational structure
CREATE TABLE departments (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    manager_id INTEGER,
    parent_id INTEGER,
    budget FLOAT DEFAULT 0.0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT departments_manager_id_fkey FOREIGN KEY (manager_id) REFERENCES users(id),
    CONSTRAINT departments_parent_id_fkey FOREIGN KEY (parent_id) REFERENCES departments(id)
);

-- Update users table foreign key
ALTER TABLE users ADD CONSTRAINT user_department_id_fkey FOREIGN KEY (department_id) REFERENCES departments(id);

-- Extended user profiles
CREATE TABLE user_profiles (
    id SERIAL PRIMARY KEY,
    user_id INTEGER UNIQUE NOT NULL,
    employee_id VARCHAR(20) UNIQUE,
    job_title VARCHAR(100),
    birth_date DATE,
    address TEXT,
    emergency_contact_name VARCHAR(100),
    emergency_contact_phone VARCHAR(20),
    emergency_contact_relationship VARCHAR(50),
    employment_type VARCHAR(50) DEFAULT 'full_time',
    work_location VARCHAR(100),
    salary FLOAT,
    salary_currency VARCHAR(3) DEFAULT 'EUR',
    probation_end_date DATE,
    contract_end_date DATE,
    notice_period_days INTEGER DEFAULT 30,
    weekly_hours FLOAT DEFAULT 40.0,
    daily_hours FLOAT DEFAULT 8.0,
    current_cv_path VARCHAR(255),
    cv_last_updated TIMESTAMP WITHOUT TIME ZONE,
    cv_analysis_data TEXT,
    profile_completion FLOAT DEFAULT 0.0,
    notes TEXT,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT user_profiles_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Job levels and hierarchies
CREATE TABLE job_levels (
    id SERIAL PRIMARY KEY,
    level_number INTEGER UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    category VARCHAR(50),
    level VARCHAR(100),
    description TEXT,
    min_salary FLOAT,
    max_salary FLOAT,
    benefits TEXT,
    requirements TEXT,
    typical_years_experience INTEGER,
    is_management BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    parent_level_id INTEGER,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT job_levels_parent_level_id_fkey FOREIGN KEY (parent_level_id) REFERENCES job_levels(id)
);

-- Employee job level history
CREATE TABLE employee_job_levels (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    job_level_id INTEGER NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE,
    current_salary FLOAT,
    notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT employee_job_levels_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id),
    CONSTRAINT employee_job_levels_job_level_id_fkey FOREIGN KEY (job_level_id) REFERENCES job_levels(id),
    CONSTRAINT employee_job_levels_created_by_fkey FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Personnel rates
CREATE TABLE personnel_rates (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    daily_rate FLOAT NOT NULL,
    valid_from DATE NOT NULL,
    valid_to DATE,
    currency VARCHAR(3) DEFAULT 'EUR',
    notes VARCHAR(255),
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT personnel_rates_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id)
);

-- =====================================================
-- SKILLS AND COMPETENCIES
-- =====================================================

-- Skills catalog
CREATE TABLE skills (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    category VARCHAR(50),
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- User skills association
CREATE TABLE user_skills (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    skill_id INTEGER NOT NULL,
    proficiency_level INTEGER DEFAULT 1,
    years_experience INTEGER DEFAULT 0,
    certified BOOLEAN DEFAULT FALSE,
    certification_date DATE,
    notes TEXT,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT user_skill_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id),
    CONSTRAINT user_skill_skill_id_fkey FOREIGN KEY (skill_id) REFERENCES skills(id),
    CONSTRAINT unique_user_skill UNIQUE (user_id, skill_id)
);

-- Detailed user skills
CREATE TABLE user_skills_detailed (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    skill_name VARCHAR(100) NOT NULL,
    skill_category VARCHAR(50),
    proficiency_level INTEGER DEFAULT 1,
    years_experience INTEGER DEFAULT 0,
    last_used DATE,
    certified BOOLEAN DEFAULT FALSE,
    certification_name VARCHAR(200),
    certification_date DATE,
    notes TEXT,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT user_skills_detailed_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Core competencies
CREATE TABLE core_competencies (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    category VARCHAR(50),
    weight FLOAT DEFAULT 1.0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- PROJECTS AND TASKS
-- =====================================================

-- CRM Clients
CREATE TABLE clients (
    id SERIAL PRIMARY KEY,
    name VARCHAR(128) NOT NULL,
    industry VARCHAR(64),
    description TEXT,
    website VARCHAR(128),
    address VARCHAR(255),
    email VARCHAR(120),
    phone VARCHAR(20),
    vat_number VARCHAR(20),
    fiscal_code VARCHAR(20),
    status VARCHAR(20) DEFAULT 'client',
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Client contacts
CREATE TABLE contacts (
    id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL,
    first_name VARCHAR(64) NOT NULL,
    last_name VARCHAR(64) NOT NULL,
    position VARCHAR(64),
    email VARCHAR(120),
    phone VARCHAR(20),
    is_primary BOOLEAN DEFAULT FALSE,
    notes TEXT,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT contacts_client_id_fkey FOREIGN KEY (client_id) REFERENCES clients(id)
);

-- Proposals
CREATE TABLE proposals (
    id SERIAL PRIMARY KEY,
    title VARCHAR(128) NOT NULL,
    client_id INTEGER NOT NULL,
    description TEXT,
    value NUMERIC(12,2),
    status VARCHAR(20) DEFAULT 'draft',
    created_by INTEGER,
    sent_date DATE,
    expiry_date DATE,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT proposal_created_by_fkey FOREIGN KEY (created_by) REFERENCES users(id),
    CONSTRAINT proposals_client_id_fkey FOREIGN KEY (client_id) REFERENCES clients(id)
);

-- Contracts
CREATE TABLE contracts (
    id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL,
    contract_number VARCHAR(50) UNIQUE NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    contract_type VARCHAR(20) DEFAULT 'hourly',
    hourly_rate NUMERIC(8,2),
    budget_hours NUMERIC(8,2),
    budget_amount NUMERIC(12,2),
    retainer_amount NUMERIC(12,2),
    retainer_frequency VARCHAR(20),
    milestone_amount NUMERIC(12,2),
    milestone_count INTEGER,
    subscription_amount NUMERIC(12,2),
    subscription_frequency VARCHAR(20),
    start_date DATE NOT NULL,
    end_date DATE,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT contracts_client_id_fkey FOREIGN KEY (client_id) REFERENCES clients(id)
);

-- Projects
CREATE TABLE projects (
    id SERIAL PRIMARY KEY,
    name VARCHAR(128) NOT NULL,
    description TEXT,
    client_id INTEGER,
    contract_id INTEGER,
    start_date DATE,
    end_date DATE,
    status VARCHAR(20) DEFAULT 'planning',
    priority VARCHAR(20) DEFAULT 'medium',
    budget NUMERIC(12,2),
    expenses NUMERIC(12,2) DEFAULT 0.00,
    project_type VARCHAR(50) DEFAULT 'service',
    is_billable BOOLEAN DEFAULT TRUE,
    client_daily_rate NUMERIC(8,2),
    markup_percentage NUMERIC(5,2) DEFAULT 0.00,
    funding_source VARCHAR(100),
    funding_application_id INTEGER,
    manager_id INTEGER,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT projects_client_id_fkey FOREIGN KEY (client_id) REFERENCES clients(id),
    CONSTRAINT projects_contract_id_fkey FOREIGN KEY (contract_id) REFERENCES contracts(id),
    CONSTRAINT projects_manager_id_fkey FOREIGN KEY (manager_id) REFERENCES users(id)
);

-- Project team assignments
CREATE TABLE project_team (
    id SERIAL PRIMARY KEY,
    project_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    role VARCHAR(100),
    start_date DATE DEFAULT CURRENT_DATE,
    end_date DATE,
    is_active BOOLEAN DEFAULT TRUE,
    CONSTRAINT project_team_project_id_fkey FOREIGN KEY (project_id) REFERENCES projects(id),
    CONSTRAINT project_team_user_id_fkey1 FOREIGN KEY (user_id) REFERENCES users(id),
    CONSTRAINT unique_project_user UNIQUE (project_id, user_id)
);

-- Tasks
CREATE TABLE tasks (
    id SERIAL PRIMARY KEY,
    title VARCHAR(128) NOT NULL,
    description TEXT,
    project_id INTEGER NOT NULL,
    assigned_to INTEGER,
    status VARCHAR(20) DEFAULT 'todo',
    priority VARCHAR(20) DEFAULT 'medium',
    start_date DATE,
    due_date DATE,
    estimated_hours NUMERIC(6,2),
    actual_hours NUMERIC(6,2) DEFAULT 0,
    completion_percentage INTEGER DEFAULT 0,
    created_by INTEGER,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT tasks_project_id_fkey FOREIGN KEY (project_id) REFERENCES projects(id),
    CONSTRAINT task_assignee_id_fkey FOREIGN KEY (assigned_to) REFERENCES users(id),
    CONSTRAINT tasks_created_by_fkey FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Task dependencies
CREATE TABLE task_dependencies (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    depends_on_task_id INTEGER NOT NULL,
    dependency_type VARCHAR(20) DEFAULT 'finish_to_start',
    CONSTRAINT task_dependencies_task_id_fkey FOREIGN KEY (task_id) REFERENCES tasks(id),
    CONSTRAINT task_dependencies_depends_on_task_id_fkey FOREIGN KEY (depends_on_task_id) REFERENCES tasks(id),
    CONSTRAINT unique_task_dependency UNIQUE (task_id, depends_on_task_id)
);

-- =====================================================
-- TIMESHEET AND TRACKING
-- =====================================================

-- Timesheet entries
CREATE TABLE timesheet_entries (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    project_id INTEGER NOT NULL,
    task_id INTEGER,
    date DATE NOT NULL,
    hours FLOAT NOT NULL,
    description TEXT,
    status VARCHAR(20) DEFAULT 'pending',
    monthly_timesheet_id INTEGER,
    billable BOOLEAN DEFAULT FALSE,
    billing_rate FLOAT,
    contract_id INTEGER,
    invoice_line_id INTEGER,
    billing_status VARCHAR(20) DEFAULT 'unbilled',
    approved BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT timesheet_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id),
    CONSTRAINT timesheet_entries_project_id_fkey FOREIGN KEY (project_id) REFERENCES projects(id),
    CONSTRAINT timesheet_entries_task_id_fkey FOREIGN KEY (task_id) REFERENCES tasks(id),
    CONSTRAINT timesheet_entries_contract_id_fkey FOREIGN KEY (contract_id) REFERENCES contracts(id)
);

-- Monthly timesheet aggregation
CREATE TABLE monthly_timesheets (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    year INTEGER NOT NULL,
    month INTEGER NOT NULL,
    status VARCHAR(20) DEFAULT 'draft',
    submission_date TIMESTAMP WITHOUT TIME ZONE,
    approval_date TIMESTAMP WITHOUT TIME ZONE,
    approved_by INTEGER,
    rejection_reason TEXT,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT monthly_timesheets_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id),
    CONSTRAINT monthly_timesheets_approved_by_fkey FOREIGN KEY (approved_by) REFERENCES users(id),
    CONSTRAINT unique_user_month UNIQUE (user_id, year, month)
);

-- Update timesheet entries with monthly reference
ALTER TABLE timesheet_entries ADD CONSTRAINT timesheet_entries_monthly_timesheet_id_fkey 
FOREIGN KEY (monthly_timesheet_id) REFERENCES monthly_timesheets(id);

-- =====================================================
-- SYSTEM AND ADMINISTRATION
-- =====================================================

-- Admin logs
CREATE TABLE admin_logs (
    id SERIAL PRIMARY KEY,
    admin_id INTEGER NOT NULL,
    action VARCHAR(255) NOT NULL,
    description TEXT,
    target_user_id INTEGER,
    timestamp TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT admin_logs_admin_id_fkey FOREIGN KEY (admin_id) REFERENCES users(id),
    CONSTRAINT admin_logs_target_user_id_fkey FOREIGN KEY (target_user_id) REFERENCES users(id)
);

-- System notifications
CREATE TABLE notifications (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    title VARCHAR(128) NOT NULL,
    message TEXT NOT NULL,
    link VARCHAR(255),
    type VARCHAR(50) DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT notification_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Feature flags
CREATE TABLE feature_flags (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    is_enabled BOOLEAN DEFAULT FALSE,
    category VARCHAR(50),
    updated_by INTEGER,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT feature_flags_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES users(id)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Users indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_department_id ON users(department_id);
CREATE INDEX idx_users_is_active ON users(is_active);

-- Projects indexes
CREATE INDEX idx_projects_client_id ON projects(client_id);
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_projects_manager_id ON projects(manager_id);

-- Tasks indexes
CREATE INDEX idx_tasks_project_id ON tasks(project_id);
CREATE INDEX idx_tasks_assigned_to ON tasks(assigned_to);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_due_date ON tasks(due_date);

-- Timesheet indexes
CREATE INDEX idx_timesheet_entries_user_id ON timesheet_entries(user_id);
CREATE INDEX idx_timesheet_entries_project_id ON timesheet_entries(project_id);
CREATE INDEX idx_timesheet_entries_date ON timesheet_entries(date);
CREATE INDEX idx_timesheet_entries_billable ON timesheet_entries(billable);

-- System indexes
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_is_read ON notifications(is_read);
CREATE INDEX idx_admin_logs_admin_id ON admin_logs(admin_id);
CREATE INDEX idx_admin_logs_timestamp ON admin_logs(timestamp);

-- =====================================================
-- COMMENTS AND DOCUMENTATION
-- =====================================================

-- Table comments
COMMENT ON TABLE users IS 'Core users table with authentication and basic profile information';
COMMENT ON TABLE user_profiles IS 'Extended user profiles with HR-specific information';
COMMENT ON TABLE departments IS 'Organizational structure with hierarchical departments';
COMMENT ON TABLE projects IS 'Project management with client relationships and budgets';
COMMENT ON TABLE tasks IS 'Task management with dependencies and time tracking';
COMMENT ON TABLE timesheet_entries IS 'Time tracking entries for billing and productivity';
COMMENT ON TABLE clients IS 'CRM client management with contact information';
COMMENT ON TABLE contracts IS 'Contract management with various billing models';

-- Column comments for key fields
COMMENT ON COLUMN users.role IS 'User role: admin, manager, employee';
COMMENT ON COLUMN projects.project_type IS 'Project type: service, license, consulting, product, rd, internal';
COMMENT ON COLUMN tasks.status IS 'Task status: todo, in-progress, review, done';
COMMENT ON COLUMN timesheet_entries.billing_status IS 'Billing status: unbilled, billed, non-billable';

-- =====================================================
-- INITIAL DATA AND SETUP
-- =====================================================

-- Insert default feature flags
INSERT INTO feature_flags (name, description, is_enabled, category) VALUES
('user_management', 'User management and profiles', true, 'core'),
('project_management', 'Project and task management', true, 'core'),
('timesheet_tracking', 'Time tracking and billing', true, 'core'),
('client_management', 'CRM and client management', true, 'crm'),
('reporting', 'Reports and analytics', true, 'analytics'),
('notifications', 'System notifications', true, 'core');

-- Insert default skills
INSERT INTO skills (name, category, description) VALUES
('Project Management', 'Management', 'Planning, organizing and managing projects'),
('Leadership', 'Management', 'Team leadership and people management'),
('Communication', 'Soft Skills', 'Effective communication and presentation'),
('Problem Solving', 'Soft Skills', 'Analytical thinking and problem resolution'),
('Time Management', 'Soft Skills', 'Efficient time and priority management');

-- =====================================================
-- SECURITY AND PERMISSIONS
-- =====================================================

-- Row Level Security (RLS) setup would go here
-- Grant permissions to application roles
-- Set up audit triggers for sensitive tables

-- =====================================================
-- MAINTENANCE AND MONITORING
-- =====================================================

-- Create maintenance procedures
-- Set up monitoring views
-- Configure backup strategies

-- End of schema 