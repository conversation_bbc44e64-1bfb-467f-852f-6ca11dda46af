           table_name            |      column_name       |   foreign_table_name    | foreign_column_name 
---------------------------------+------------------------+-------------------------+---------------------
 admin_logs                      | admin_id               | users                   | id
 admin_logs                      | target_user_id         | users                   | id
 ai_generated_content            | generated_by           | users                   | id
 ai_generated_content            | applied_by             | users                   | id
 ai_interactions                 | user_id                | users                   | id
 applications                    | job_posting_id         | job_postings            | id
 applications                    | candidate_id           | candidates              | id
 bi_reports                      | created_by             | users                   | id
 business_processes              | owner_id               | users                   | id
 candidate_ai_scores             | evaluated_by           | users                   | id
 candidate_ai_scores             | job_posting_id         | job_postings            | id
 candidate_ai_scores             | candidate_id           | candidates              | id
 candidate_ai_scores             | application_id         | applications            | id
 candidate_skills                | candidate_id           | candidates              | id
 candidates                      | hired_as_user_id       | users                   | id
 case_studies                    | created_by             | users                   | id
 case_studies                    | project_id             | projects                | id
 case_studies                    | client_id              | clients                 | id
 case_studies                    | approved_by            | users                   | id
 certification_audits            | created_by             | users                   | id
 certification_audits            | certification_id       | company_certifications  | id
 certification_documents         | certification_id       | company_certifications  | id
 certification_documents         | uploaded_by            | users                   | id
 certification_documents         | previous_version_id    | certification_documents | id
 communication_reactions         | user_id                | users                   | id
 company_certifications          | project_id             | projects                | id
 company_certifications          | created_by             | users                   | id
 company_certifications          | backup_person_id       | users                   | id
 company_certifications          | responsible_person_id  | users                   | id
 company_certifications          | standard_code          | certification_standards | code
 company_communications          | author_id              | users                   | id
 company_event_registrations     | user_id                | users                   | id
 company_event_registrations     | event_id               | company_events          | id
 company_events                  | project_id             | projects                | id
 company_events                  | created_by             | users                   | id
 company_profiles                | updated_by             | users                   | id
 compliance_audit_logs           | user_id                | users                   | id
 compliance_events               | user_id                | users                   | id
 compliance_events               | resolved_by            | users                   | id
 compliance_policies             | reviewed_by            | users                   | id
 compliance_reports              | generated_by           | users                   | id
 compliance_reports              | reviewed_by            | users                   | id
 contacts                        | client_id              | clients                 | id
 contracts                       | client_id              | clients                 | id
 departments                     | manager_id             | users                   | id
 departments                     | parent_id              | departments             | id
 direct_messages                 | sender_id              | users                   | id
 direct_messages                 | recipient_id           | users                   | id
 documents                       | uploaded_by            | users                   | id
 employee_job_levels             | job_level_id           | job_levels              | id
 employee_job_levels             | created_by             | users                   | id
 employee_job_levels             | user_id                | users                   | id
 engagement_campaigns            | created_by_id          | users                   | id
 engagement_leaderboards         | user_id                | users                   | id
 engagement_leaderboards         | campaign_id            | engagement_campaigns    | id
 engagement_points               | campaign_id            | engagement_campaigns    | id
 engagement_points               | user_id                | users                   | id
 engagement_rewards              | created_by_id          | users                   | id
 engagement_rewards              | campaign_id            | engagement_campaigns    | id
 engagement_user_profiles        | user_id                | users                   | id
 engagement_user_profiles        | next_level_id          | engagement_levels       | id
 engagement_user_profiles        | current_level_id       | engagement_levels       | id
 engagement_user_rewards         | user_id                | users                   | id
 engagement_user_rewards         | reward_id              | engagement_rewards      | id
 feature_flags                   | updated_by             | users                   | id
 forum_comments                  | parent_comment_id      | forum_comments          | id
 forum_comments                  | topic_id               | forum_topics            | id
 forum_comments                  | author_id              | users                   | id
 forum_topics                    | author_id              | users                   | id
 funding_applications            | opportunity_id         | funding_opportunities   | id
 funding_applications            | created_by             | users                   | id
 funding_applications            | linked_project_id      | projects                | id
 funding_applications            | project_manager_id     | users                   | id
 funding_expenses                | approved_by            | users                   | id
 funding_expenses                | application_id         | funding_applications    | id
 funding_expenses                | project_id             | projects                | id
 funding_expenses                | timesheet_entry_id     | timesheet_entries       | id
 funding_opportunities           | created_by             | users                   | id
 healing_sessions                | initiated_by           | users                   | id
 healing_sessions                | error_pattern_id       | error_patterns          | id
 help_analytics                  | most_viewed_content_id | help_content            | id
 help_categories                 | parent_id              | help_categories         | id
 help_categories                 | created_by             | users                   | id
 help_content                    | updated_by             | users                   | id
 help_content                    | previous_version_id    | help_content            | id
 help_content                    | category_id            | help_categories         | id
 help_content                    | created_by             | users                   | id
 help_content                    | reviewed_by            | users                   | id
 help_conversations              | user_id                | users                   | id
 help_feedback                   | responded_by           | users                   | id
 help_feedback                   | conversation_id        | help_conversations      | id
 help_feedback                   | content_id             | help_content            | id
 help_feedback                   | user_id                | users                   | id
 hr_chat_conversations           | user_id                | users                   | id
 hr_knowledge_base               | created_by             | users                   | id
 integration_settings            | created_by             | users                   | id
 interview_sessions              | application_id         | applications            | id
 interview_sessions              | interviewer_id         | users                   | id
 invoice_lines                   | contract_id            | contracts               | id
 invoice_lines                   | invoice_id             | invoices                | id
 invoice_lines                   | project_id             | projects                | id
 invoices                        | client_id              | clients                 | id
 job_postings                    | project_id             | projects                | id
 job_postings                    | department_id          | departments             | id
 job_postings                    | created_by             | users                   | id
 job_postings                    | proposal_id            | proposals               | id
 market_prospects                | assigned_to            | users                   | id
 monthly_timesheets              | approved_by            | users                   | id
 monthly_timesheets              | user_id                | users                   | id
 news                            | author_id              | users                   | id
 notifications                   | user_id                | users                   | id
 oauth_accounts                  | user_id                | users                   | id
 performance_feedbacks           | feedback_giver_id      | users                   | id
 performance_feedbacks           | from_user_id           | users                   | id
 performance_feedbacks           | review_id              | performance_reviews     | id
 performance_feedbacks           | to_user_id             | users                   | id
 performance_goals               | review_id              | performance_reviews     | id
 performance_goals               | employee_id            | users                   | id
 performance_goals               | template_id            | performance_goals       | id
 performance_goals               | assigned_by_id         | users                   | id
 performance_goals               | set_by_id              | users                   | id
 performance_goals               | created_by             | users                   | id
 performance_kpis                | goal_id                | performance_goals       | id
 performance_review_participants | review_id              | performance_reviews     | id
 performance_review_participants | participant_id         | users                   | id
 performance_reviews             | created_by             | users                   | id
 performance_reviews             | approved_by            | users                   | id
 performance_reviews             | employee_id            | users                   | id
 performance_reviews             | reviewer_id            | users                   | id
 performance_rewards             | review_id              | performance_reviews     | id
 performance_rewards             | approved_by_hr         | users                   | id
 performance_rewards             | awarded_by_id          | users                   | id
 performance_rewards             | employee_id            | users                   | id
 performance_rewards             | goal_id                | performance_goals       | id
 performance_rewards             | approved_by            | users                   | id
 performance_rewards             | created_by             | users                   | id
 performance_templates           | created_by             | users                   | id
 personnel_rates                 | user_id                | users                   | id
 poll_options                    | poll_id                | polls                   | id
 poll_votes                      | poll_id                | polls                   | id
 poll_votes                      | option_id              | poll_options            | id
 poll_votes                      | user_id                | users                   | id
 polls                           | author_id              | users                   | id
 pre_invoice_lines               | pre_invoice_id         | pre_invoices            | id
 pre_invoice_lines               | project_id             | projects                | id
 pre_invoices                    | created_by             | users                   | id
 pre_invoices                    | contract_id            | contracts               | id
 pre_invoices                    | client_id              | clients                 | id
 process_steps                   | process_id             | business_processes      | id
 project_expenses                | user_id                | users                   | id
 project_expenses                | project_id             | projects                | id
 project_funding_links           | created_by             | users                   | id
 project_funding_links           | funding_application_id | funding_applications    | id
 project_funding_links           | project_id             | projects                | id
 project_kpi_targets             | project_id             | projects                | id
 project_kpi_targets             | created_by             | users                   | id
 project_kpis                    | project_id             | projects                | id
 project_kpis                    | kpi_id                 | kpis                    | id
 project_resources               | project_id             | projects                | id
 project_resources               | user_id                | users                   | id
 project_team                    | project_id             | projects                | id
 project_team                    | user_id                | users                   | id
 projects                        | client_id              | clients                 | id
 projects                        | funding_application_id | funding_applications    | id
 proposals                       | client_id              | clients                 | id
 proposals                       | created_by             | users                   | id
 readiness_tasks                 | created_by             | users                   | id
 readiness_tasks                 | assigned_to_id         | users                   | id
 readiness_tasks                 | certification_id       | company_certifications  | id
 recruiting_ai_usage             | user_id                | users                   | id
 recruiting_workflows            | application_id         | applications            | id
 recruiting_workflows            | assigned_to            | users                   | id
 research_queries                | session_id             | research_sessions       | id
 research_sessions               | user_id                | users                   | id
 risks                           | owner_id               | users                   | id
 scheduled_tasks                 | created_by             | users                   | id
 strategic_insights              | reviewed_by            | users                   | id
 strategic_insights              | session_id             | research_sessions       | id
 task_dependencies               | depends_on_id          | tasks                   | id
 task_dependencies               | task_id                | tasks                   | id
 tasks                           | assignee_id            | users                   | id
 tasks                           | project_id             | projects                | id
 technical_offers                | core_competency_id     | core_competencies       | id
 technical_offers                | created_by             | users                   | id
 time_off_requests               | user_id                | users                   | id
 time_off_requests               | approved_by            | users                   | id
 timesheet_entries               | task_id                | tasks                   | id
 timesheet_entries               | project_id             | projects                | id
 timesheet_entries               | user_id                | users                   | id
 user_profiles                   | user_id                | users                   | id
 user_skills                     | skill_id               | skills                  | id
 user_skills                     | user_id                | users                   | id
 user_skills_detailed            | skill_id               | skills                  | id
 user_skills_detailed            | user_id                | users                   | id
 users                           | department_id          | departments             | id
(195 rows)

