           table_name            |          column_name           |          data_type          | is_nullable |                       column_default                        
---------------------------------+--------------------------------+-----------------------------+-------------+-------------------------------------------------------------
 admin_logs                      | id                             | integer                     | NO          | nextval('admin_logs_id_seq'::regclass)
 admin_logs                      | admin_id                       | integer                     | NO          | 
 admin_logs                      | action                         | character varying           | NO          | 
 admin_logs                      | target_user_id                 | integer                     | YES         | 
 admin_logs                      | timestamp                      | timestamp without time zone | NO          | now()
 ai_generated_content            | id                             | integer                     | NO          | nextval('ai_generated_content_id_seq'::regclass)
 ai_generated_content            | entity_type                    | character varying           | NO          | 
 ai_generated_content            | entity_id                      | integer                     | YES         | 
 ai_generated_content            | content_type                   | character varying           | NO          | 
 ai_generated_content            | prompt_data                    | text                        | YES         | 
 ai_generated_content            | generated_content              | text                        | NO          | 
 ai_generated_content            | ai_model_used                  | character varying           | YES         | 
 ai_generated_content            | generation_tokens              | integer                     | YES         | 
 ai_generated_content            | generated_by                   | integer                     | NO          | 
 ai_generated_content            | applied_at                     | timestamp without time zone | YES         | 
 ai_generated_content            | applied_by                     | integer                     | YES         | 
 ai_generated_content            | user_rating                    | integer                     | YES         | 
 ai_generated_content            | user_feedback                  | text                        | YES         | 
 ai_generated_content            | created_at                     | timestamp without time zone | YES         | 
 ai_generated_content            | updated_at                     | timestamp without time zone | YES         | 
 ai_interactions                 | id                             | integer                     | NO          | nextval('ai_interactions_id_seq'::regclass)
 ai_interactions                 | user_id                        | integer                     | NO          | 
 ai_interactions                 | query                          | text                        | NO          | 
 ai_interactions                 | response                       | text                        | NO          | 
 ai_interactions                 | conversation_id                | character varying           | YES         | 
 ai_interactions                 | category                       | character varying           | YES         | 
 ai_interactions                 | context_data                   | json                        | YES         | 
 ai_interactions                 | model_used                     | character varying           | YES         | 
 ai_interactions                 | confidence_score               | double precision            | YES         | 
 ai_interactions                 | response_time_seconds          | double precision            | YES         | 
 ai_interactions                 | token_count_input              | integer                     | YES         | 
 ai_interactions                 | token_count_output             | integer                     | YES         | 
 ai_interactions                 | cost_estimate                  | double precision            | YES         | 
 ai_interactions                 | user_rating                    | integer                     | YES         | 
 ai_interactions                 | user_feedback                  | text                        | YES         | 
 ai_interactions                 | timestamp                      | timestamp without time zone | YES         | 
 alembic_version                 | version_num                    | character varying           | NO          | 
 applications                    | id                             | integer                     | NO          | nextval('applications_id_seq'::regclass)
 applications                    | job_posting_id                 | integer                     | NO          | 
 applications                    | candidate_id                   | integer                     | NO          | 
 applications                    | applied_at                     | timestamp without time zone | YES         | 
 applications                    | cover_letter                   | text                        | YES         | 
 applications                    | current_step                   | character varying           | YES         | 
 applications                    | status                         | character varying           | YES         | 
 applications                    | overall_score                  | integer                     | YES         | 
 applications                    | interview_notes                | text                        | YES         | 
 applications                    | rejection_reason               | text                        | YES         | 
 applications                    | created_at                     | timestamp without time zone | YES         | 
 applications                    | updated_at                     | timestamp without time zone | YES         | 
 bi_reports                      | id                             | integer                     | NO          | nextval('bi_reports_id_seq'::regclass)
 bi_reports                      | name                           | character varying           | NO          | 
 bi_reports                      | description                    | text                        | YES         | 
 bi_reports                      | report_type                    | character varying           | NO          | 
 bi_reports                      | data_sources                   | json                        | YES         | 
 bi_reports                      | filters                        | json                        | YES         | 
 bi_reports                      | chart_config                   | json                        | YES         | 
 bi_reports                      | schedule_config                | json                        | YES         | 
 bi_reports                      | export_formats                 | json                        | YES         | 
 bi_reports                      | is_scheduled                   | boolean                     | YES         | 
 bi_reports                      | last_generated                 | timestamp without time zone | YES         | 
 bi_reports                      | next_generation                | timestamp without time zone | YES         | 
 bi_reports                      | status                         | character varying           | YES         | 
 bi_reports                      | created_by                     | integer                     | YES         | 
 bi_reports                      | created_at                     | timestamp without time zone | YES         | 
 bi_reports                      | updated_at                     | timestamp without time zone | YES         | 
 business_processes              | id                             | integer                     | NO          | nextval('business_process_id_seq'::regclass)
 business_processes              | name                           | character varying           | NO          | 
 business_processes              | description                    | text                        | YES         | 
 business_processes              | owner_id                       | integer                     | YES         | 
 business_processes              | status                         | character varying           | YES         | 
 business_processes              | created_at                     | timestamp without time zone | YES         | 
 business_processes              | updated_at                     | timestamp without time zone | YES         | 
 candidate_ai_scores             | id                             | integer                     | NO          | nextval('candidate_ai_scores_id_seq'::regclass)
 candidate_ai_scores             | candidate_id                   | integer                     | NO          | 
 candidate_ai_scores             | job_posting_id                 | integer                     | NO          | 
 candidate_ai_scores             | application_id                 | integer                     | YES         | 
 candidate_ai_scores             | overall_score                  | integer                     | NO          | 
 candidate_ai_scores             | confidence_score               | double precision            | NO          | 
 candidate_ai_scores             | technical_skills_score         | integer                     | YES         | 
 candidate_ai_scores             | experience_score               | integer                     | YES         | 
 candidate_ai_scores             | motivation_score               | integer                     | YES         | 
 candidate_ai_scores             | cultural_fit_score             | integer                     | YES         | 
 candidate_ai_scores             | growth_potential_score         | integer                     | YES         | 
 candidate_ai_scores             | ai_reasoning                   | text                        | YES         | 
 candidate_ai_scores             | recommendation                 | character varying           | YES         | 
 candidate_ai_scores             | strengths                      | text                        | YES         | 
 candidate_ai_scores             | concerns                       | text                        | YES         | 
 candidate_ai_scores             | ai_model_used                  | character varying           | YES         | 
 candidate_ai_scores             | evaluation_version             | character varying           | YES         | 
 candidate_ai_scores             | evaluated_by                   | integer                     | NO          | 
 candidate_ai_scores             | created_at                     | timestamp without time zone | YES         | 
 candidate_ai_scores             | updated_at                     | timestamp without time zone | YES         | 
 candidate_skills                | id                             | integer                     | NO          | nextval('candidate_skills_id_seq'::regclass)
 candidate_skills                | candidate_id                   | integer                     | NO          | 
 candidate_skills                | skill_name                     | character varying           | NO          | 
 candidate_skills                | skill_category                 | character varying           | YES         | 
 candidate_skills                | skill_level                    | integer                     | YES         | 
 candidate_skills                | years_experience               | integer                     | YES         | 
 candidate_skills                | extracted_from_cv              | boolean                     | YES         | 
 candidate_skills                | confidence_score               | double precision            | YES         | 
 candidate_skills                | created_at                     | timestamp without time zone | YES         | 
 candidate_skills                | updated_at                     | timestamp without time zone | YES         | 
 candidates                      | id                             | integer                     | NO          | nextval('candidates_id_seq'::regclass)
 candidates                      | first_name                     | character varying           | NO          | 
 candidates                      | last_name                      | character varying           | NO          | 
 candidates                      | email                          | character varying           | NO          | 
 candidates                      | phone                          | character varying           | YES         | 
 candidates                      | location                       | character varying           | YES         | 
 candidates                      | linkedin_url                   | character varying           | YES         | 
 candidates                      | current_cv_path                | character varying           | YES         | 
 candidates                      | cv_last_updated                | timestamp without time zone | YES         | 
 candidates                      | cv_analysis_data               | text                        | YES         | 
 candidates                      | source                         | character varying           | YES         | 
 candidates                      | status                         | character varying           | YES         | 
 candidates                      | notes                          | text                        | YES         | 
 candidates                      | tags                           | text                        | YES         | 
 candidates                      | hired_as_user_id               | integer                     | YES         | 
 candidates                      | hired_date                     | date                        | YES         | 
 candidates                      | created_at                     | timestamp without time zone | YES         | 
 candidates                      | updated_at                     | timestamp without time zone | YES         | 
 case_studies                    | id                             | integer                     | NO          | nextval('case_studies_id_seq'::regclass)
 case_studies                    | title                          | character varying           | NO          | 
 case_studies                    | overview                       | text                        | NO          | 
 case_studies                    | content                        | text                        | YES         | 
 case_studies                    | case_type                      | character varying           | NO          | 
 case_studies                    | primary_sector                 | character varying           | YES         | 
 case_studies                    | secondary_sectors              | json                        | YES         | 
 case_studies                    | project_id                     | integer                     | YES         | 
 case_studies                    | client_id                      | integer                     | YES         | 
 case_studies                    | technologies                   | json                        | YES         | 
 case_studies                    | business_kpis                  | json                        | YES         | 
 case_studies                    | implementation_duration        | integer                     | YES         | 
 case_studies                    | team_size                      | integer                     | YES         | 
 case_studies                    | status                         | character varying           | YES         | 
 case_studies                    | generated_by_ai                | boolean                     | YES         | 
 case_studies                    | ai_prompt_used                 | text                        | YES         | 
 case_studies                    | target_audience                | character varying           | YES         | 
 case_studies                    | created_by                     | integer                     | NO          | 
 case_studies                    | approved_by                    | integer                     | YES         | 
 case_studies                    | created_at                     | timestamp without time zone | YES         | 
 case_studies                    | updated_at                     | timestamp without time zone | YES         | 
 case_studies                    | approved_at                    | timestamp without time zone | YES         | 
 certification_audits            | id                             | integer                     | NO          | nextval('certification_audits_id_seq'::regclass)
 certification_audits            | certification_id               | integer                     | NO          | 
 certification_audits            | audit_type                     | character varying           | NO          | 
 certification_audits            | planned_date                   | date                        | NO          | 
 certification_audits            | actual_date                    | date                        | YES         | 
 certification_audits            | duration_days                  | integer                     | YES         | 
 certification_audits            | lead_auditor                   | character varying           | YES         | 
 certification_audits            | audit_team                     | json                        | YES         | 
 certification_audits            | auditor_contact                | text                        | YES         | 
 certification_audits            | status                         | character varying           | YES         | 
 certification_audits            | result                         | character varying           | YES         | 
 certification_audits            | overall_score                  | integer                     | YES         | 
 certification_audits            | major_findings                 | integer                     | YES         | 
 certification_audits            | minor_findings                 | integer                     | YES         | 
 certification_audits            | observations                   | integer                     | YES         | 
 certification_audits            | findings_summary               | text                        | YES         | 
 certification_audits            | audit_report_path              | character varying           | YES         | 
 certification_audits            | corrective_actions_path        | character varying           | YES         | 
 certification_audits            | evidence_folder_path           | character varying           | YES         | 
 certification_audits            | audit_cost                     | double precision            | YES         | 
 certification_audits            | travel_expenses                | double precision            | YES         | 
 certification_audits            | corrective_actions_due         | date                        | YES         | 
 certification_audits            | follow_up_audit_date           | date                        | YES         | 
 certification_audits            | next_audit_type                | character varying           | YES         | 
 certification_audits            | notes                          | text                        | YES         | 
 certification_audits            | created_at                     | timestamp without time zone | YES         | 
 certification_audits            | updated_at                     | timestamp without time zone | YES         | 
 certification_audits            | created_by                     | integer                     | NO          | 
 certification_documents         | id                             | integer                     | NO          | nextval('certification_documents_id_seq'::regclass)
 certification_documents         | certification_id               | integer                     | NO          | 
 certification_documents         | document_name                  | character varying           | NO          | 
 certification_documents         | document_type                  | character varying           | NO          | 
 certification_documents         | document_category              | character varying           | YES         | 
 certification_documents         | file_path                      | character varying           | NO          | 
 certification_documents         | file_size                      | integer                     | YES         | 
 certification_documents         | file_type                      | character varying           | YES         | 
 certification_documents         | version                        | character varying           | YES         | 
 certification_documents         | is_current_version             | boolean                     | YES         | 
 certification_documents         | previous_version_id            | integer                     | YES         | 
 certification_documents         | description                    | text                        | YES         | 
 certification_documents         | tags                           | json                        | YES         | 
 certification_documents         | created_at                     | timestamp without time zone | YES         | 
 certification_documents         | updated_at                     | timestamp without time zone | YES         | 
 certification_documents         | uploaded_by                    | integer                     | NO          | 
 certification_documents         | access_level                   | character varying           | YES         | 
 certification_standards         | code                           | character varying           | NO          | 
 certification_standards         | name                           | character varying           | NO          | 
 certification_standards         | version                        | character varying           | YES         | 
 certification_standards         | category                       | character varying           | NO          | 
 certification_standards         | industry_sector                | character varying           | YES         | 
 certification_standards         | typical_validity_years         | integer                     | YES         | 
 certification_standards         | renewal_notice_months          | integer                     | YES         | 
 certification_standards         | audit_frequency_months         | integer                     | YES         | 
 certification_standards         | estimated_cost_min             | double precision            | YES         | 
 certification_standards         | estimated_cost_max             | double precision            | YES         | 
 certification_standards         | currency                       | character varying           | YES         | 
 certification_standards         | requirements                   | json                        | YES         | 
 certification_standards         | preparatory_tasks              | json                        | YES         | 
 certification_standards         | documentation_required         | json                        | YES         | 
 certification_standards         | description                    | text                        | YES         | 
 certification_standards         | issuing_body                   | character varying           | YES         | 
 certification_standards         | website_url                    | character varying           | YES         | 
 certification_standards         | is_active                      | boolean                     | YES         | 
 certification_standards         | tenant_enabled                 | boolean                     | YES         | 
 certification_standards         | tenant_priority                | integer                     | YES         | 
 clients                         | id                             | integer                     | NO          | nextval('client_id_seq'::regclass)
 clients                         | name                           | character varying           | NO          | 
 clients                         | industry                       | character varying           | YES         | 
 clients                         | description                    | text                        | YES         | 
 clients                         | website                        | character varying           | YES         | 
 clients                         | address                        | character varying           | YES         | 
 clients                         | created_at                     | timestamp without time zone | YES         | 
 clients                         | updated_at                     | timestamp without time zone | YES         | 
 clients                         | status                         | character varying           | YES         | 'client'::character varying
 clients                         | email                          | character varying           | YES         | 
 clients                         | phone                          | character varying           | YES         | 
 clients                         | vat_number                     | character varying           | YES         | 
 clients                         | fiscal_code                    | character varying           | YES         | 
 communication_reactions         | id                             | integer                     | NO          | nextval('communication_reactions_id_seq'::regclass)
 communication_reactions         | user_id                        | integer                     | NO          | 
 communication_reactions         | target_type                    | character varying           | NO          | 
 communication_reactions         | target_id                      | integer                     | NO          | 
 communication_reactions         | reaction_type                  | character varying           | NO          | 
 communication_reactions         | created_at                     | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 company_certifications          | id                             | integer                     | NO          | nextval('company_certifications_id_seq'::regclass)
 company_certifications          | standard_code                  | character varying           | NO          | 
 company_certifications          | certificate_number             | character varying           | YES         | 
 company_certifications          | certifying_body                | character varying           | NO          | 
 company_certifications          | certifying_body_contact        | text                        | YES         | 
 company_certifications          | issue_date                     | date                        | NO          | 
 company_certifications          | expiry_date                    | date                        | NO          | 
 company_certifications          | next_audit_date                | date                        | YES         | 
 company_certifications          | status                         | character varying           | YES         | 
 company_certifications          | health_score                   | integer                     | YES         | 
 company_certifications          | initial_cost                   | double precision            | YES         | 
 company_certifications          | annual_maintenance_cost        | double precision            | YES         | 
 company_certifications          | last_audit_cost                | double precision            | YES         | 
 company_certifications          | responsible_person_id          | integer                     | NO          | 
 company_certifications          | backup_person_id               | integer                     | YES         | 
 company_certifications          | certificate_file_path          | character varying           | YES         | 
 company_certifications          | documentation_folder_path      | character varying           | YES         | 
 company_certifications          | created_at                     | timestamp without time zone | YES         | 
 company_certifications          | updated_at                     | timestamp without time zone | YES         | 
 company_certifications          | created_by                     | integer                     | NO          | 
 company_certifications          | project_id                     | integer                     | YES         | 
 company_communications          | id                             | integer                     | NO          | nextval('news_id_seq'::regclass)
 company_communications          | title                          | character varying           | NO          | 
 company_communications          | content                        | text                        | NO          | 
 company_communications          | author_id                      | integer                     | NO          | 
 company_communications          | image_url                      | character varying           | YES         | 
 company_communications          | is_published                   | boolean                     | YES         | 
 company_communications          | created_at                     | timestamp without time zone | YES         | 
 company_communications          | updated_at                     | timestamp without time zone | YES         | 
 company_communications          | communication_type             | character varying           | YES         | 'news'::character varying
 company_communications          | target_audience                | character varying           | YES         | 
 company_communications          | priority_level                 | character varying           | YES         | 'normal'::character varying
 company_communications          | is_pinned                      | boolean                     | YES         | false
 company_communications          | scheduled_at                   | timestamp without time zone | YES         | 
 company_communications          | expires_at                     | timestamp without time zone | YES         | 
 company_communications          | tags                           | ARRAY                       | YES         | 
 company_communications          | allow_comments                 | boolean                     | YES         | true
 company_communications          | view_count                     | integer                     | YES         | 0
 company_event_registrations     | id                             | integer                     | NO          | nextval('company_event_registrations_id_seq'::regclass)
 company_event_registrations     | event_id                       | integer                     | NO          | 
 company_event_registrations     | user_id                        | integer                     | NO          | 
 company_event_registrations     | status                         | character varying           | YES         | 'registered'::character varying
 company_event_registrations     | registered_at                  | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 company_event_registrations     | notes                          | text                        | YES         | 
 company_events                  | id                             | integer                     | NO          | nextval('company_events_id_seq'::regclass)
 company_events                  | title                          | character varying           | NO          | 
 company_events                  | description                    | text                        | YES         | 
 company_events                  | project_id                     | integer                     | YES         | 
 company_events                  | start_time                     | timestamp without time zone | NO          | 
 company_events                  | end_time                       | timestamp without time zone | NO          | 
 company_events                  | location                       | character varying           | YES         | 
 company_events                  | event_type                     | character varying           | YES         | 
 company_events                  | created_by                     | integer                     | YES         | 
 company_events                  | created_at                     | timestamp without time zone | YES         | 
 company_events                  | is_company_wide                | boolean                     | YES         | 
 company_events                  | max_participants               | integer                     | YES         | 
 company_events                  | registration_required          | boolean                     | YES         | 
 company_events                  | registration_deadline          | timestamp without time zone | YES         | 
 company_events                  | is_public                      | boolean                     | YES         | 
 company_events                  | tags                           | ARRAY                       | YES         | 
 company_events                  | allow_comments                 | boolean                     | YES         | 
 company_events                  | updated_at                     | timestamp without time zone | YES         | 
 company_invoicing_settings      | id                             | integer                     | NO          | nextval('company_invoicing_settings_id_seq'::regclass)
 company_invoicing_settings      | company_name                   | character varying           | NO          | 
 company_invoicing_settings      | vat_number                     | character varying           | YES         | 
 company_invoicing_settings      | fiscal_code                    | character varying           | YES         | 
 company_invoicing_settings      | address                        | text                        | YES         | 
 company_invoicing_settings      | phone                          | character varying           | YES         | 
 company_invoicing_settings      | email                          | character varying           | YES         | 
 company_invoicing_settings      | pec                            | character varying           | YES         | 
 company_invoicing_settings      | default_vat_rate               | numeric                     | YES         | 
 company_invoicing_settings      | default_retention_rate         | numeric                     | YES         | 
 company_invoicing_settings      | default_payment_terms          | integer                     | YES         | 
 company_invoicing_settings      | invoice_prefix                 | character varying           | YES         | 
 company_invoicing_settings      | current_year                   | integer                     | YES         | 
 company_invoicing_settings      | last_number                    | integer                     | YES         | 
 company_invoicing_settings      | tax_regime                     | character varying           | YES         | 
 company_invoicing_settings      | is_active                      | boolean                     | YES         | 
 company_invoicing_settings      | created_at                     | timestamp without time zone | YES         | 
 company_invoicing_settings      | updated_at                     | timestamp without time zone | YES         | 
 company_profiles                | id                             | integer                     | NO          | nextval('company_profiles_id_seq'::regclass)
 company_profiles                | company_name                   | character varying           | YES         | 
 company_profiles                | mission                        | text                        | YES         | 
 company_profiles                | vision                         | text                        | YES         | 
 company_profiles                | values                         | text                        | YES         | 
 company_profiles                | industry                       | character varying           | YES         | 
 company_profiles                | business_model                 | character varying           | YES         | 
 company_profiles                | company_size                   | character varying           | YES         | 
 company_profiles                | target_market                  | text                        | YES         | 
 company_profiles                | competitive_advantages         | text                        | YES         | 
 company_profiles                | key_challenges                 | text                        | YES         | 
 company_profiles                | strategic_objectives           | json                        | YES         | 
 company_profiles                | market_segment                 | character varying           | YES         | 
 company_profiles                | geographic_focus               | character varying           | YES         | 
 company_profiles                | revenue_model                  | character varying           | YES         | 
 company_profiles                | current_stage                  | character varying           | YES         | 
 company_profiles                | analysis_focus_areas           | json                        | YES         | 
 company_profiles                | reporting_preferences          | json                        | YES         | 
 company_profiles                | is_active                      | boolean                     | YES         | 
 company_profiles                | created_at                     | timestamp without time zone | YES         | 
 company_profiles                | updated_at                     | timestamp without time zone | YES         | 
 company_profiles                | updated_by                     | integer                     | YES         | 
 compliance_audit_logs           | id                             | integer                     | NO          | nextval('compliance_audit_logs_id_seq'::regclass)
 compliance_audit_logs           | user_id                        | integer                     | YES         | 
 compliance_audit_logs           | session_id                     | character varying           | YES         | 
 compliance_audit_logs           | action_type                    | character varying           | NO          | 
 compliance_audit_logs           | resource_type                  | character varying           | YES         | 
 compliance_audit_logs           | resource_id                    | character varying           | YES         | 
 compliance_audit_logs           | endpoint                       | character varying           | YES         | 
 compliance_audit_logs           | method                         | character varying           | YES         | 
 compliance_audit_logs           | ip_address                     | character varying           | YES         | 
 compliance_audit_logs           | user_agent                     | text                        | YES         | 
 compliance_audit_logs           | request_data                   | json                        | YES         | 
 compliance_audit_logs           | response_status                | integer                     | YES         | 
 compliance_audit_logs           | response_size                  | integer                     | YES         | 
 compliance_audit_logs           | processing_time_ms             | integer                     | YES         | 
 compliance_audit_logs           | compliance_context             | json                        | YES         | 
 compliance_audit_logs           | timestamp                      | timestamp without time zone | NO          | 
 compliance_audit_logs           | risk_level                     | character varying           | YES         | 
 compliance_audit_logs           | data_classification            | character varying           | YES         | 
 compliance_audit_logs           | retention_policy               | character varying           | YES         | 
 compliance_audit_logs           | is_sensitive                   | boolean                     | YES         | 
 compliance_events               | id                             | integer                     | NO          | nextval('compliance_events_id_seq'::regclass)
 compliance_events               | event_type                     | character varying           | NO          | 
 compliance_events               | event_category                 | character varying           | YES         | 
 compliance_events               | severity                       | character varying           | YES         | 
 compliance_events               | user_id                        | integer                     | YES         | 
 compliance_events               | affected_users                 | json                        | YES         | 
 compliance_events               | title                          | character varying           | NO          | 
 compliance_events               | description                    | text                        | NO          | 
 compliance_events               | compliance_framework           | character varying           | YES         | 
 compliance_events               | policy_reference               | character varying           | YES         | 
 compliance_events               | regulatory_impact              | boolean                     | YES         | 
 compliance_events               | source_ip                      | character varying           | YES         | 
 compliance_events               | source_system                  | character varying           | YES         | 
 compliance_events               | affected_resources             | json                        | YES         | 
 compliance_events               | risk_score                     | integer                     | YES         | 
 compliance_events               | event_metadata                 | json                        | YES         | 
 compliance_events               | evidence_data                  | json                        | YES         | 
 compliance_events               | created_at                     | timestamp without time zone | NO          | 
 compliance_events               | detected_at                    | timestamp without time zone | YES         | 
 compliance_events               | occurred_at                    | timestamp without time zone | YES         | 
 compliance_events               | status                         | character varying           | YES         | 
 compliance_events               | resolved_at                    | timestamp without time zone | YES         | 
 compliance_events               | resolved_by                    | integer                     | YES         | 
 compliance_events               | resolution_notes               | text                        | YES         | 
 compliance_events               | notification_sent              | boolean                     | YES         | 
 compliance_events               | escalated                      | boolean                     | YES         | 
 compliance_events               | escalated_at                   | timestamp without time zone | YES         | 
 compliance_policies             | id                             | integer                     | NO          | nextval('compliance_policies_id_seq'::regclass)
 compliance_policies             | name                           | character varying           | NO          | 
 compliance_policies             | description                    | text                        | YES         | 
 compliance_policies             | policy_type                    | character varying           | NO          | 
 compliance_policies             | rules_config                   | json                        | YES         | 
 compliance_policies             | trigger_conditions             | json                        | YES         | 
 compliance_policies             | actions                        | json                        | YES         | 
 compliance_policies             | framework                      | character varying           | YES         | 
 compliance_policies             | article_reference              | character varying           | YES         | 
 compliance_policies             | is_active                      | boolean                     | YES         | 
 compliance_policies             | version                        | character varying           | YES         | 
 compliance_policies             | effective_date                 | timestamp without time zone | YES         | 
 compliance_policies             | expiry_date                    | timestamp without time zone | YES         | 
 compliance_policies             | owner_role                     | character varying           | YES         | 
 compliance_policies             | reviewed_by                    | integer                     | YES         | 
 compliance_policies             | last_review_date               | timestamp without time zone | YES         | 
 compliance_policies             | next_review_date               | timestamp without time zone | YES         | 
 compliance_policies             | created_at                     | timestamp without time zone | YES         | 
 compliance_policies             | updated_at                     | timestamp without time zone | YES         | 
 compliance_reports              | id                             | integer                     | NO          | nextval('compliance_reports_id_seq'::regclass)
 compliance_reports              | name                           | character varying           | NO          | 
 compliance_reports              | report_type                    | character varying           | NO          | 
 compliance_reports              | period_type                    | character varying           | YES         | 
 compliance_reports              | period_start                   | timestamp without time zone | NO          | 
 compliance_reports              | period_end                     | timestamp without time zone | NO          | 
 compliance_reports              | summary_data                   | json                        | YES         | 
 compliance_reports              | detailed_data                  | json                        | YES         | 
 compliance_reports              | recommendations                | json                        | YES         | 
 compliance_reports              | framework                      | character varying           | YES         | 
 compliance_reports              | scope                          | character varying           | YES         | 
 compliance_reports              | risk_level                     | character varying           | YES         | 
 compliance_reports              | status                         | character varying           | YES         | 
 compliance_reports              | generated_by                   | integer                     | YES         | 
 compliance_reports              | generated_at                   | timestamp without time zone | YES         | 
 compliance_reports              | reviewed_by                    | integer                     | YES         | 
 compliance_reports              | reviewed_at                    | timestamp without time zone | YES         | 
 compliance_reports              | file_path                      | character varying           | YES         | 
 compliance_reports              | file_hash                      | character varying           | YES         | 
 contacts                        | id                             | integer                     | NO          | nextval('contact_id_seq'::regclass)
 contacts                        | client_id                      | integer                     | NO          | 
 contacts                        | first_name                     | character varying           | NO          | 
 contacts                        | last_name                      | character varying           | NO          | 
 contacts                        | position                       | character varying           | YES         | 
 contacts                        | email                          | character varying           | YES         | 
 contacts                        | phone                          | character varying           | YES         | 
 contacts                        | notes                          | text                        | YES         | 
 contacts                        | created_at                     | timestamp without time zone | YES         | 
 contacts                        | updated_at                     | timestamp without time zone | YES         | 
 contracts                       | id                             | integer                     | NO          | nextval('contracts_id_seq'::regclass)
 contracts                       | client_id                      | integer                     | NO          | 
 contracts                       | contract_number                | character varying           | NO          | 
 contracts                       | title                          | character varying           | NO          | 
 contracts                       | description                    | text                        | YES         | 
 contracts                       | contract_type                  | character varying           | YES         | 
 contracts                       | hourly_rate                    | double precision            | YES         | 
 contracts                       | budget_hours                   | double precision            | YES         | 
 contracts                       | budget_amount                  | double precision            | YES         | 
 contracts                       | start_date                     | date                        | NO          | 
 contracts                       | end_date                       | date                        | YES         | 
 contracts                       | status                         | character varying           | YES         | 
 contracts                       | created_at                     | timestamp without time zone | YES         | 
 contracts                       | updated_at                     | timestamp without time zone | YES         | 
 contracts                       | milestone_amount               | double precision            | YES         | 
 contracts                       | milestone_count                | integer                     | YES         | 
 contracts                       | subscription_frequency         | character varying           | YES         | 
 contracts                       | subscription_amount            | double precision            | YES         | 
 contracts                       | retainer_amount                | double precision            | YES         | 
 contracts                       | retainer_frequency             | character varying           | YES         | 
 core_competencies               | id                             | integer                     | NO          | nextval('core_competencies_id_seq'::regclass)
 core_competencies               | name                           | character varying           | NO          | 
 core_competencies               | description                    | text                        | YES         | 
 core_competencies               | category                       | character varying           | YES         | 
 core_competencies               | market_positioning             | text                        | YES         | 
 core_competencies               | skill_ids                      | json                        | YES         | 
 core_competencies               | min_team_size                  | integer                     | YES         | 
 core_competencies               | avg_proficiency_required       | double precision            | YES         | 
 core_competencies               | business_value                 | text                        | YES         | 
 core_competencies               | target_markets                 | json                        | YES         | 
 core_competencies               | competitive_advantage          | text                        | YES         | 
 core_competencies               | is_active                      | boolean                     | YES         | 
 core_competencies               | created_at                     | timestamp without time zone | YES         | 
 core_competencies               | updated_at                     | timestamp without time zone | YES         | 
 departments                     | id                             | integer                     | NO          | nextval('departments_id_seq'::regclass)
 departments                     | name                           | character varying           | NO          | 
 departments                     | description                    | text                        | YES         | 
 departments                     | manager_id                     | integer                     | YES         | 
 departments                     | parent_id                      | integer                     | YES         | 
 departments                     | budget                         | double precision            | YES         | 0.0
 departments                     | is_active                      | boolean                     | YES         | true
 departments                     | created_at                     | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 departments                     | updated_at                     | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 direct_messages                 | id                             | integer                     | NO          | nextval('direct_messages_id_seq'::regclass)
 direct_messages                 | sender_id                      | integer                     | NO          | 
 direct_messages                 | recipient_id                   | integer                     | NO          | 
 direct_messages                 | message                        | text                        | NO          | 
 direct_messages                 | is_read                        | boolean                     | YES         | false
 direct_messages                 | read_at                        | timestamp without time zone | YES         | 
 direct_messages                 | created_at                     | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 direct_messages                 | updated_at                     | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 documents                       | id                             | integer                     | NO          | nextval('document_id_seq'::regclass)
 documents                       | title                          | character varying           | NO          | 
 documents                       | description                    | text                        | YES         | 
 documents                       | category                       | character varying           | YES         | 
 documents                       | file_path                      | character varying           | NO          | 
 documents                       | uploaded_by                    | integer                     | NO          | 
 documents                       | version                        | character varying           | YES         | 
 documents                       | created_at                     | timestamp without time zone | YES         | 
 documents                       | updated_at                     | timestamp without time zone | YES         | 
 employee_job_levels             | id                             | integer                     | NO          | nextval('employee_job_levels_id_seq'::regclass)
 employee_job_levels             | user_id                        | integer                     | NO          | 
 employee_job_levels             | job_level_id                   | integer                     | NO          | 
 employee_job_levels             | start_date                     | date                        | NO          | 
 employee_job_levels             | end_date                       | date                        | YES         | 
 employee_job_levels             | current_salary                 | double precision            | YES         | 
 employee_job_levels             | notes                          | text                        | YES         | 
 employee_job_levels             | is_active                      | boolean                     | YES         | 
 employee_job_levels             | created_by                     | integer                     | YES         | 
 employee_job_levels             | created_at                     | timestamp without time zone | YES         | 
 engagement_campaigns            | id                             | integer                     | NO          | nextval('engagement_campaigns_id_seq'::regclass)
 engagement_campaigns            | name                           | character varying           | NO          | 
 engagement_campaigns            | description                    | text                        | YES         | 
 engagement_campaigns            | start_date                     | date                        | NO          | 
 engagement_campaigns            | end_date                       | date                        | NO          | 
 engagement_campaigns            | status                         | character varying           | YES         | 
 engagement_campaigns            | points_multiplier              | numeric                     | YES         | 
 engagement_campaigns            | objectives_config              | json                        | YES         | 
 engagement_campaigns            | points_rules                   | json                        | YES         | 
 engagement_campaigns            | created_by_id                  | integer                     | YES         | 
 engagement_campaigns            | created_at                     | timestamp without time zone | YES         | 
 engagement_campaigns            | updated_at                     | timestamp without time zone | YES         | 
 engagement_leaderboards         | id                             | integer                     | NO          | nextval('engagement_leaderboards_id_seq'::regclass)
 engagement_leaderboards         | user_id                        | integer                     | NO          | 
 engagement_leaderboards         | campaign_id                    | integer                     | YES         | 
 engagement_leaderboards         | ranking_position               | integer                     | NO          | 
 engagement_leaderboards         | total_points                   | integer                     | NO          | 
 engagement_leaderboards         | period_type                    | character varying           | YES         | 
 engagement_leaderboards         | period_start                   | date                        | YES         | 
 engagement_leaderboards         | period_end                     | date                        | YES         | 
 engagement_leaderboards         | calculated_at                  | timestamp without time zone | YES         | 
 engagement_levels               | id                             | integer                     | NO          | nextval('engagement_levels_id_seq'::regclass)
 engagement_levels               | name                           | character varying           | NO          | 
 engagement_levels               | description                    | text                        | YES         | 
 engagement_levels               | points_threshold               | integer                     | NO          | 
 engagement_levels               | level_order                    | integer                     | NO          | 
 engagement_levels               | rewards_config                 | json                        | YES         | 
 engagement_levels               | color_hex                      | character varying           | YES         | 
 engagement_levels               | icon_name                      | character varying           | YES         | 
 engagement_levels               | is_active                      | boolean                     | YES         | 
 engagement_levels               | created_at                     | timestamp without time zone | YES         | 
 engagement_points               | id                             | integer                     | NO          | nextval('engagement_points_id_seq'::regclass)
 engagement_points               | user_id                        | integer                     | NO          | 
 engagement_points               | campaign_id                    | integer                     | YES         | 
 engagement_points               | points_earned                  | integer                     | NO          | 
 engagement_points               | source_type                    | character varying           | NO          | 
 engagement_points               | source_id                      | integer                     | YES         | 
 engagement_points               | action_type                    | character varying           | YES         | 
 engagement_points               | resource_type                  | character varying           | YES         | 
 engagement_points               | resource_id                    | integer                     | YES         | 
 engagement_points               | description                    | character varying           | YES         | 
 engagement_points               | multiplier_applied             | numeric                     | YES         | 
 engagement_points               | earned_at                      | timestamp without time zone | YES         | 
 engagement_points               | created_at                     | timestamp without time zone | YES         | 
 engagement_rewards              | id                             | integer                     | NO          | nextval('engagement_rewards_id_seq'::regclass)
 engagement_rewards              | name                           | character varying           | NO          | 
 engagement_rewards              | description                    | text                        | YES         | 
 engagement_rewards              | points_cost                    | integer                     | NO          | 
 engagement_rewards              | reward_type                    | character varying           | NO          | 
 engagement_rewards              | available_from                 | date                        | YES         | 
 engagement_rewards              | available_until                | date                        | YES         | 
 engagement_rewards              | campaign_id                    | integer                     | YES         | 
 engagement_rewards              | max_redemptions                | integer                     | YES         | 
 engagement_rewards              | current_redemptions            | integer                     | YES         | 
 engagement_rewards              | per_user_limit                 | integer                     | YES         | 
 engagement_rewards              | image_url                      | character varying           | YES         | 
 engagement_rewards              | external_url                   | character varying           | YES         | 
 engagement_rewards              | is_active                      | boolean                     | YES         | 
 engagement_rewards              | created_by_id                  | integer                     | YES         | 
 engagement_rewards              | created_at                     | timestamp without time zone | YES         | 
 engagement_rewards              | updated_at                     | timestamp without time zone | YES         | 
 engagement_user_profiles        | id                             | integer                     | NO          | nextval('engagement_user_profiles_id_seq'::regclass)
 engagement_user_profiles        | user_id                        | integer                     | NO          | 
 engagement_user_profiles        | total_points                   | integer                     | YES         | 
 engagement_user_profiles        | total_points_spent             | integer                     | YES         | 
 engagement_user_profiles        | available_points               | integer                     | YES         | 
 engagement_user_profiles        | current_level_id               | integer                     | YES         | 
 engagement_user_profiles        | next_level_id                  | integer                     | YES         | 
 engagement_user_profiles        | total_logins                   | integer                     | YES         | 
 engagement_user_profiles        | total_actions                  | integer                     | YES         | 
 engagement_user_profiles        | streak_days                    | integer                     | YES         | 
 engagement_user_profiles        | last_activity_date             | date                        | YES         | 
 engagement_user_profiles        | created_at                     | timestamp without time zone | YES         | 
 engagement_user_profiles        | updated_at                     | timestamp without time zone | YES         | 
 engagement_user_profiles        | activities_completed           | integer                     | YES         | 0
 engagement_user_profiles        | achievements_count             | integer                     | YES         | 0
 engagement_user_profiles        | current_streak_days            | integer                     | YES         | 0
 engagement_user_rewards         | id                             | integer                     | NO          | nextval('engagement_user_rewards_id_seq'::regclass)
 engagement_user_rewards         | user_id                        | integer                     | NO          | 
 engagement_user_rewards         | reward_id                      | integer                     | NO          | 
 engagement_user_rewards         | points_spent                   | integer                     | NO          | 
 engagement_user_rewards         | redemption_status              | character varying           | YES         | 
 engagement_user_rewards         | redemption_notes               | text                        | YES         | 
 engagement_user_rewards         | admin_notes                    | text                        | YES         | 
 engagement_user_rewards         | redeemed_at                    | timestamp without time zone | YES         | 
 engagement_user_rewards         | fulfilled_at                   | timestamp without time zone | YES         | 
 error_patterns                  | id                             | integer                     | NO          | nextval('error_patterns_id_seq'::regclass)
 error_patterns                  | pattern_hash                   | character varying           | NO          | 
 error_patterns                  | error_type                     | character varying           | NO          | 
 error_patterns                  | message_pattern                | text                        | NO          | 
 error_patterns                  | file_pattern                   | character varying           | YES         | 
 error_patterns                  | occurrence_count               | integer                     | YES         | 
 error_patterns                  | first_seen                     | timestamp without time zone | YES         | 
 error_patterns                  | last_seen                      | timestamp without time zone | YES         | 
 error_patterns                  | severity                       | character varying           | YES         | 
 error_patterns                  | is_auto_healable               | boolean                     | YES         | 
 error_patterns                  | healing_success_rate           | double precision            | YES         | 
 error_patterns                  | last_healing_attempt           | timestamp without time zone | YES         | 
 error_patterns                  | ai_analysis                    | text                        | YES         | 
 error_patterns                  | suggested_fix                  | text                        | YES         | 
 feature_flags                   | id                             | integer                     | NO          | nextval('feature_flags_id_seq'::regclass)
 feature_flags                   | feature_key                    | character varying           | NO          | 
 feature_flags                   | display_name                   | character varying           | NO          | 
 feature_flags                   | description                    | text                        | YES         | 
 feature_flags                   | is_enabled                     | boolean                     | NO          | 
 feature_flags                   | created_at                     | timestamp without time zone | NO          | 
 feature_flags                   | updated_at                     | timestamp without time zone | NO          | 
 feature_flags                   | updated_by                     | integer                     | YES         | 
 feature_flags                   | category                       | character varying           | NO          | 'core'::character varying
 forum_comments                  | id                             | integer                     | NO          | nextval('forum_comments_id_seq'::regclass)
 forum_comments                  | topic_id                       | integer                     | NO          | 
 forum_comments                  | author_id                      | integer                     | NO          | 
 forum_comments                  | content                        | text                        | NO          | 
 forum_comments                  | parent_comment_id              | integer                     | YES         | 
 forum_comments                  | is_edited                      | boolean                     | YES         | false
 forum_comments                  | edited_at                      | timestamp without time zone | YES         | 
 forum_comments                  | created_at                     | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 forum_comments                  | updated_at                     | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 forum_topics                    | id                             | integer                     | NO          | nextval('forum_topics_id_seq'::regclass)
 forum_topics                    | title                          | character varying           | NO          | 
 forum_topics                    | description                    | text                        | YES         | 
 forum_topics                    | author_id                      | integer                     | NO          | 
 forum_topics                    | category                       | character varying           | YES         | 
 forum_topics                    | is_pinned                      | boolean                     | YES         | false
 forum_topics                    | is_locked                      | boolean                     | YES         | false
 forum_topics                    | view_count                     | integer                     | YES         | 0
 forum_topics                    | reply_count                    | integer                     | YES         | 0
 forum_topics                    | last_activity_at               | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 forum_topics                    | created_at                     | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 forum_topics                    | updated_at                     | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 funding_applications            | id                             | integer                     | NO          | nextval('funding_applications_id_seq'::regclass)
 funding_applications            | opportunity_id                 | integer                     | NO          | 
 funding_applications            | project_title                  | character varying           | NO          | 
 funding_applications            | project_description            | text                        | YES         | 
 funding_applications            | project_duration_months        | integer                     | YES         | 
 funding_applications            | requested_amount               | double precision            | NO          | 
 funding_applications            | co_financing_amount            | double precision            | YES         | 
 funding_applications            | project_manager_id             | integer                     | YES         | 
 funding_applications            | team_composition               | text                        | YES         | 
 funding_applications            | budget_breakdown               | text                        | YES         | 
 funding_applications            | status                         | character varying           | YES         | 
 funding_applications            | submission_date                | timestamp without time zone | YES         | 
 funding_applications            | evaluation_feedback            | text                        | YES         | 
 funding_applications            | approval_date                  | timestamp without time zone | YES         | 
 funding_applications            | rejection_reason               | text                        | YES         | 
 funding_applications            | documents_checklist            | text                        | YES         | 
 funding_applications            | attachments_path               | character varying           | YES         | 
 funding_applications            | approved_amount                | double precision            | YES         | 
 funding_applications            | funding_percentage             | double precision            | YES         | 
 funding_applications            | linked_project_id              | integer                     | YES         | 
 funding_applications            | created_by                     | integer                     | NO          | 
 funding_applications            | internal_notes                 | text                        | YES         | 
 funding_applications            | priority_score                 | double precision            | YES         | 
 funding_applications            | created_at                     | timestamp without time zone | YES         | 
 funding_applications            | updated_at                     | timestamp without time zone | YES         | 
 funding_expenses                | id                             | integer                     | NO          | nextval('funding_expenses_id_seq'::regclass)
 funding_expenses                | application_id                 | integer                     | NO          | 
 funding_expenses                | project_id                     | integer                     | YES         | 
 funding_expenses                | description                    | character varying           | NO          | 
 funding_expenses                | amount                         | double precision            | NO          | 
 funding_expenses                | expense_date                   | date                        | NO          | 
 funding_expenses                | category                       | character varying           | YES         | 
 funding_expenses                | timesheet_entry_id             | integer                     | YES         | 
 funding_expenses                | is_eligible                    | boolean                     | YES         | 
 funding_expenses                | approval_status                | character varying           | YES         | 
 funding_expenses                | receipt_path                   | character varying           | YES         | 
 funding_expenses                | notes                          | text                        | YES         | 
 funding_expenses                | approved_by                    | integer                     | YES         | 
 funding_expenses                | approved_date                  | timestamp without time zone | YES         | 
 funding_expenses                | rejection_reason               | text                        | YES         | 
 funding_expenses                | created_at                     | timestamp without time zone | YES         | 
 funding_expenses                | updated_at                     | timestamp without time zone | YES         | 
 funding_opportunities           | id                             | integer                     | NO          | nextval('funding_opportunities_id_seq'::regclass)
 funding_opportunities           | title                          | character varying           | NO          | 
 funding_opportunities           | description                    | text                        | YES         | 
 funding_opportunities           | source_entity                  | character varying           | YES         | 
 funding_opportunities           | program_name                   | character varying           | YES         | 
 funding_opportunities           | call_identifier                | character varying           | YES         | 
 funding_opportunities           | total_budget                   | double precision            | YES         | 
 funding_opportunities           | max_grant_amount               | double precision            | YES         | 
 funding_opportunities           | min_grant_amount               | double precision            | YES         | 
 funding_opportunities           | contribution_percentage        | double precision            | YES         | 
 funding_opportunities           | publication_date               | date                        | YES         | 
 funding_opportunities           | application_deadline           | date                        | NO          | 
 funding_opportunities           | evaluation_period_end          | date                        | YES         | 
 funding_opportunities           | funding_period_start           | date                        | YES         | 
 funding_opportunities           | project_duration_months        | integer                     | YES         | 
 funding_opportunities           | eligibility_criteria           | text                        | YES         | 
 funding_opportunities           | evaluation_criteria            | text                        | YES         | 
 funding_opportunities           | required_documents             | text                        | YES         | 
 funding_opportunities           | target_sectors                 | text                        | YES         | 
 funding_opportunities           | geographic_scope               | character varying           | YES         | 
 funding_opportunities           | status                         | character varying           | YES         | 
 funding_opportunities           | application_procedure          | text                        | YES         | 
 funding_opportunities           | contact_info                   | text                        | YES         | 
 funding_opportunities           | official_url                   | character varying           | YES         | 
 funding_opportunities           | guidelines_url                 | character varying           | YES         | 
 funding_opportunities           | form_url                       | character varying           | YES         | 
 funding_opportunities           | created_by                     | integer                     | NO          | 
 funding_opportunities           | is_active                      | boolean                     | YES         | 
 funding_opportunities           | internal_notes                 | text                        | YES         | 
 funding_opportunities           | match_score                    | double precision            | YES         | 
 funding_opportunities           | created_at                     | timestamp without time zone | YES         | 
 funding_opportunities           | updated_at                     | timestamp without time zone | YES         | 
 funding_opportunities           | ai_generated                   | boolean                     | YES         | false
 funding_opportunities           | ai_search_query                | text                        | YES         | 
 funding_opportunities           | ai_match_score                 | double precision            | YES         | 0.0
 funding_opportunities           | ai_content                     | text                        | YES         | 
 healing_sessions                | id                             | integer                     | NO          | nextval('healing_sessions_id_seq'::regclass)
 healing_sessions                | error_pattern_id               | integer                     | YES         | 
 healing_sessions                | initiated_by                   | integer                     | YES         | 
 healing_sessions                | healing_type                   | character varying           | NO          | 
 healing_sessions                | started_at                     | timestamp without time zone | YES         | 
 healing_sessions                | completed_at                   | timestamp without time zone | YES         | 
 healing_sessions                | duration_seconds               | integer                     | YES         | 
 healing_sessions                | status                         | character varying           | YES         | 
 healing_sessions                | success                        | boolean                     | YES         | 
 healing_sessions                | claude_prompt_generated        | text                        | YES         | 
 healing_sessions                | actions_taken                  | text                        | YES         | 
 healing_sessions                | error_before_healing           | text                        | YES         | 
 healing_sessions                | verification_results           | text                        | YES         | 
 healing_sessions                | effectiveness_score            | double precision            | YES         | 
 healing_sessions                | admin_feedback                 | text                        | YES         | 
 help_analytics                  | id                             | integer                     | NO          | nextval('help_analytics_id_seq'::regclass)
 help_analytics                  | date                           | date                        | NO          | 
 help_analytics                  | period_type                    | character varying           | YES         | 
 help_analytics                  | total_views                    | integer                     | YES         | 
 help_analytics                  | unique_users                   | integer                     | YES         | 
 help_analytics                  | most_viewed_content_id         | integer                     | YES         | 
 help_analytics                  | chat_sessions                  | integer                     | YES         | 
 help_analytics                  | avg_messages_per_session       | double precision            | YES         | 
 help_analytics                  | ai_resolution_rate             | double precision            | YES         | 
 help_analytics                  | avg_satisfaction_score         | double precision            | YES         | 
 help_analytics                  | total_searches                 | integer                     | YES         | 
 help_analytics                  | no_results_searches            | integer                     | YES         | 
 help_analytics                  | top_search_terms               | text                        | YES         | 
 help_analytics                  | bounce_rate                    | double precision            | YES         | 
 help_analytics                  | avg_session_duration           | double precision            | YES         | 
 help_analytics                  | calculated_at                  | timestamp without time zone | YES         | 
 help_categories                 | id                             | integer                     | NO          | nextval('help_categories_id_seq'::regclass)
 help_categories                 | name                           | character varying           | NO          | 
 help_categories                 | slug                           | character varying           | NO          | 
 help_categories                 | description                    | text                        | YES         | 
 help_categories                 | icon                           | character varying           | YES         | 
 help_categories                 | color                          | character varying           | YES         | 
 help_categories                 | parent_id                      | integer                     | YES         | 
 help_categories                 | sort_order                     | integer                     | YES         | 
 help_categories                 | is_active                      | boolean                     | YES         | 
 help_categories                 | is_public                      | boolean                     | YES         | 
 help_categories                 | required_permission            | character varying           | YES         | 
 help_categories                 | created_by                     | integer                     | NO          | 
 help_categories                 | created_at                     | timestamp without time zone | YES         | 
 help_categories                 | updated_at                     | timestamp without time zone | YES         | 
 help_content                    | id                             | integer                     | NO          | nextval('help_content_id_seq'::regclass)
 help_content                    | title                          | character varying           | NO          | 
 help_content                    | slug                           | character varying           | NO          | 
 help_content                    | content                        | text                        | NO          | 
 help_content                    | excerpt                        | text                        | YES         | 
 help_content                    | category_id                    | integer                     | NO          | 
 help_content                    | content_type                   | character varying           | YES         | 
 help_content                    | difficulty_level               | character varying           | YES         | 
 help_content                    | estimated_read_time            | integer                     | YES         | 
 help_content                    | tags                           | text                        | YES         | 
 help_content                    | keywords                       | text                        | YES         | 
 help_content                    | related_modules                | text                        | YES         | 
 help_content                    | status                         | character varying           | YES         | 
 help_content                    | is_published                   | boolean                     | YES         | 
 help_content                    | published_at                   | timestamp without time zone | YES         | 
 help_content                    | version                        | character varying           | YES         | 
 help_content                    | previous_version_id            | integer                     | YES         | 
 help_content                    | is_public                      | boolean                     | YES         | 
 help_content                    | required_permission            | character varying           | YES         | 
 help_content                    | featured                       | boolean                     | YES         | 
 help_content                    | view_count                     | integer                     | YES         | 
 help_content                    | helpful_votes                  | integer                     | YES         | 
 help_content                    | not_helpful_votes              | integer                     | YES         | 
 help_content                    | created_by                     | integer                     | NO          | 
 help_content                    | updated_by                     | integer                     | YES         | 
 help_content                    | reviewed_by                    | integer                     | YES         | 
 help_content                    | created_at                     | timestamp without time zone | YES         | 
 help_content                    | updated_at                     | timestamp without time zone | YES         | 
 help_content                    | reviewed_at                    | timestamp without time zone | YES         | 
 help_conversations              | id                             | integer                     | NO          | nextval('help_conversations_id_seq'::regclass)
 help_conversations              | user_id                        | integer                     | NO          | 
 help_conversations              | session_id                     | character varying           | NO          | 
 help_conversations              | title                          | character varying           | YES         | 
 help_conversations              | category                       | character varying           | YES         | 
 help_conversations              | current_module                 | character varying           | YES         | 
 help_conversations              | messages                       | text                        | NO          | 
 help_conversations              | message_count                  | integer                     | YES         | 
 help_conversations              | ai_confidence_avg              | double precision            | YES         | 
 help_conversations              | escalated_to_human             | boolean                     | YES         | 
 help_conversations              | escalation_reason              | text                        | YES         | 
 help_conversations              | status                         | character varying           | YES         | 
 help_conversations              | resolution_type                | character varying           | YES         | 
 help_conversations              | user_satisfaction              | integer                     | YES         | 
 help_conversations              | user_feedback                  | text                        | YES         | 
 help_conversations              | started_at                     | timestamp without time zone | YES         | 
 help_conversations              | last_activity                  | timestamp without time zone | YES         | 
 help_conversations              | resolved_at                    | timestamp without time zone | YES         | 
 help_conversations              | created_at                     | timestamp without time zone | YES         | 
 help_conversations              | updated_at                     | timestamp without time zone | YES         | 
 help_feedback                   | id                             | integer                     | NO          | nextval('help_feedback_id_seq'::regclass)
 help_feedback                   | user_id                        | integer                     | NO          | 
 help_feedback                   | content_id                     | integer                     | YES         | 
 help_feedback                   | conversation_id                | integer                     | YES         | 
 help_feedback                   | feedback_type                  | character varying           | NO          | 
 help_feedback                   | rating                         | integer                     | YES         | 
 help_feedback                   | is_helpful                     | boolean                     | YES         | 
 help_feedback                   | feedback_text                  | text                        | YES         | 
 help_feedback                   | suggestion                     | text                        | YES         | 
 help_feedback                   | current_page                   | character varying           | YES         | 
 help_feedback                   | user_agent                     | character varying           | YES         | 
 help_feedback                   | status                         | character varying           | YES         | 
 help_feedback                   | admin_response                 | text                        | YES         | 
 help_feedback                   | responded_by                   | integer                     | YES         | 
 help_feedback                   | responded_at                   | timestamp without time zone | YES         | 
 help_feedback                   | created_at                     | timestamp without time zone | YES         | 
 help_feedback                   | updated_at                     | timestamp without time zone | YES         | 
 hr_analytics                    | id                             | integer                     | NO          | nextval('hr_analytics_id_seq'::regclass)
 hr_analytics                    | date                           | date                        | NO          | 
 hr_analytics                    | total_conversations            | integer                     | YES         | 
 hr_analytics                    | unique_users                   | integer                     | YES         | 
 hr_analytics                    | avg_response_time_ms           | double precision            | YES         | 
 hr_analytics                    | category_distribution          | text                        | YES         | 
 hr_analytics                    | feedback_distribution          | text                        | YES         | 
 hr_analytics                    | total_kb_entries               | integer                     | YES         | 
 hr_analytics                    | ai_generated_entries           | integer                     | YES         | 
 hr_analytics                    | created_at                     | timestamp without time zone | YES         | 
 hr_chat_conversations           | id                             | integer                     | NO          | nextval('hr_chat_conversations_id_seq'::regclass)
 hr_chat_conversations           | user_id                        | integer                     | NO          | 
 hr_chat_conversations           | session_id                     | character varying           | NO          | 
 hr_chat_conversations           | user_message                   | text                        | NO          | 
 hr_chat_conversations           | bot_response                   | text                        | NO          | 
 hr_chat_conversations           | category_detected              | character varying           | YES         | 
 hr_chat_conversations           | confidence_score               | character varying           | YES         | 
 hr_chat_conversations           | kb_entries_used                | text                        | YES         | 
 hr_chat_conversations           | response_time_ms               | integer                     | YES         | 
 hr_chat_conversations           | user_feedback                  | character varying           | YES         | 
 hr_chat_conversations           | created_at                     | timestamp without time zone | YES         | 
 hr_content_templates            | id                             | integer                     | NO          | nextval('hr_content_templates_id_seq'::regclass)
 hr_content_templates            | name                           | character varying           | NO          | 
 hr_content_templates            | category                       | character varying           | NO          | 
 hr_content_templates            | description                    | text                        | YES         | 
 hr_content_templates            | prompt_template                | text                        | NO          | 
 hr_content_templates            | required_fields                | text                        | YES         | 
 hr_content_templates            | output_format                  | text                        | YES         | 
 hr_content_templates            | usage_count                    | integer                     | YES         | 
 hr_content_templates            | last_used                      | timestamp without time zone | YES         | 
 hr_content_templates            | is_active                      | boolean                     | YES         | 
 hr_content_templates            | created_at                     | timestamp without time zone | YES         | 
 hr_knowledge_base               | id                             | integer                     | NO          | nextval('hr_knowledge_base_id_seq'::regclass)
 hr_knowledge_base               | title                          | character varying           | NO          | 
 hr_knowledge_base               | content                        | text                        | NO          | 
 hr_knowledge_base               | category                       | character varying           | NO          | 
 hr_knowledge_base               | tags                           | text                        | YES         | 
 hr_knowledge_base               | is_active                      | boolean                     | YES         | 
 hr_knowledge_base               | created_with_ai                | boolean                     | YES         | 
 hr_knowledge_base               | ai_sources                     | text                        | YES         | 
 hr_knowledge_base               | ai_confidence                  | character varying           | YES         | 
 hr_knowledge_base               | created_by                     | integer                     | NO          | 
 hr_knowledge_base               | created_at                     | timestamp without time zone | YES         | 
 hr_knowledge_base               | updated_at                     | timestamp without time zone | YES         | 
 integration_settings            | id                             | integer                     | NO          | nextval('integration_settings_id_seq'::regclass)
 integration_settings            | provider                       | character varying           | NO          | 
 integration_settings            | api_key_encrypted              | text                        | YES         | 
 integration_settings            | company_id                     | character varying           | YES         | 
 integration_settings            | settings_json                  | json                        | YES         | 
 integration_settings            | is_active                      | boolean                     | YES         | 
 integration_settings            | last_sync_date                 | timestamp without time zone | YES         | 
 integration_settings            | last_error                     | text                        | YES         | 
 integration_settings            | created_by                     | integer                     | NO          | 
 integration_settings            | created_at                     | timestamp without time zone | YES         | 
 integration_settings            | updated_at                     | timestamp without time zone | YES         | 
 interview_sessions              | id                             | integer                     | NO          | nextval('interview_sessions_id_seq'::regclass)
 interview_sessions              | application_id                 | integer                     | NO          | 
 interview_sessions              | interview_type                 | character varying           | NO          | 
 interview_sessions              | scheduled_date                 | timestamp without time zone | NO          | 
 interview_sessions              | duration_minutes               | integer                     | YES         | 
 interview_sessions              | location                       | character varying           | YES         | 
 interview_sessions              | interviewer_id                 | integer                     | NO          | 
 interview_sessions              | additional_interviewers        | text                        | YES         | 
 interview_sessions              | status                         | character varying           | YES         | 
 interview_sessions              | score                          | integer                     | YES         | 
 interview_sessions              | notes                          | text                        | YES         | 
 interview_sessions              | feedback                       | text                        | YES         | 
 interview_sessions              | recommendation                 | character varying           | YES         | 
 interview_sessions              | created_at                     | timestamp without time zone | YES         | 
 interview_sessions              | updated_at                     | timestamp without time zone | YES         | 
 interview_sessions              | completed_at                   | timestamp without time zone | YES         | 
 invoice_lines                   | id                             | integer                     | NO          | nextval('invoice_lines_id_seq'::regclass)
 invoice_lines                   | invoice_id                     | integer                     | NO          | 
 invoice_lines                   | project_id                     | integer                     | YES         | 
 invoice_lines                   | contract_id                    | integer                     | YES         | 
 invoice_lines                   | description                    | text                        | NO          | 
 invoice_lines                   | total_hours                    | double precision            | NO          | 
 invoice_lines                   | hourly_rate                    | double precision            | NO          | 
 invoice_lines                   | total_amount                   | double precision            | NO          | 
 invoice_lines                   | created_at                     | timestamp without time zone | YES         | 
 invoices                        | id                             | integer                     | NO          | nextval('invoices_id_seq'::regclass)
 invoices                        | client_id                      | integer                     | NO          | 
 invoices                        | invoice_number                 | character varying           | NO          | 
 invoices                        | billing_period_start           | date                        | NO          | 
 invoices                        | billing_period_end             | date                        | NO          | 
 invoices                        | issue_date                     | date                        | NO          | 
 invoices                        | due_date                       | date                        | NO          | 
 invoices                        | status                         | character varying           | YES         | 
 invoices                        | subtotal                       | double precision            | YES         | 
 invoices                        | tax_rate                       | double precision            | YES         | 
 invoices                        | tax_amount                     | double precision            | YES         | 
 invoices                        | total_amount                   | double precision            | YES         | 
 invoices                        | notes                          | text                        | YES         | 
 invoices                        | created_at                     | timestamp without time zone | YES         | 
 invoices                        | updated_at                     | timestamp without time zone | YES         | 
 job_levels                      | id                             | integer                     | NO          | nextval('job_levels_id_seq'::regclass)
 job_levels                      | name                           | character varying           | NO          | 
 job_levels                      | description                    | text                        | YES         | 
 job_levels                      | is_active                      | boolean                     | YES         | 
 job_levels                      | created_at                     | timestamp without time zone | YES         | 
 job_levels                      | updated_at                     | timestamp without time zone | YES         | 
 job_levels                      | level_number                   | integer                     | NO          | 
 job_levels                      | min_salary                     | double precision            | YES         | 
 job_levels                      | max_salary                     | double precision            | YES         | 
 job_levels                      | typical_years_experience       | integer                     | YES         | 
 job_levels                      | is_management                  | boolean                     | YES         | false
 job_levels                      | parent_level_id                | integer                     | YES         | 
 job_levels                      | level                          | character varying           | YES         | 
 job_levels                      | benefits                       | text                        | YES         | 
 job_levels                      | requirements                   | text                        | YES         | 
 job_levels                      | category                       | character varying           | YES         | 
 job_postings                    | id                             | integer                     | NO          | nextval('job_postings_id_seq'::regclass)
 job_postings                    | title                          | character varying           | NO          | 
 job_postings                    | description                    | text                        | YES         | 
 job_postings                    | requirements                   | text                        | YES         | 
 job_postings                    | responsibilities               | text                        | YES         | 
 job_postings                    | location                       | character varying           | YES         | 
 job_postings                    | remote_allowed                 | boolean                     | YES         | 
 job_postings                    | employment_type                | character varying           | YES         | 
 job_postings                    | salary_min                     | numeric                     | YES         | 
 job_postings                    | salary_max                     | numeric                     | YES         | 
 job_postings                    | salary_currency                | character varying           | YES         | 
 job_postings                    | project_id                     | integer                     | YES         | 
 job_postings                    | proposal_id                    | integer                     | YES         | 
 job_postings                    | department_id                  | integer                     | YES         | 
 job_postings                    | status                         | character varying           | YES         | 
 job_postings                    | is_public                      | boolean                     | YES         | 
 job_postings                    | created_by                     | integer                     | NO          | 
 job_postings                    | created_at                     | timestamp without time zone | YES         | 
 job_postings                    | updated_at                     | timestamp without time zone | YES         | 
 job_postings                    | published_at                   | timestamp without time zone | YES         | 
 job_postings                    | closed_at                      | timestamp without time zone | YES         | 
 kpis                            | id                             | integer                     | NO          | nextval('kpi_id_seq'::regclass)
 kpis                            | name                           | character varying           | NO          | 
 kpis                            | description                    | text                        | YES         | 
 kpis                            | category                       | character varying           | YES         | 
 kpis                            | target_value                   | double precision            | YES         | 
 kpis                            | current_value                  | double precision            | YES         | 
 kpis                            | unit                           | character varying           | YES         | 
 kpis                            | frequency                      | character varying           | YES         | 
 kpis                            | created_at                     | timestamp without time zone | YES         | 
 kpis                            | updated_at                     | timestamp without time zone | YES         | 
 kpis                            | progress                       | double precision            | YES         | 0.0
 kpis                            | project_id                     | integer                     | YES         | 
 kpis                            | owner_id                       | integer                     | YES         | 
 market_prospects                | id                             | integer                     | NO          | nextval('market_prospects_id_seq'::regclass)
 market_prospects                | company_name                   | character varying           | NO          | 
 market_prospects                | sector                         | character varying           | YES         | 
 market_prospects                | size_category                  | character varying           | YES         | 
 market_prospects                | location                       | character varying           | YES         | 
 market_prospects                | website                        | character varying           | YES         | 
 market_prospects                | contact_info                   | json                        | YES         | 
 market_prospects                | technology_needs               | json                        | YES         | 
 market_prospects                | matching_competencies          | json                        | YES         | 
 market_prospects                | fit_score                      | double precision            | YES         | 
 market_prospects                | estimated_budget_min           | double precision            | YES         | 
 market_prospects                | estimated_budget_max           | double precision            | YES         | 
 market_prospects                | lead_status                    | character varying           | YES         | 
 market_prospects                | source                         | character varying           | YES         | 
 market_prospects                | source_data                    | json                        | YES         | 
 market_prospects                | notes                          | text                        | YES         | 
 market_prospects                | assigned_to                    | integer                     | YES         | 
 market_prospects                | created_at                     | timestamp without time zone | YES         | 
 market_prospects                | updated_at                     | timestamp without time zone | YES         | 
 monthly_timesheets              | id                             | integer                     | NO          | nextval('monthly_timesheets_id_seq'::regclass)
 monthly_timesheets              | user_id                        | integer                     | NO          | 
 monthly_timesheets              | year                           | integer                     | NO          | 
 monthly_timesheets              | month                          | integer                     | NO          | 
 monthly_timesheets              | status                         | character varying           | YES         | 
 monthly_timesheets              | submission_date                | timestamp without time zone | YES         | 
 monthly_timesheets              | approval_date                  | timestamp without time zone | YES         | 
 monthly_timesheets              | approved_by                    | integer                     | YES         | 
 monthly_timesheets              | rejection_reason               | text                        | YES         | 
 monthly_timesheets              | created_at                     | timestamp without time zone | YES         | 
 monthly_timesheets              | updated_at                     | timestamp without time zone | YES         | 
 news                            | id                             | integer                     | NO          | nextval('news_id_seq1'::regclass)
 news                            | title                          | character varying           | NO          | 
 news                            | content                        | text                        | NO          | 
 news                            | author_id                      | integer                     | NO          | 
 news                            | image_url                      | character varying           | YES         | 
 news                            | is_published                   | boolean                     | YES         | 
 news                            | created_at                     | timestamp without time zone | YES         | 
 news                            | updated_at                     | timestamp without time zone | YES         | 
 notifications                   | id                             | integer                     | NO          | nextval('notification_id_seq'::regclass)
 notifications                   | user_id                        | integer                     | NO          | 
 notifications                   | title                          | character varying           | NO          | 
 notifications                   | message                        | text                        | NO          | 
 notifications                   | link                           | character varying           | YES         | 
 notifications                   | is_read                        | boolean                     | YES         | 
 notifications                   | created_at                     | timestamp without time zone | YES         | 
 notifications                   | type                           | character varying           | YES         | 'info'::character varying
 oauth_accounts                  | id                             | integer                     | NO          | nextval('oauth_accounts_id_seq'::regclass)
 oauth_accounts                  | user_id                        | integer                     | NO          | 
 oauth_accounts                  | provider                       | character varying           | NO          | 
 oauth_accounts                  | provider_user_id               | character varying           | NO          | 
 oauth_accounts                  | email                          | character varying           | NO          | 
 oauth_accounts                  | display_name                   | character varying           | YES         | 
 oauth_accounts                  | avatar_url                     | character varying           | YES         | 
 oauth_accounts                  | created_at                     | timestamp without time zone | NO          | 
 oauth_accounts                  | last_login                     | timestamp without time zone | YES         | 
 performance_feedbacks           | id                             | integer                     | NO          | nextval('performance_feedback_id_seq'::regclass)
 performance_feedbacks           | review_id                      | integer                     | NO          | 
 performance_feedbacks           | feedback_giver_id              | integer                     | NO          | 
 performance_feedbacks           | feedback_type                  | character varying           | NO          | 
 performance_feedbacks           | title                          | character varying           | NO          | 
 performance_feedbacks           | content                        | text                        | NO          | 
 performance_feedbacks           | rating                         | double precision            | YES         | 
 performance_feedbacks           | technical_feedback             | text                        | YES         | 
 performance_feedbacks           | behavioral_feedback            | text                        | YES         | 
 performance_feedbacks           | suggestions                    | text                        | YES         | 
 performance_feedbacks           | is_anonymous                   | boolean                     | YES         | false
 performance_feedbacks           | is_visible_to_employee         | boolean                     | YES         | true
 performance_feedbacks           | created_at                     | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 performance_feedbacks           | updated_at                     | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 performance_feedbacks           | feedback_period                | character varying           | YES         | 'annual'::character varying
 performance_feedbacks           | strengths                      | text                        | YES         | 
 performance_feedbacks           | improvements                   | text                        | YES         | 
 performance_feedbacks           | status                         | character varying           | YES         | 'draft'::character varying
 performance_feedbacks           | submitted_date                 | timestamp without time zone | YES         | 
 performance_feedbacks           | from_user_id                   | integer                     | YES         | 
 performance_feedbacks           | to_user_id                     | integer                     | YES         | 
 performance_feedbacks           | priority                       | character varying           | YES         | 'normal'::character varying
 performance_feedbacks           | requires_response              | boolean                     | YES         | false
 performance_feedbacks           | acknowledged_date              | timestamp without time zone | YES         | 
 performance_feedbacks           | resolved_date                  | timestamp without time zone | YES         | 
 performance_goals               | id                             | integer                     | NO          | nextval('performance_goals_id_seq'::regclass)
 performance_goals               | employee_id                    | integer                     | NO          | 
 performance_goals               | review_id                      | integer                     | YES         | 
 performance_goals               | title                          | character varying           | NO          | 
 performance_goals               | description                    | text                        | NO          | 
 performance_goals               | category                       | character varying           | YES         | 
 performance_goals               | target_year                    | integer                     | NO          | 
 performance_goals               | start_date                     | date                        | YES         | 
 performance_goals               | target_date                    | date                        | YES         | 
 performance_goals               | status                         | character varying           | YES         | 'draft'::character varying
 performance_goals               | progress_percentage            | double precision            | YES         | 0.0
 performance_goals               | completion_date                | date                        | YES         | 
 performance_goals               | success_criteria               | text                        | YES         | 
 performance_goals               | measurable_outcomes            | json                        | YES         | 
 performance_goals               | weight                         | double precision            | YES         | 1.0
 performance_goals               | priority                       | character varying           | YES         | 'medium'::character varying
 performance_goals               | achievement_rating             | double precision            | YES         | 
 performance_goals               | manager_assessment             | text                        | YES         | 
 performance_goals               | employee_self_assessment       | text                        | YES         | 
 performance_goals               | created_at                     | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 performance_goals               | updated_at                     | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 performance_goals               | created_by                     | integer                     | YES         | 
 performance_goals               | set_by_id                      | integer                     | YES         | 
 performance_goals               | quarter                        | character varying           | YES         | 
 performance_goals               | year                           | integer                     | YES         | 
 performance_goals               | notes                          | text                        | YES         | 
 performance_goals               | completion_notes               | text                        | YES         | 
 performance_goals               | progress                       | integer                     | YES         | 0
 performance_goals               | is_template                    | boolean                     | YES         | false
 performance_goals               | template_id                    | integer                     | YES         | 
 performance_goals               | assigned_by_id                 | integer                     | YES         | 
 performance_goals               | visibility                     | character varying           | YES         | 'private'::character varying
 performance_kpis                | id                             | integer                     | NO          | nextval('performance_kpis_id_seq'::regclass)
 performance_kpis                | goal_id                        | integer                     | NO          | 
 performance_kpis                | name                           | character varying           | NO          | 
 performance_kpis                | description                    | text                        | YES         | 
 performance_kpis                | unit_of_measure                | character varying           | YES         | 
 performance_kpis                | target_value                   | double precision            | NO          | 
 performance_kpis                | current_value                  | double precision            | YES         | 0.0
 performance_kpis                | baseline_value                 | double precision            | YES         | 
 performance_kpis                | measurement_frequency          | character varying           | YES         | 
 performance_kpis                | last_measured_at               | date                        | YES         | 
 performance_kpis                | is_active                      | boolean                     | YES         | true
 performance_kpis                | created_at                     | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 performance_kpis                | updated_at                     | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 performance_kpis                | measurement_unit               | character varying           | YES         | 
 performance_kpis                | is_achieved                    | boolean                     | YES         | false
 performance_kpis                | achievement_date               | date                        | YES         | 
 performance_review_participants | id                             | integer                     | NO          | nextval('performance_review_participants_id_seq'::regclass)
 performance_review_participants | review_id                      | integer                     | NO          | 
 performance_review_participants | participant_id                 | integer                     | NO          | 
 performance_review_participants | role                           | character varying           | NO          | 
 performance_review_participants | invited_at                     | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 performance_review_participants | responded_at                   | timestamp without time zone | YES         | 
 performance_review_participants | is_completed                   | boolean                     | YES         | false
 performance_review_participants | participant_role               | character varying           | YES         | 
 performance_review_participants | invitation_sent                | boolean                     | YES         | false
 performance_review_participants | invitation_date                | timestamp without time zone | YES         | 
 performance_review_participants | feedback_completed             | boolean                     | YES         | false
 performance_review_participants | completion_date                | timestamp without time zone | YES         | 
 performance_review_participants | created_at                     | timestamp without time zone | YES         | 
 performance_reviews             | id                             | integer                     | NO          | nextval('performance_reviews_id_seq'::regclass)
 performance_reviews             | employee_id                    | integer                     | NO          | 
 performance_reviews             | reviewer_id                    | integer                     | NO          | 
 performance_reviews             | review_year                    | integer                     | NO          | 
 performance_reviews             | review_period_start            | date                        | NO          | 
 performance_reviews             | review_period_end              | date                        | NO          | 
 performance_reviews             | status                         | character varying           | YES         | 'draft'::character varying
 performance_reviews             | due_date                       | date                        | YES         | 
 performance_reviews             | overall_rating                 | double precision            | YES         | 
 performance_reviews             | technical_skills_rating        | double precision            | YES         | 
 performance_reviews             | soft_skills_rating             | double precision            | YES         | 
 performance_reviews             | goals_achievement_rating       | double precision            | YES         | 
 performance_reviews             | leadership_rating              | double precision            | YES         | 
 performance_reviews             | teamwork_rating                | double precision            | YES         | 
 performance_reviews             | communication_rating           | double precision            | YES         | 
 performance_reviews             | initiative_rating              | double precision            | YES         | 
 performance_reviews             | strengths                      | text                        | YES         | 
 performance_reviews             | areas_for_improvement          | text                        | YES         | 
 performance_reviews             | manager_comments               | text                        | YES         | 
 performance_reviews             | employee_comments              | text                        | YES         | 
 performance_reviews             | hr_comments                    | text                        | YES         | 
 performance_reviews             | promotion_recommendation       | boolean                     | YES         | false
 performance_reviews             | salary_increase_recommendation | double precision            | YES         | 
 performance_reviews             | bonus_recommendation           | double precision            | YES         | 
 performance_reviews             | training_recommendations       | text                        | YES         | 
 performance_reviews             | employee_signed_at             | timestamp without time zone | YES         | 
 performance_reviews             | manager_signed_at              | timestamp without time zone | YES         | 
 performance_reviews             | hr_signed_at                   | timestamp without time zone | YES         | 
 performance_reviews             | created_at                     | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 performance_reviews             | updated_at                     | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 performance_reviews             | created_by                     | integer                     | YES         | 
 performance_reviews             | template_id                    | integer                     | YES         | 
 performance_reviews             | comments                       | text                        | YES         | 
 performance_reviews             | submitted_date                 | timestamp without time zone | YES         | 
 performance_reviews             | completed_date                 | timestamp without time zone | YES         | 
 performance_reviews             | approved_date                  | timestamp without time zone | YES         | 
 performance_reviews             | approved_by                    | integer                     | YES         | 
 performance_reviews             | achievements                   | text                        | YES         | 
 performance_reviews             | areas_improvement              | text                        | YES         | 
 performance_reviews             | development_goals              | text                        | YES         | 
 performance_reviews             | reviewer_comments              | text                        | YES         | 
 performance_rewards             | id                             | integer                     | NO          | nextval('performance_rewards_id_seq'::regclass)
 performance_rewards             | employee_id                    | integer                     | NO          | 
 performance_rewards             | goal_id                        | integer                     | YES         | 
 performance_rewards             | review_id                      | integer                     | YES         | 
 performance_rewards             | reward_type                    | character varying           | NO          | 
 performance_rewards             | title                          | character varying           | NO          | 
 performance_rewards             | description                    | text                        | YES         | 
 performance_rewards             | monetary_value                 | double precision            | YES         | 
 performance_rewards             | percentage_value               | double precision            | YES         | 
 performance_rewards             | awarded_date                   | date                        | YES         | 
 performance_rewards             | effective_date                 | date                        | YES         | 
 performance_rewards             | expiry_date                    | date                        | YES         | 
 performance_rewards             | status                         | character varying           | YES         | 'pending'::character varying
 performance_rewards             | approved_by                    | integer                     | YES         | 
 performance_rewards             | approved_at                    | timestamp without time zone | YES         | 
 performance_rewards             | disbursed_at                   | timestamp without time zone | YES         | 
 performance_rewards             | notes                          | text                        | YES         | 
 performance_rewards             | created_at                     | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 performance_rewards             | updated_at                     | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 performance_rewards             | created_by                     | integer                     | YES         | 
 performance_rewards             | currency                       | character varying           | YES         | 'EUR'::character varying
 performance_rewards             | award_date                     | date                        | YES         | 
 performance_rewards             | approved_date                  | date                        | YES         | 
 performance_rewards             | awarded_by_id                  | integer                     | YES         | 
 performance_rewards             | approved_by_hr                 | integer                     | YES         | 
 performance_templates           | id                             | integer                     | NO          | nextval('performance_templates_id_seq'::regclass)
 performance_templates           | name                           | character varying           | NO          | 
 performance_templates           | description                    | text                        | YES         | 
 performance_templates           | template_type                  | character varying           | YES         | 
 performance_templates           | job_level                      | character varying           | YES         | 
 performance_templates           | department                     | character varying           | YES         | 
 performance_templates           | evaluation_criteria            | json                        | YES         | 
 performance_templates           | rating_scale                   | json                        | YES         | 
 performance_templates           | is_active                      | boolean                     | YES         | true
 performance_templates           | created_at                     | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 performance_templates           | updated_at                     | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 performance_templates           | created_by                     | integer                     | YES         | 
 performance_templates           | fields_config                  | text                        | YES         | 
 performance_templates           | target_role                    | character varying           | YES         | 
 performance_templates           | is_default                     | boolean                     | YES         | false
 personnel_rates                 | id                             | integer                     | NO          | nextval('personnel_rates_id_seq'::regclass)
 personnel_rates                 | user_id                        | integer                     | NO          | 
 personnel_rates                 | daily_rate                     | double precision            | NO          | 
 personnel_rates                 | valid_from                     | date                        | NO          | 
 personnel_rates                 | valid_to                       | date                        | YES         | 
 personnel_rates                 | currency                       | character varying           | YES         | 
 personnel_rates                 | notes                          | character varying           | YES         | 
 personnel_rates                 | created_at                     | timestamp without time zone | YES         | 
 poll_options                    | id                             | integer                     | NO          | nextval('poll_options_id_seq'::regclass)
 poll_options                    | poll_id                        | integer                     | NO          | 
 poll_options                    | option_text                    | character varying           | NO          | 
 poll_options                    | vote_count                     | integer                     | YES         | 0
 poll_options                    | created_at                     | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 poll_votes                      | id                             | integer                     | NO          | nextval('poll_votes_id_seq'::regclass)
 poll_votes                      | poll_id                        | integer                     | NO          | 
 poll_votes                      | option_id                      | integer                     | NO          | 
 poll_votes                      | user_id                        | integer                     | NO          | 
 poll_votes                      | created_at                     | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 polls                           | id                             | integer                     | NO          | nextval('polls_id_seq'::regclass)
 polls                           | title                          | character varying           | NO          | 
 polls                           | description                    | text                        | YES         | 
 polls                           | author_id                      | integer                     | NO          | 
 polls                           | is_anonymous                   | boolean                     | YES         | false
 polls                           | multiple_choice                | boolean                     | YES         | false
 polls                           | expires_at                     | timestamp without time zone | YES         | 
 polls                           | is_active                      | boolean                     | YES         | true
 polls                           | total_votes                    | integer                     | YES         | 0
 polls                           | created_at                     | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 polls                           | updated_at                     | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 pre_invoice_lines               | id                             | integer                     | NO          | nextval('pre_invoice_lines_id_seq'::regclass)
 pre_invoice_lines               | pre_invoice_id                 | integer                     | NO          | 
 pre_invoice_lines               | project_id                     | integer                     | YES         | 
 pre_invoice_lines               | description                    | character varying           | NO          | 
 pre_invoice_lines               | total_hours                    | numeric                     | YES         | 
 pre_invoice_lines               | hourly_rate                    | numeric                     | YES         | 
 pre_invoice_lines               | total_amount                   | numeric                     | YES         | 
 pre_invoice_lines               | timesheet_entries_ids          | json                        | YES         | 
 pre_invoice_lines               | created_at                     | timestamp without time zone | YES         | 
 pre_invoices                    | id                             | integer                     | NO          | nextval('pre_invoices_id_seq'::regclass)
 pre_invoices                    | client_id                      | integer                     | NO          | 
 pre_invoices                    | contract_id                    | integer                     | YES         | 
 pre_invoices                    | pre_invoice_number             | character varying           | NO          | 
 pre_invoices                    | billing_period_start           | date                        | NO          | 
 pre_invoices                    | billing_period_end             | date                        | NO          | 
 pre_invoices                    | generated_date                 | date                        | YES         | 
 pre_invoices                    | status                         | character varying           | YES         | 
 pre_invoices                    | subtotal                       | numeric                     | YES         | 
 pre_invoices                    | vat_rate                       | numeric                     | YES         | 
 pre_invoices                    | vat_amount                     | numeric                     | YES         | 
 pre_invoices                    | retention_rate                 | numeric                     | YES         | 
 pre_invoices                    | retention_amount               | numeric                     | YES         | 
 pre_invoices                    | total_amount                   | numeric                     | YES         | 
 pre_invoices                    | external_invoice_id            | character varying           | YES         | 
 pre_invoices                    | external_status                | character varying           | YES         | 
 pre_invoices                    | external_pdf_url               | character varying           | YES         | 
 pre_invoices                    | external_sent_date             | date                        | YES         | 
 pre_invoices                    | notes                          | text                        | YES         | 
 pre_invoices                    | created_by                     | integer                     | NO          | 
 pre_invoices                    | created_at                     | timestamp without time zone | YES         | 
 pre_invoices                    | updated_at                     | timestamp without time zone | YES         | 
 process_steps                   | id                             | integer                     | NO          | nextval('process_step_id_seq'::regclass)
 process_steps                   | process_id                     | integer                     | NO          | 
 process_steps                   | name                           | character varying           | NO          | 
 process_steps                   | description                    | text                        | YES         | 
 process_steps                   | order                          | integer                     | NO          | 
 process_steps                   | responsible_role               | character varying           | YES         | 
 process_steps                   | estimated_time                 | double precision            | YES         | 
 products                        | id                             | integer                     | NO          | nextval('product_id_seq'::regclass)
 products                        | name                           | character varying           | NO          | 
 products                        | description                    | text                        | YES         | 
 products                        | category                       | character varying           | YES         | 
 products                        | price                          | double precision            | YES         | 
 products                        | status                         | character varying           | YES         | 
 products                        | created_at                     | timestamp without time zone | YES         | 
 products                        | updated_at                     | timestamp without time zone | YES         | 
 project_expenses                | id                             | integer                     | NO          | nextval('project_expenses_id_seq'::regclass)
 project_expenses                | project_id                     | integer                     | NO          | 
 project_expenses                | user_id                        | integer                     | NO          | 
 project_expenses                | category                       | character varying           | NO          | 
 project_expenses                | description                    | character varying           | NO          | 
 project_expenses                | amount                         | double precision            | NO          | 
 project_expenses                | billing_type                   | character varying           | YES         | 
 project_expenses                | date                           | date                        | NO          | 
 project_expenses                | receipt_path                   | character varying           | YES         | 
 project_expenses                | status                         | character varying           | YES         | 
 project_expenses                | created_at                     | timestamp without time zone | YES         | 
 project_funding_links           | id                             | integer                     | NO          | nextval('project_funding_links_id_seq'::regclass)
 project_funding_links           | project_id                     | integer                     | NO          | 
 project_funding_links           | funding_application_id         | integer                     | NO          | 
 project_funding_links           | percentage_allocation          | double precision            | YES         | 
 project_funding_links           | linked_at                      | timestamp without time zone | YES         | 
 project_funding_links           | notes                          | text                        | YES         | 
 project_funding_links           | created_by                     | integer                     | NO          | 
 project_funding_links           | created_at                     | timestamp without time zone | YES         | 
 project_funding_links           | updated_at                     | timestamp without time zone | YES         | 
 project_kpi_targets             | id                             | integer                     | NO          | nextval('project_kpi_targets_id_seq'::regclass)
 project_kpi_targets             | project_id                     | integer                     | NO          | 
 project_kpi_targets             | kpi_name                       | character varying           | NO          | 
 project_kpi_targets             | target_value                   | double precision            | YES         | 
 project_kpi_targets             | warning_threshold              | double precision            | YES         | 
 project_kpi_targets             | custom_description             | text                        | YES         | 
 project_kpi_targets             | created_by                     | integer                     | NO          | 
 project_kpi_targets             | created_at                     | timestamp without time zone | YES         | 
 project_kpi_targets             | updated_at                     | timestamp without time zone | YES         | 
 project_kpi_templates           | id                             | integer                     | NO          | nextval('project_kpi_templates_id_seq'::regclass)
 project_kpi_templates           | project_type                   | character varying           | NO          | 
 project_kpi_templates           | kpi_name                       | character varying           | NO          | 
 project_kpi_templates           | target_min                     | double precision            | YES         | 
 project_kpi_templates           | target_max                     | double precision            | YES         | 
 project_kpi_templates           | warning_threshold              | double precision            | YES         | 
 project_kpi_templates           | unit                           | character varying           | YES         | 
 project_kpi_templates           | description                    | text                        | YES         | 
 project_kpi_templates           | is_active                      | boolean                     | YES         | 
 project_kpi_templates           | created_at                     | timestamp without time zone | YES         | 
 project_kpi_templates           | updated_at                     | timestamp without time zone | YES         | 
 project_kpis                    | id                             | integer                     | NO          | nextval('project_kpi_id_seq'::regclass)
 project_kpis                    | project_id                     | integer                     | NO          | 
 project_kpis                    | kpi_id                         | integer                     | NO          | 
 project_kpis                    | target_value                   | double precision            | YES         | 
 project_kpis                    | current_value                  | double precision            | YES         | 
 project_resources               | id                             | integer                     | NO          | nextval('project_resource_id_seq'::regclass)
 project_resources               | project_id                     | integer                     | NO          | 
 project_resources               | user_id                        | integer                     | NO          | 
 project_resources               | allocation_percentage          | integer                     | YES         | 100
 project_resources               | role                           | character varying           | YES         | 
 project_team                    | project_id                     | integer                     | NO          | 
 project_team                    | user_id                        | integer                     | NO          | 
 project_team                    | role                           | character varying           | YES         | 
 projects                        | id                             | integer                     | NO          | nextval('project_id_seq'::regclass)
 projects                        | name                           | character varying           | NO          | 
 projects                        | description                    | text                        | YES         | 
 projects                        | client_id                      | integer                     | YES         | 
 projects                        | start_date                     | date                        | YES         | 
 projects                        | end_date                       | date                        | YES         | 
 projects                        | status                         | character varying           | YES         | 
 projects                        | budget                         | double precision            | YES         | 
 projects                        | expenses                       | double precision            | YES         | 
 projects                        | created_at                     | timestamp without time zone | YES         | 
 projects                        | updated_at                     | timestamp without time zone | YES         | 
 projects                        | is_billable                    | boolean                     | YES         | true
 projects                        | client_daily_rate              | double precision            | YES         | 
 projects                        | markup_percentage              | double precision            | YES         | 0.0
 projects                        | project_type                   | character varying           | YES         | 'service'::character varying
 projects                        | contract_id                    | integer                     | YES         | 
 projects                        | funding_source                 | character varying           | YES         | 
 projects                        | funding_application_id         | integer                     | YES         | 
 proposals                       | id                             | integer                     | NO          | nextval('proposal_id_seq'::regclass)
 proposals                       | title                          | character varying           | NO          | 
 proposals                       | client_id                      | integer                     | NO          | 
 proposals                       | description                    | text                        | YES         | 
 proposals                       | value                          | double precision            | YES         | 
 proposals                       | status                         | character varying           | YES         | 
 proposals                       | created_by                     | integer                     | YES         | 
 proposals                       | sent_date                      | date                        | YES         | 
 proposals                       | expiry_date                    | date                        | YES         | 
 proposals                       | created_at                     | timestamp without time zone | YES         | 
 proposals                       | updated_at                     | timestamp without time zone | YES         | 
 readiness_tasks                 | id                             | integer                     | NO          | nextval('readiness_tasks_id_seq'::regclass)
 readiness_tasks                 | certification_id               | integer                     | NO          | 
 readiness_tasks                 | task_name                      | character varying           | NO          | 
 readiness_tasks                 | task_description               | text                        | YES         | 
 readiness_tasks                 | task_category                  | character varying           | YES         | 
 readiness_tasks                 | requirement_reference          | character varying           | YES         | 
 readiness_tasks                 | completion_percentage          | integer                     | YES         | 
 readiness_tasks                 | status                         | character varying           | YES         | 
 readiness_tasks                 | assigned_to_id                 | integer                     | YES         | 
 readiness_tasks                 | assigned_department            | character varying           | YES         | 
 readiness_tasks                 | due_date                       | date                        | YES         | 
 readiness_tasks                 | started_date                   | date                        | YES         | 
 readiness_tasks                 | completed_date                 | date                        | YES         | 
 readiness_tasks                 | priority                       | character varying           | YES         | 
 readiness_tasks                 | estimated_hours                | double precision            | YES         | 
 readiness_tasks                 | actual_hours                   | double precision            | YES         | 
 readiness_tasks                 | evidence_files                 | json                        | YES         | 
 readiness_tasks                 | notes                          | text                        | YES         | 
 readiness_tasks                 | depends_on_task_ids            | json                        | YES         | 
 readiness_tasks                 | blocks_task_ids                | json                        | YES         | 
 readiness_tasks                 | created_at                     | timestamp without time zone | YES         | 
 readiness_tasks                 | updated_at                     | timestamp without time zone | YES         | 
 readiness_tasks                 | created_by                     | integer                     | NO          | 
 recruiting_ai_usage             | id                             | integer                     | NO          | nextval('recruiting_ai_usage_id_seq'::regclass)
 recruiting_ai_usage             | user_id                        | integer                     | NO          | 
 recruiting_ai_usage             | feature_type                   | character varying           | NO          | 
 recruiting_ai_usage             | action                         | character varying           | NO          | 
 recruiting_ai_usage             | processing_time_ms             | integer                     | YES         | 
 recruiting_ai_usage             | tokens_used                    | integer                     | YES         | 
 recruiting_ai_usage             | success                        | boolean                     | YES         | 
 recruiting_ai_usage             | error_message                  | text                        | YES         | 
 recruiting_ai_usage             | entity_type                    | character varying           | YES         | 
 recruiting_ai_usage             | entity_id                      | integer                     | YES         | 
 recruiting_ai_usage             | created_at                     | timestamp without time zone | YES         | 
 recruiting_workflows            | id                             | integer                     | NO          | nextval('recruiting_workflows_id_seq'::regclass)
 recruiting_workflows            | application_id                 | integer                     | NO          | 
 recruiting_workflows            | step_name                      | character varying           | NO          | 
 recruiting_workflows            | step_order                     | integer                     | NO          | 
 recruiting_workflows            | status                         | character varying           | YES         | 
 recruiting_workflows            | started_at                     | timestamp without time zone | YES         | 
 recruiting_workflows            | completed_at                   | timestamp without time zone | YES         | 
 recruiting_workflows            | due_date                       | timestamp without time zone | YES         | 
 recruiting_workflows            | assigned_to                    | integer                     | YES         | 
 recruiting_workflows            | result                         | character varying           | YES         | 
 recruiting_workflows            | notes                          | text                        | YES         | 
 recruiting_workflows            | created_at                     | timestamp without time zone | YES         | 
 recruiting_workflows            | updated_at                     | timestamp without time zone | YES         | 
 regulations                     | id                             | integer                     | NO          | nextval('regulation_id_seq'::regclass)
 regulations                     | title                          | character varying           | NO          | 
 regulations                     | content                        | text                        | NO          | 
 regulations                     | category                       | character varying           | YES         | 
 regulations                     | is_active                      | boolean                     | YES         | 
 regulations                     | created_at                     | timestamp without time zone | YES         | 
 regulations                     | updated_at                     | timestamp without time zone | YES         | 
 research_queries                | id                             | integer                     | NO          | nextval('research_queries_id_seq'::regclass)
 research_queries                | session_id                     | integer                     | NO          | 
 research_queries                | query_text                     | text                        | NO          | 
 research_queries                | query_type                     | character varying           | YES         | 
 research_queries                | perplexity_response            | json                        | YES         | 
 research_queries                | processed_insights             | json                        | YES         | 
 research_queries                | status                         | character varying           | YES         | 
 research_queries                | error_message                  | text                        | YES         | 
 research_queries                | started_at                     | timestamp without time zone | YES         | 
 research_queries                | completed_at                   | timestamp without time zone | YES         | 
 research_queries                | response_time_seconds          | double precision            | YES         | 
 research_queries                | token_count                    | integer                     | YES         | 
 research_queries                | cost_estimate                  | double precision            | YES         | 
 research_sessions               | id                             | integer                     | NO          | nextval('research_sessions_id_seq'::regclass)
 research_sessions               | user_id                        | integer                     | NO          | 
 research_sessions               | title                          | character varying           | NO          | 
 research_sessions               | category                       | character varying           | NO          | 
 research_sessions               | status                         | character varying           | YES         | 
 research_sessions               | research_config                | json                        | YES         | 
 research_sessions               | company_context                | json                        | YES         | 
 research_sessions               | created_at                     | timestamp without time zone | YES         | 
 research_sessions               | completed_at                   | timestamp without time zone | YES         | 
 risks                           | id                             | integer                     | NO          | nextval('risks_id_seq'::regclass)
 risks                           | title                          | character varying           | NO          | 
 risks                           | description                    | text                        | NO          | 
 risks                           | category                       | character varying           | NO          | 
 risks                           | risk_type                      | character varying           | YES         | 
 risks                           | probability                    | integer                     | NO          | 
 risks                           | impact                         | integer                     | NO          | 
 risks                           | risk_level                     | character varying           | NO          | 
 risks                           | risk_score                     | double precision            | YES         | 
 risks                           | status                         | character varying           | YES         | 
 risks                           | owner_id                       | integer                     | YES         | 
 risks                           | responsible_department         | character varying           | YES         | 
 risks                           | mitigation_strategy            | text                        | YES         | 
 risks                           | mitigation_actions             | json                        | YES         | 
 risks                           | mitigation_deadline            | timestamp without time zone | YES         | 
 risks                           | mitigation_cost                | double precision            | YES         | 
 risks                           | regulatory_requirements        | json                        | YES         | 
 risks                           | compliance_framework           | character varying           | YES         | 
 risks                           | identified_date                | timestamp without time zone | YES         | 
 risks                           | last_review_date               | timestamp without time zone | YES         | 
 risks                           | next_review_date               | timestamp without time zone | YES         | 
 risks                           | resolved_date                  | timestamp without time zone | YES         | 
 risks                           | tags                           | json                        | YES         | 
 risks                           | external_references            | json                        | YES         | 
 risks                           | created_at                     | timestamp without time zone | YES         | 
 risks                           | updated_at                     | timestamp without time zone | YES         | 
 scheduled_tasks                 | id                             | integer                     | NO          | nextval('scheduled_tasks_id_seq'::regclass)
 scheduled_tasks                 | title                          | character varying           | NO          | 
 scheduled_tasks                 | description                    | text                        | YES         | 
 scheduled_tasks                 | task_type                      | character varying           | NO          | 
 scheduled_tasks                 | frequency                      | character varying           | NO          | 
 scheduled_tasks                 | cron_expression                | character varying           | YES         | 
 scheduled_tasks                 | last_run                       | timestamp without time zone | YES         | 
 scheduled_tasks                 | next_run                       | timestamp without time zone | YES         | 
 scheduled_tasks                 | config_params                  | json                        | YES         | 
 scheduled_tasks                 | data_sources                   | json                        | YES         | 
 scheduled_tasks                 | status                         | character varying           | YES         | 
 scheduled_tasks                 | last_result                    | json                        | YES         | 
 scheduled_tasks                 | error_message                  | text                        | YES         | 
 scheduled_tasks                 | average_duration_seconds       | double precision            | YES         | 
 scheduled_tasks                 | success_rate                   | double precision            | YES         | 
 scheduled_tasks                 | created_at                     | timestamp without time zone | YES         | 
 scheduled_tasks                 | updated_at                     | timestamp without time zone | YES         | 
 scheduled_tasks                 | created_by                     | integer                     | NO          | 
 services                        | id                             | integer                     | NO          | nextval('service_id_seq'::regclass)
 services                        | name                           | character varying           | NO          | 
 services                        | description                    | text                        | YES         | 
 services                        | category                       | character varying           | YES         | 
 services                        | hourly_rate                    | double precision            | YES         | 
 services                        | status                         | character varying           | YES         | 
 services                        | created_at                     | timestamp without time zone | YES         | 
 services                        | updated_at                     | timestamp without time zone | YES         | 
 skills                          | id                             | integer                     | NO          | nextval('skill_id_seq'::regclass)
 skills                          | name                           | character varying           | NO          | 
 skills                          | category                       | character varying           | YES         | 
 skills                          | description                    | text                        | YES         | 
 startup_resources               | id                             | integer                     | NO          | nextval('startup_resource_id_seq'::regclass)
 startup_resources               | title                          | character varying           | NO          | 
 startup_resources               | description                    | text                        | YES         | 
 startup_resources               | resource_type                  | character varying           | YES         | 
 startup_resources               | link                           | character varying           | YES         | 
 startup_resources               | is_active                      | boolean                     | YES         | 
 startup_resources               | created_at                     | timestamp without time zone | YES         | 
 startup_resources               | updated_at                     | timestamp without time zone | YES         | 
 strategic_insights              | id                             | integer                     | NO          | nextval('strategic_insights_id_seq'::regclass)
 strategic_insights              | session_id                     | integer                     | NO          | 
 strategic_insights              | insight_type                   | character varying           | YES         | 
 strategic_insights              | title                          | character varying           | NO          | 
 strategic_insights              | content                        | text                        | NO          | 
 strategic_insights              | summary                        | text                        | YES         | 
 strategic_insights              | confidence_score               | double precision            | YES         | 
 strategic_insights              | priority                       | character varying           | YES         | 
 strategic_insights              | impact_score                   | integer                     | YES         | 
 strategic_insights              | action_items                   | json                        | YES         | 
 strategic_insights              | timeline_estimate              | character varying           | YES         | 
 strategic_insights              | source_queries                 | json                        | YES         | 
 strategic_insights              | tags                           | json                        | YES         | 
 strategic_insights              | status                         | character varying           | YES         | 
 strategic_insights              | reviewed_by                    | integer                     | YES         | 
 strategic_insights              | reviewed_at                    | timestamp without time zone | YES         | 
 strategic_insights              | notes                          | text                        | YES         | 
 strategic_insights              | created_at                     | timestamp without time zone | YES         | 
 strategic_insights              | updated_at                     | timestamp without time zone | YES         | 
 system_health                   | id                             | integer                     | NO          | nextval('system_health_id_seq'::regclass)
 system_health                   | timestamp                      | timestamp without time zone | NO          | 
 system_health                   | health_score                   | double precision            | NO          | 
 system_health                   | error_count_24h                | integer                     | YES         | 
 system_health                   | critical_errors                | integer                     | YES         | 
 system_health                   | auto_healed_count              | integer                     | YES         | 
 system_health                   | pending_issues                 | integer                     | YES         | 
 system_health                   | system_load                    | double precision            | YES         | 
 system_health                   | system_metadata                | text                        | YES         | 
 task_dependencies               | id                             | integer                     | NO          | nextval('task_dependency_id_seq'::regclass)
 task_dependencies               | task_id                        | integer                     | NO          | 
 task_dependencies               | depends_on_id                  | integer                     | NO          | 
 tasks                           | id                             | integer                     | NO          | nextval('task_id_seq'::regclass)
 tasks                           | name                           | character varying           | NO          | 
 tasks                           | description                    | text                        | YES         | 
 tasks                           | project_id                     | integer                     | NO          | 
 tasks                           | assignee_id                    | integer                     | YES         | 
 tasks                           | status                         | character varying           | YES         | 
 tasks                           | priority                       | character varying           | YES         | 
 tasks                           | due_date                       | date                        | YES         | 
 tasks                           | created_at                     | timestamp without time zone | YES         | 
 tasks                           | updated_at                     | timestamp without time zone | YES         | 
 tasks                           | start_date                     | date                        | YES         | 
 tasks                           | estimated_hours                | double precision            | YES         | 
 technical_offers                | id                             | integer                     | NO          | nextval('technical_offers_id_seq'::regclass)
 technical_offers                | title                          | character varying           | NO          | 
 technical_offers                | description                    | text                        | YES         | 
 technical_offers                | core_competency_id             | integer                     | YES         | 
 technical_offers                | target_sector                  | character varying           | YES         | 
 technical_offers                | technology_stack               | json                        | YES         | 
 technical_offers                | team_composition               | json                        | YES         | 
 technical_offers                | estimated_duration_days        | integer                     | YES         | 
 technical_offers                | estimated_cost_min             | double precision            | YES         | 
 technical_offers                | estimated_cost_max             | double precision            | YES         | 
 technical_offers                | deliverables                   | json                        | YES         | 
 technical_offers                | success_metrics                | json                        | YES         | 
 technical_offers                | risk_factors                   | json                        | YES         | 
 technical_offers                | generated_by_ai                | boolean                     | YES         | 
 technical_offers                | ai_prompt_used                 | text                        | YES         | 
 technical_offers                | status                         | character varying           | YES         | 
 technical_offers                | created_by                     | integer                     | YES         | 
 technical_offers                | created_at                     | timestamp without time zone | YES         | 
 technical_offers                | updated_at                     | timestamp without time zone | YES         | 
 time_off_requests               | id                             | integer                     | NO          | nextval('time_off_requests_id_seq'::regclass)
 time_off_requests               | user_id                        | integer                     | NO          | 
 time_off_requests               | request_type                   | character varying           | NO          | 
 time_off_requests               | start_date                     | date                        | NO          | 
 time_off_requests               | end_date                       | date                        | NO          | 
 time_off_requests               | status                         | character varying           | YES         | 
 time_off_requests               | notes                          | text                        | YES         | 
 time_off_requests               | submission_date                | timestamp without time zone | YES         | 
 time_off_requests               | approval_date                  | timestamp without time zone | YES         | 
 time_off_requests               | approved_by                    | integer                     | YES         | 
 time_off_requests               | rejection_reason               | text                        | YES         | 
 time_off_requests               | created_at                     | timestamp without time zone | YES         | 
 time_off_requests               | updated_at                     | timestamp without time zone | YES         | 
 timesheet_entries               | id                             | integer                     | NO          | nextval('timesheet_id_seq'::regclass)
 timesheet_entries               | user_id                        | integer                     | NO          | 
 timesheet_entries               | project_id                     | integer                     | NO          | 
 timesheet_entries               | task_id                        | integer                     | YES         | 
 timesheet_entries               | date                           | date                        | NO          | 
 timesheet_entries               | hours                          | double precision            | NO          | 
 timesheet_entries               | description                    | text                        | YES         | 
 timesheet_entries               | status                         | character varying           | YES         | 
 timesheet_entries               | created_at                     | timestamp without time zone | YES         | 
 timesheet_entries               | monthly_timesheet_id           | integer                     | YES         | 
 timesheet_entries               | billable                       | boolean                     | YES         | false
 timesheet_entries               | billing_rate                   | double precision            | YES         | 
 timesheet_entries               | contract_id                    | integer                     | YES         | 
 timesheet_entries               | invoice_line_id                | integer                     | YES         | 
 timesheet_entries               | billing_status                 | character varying           | YES         | 'unbilled'::character varying
 user_profiles                   | id                             | integer                     | NO          | nextval('user_profiles_id_seq'::regclass)
 user_profiles                   | user_id                        | integer                     | NO          | 
 user_profiles                   | employee_id                    | character varying           | YES         | 
 user_profiles                   | job_title                      | character varying           | YES         | 
 user_profiles                   | birth_date                     | date                        | YES         | 
 user_profiles                   | address                        | text                        | YES         | 
 user_profiles                   | emergency_contact_name         | character varying           | YES         | 
 user_profiles                   | emergency_contact_phone        | character varying           | YES         | 
 user_profiles                   | emergency_contact_relationship | character varying           | YES         | 
 user_profiles                   | employment_type                | character varying           | YES         | 'full_time'::character varying
 user_profiles                   | work_location                  | character varying           | YES         | 
 user_profiles                   | salary                         | double precision            | YES         | 
 user_profiles                   | salary_currency                | character varying           | YES         | 'EUR'::character varying
 user_profiles                   | probation_end_date             | date                        | YES         | 
 user_profiles                   | contract_end_date              | date                        | YES         | 
 user_profiles                   | notice_period_days             | integer                     | YES         | 30
 user_profiles                   | weekly_hours                   | double precision            | YES         | 40.0
 user_profiles                   | daily_hours                    | double precision            | YES         | 8.0
 user_profiles                   | profile_completion             | double precision            | YES         | 0.0
 user_profiles                   | notes                          | text                        | YES         | 
 user_profiles                   | created_at                     | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 user_profiles                   | updated_at                     | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 user_profiles                   | current_cv_path                | character varying           | YES         | 
 user_profiles                   | cv_last_updated                | timestamp without time zone | YES         | 
 user_profiles                   | cv_analysis_data               | text                        | YES         | 
 user_skills                     | id                             | integer                     | NO          | nextval('user_skill_id_seq'::regclass)
 user_skills                     | user_id                        | integer                     | NO          | 
 user_skills                     | skill_id                       | integer                     | NO          | 
 user_skills                     | proficiency_level              | integer                     | YES         | 
 user_skills                     | years_experience               | double precision            | YES         | 
 user_skills                     | last_used                      | date                        | YES         | 
 user_skills                     | certified                      | boolean                     | YES         | 
 user_skills                     | certification_date             | date                        | YES         | 
 user_skills                     | certification_name             | character varying           | YES         | 
 user_skills_detailed            | id                             | integer                     | NO          | nextval('user_skills_detailed_id_seq'::regclass)
 user_skills_detailed            | user_id                        | integer                     | NO          | 
 user_skills_detailed            | skill_id                       | integer                     | NO          | 
 user_skills_detailed            | proficiency_level              | integer                     | YES         | 1
 user_skills_detailed            | years_experience               | double precision            | YES         | 0.0
 user_skills_detailed            | is_certified                   | boolean                     | YES         | false
 user_skills_detailed            | certification_name             | character varying           | YES         | 
 user_skills_detailed            | certification_date             | date                        | YES         | 
 user_skills_detailed            | certification_expiry           | date                        | YES         | 
 user_skills_detailed            | self_assessed                  | boolean                     | YES         | true
 user_skills_detailed            | manager_assessed               | boolean                     | YES         | false
 user_skills_detailed            | manager_assessment_date        | date                        | YES         | 
 user_skills_detailed            | notes                          | text                        | YES         | 
 user_skills_detailed            | created_at                     | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 user_skills_detailed            | updated_at                     | timestamp without time zone | YES         | CURRENT_TIMESTAMP
 user_skills_detailed            | last_used                      | date                        | YES         | 
 user_skills_detailed            | certified                      | boolean                     | YES         | false
 users                           | id                             | integer                     | NO          | nextval('user_id_seq'::regclass)
 users                           | username                       | character varying           | NO          | 
 users                           | email                          | character varying           | NO          | 
 users                           | password_hash                  | character varying           | YES         | 
 users                           | first_name                     | character varying           | YES         | 
 users                           | last_name                      | character varying           | YES         | 
 users                           | role                           | character varying           | NO          | 'employee'::character varying
 users                           | department                     | character varying           | YES         | 
 users                           | position                       | character varying           | YES         | 
 users                           | hire_date                      | date                        | YES         | 
 users                           | phone                          | character varying           | YES         | 
 users                           | profile_image                  | character varying           | YES         | 
 users                           | bio                            | text                        | YES         | 
 users                           | is_active                      | boolean                     | YES         | 
 users                           | dark_mode                      | boolean                     | YES         | 
 users                           | created_at                     | timestamp without time zone | YES         | 
 users                           | last_login                     | timestamp without time zone | YES         | 
 users                           | reset_token                    | character varying           | YES         | 
 users                           | reset_token_expiry             | timestamp without time zone | YES         | 
 users                           | department_id                  | integer                     | YES         | 
(1637 rows)

